{
  "root": true,
  "env": {
    "node": true,
    "browser": true,
    "jquery": true,
    "es6": true
  },
  "globals": {
    "moment": "readonly",
    "js_beautify": true,
    "QZAuth": true
  },
  //'plugin:prettier/recommended'作为extends最后一项，在eslint与prettier冲突时使用prettier格式化代码样式，使用前提是下载eslint-config-prettier npm包
  "extends": [
    "eslint:recommended",
    "plugin:vue/no-layout-rules",
    "plugin:prettier/recommended"
  ],
  "parser": "vue-eslint-parser", // 解析vue文件
  // 配置解析器的选项
  "parserOptions": {
    "parser": "espree",
    "sourceType": "module", // 使用 ES6 模块
    "ecmaVersion": 2022, // 设置 ECMAScript 版本为 2022
    "ecmaFeatures": {
      "jsx": true
    },
    "requireConfigFile": false
  },
  /**
   * 启用 React 插件
   * Parsing error: This experimental syntax requires enabling one of the following parser plugin(s): "jsx", "flow", "typescript"
   */
  "plugins": ["react"],
  "settings": {
    "react": {
      "pragma": "React",
      "version": "detect" // 自动检测 React 版本
    }
  },
  "rules": {
    "no-unused-vars": "warn",
    "vue/multi-word-component-names": "off",
    "no-const-assign": "error", //不允许改变用const声明的变量
    "no-use-before-define": "warn", //禁止定义前使用
    "default-case": "error", // 要求 Switch 语句中有 Default 分支
    "handle-callback-err": "error", // 强制回调错误处理
    // 声明后永远不会重新分配的变量需要 const 声明，自动监测，会将没有被重新赋值的let改为const
    "prefer-const": [
      "error",
      {
        "destructuring": "any",
        "ignoreReadBeforeAssign": false
      }
    ]
  },
  // 为不同文件类型设置不同的规则
  "overrides": [
    {
      "files": ["src/components-react/**/*.jsx"],
      "extends": ["plugin:react/recommended"],
      "rules": {
        "no-unused-vars": "off",
        "react/prop-types": "off",
        "react/no-find-dom-node": "off",
        "react/jsx-no-undef": "off"
      }
    }
  ]
}
