

# docker build -t common:latest -f Dockerfile  target/

#FROM busybox:latest
#ADD *.jar /home/

## 内网公共仓库环境变量
ARG DIND_PUBLIC_REGIATRY_URL
# 内网私有仓库环境变量
ARG DIND_REGIATRY_URL

#版本信息
ARG VERSION

# git环境
ARG GIT_REPOSITORY
ARG GIT_BRANCH
ARG GIT_TAG
ARG GIT_REVISION
ARG GIT_COMMIT_HASH
ARG GIT_COMMITTER_NAME
ARG GIT_COMMITTER_EMAIL
ARG GIT_COMMITTER_DATE

FROM ${DIND_PUBLIC_REGIATRY_URL}nginx:latest

# FROM registry-ops.qzkeji.cn/qzkj-ops/plate-form-f:1.1.0

# 在构建镜像过程中使用参数变量
ARG DIND_PUBLIC_REGIATRY_URL

#版本信息
ARG VERSION

# git环境
ARG GIT_REPOSITORY
ARG GIT_BRANCH
ARG GIT_TAG
ARG GIT_REVISION
ARG GIT_COMMIT_HASH
ARG GIT_COMMITTER_NAME
ARG GIT_COMMITTER_EMAIL
ARG GIT_COMMITTER_DATE

ADD dist /webapp/dist


# RUN mkdir -p /home/<USER>/app-operation-f/home/<USER>/discover
# CMD docker start nginx
# 基本信息
LABEL name="plate-form-fc" description="新数据地图前端"  version="$VERSION"

# 代码信息
LABEL git.revision="$GIT_REVISION" git.commit_hash="$GIT_COMMIT_HASH" git.committer_name="$GIT_COMMITTER_NAME" git.committer_email="$GIT_COMMITTER_EMAIL" git.committer_date="$GIT_COMMITTER_DATE" git.repository="$GIT_REPOSITORY" git.branch="$GIT_BRANCH" git.tag="$GIT_TAG"
