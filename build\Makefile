
# 本文件用于配置仓库的构建阶段，支持阶段内多种任务的依赖和并发管理

# 当前目录为仓库的build目录


.PHONY: prep build sign deploy docker-push helm-push   default


MAKE_DEFAULT_EXIT_CODE=1



default:
# 	@exit ${MAKE_DEFAULT_EXIT_CODE}
	@echo default


# 编译组件
build:
# 	@exit ${MAKE_DEFAULT_EXIT_CODE}
	@test -f bb-$@.sh && bash ${BASH_ARGS} bb-$@.sh


# 上传到组件库，开发库或生产库
#deploy: 
# 	@exit ${MAKE_DEFAULT_EXIT_CODE}
#	@test -f bb-$@.sh && bash ${BASH_ARGS} bb-$@.sh || exit


# 上传到容器镜像库，开发库或生产库
docker-push:
# 	@exit ${MAKE_DEFAULT_EXIT_CODE}
	@test -f bb-$@.sh && bash ${BASH_ARGS} bb-$@.sh


# 上传到helm charts库，开发库或生产库
helm-push:
# 	@exit ${MAKE_DEFAULT_EXIT_CODE}
	@test -f bb-$@.sh && bash ${BASH_ARGS} bb-$@.sh