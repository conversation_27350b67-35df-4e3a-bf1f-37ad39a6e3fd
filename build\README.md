# 说明

本目录用于在CI构建时使用，主要入口Makefile文件，里面会包含各种阶段的target，CI构建时会进入此目录并通过make命令来完成各个阶段的构建

会区分组件和产品

- 组件的主要阶段target
* prep
* build
* test
* sign
* deploy
* docker-push
* helm-push


- 产品的主要阶段target
* prep
* build
* test
* package
* iso
* sign
* upload



- 组件构建  

前缀 | 步骤 | 说明 | 备注
|:--|:--:|:--:|:--:|
C1 | git-pull | 下载代码 | 
C2 | prep | 预操作 | 检查系统环境
C3 | build | 构建组件 | 
C4 | test | 测试 | 可选
C5 | sign | 代码签名 | 可选
C6 | deploy | 部署上传组件库 | 可选
C7 | docker-push | 上传镜像 | 可选 （内含dive）
C8 | helm-push | 上传K8s配置 | 可选


- 产品构建  

前缀 | 步骤 | 说明 | 备注
|:--|:--:|:--:|:--:|
P1 | git-pull | 下载配置文件 | 
P2 | prep | 预操作 | 检查系统环境
P3 | build | 构建产品版本 | 升级版本相似
P4 | test | 测试 | 可选 升级版本相似
P5 | package | 生成安装包 | 升级版本相似
P6 | iso | 生成安装光盘 | 可选 升级版本相似
P7 | sign | 代码签名 | 可选
P8 | upload | 上传版本交付文件 | 包含 安装包和安装光盘




