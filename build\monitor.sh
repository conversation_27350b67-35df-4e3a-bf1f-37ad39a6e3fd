#!/bin/bash

#启动nginx

#echo '启动ngninx' >> /tmp/monitor.log
nginx -c /usr/local/openresty/nginx/conf/nginx.conf
#echo 'nginx启动完成' >> /tmp/monitor.log

md5sum /usr/local/openresty/nginx/conf/nginx.conf > /etc/nginx/qz/nginx_conf_md5_log.log

#死循环一直检测md5
while [ true ];
do
 #最后一行包含文件名的md5字符串，也就是上一次的
 md5_contain_file_ori=$(tail -n 1 /etc/nginx/qz/nginx_conf_md5_log.log)

 #截取不包含文件名的单独的md5字符串
 md5_ori=${md5_contain_file_ori:0:33}
 #echo '上一次的md5' >> /tmp/monitor.log
 #echo $md5_ori >> /tmp/monitor.log

 md5_contain_file_new=$(md5sum /usr/local/openresty/nginx/conf/nginx.conf)
 #echo '最新的md5' >> /tmp/monitor.log
 md5_new=${md5_contain_file_new:0:33}
 #echo $md5_new >> /tmp/monitor.log


 if [ $md5_ori != $md5_new ]; then
   #文件已发生更改
   #echo '文件发生变更' >> /tmp/monitor.log
   md5sum /usr/local/openresty/nginx/conf/nginx.conf > /etc/nginx/qz/nginx_conf_md5_log.log
   nginx -s reload
 fi
 #休息一秒继续检测
 sleep 1s
done
