#user www-data;
worker_processes auto;
pid /tmp/nginx.pid;

events {
        worker_connections 768;
        # multi_accept on;
}

http {

        ##
        # Basic Settings
        ##

        sendfile on;
        tcp_nopush on;
        tcp_nodelay on;
        types_hash_max_size 2048;
        # server_tokens off;

        # server_names_hash_bucket_size 64;
        # server_name_in_redirect off;

        include       mime.types;
        default_type application/octet-stream;

        ##
        # SSL Settings
        ##

        ssl_protocols TLSv1 TLSv1.1 TLSv1.2; # Dropping SSLv3, ref: POODLE
        ssl_prefer_server_ciphers on;

        ##
        # Logging Settings
        ##

        access_log /usr/local/openresty/nginx/logs/access.log;
        error_log /usr/local/openresty/nginx/logs/error.log;

        ##
        # Gzip Settings
        ##

        gzip on;
        gzip_disable "msie6";


        gzip_vary on;
        gzip_comp_level 5;
        gzip_min_length 1024;
        gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

        # gzip_vary on;
        # gzip_proxied any;
        # gzip_comp_level 6;
        # gzip_buffers 16 8k;
        # gzip_http_version 1.1;
        # gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

        ##
        # Virtual Host Configs
        ##

        #include /etc/nginx/conf.d/nginx_server.conf;
        ##        include /etc/nginx/sites-enabled/*;
        limit_conn_zone $binary_remote_addr zone=perip:10m;
        client_max_body_size 6g;
        client_body_timeout 60;
        client_header_timeout 30;
        keepalive_timeout 120;
        send_timeout 30;
        server_tokens off;


        map $http_upgrade $connection_upgrade {
          default upgrade;
          '' close;
        }

        server {
            listen 80;
            return 301 https://$http_host$1;
            server_tokens off;
            error_page 400 404 413 502 504 /error;
        }

        server{
                listen 8000 ssl;
                server_name localhost;   #你的serverName
                #root /webapp/dist;
                index index.html;
                ssl_certificate /home/<USER>/tls.cert;
                ssl_certificate_key /home/<USER>/tls.key;

                add_header X-Frame-Options SAMEORIGIN;
                server_tokens off;
                error_page 400 404 413 502 504 /error;

                # proxy_intercept_errors off;
                # error_page 400 https://$http_host/#/error?code=400;
                # error_page 404 https://$http_host/#/error?code=404;
                # error_page 500 https://$http_host/#/error?code=500;

                include /home/<USER>/*.conf;
                location / {
                        root  /webapp/dist;
                        index  index.html index.htm;
                        try_files $uri $uri/ /index.html;
                        limit_rate 10240k;
                        limit_conn perip 100;
                }
                location /app-operation/ws/ {
                        proxy_pass   http://unified-platform-b:8081/app-operation/ws/;
                        proxy_set_header HOST $http_host;
                        proxy_set_header  X-Real-IP $remote_addr;
                        proxy_set_header  X-Forwarded-For $proxy_add_x_forwarded_for;
                        proxy_http_version 1.1;
                        proxy_set_header Upgrade $http_upgrade;
                        proxy_set_header Connection $connection_upgrade;
                        limit_rate 10240k;
                        limit_conn perip 100;
                }
                location /app-operation/ {
                        proxy_pass   http://unified-platform-b:8081/app-operation/;
                        proxy_redirect  http:// https://;
                        proxy_set_header HOST $http_host;
                        proxy_set_header  X-Real-IP $remote_addr;
                        proxy_set_header  X-Forwarded-For $proxy_add_x_forwarded_for;
                        proxy_cookie_path / "/; httponly; secure; SameSite=Lax";
                        client_max_body_size   1024m;
                        proxy_connect_timeout 3600s;
                        proxy_send_timeout 3600s;
                        proxy_read_timeout 3600s;
                        add_header X-Frame-Options SAMEORIGIN;
                        limit_rate 10240k;
                        limit_conn perip 100;
                }
                # 支持自定义免登跳转
                location /custom/ {
                        proxy_pass   http://unified-platform-b:8081/app-operation/custom/;
                        proxy_redirect  http:// https://;
                        proxy_set_header HOST $http_host;
                        proxy_set_header  X-Real-IP $remote_addr;
                        proxy_set_header  X-Forwarded-For $proxy_add_x_forwarded_for;
                        proxy_cookie_path / "/; httponly; secure; SameSite=Lax";
                        client_max_body_size   1024m;
                        proxy_connect_timeout 3600s;
                        proxy_send_timeout 3600s;
                        proxy_read_timeout 3600s;
                        add_header X-Frame-Options SAMEORIGIN;
                        limit_rate 10240k;
                        limit_conn perip 100;
                }
                location /condor-server/websocket/ {
                        proxy_pass   http://common-com:8080/condor-server/websocket/;
                        proxy_set_header HOST $http_host;
                        proxy_set_header  X-Real-IP $remote_addr;
                        proxy_set_header  X-Forwarded-For $proxy_add_x_forwarded_for;
                        proxy_http_version 1.1;
                        proxy_set_header Upgrade $http_upgrade;
                        proxy_set_header Connection $connection_upgrade;
                        limit_rate 10240k;
                        limit_conn perip 100;
                }


                # 升级包上传较大且耗时较久，需要单独配置
                location ^~ /app-operation/api/sysUpgrade/uploadFile {
                        proxy_pass   http://unified-platform-b:8081/app-operation/api/sysUpgrade/uploadFile;
                        proxy_redirect  http:// https://;
                        proxy_set_header HOST $http_host;
                        proxy_set_header  X-Real-IP $remote_addr;
                        proxy_set_header  X-Forwarded-For $proxy_add_x_forwarded_for;
                        client_max_body_size   6144m;
                        proxy_connect_timeout 3600s;
                        proxy_read_timeout 3600s;
                        proxy_send_timeout 3600s;
                        add_header X-Frame-Options SAMEORIGIN;
                        limit_rate 10240k;
                        limit_conn perip 100;
                }
                location /platform-report/ {
                        alias /home/<USER>/qz-app-operation/plugins/app-operation-report/report/;
                        index index.html index.htm;
                        limit_rate 10240k;
                        limit_conn perip 100;
                }
                location /grafana/ {
                        set_by_lua_file $auth_cookie /home/<USER>/checkLoginAuth.lua;

                        proxy_pass   http://grafana-server:3000/grafana/;
                        proxy_redirect  http:// https://;
                        proxy_set_header HOST $http_host;
                        proxy_set_header  X-Real-IP $remote_addr;
                        proxy_set_header  X-Forwarded-For $proxy_add_x_forwarded_for;
                        proxy_cookie_path / "/; httponly; secure; SameSite=Lax";
                        client_max_body_size   1024m;
                        proxy_connect_timeout 3600s;
                        proxy_send_timeout 3600s;
                        proxy_read_timeout 3600s;
                        add_header X-Frame-Options SAMEORIGIN;
                        limit_rate 10240k;
                        limit_conn perip 100;
                }
                # 修改地图文件系统代理地址
                location /scan-file/ {
                        proxy_pass http://datamapping-scan-file:8082/;
                        proxy_redirect http:// https://;
                        proxy_set_header HOST $http_host;
                        proxy_set_header X-Real-IP $remote_addr;
                        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                        proxy_cookie_path / "/; httponly; secure; SameSite=Lax";
                        client_max_body_size 1024m;
                        proxy_connect_timeout 3600s;
                        proxy_send_timeout 3600s;
                        proxy_read_timeout 3600s;
                        add_header X-Frame-Options SAMEORIGIN;
                        limit_rate 10240k;
                        limit_conn perip 100;
                }

                #location /nifi {
                #        proxy_pass https://nifi-server:8443/nifi;  # 替换为 NiFi 服务器的实际 IP 和端口
                #        proxy_set_header Host $host;
                #        proxy_set_header X-Real-IP $remote_addr;
                #        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                #        proxy_set_header X-Forwarded-Proto $scheme;
                #}
        }

}
