apiVersion: v1
kind: ConfigMap
metadata:
  name: plate-form-fc
data:
  nginx.conf: |
    user www-data;
    worker_processes auto;
    pid /tmp/nginx.pid;

    events {
            worker_connections 768;
            # multi_accept on;
    }

    http {

            ##
            # Basic Settings
            ##

            sendfile on;
            tcp_nopush on;
            tcp_nodelay on;
            keepalive_timeout 65;
            types_hash_max_size 2048;
            # server_tokens off;

            # server_names_hash_bucket_size 64;
            # server_name_in_redirect off;

            include /etc/nginx/mime.types;
            default_type application/octet-stream;

            ##
            # SSL Settings
            ##

            ssl_protocols TLSv1 TLSv1.1 TLSv1.2; # Dropping SSLv3, ref: POODLE
            ssl_prefer_server_ciphers on;

            ##
            # Logging Settings
            ##

            access_log /var/log/nginx/access.log;
            error_log /var/log/nginx/error.log;

            ##
            # Gzip Settings
            ##

            gzip on;
            gzip_disable "msie6";

            # gzip_vary on;
            # gzip_proxied any;
            # gzip_comp_level 6;
            # gzip_buffers 16 8k;
            # gzip_http_version 1.1;
            # gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

            ##
            # Virtual Host Configs
            ##

            ##        include /etc/nginx/conf.d/*.conf;
            ##        include /etc/nginx/sites-enabled/*;

            server{
                    listen 8000;
                    server_name localhost;   #你的serverName
                    root /webapp/dist;
                    index index.html;
                    add_header X-Frame-Options SAMEORIGIN;
                    client_max_body_size 20g;

                    proxy_intercept_errors on;
                    error_page 400 https://$http_host/#/error?code=400;
                    error_page 404 https://$http_host/#/error?code=404;
                    error_page 500 https://$http_host/#/error?code=500;

                    location / {
                      try_files $uri $uri/ /index.html;
                    }
                    location /dc/ {
                            proxy_pass   http://data-classification:8080/;
                            proxy_redirect  http:// https://;
                            proxy_set_header HOST $http_host;
                            proxy_set_header  X-Real-IP $remote_addr;
                            proxy_set_header  X-Forwarded-For $proxy_add_x_forwarded_for;
                            proxy_cookie_path / "/; httponly; secure; SameSite=Lax";
                            client_max_body_size   1024m;
                            proxy_connect_timeout 3600s;
                            proxy_send_timeout 3600s;
                            proxy_read_timeout 3600s;
                            add_header X-Frame-Options SAMEORIGIN;
                    }
            }

    }
