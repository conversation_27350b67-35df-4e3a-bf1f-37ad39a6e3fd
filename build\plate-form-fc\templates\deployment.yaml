apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
    appname: 前端服务
  name: {{ include "plate-form-fc.fullname" . }}
  labels:
    {{- include "plate-form-fc.labels" . | nindent 4 }}
spec:
  {{- if not .Values.autoscaling.enabled }}
  replicas: {{ .Values.replicaCount }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "plate-form-fc.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "plate-form-fc.selectorLabels" . | nindent 8 }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "plate-form-fc.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      initContainers:
        - name: init-dir-permission
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          env:
            - name: KUBE_POD_NAME
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: metadata.name
            - name: KUBE_NS_NAME
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: metadata.namespace
          command:
            - bash
          args:
            - -c
            - "mkdir -p $(KUBE_NS_NAME)/logs/plate-form-fc/$(KUBE_POD_NAME) && chown 9292:9292 $(KUBE_NS_NAME)/logs/plate-form-fc/$(KUBE_POD_NAME)"
          workingDir: /opt/dsd/dd
          volumeMounts:
            - name: dns-domain
              mountPath: /opt/dsd/dd
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          env:
            - name: KUBE_POD_NAME
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: metadata.name
            - name: KUBE_NS_NAME
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: metadata.namespace
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            - name: http
              containerPort: {{ .Values.service.port }}
              protocol: TCP
          livenessProbe:
            httpGet:
              path: /
              port: http
          readinessProbe:
            httpGet:
              path: /
              port: http
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          volumeMounts:
            - name: nginx-conf-d
              mountPath: /etc/nginx/nginx.conf
              subPath: nginx.conf
              readOnly: true
            - name: dns-domain
              mountPath: /var/log/nginx
              subPathExpr: $(KUBE_NS_NAME)/logs/plate-form-fc/$(KUBE_POD_NAME)
            - name: nginx-cache
              mountPath: /var/cache/nginx
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      volumes:
        - name: nginx-conf-d
          configMap:
            name: plate-form-fc
        - name: dns-domain
          hostPath:
            path: /opt/dsd/qzprod
            type: DirectoryOrCreate
        - name: nginx-cache
          emptyDir:
            sizeLimit: 500Mi
