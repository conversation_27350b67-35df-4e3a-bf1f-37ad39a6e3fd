/**
 * 死代码检测插件 - 高精度版本
 *
 * 功能说明：
 * 1. 检测项目中未使用的文件（Unused Files）
 * 2. 检测模块中未使用的导出（Unused Exports）
 * 3. 高精度静态分析避免误报
 * 4. 兼容 Webpack 4/5 和 Rspack
 *
 * 工作原理：
 * - 多重依赖收集策略提高精度
 * - 深度静态分析发现隐式依赖
 * - 精确路径解析处理各种导入模式
 * - 动态导入和代码分割感知
 *
 * <AUTHOR>
 * @version 0.1.1
 */

const path = require('path');
const chalk = require('chalk');
const fs = require('fs');
const fg = require('fast-glob');
const getDirName = path.dirname;

/**
 * 高精度死代码检测插件类
 */
class DeadCodePlugin {
  constructor(options = {}) {
    this.options = {
      context: process.cwd(),
      patterns: ['**/*.js', '**/*.jsx', '**/*.ts', '**/*.tsx', '**/*.vue'],
      exclude: ['node_modules/**/*'],
      failOnHint: false,
      detectUnusedFiles: true,
      detectUnusedExport: true,
      exportJSON: false,
      log: 'warn', // 'all', 'warn', 'none'
      ...options
    };

    // 缓存已解析的路径，避免重复计算
    this.resolveCache = new Map();
    this.analysisCache = new Map();
  }

  apply(compiler) {
    compiler.hooks.afterEmit.tapAsync(
      'DeadCodePlugin',
      (compilation, callback) => {
        this.detectDeadCode(compilation);
        callback();
      }
    );
  }

  convertToUnixPath(path) {
    return path.replace(/\\+/g, '/');
  }

  /**
   * 高精度获取 Webpack/Rspack 编译资源
   * 使用多重策略确保收集到所有被使用的文件
   */
  getWebpackAssets(compilation) {
    const assets = new Set();
    const processedModules = new Set(); // 避免重复处理

    // === 策略1：文件依赖收集 ===
    this.collectFileDependencies(compilation, assets);

    // === 策略2：模块图遍历 ===
    this.collectModuleDependencies(compilation, assets, processedModules);

    // === 策略3：代码块分析 ===
    this.collectChunkDependencies(compilation, assets);

    // === 策略4：统计数据提取 ===
    this.collectStatsModules(compilation, assets);

    // === 策略5：入口点追踪 ===
    this.collectEntryDependencies(compilation, assets);

    // === 策略6：动态导入分析 ===
    this.collectDynamicImports(compilation, assets);

    // === 策略7：深度静态分析 ===
    this.performDeepStaticAnalysis(assets);

    return Array.from(assets);
  }

  /**
   * 收集文件依赖
   */
  collectFileDependencies(compilation, assets) {
    const fileDependencies =
      compilation.fileDependencies ||
      compilation.compilationDependencies ||
      new Set();

    if (
      fileDependencies &&
      typeof fileDependencies[Symbol.iterator] === 'function'
    ) {
      for (const dep of fileDependencies) {
        if (dep && this.isProjectFile(dep)) {
          assets.add(this.convertToUnixPath(path.resolve(dep)));
        }
      }
    }
  }

  /**
   * 收集模块依赖
   */
  collectModuleDependencies(compilation, assets, processedModules) {
    if (!compilation.modules) return;

    for (const module of compilation.modules) {
      const moduleId = this.getModuleId(module);
      if (processedModules.has(moduleId)) continue;
      processedModules.add(moduleId);

      // 模块资源文件
      if (module.resource && this.isProjectFile(module.resource)) {
        assets.add(this.convertToUnixPath(path.resolve(module.resource)));
      }

      // 模块依赖
      if (module.dependencies) {
        module.dependencies.forEach((dep) => {
          if (
            dep.module &&
            dep.module.resource &&
            this.isProjectFile(dep.module.resource)
          ) {
            assets.add(
              this.convertToUnixPath(path.resolve(dep.module.resource))
            );
          }

          // 处理异步依赖
          if (dep.request && this.isRelativeOrAliasPath(dep.request)) {
            const resolvedPath = this.resolveImportPath(
              module.resource,
              dep.request
            );
            if (resolvedPath) {
              assets.add(resolvedPath);
            }
          }
        });
      }

      // 处理模块块（用于代码分割）
      if (module.blocks) {
        module.blocks.forEach((block) => {
          if (block.dependencies) {
            block.dependencies.forEach((dep) => {
              if (
                dep.module &&
                dep.module.resource &&
                this.isProjectFile(dep.module.resource)
              ) {
                assets.add(
                  this.convertToUnixPath(path.resolve(dep.module.resource))
                );
              }
            });
          }
        });
      }
    }
  }

  /**
   * 收集代码块依赖
   */
  collectChunkDependencies(compilation, assets) {
    const isRspack = typeof compilation.moduleGraph !== 'undefined';
    const isWebpack5 = compilation.chunkGraph ? true : false;

    compilation.chunks.forEach((chunk) => {
      let modules = [];

      if (isRspack) {
        modules = compilation.chunkGraph?.getChunkModules(chunk) || [];
      } else if (isWebpack5) {
        modules = compilation.chunkGraph.getChunkModules(chunk);
      } else {
        modules = Array.from(chunk.modulesIterable || []);
      }

      modules.forEach((module) => {
        if (module.resource && this.isProjectFile(module.resource)) {
          assets.add(this.convertToUnixPath(path.resolve(module.resource)));
        }

        // 收集异步块的模块
        if (module.blocks) {
          module.blocks.forEach((block) => {
            const chunkGroup = block.chunkGroup;
            if (chunkGroup && chunkGroup.chunks) {
              chunkGroup.chunks.forEach((asyncChunk) => {
                let asyncModules = [];
                if (isRspack) {
                  asyncModules =
                    compilation.chunkGraph?.getChunkModules(asyncChunk) || [];
                } else if (isWebpack5) {
                  asyncModules =
                    compilation.chunkGraph.getChunkModules(asyncChunk);
                } else {
                  asyncModules = Array.from(asyncChunk.modulesIterable || []);
                }

                asyncModules.forEach((asyncModule) => {
                  if (
                    asyncModule.resource &&
                    this.isProjectFile(asyncModule.resource)
                  ) {
                    assets.add(
                      this.convertToUnixPath(path.resolve(asyncModule.resource))
                    );
                  }
                });
              });
            }
          });
        }
      });
    });
  }

  /**
   * 从统计数据中收集模块
   */
  collectStatsModules(compilation, assets) {
    try {
      const stats = compilation.getStats().toJson({
        all: false,
        modules: true,
        chunkModules: true,
        nestedModules: true
      });

      if (stats.modules) {
        stats.modules.forEach((module) => {
          if (module.name && this.isProjectFile(module.name)) {
            let modulePath = module.name;
            if (!path.isAbsolute(modulePath)) {
              modulePath = path.resolve(this.options.context, modulePath);
            }
            assets.add(this.convertToUnixPath(modulePath));
          }

          // 处理嵌套模块
          if (module.modules) {
            module.modules.forEach((nestedModule) => {
              if (nestedModule.name && this.isProjectFile(nestedModule.name)) {
                let nestedPath = nestedModule.name;
                if (!path.isAbsolute(nestedPath)) {
                  nestedPath = path.resolve(this.options.context, nestedPath);
                }
                assets.add(this.convertToUnixPath(nestedPath));
              }
            });
          }
        });
      }
    } catch (error) {
      console.warn('获取编译统计信息时出错:', error.message);
    }
  }

  /**
   * 收集入口点依赖
   */
  collectEntryDependencies(compilation, assets) {
    const entries = compilation.entries || compilation.entrypoints;

    if (entries) {
      if (entries instanceof Map) {
        // Webpack 5 格式
        for (const [, entry] of entries) {
          if (entry.dependencies) {
            entry.dependencies.forEach((dep) => {
              if (
                dep.module &&
                dep.module.resource &&
                this.isProjectFile(dep.module.resource)
              ) {
                assets.add(
                  this.convertToUnixPath(path.resolve(dep.module.resource))
                );
              }
            });
          }
        }
      } else if (typeof entries === 'object') {
        // Webpack 4 格式
        Object.values(entries).forEach((entry) => {
          if (entry.chunks) {
            entry.chunks.forEach((chunk) => {
              const modules = Array.from(chunk.modulesIterable || []);
              modules.forEach((module) => {
                if (module.resource && this.isProjectFile(module.resource)) {
                  assets.add(
                    this.convertToUnixPath(path.resolve(module.resource))
                  );
                }
              });
            });
          }
        });
      }
    }
  }

  /**
   * 收集动态导入
   */
  collectDynamicImports(compilation, assets) {
    // 从编译记录中查找动态导入
    if (compilation.records && compilation.records.modules) {
      compilation.records.modules.forEach((module) => {
        if (module.resource && this.isProjectFile(module.resource)) {
          assets.add(this.convertToUnixPath(path.resolve(module.resource)));
        }
      });
    }

    // 分析 webpack 的魔法注释
    assets.forEach((assetPath) => {
      const dynamicImports = this.findDynamicImports(assetPath);
      dynamicImports.forEach((importPath) => {
        const resolved = this.resolveImportPath(assetPath, importPath);
        if (resolved) {
          assets.add(resolved);
        }
      });
    });
  }

  /**
   * 执行深度静态分析
   */
  performDeepStaticAnalysis(assets) {
    const staticDependencies = new Set();
    const processedFiles = new Set();

    // 从已知资源开始分析
    const entryPoints = new Set(assets);

    // 递归分析所有依赖
    const analyzeRecursively = (filePath, depth = 0) => {
      const normalizedPath = this.convertToUnixPath(path.resolve(filePath));

      if (processedFiles.has(normalizedPath) || depth > 15) {
        return;
      }

      processedFiles.add(normalizedPath);
      staticDependencies.add(normalizedPath);

      // 分析文件依赖
      const deps = this.analyzeFileDependencies(normalizedPath);
      for (const dep of deps) {
        const depPath = this.convertToUnixPath(path.resolve(dep));
        if (!processedFiles.has(depPath) && this.isProjectFile(depPath)) {
          staticDependencies.add(depPath);
          analyzeRecursively(depPath, depth + 1);
        }
      }
    };

    // 开始递归分析
    for (const entryPoint of entryPoints) {
      if (this.isProjectFile(entryPoint)) {
        analyzeRecursively(entryPoint);
      }
    }

    // 将静态分析结果添加到资源列表
    staticDependencies.forEach((dep) => assets.add(dep));
  }

  /**
   * 增强的静态分析文件依赖关系
   */
  analyzeFileDependencies(filePath) {
    if (this.analysisCache.has(filePath)) {
      return this.analysisCache.get(filePath);
    }

    const dependencies = new Set();

    try {
      if (!fs.existsSync(filePath)) return dependencies;

      const content = fs.readFileSync(filePath, 'utf-8');
      const ext = path.extname(filePath);

      // 根据文件类型使用不同的分析策略
      if (ext === '.vue') {
        this.analyzeVueFile(content, filePath, dependencies);
      } else if (['.js', '.jsx', '.ts', '.tsx'].includes(ext)) {
        this.analyzeJavaScriptFile(content, filePath, dependencies);
      }
    } catch (error) {
      console.warn(`分析文件依赖时出错 ${filePath}:`, error.message);
    }

    this.analysisCache.set(filePath, dependencies);
    return dependencies;
  }

  /**
   * 分析 Vue 文件
   */
  analyzeVueFile(content, filePath, dependencies) {
    // 分析 <script> 部分
    const scriptMatch = content.match(/<script[^>]*>([\s\S]*?)<\/script>/);
    if (scriptMatch) {
      this.analyzeJavaScriptFile(scriptMatch[1], filePath, dependencies);
    }

    // 分析 <style> 中的 @import
    const styleMatches = content.matchAll(/<style[^>]*>([\s\S]*?)<\/style>/g);
    for (const styleMatch of styleMatches) {
      this.analyzeCSSImports(styleMatch[1], filePath, dependencies);
    }
  }

  /**
   * 分析 JavaScript/TypeScript 文件 - 增强版
   */
  analyzeJavaScriptFile(content, filePath, dependencies) {
    // 超全面的导入模式 - 覆盖所有可能的导入方式
    const importPatterns = [
      // === ES6 导入 ===
      /import\s+['"`]([^'"`]+)['"`]/g, // 副作用导入
      /import\s+(?:\{[^}]*\}|\*\s+as\s+\w+|\w+)(?:\s*,\s*(?:\{[^}]*\}|\*\s+as\s+\w+|\w+))*\s+from\s+['"`]([^'"`]+)['"`]/g,
      /import\s+type\s+(?:\{[^}]*\}|\*\s+as\s+\w+|\w+)(?:\s*,\s*(?:\{[^}]*\}|\*\s+as\s+\w+|\w+))*\s+from\s+['"`]([^'"`]+)['"`]/g, // TypeScript type imports

      // === CommonJS ===
      /require\s*\(\s*['"`]([^'"`]+)['"`]\s*\)/g,
      /require\.resolve\s*\(\s*['"`]([^'"`]+)['"`]\s*\)/g,
      /require\.cache\s*\[\s*require\.resolve\s*\(\s*['"`]([^'"`]+)['"`]\s*\)\s*\]/g,

      // === 动态导入 ===
      /import\s*\(\s*['"`]([^'"`]+)['"`]\s*\)/g,
      /import\s*\(\s*`([^`]+)`\s*\)/g, // 模板字符串
      /import\s*\(\s*\/\*.*?\*\/\s*['"`]([^'"`]+)['"`]\s*\)/g, // 带注释的动态导入

      // === Vue 特殊语法 ===
      /defineAsyncComponent\s*\(\s*(?:\(\s*\)\s*=>\s*)?import\s*\(\s*['"`]([^'"`]+)['"`]\s*\)\s*\)/g,
      /\$router\.push\s*\(\s*['"`]([^'"`/]+)['"`]\s*\)/g,
      /\$router\.replace\s*\(\s*['"`]([^'"`/]+)['"`]\s*\)/g,
      /router\.push\s*\(\s*['"`]([^'"`/]+)['"`]\s*\)/g,
      /router\.replace\s*\(\s*['"`]([^'"`/]+)['"`]\s*\)/g,

      // === React 特殊语法 ===
      /React\.lazy\s*\(\s*(?:\(\s*\)\s*=>\s*)?import\s*\(\s*['"`]([^'"`]+)['"`]\s*\)\s*\)/g,
      /lazy\s*\(\s*(?:\(\s*\)\s*=>\s*)?import\s*\(\s*['"`]([^'"`]+)['"`]\s*\)\s*\)/g,

      // === Webpack 特殊语法 ===
      /require\.context\s*\(\s*['"`]([^'"`]+)['"`]/g,
      /webpackChunkName:\s*['"`]([^'"`]+)['"`]/g,
      /webpackMode:\s*['"`]([^'"`]+)['"`]/g,

      // === 文件类型导入 ===
      /import\s+(?:\{[^}]*\}|\*\s+as\s+\w+|\w+)\s+from\s+['"`]([^'"`]+\.json)['"`]/g, // JSON
      /import\s+['"`]([^'"`]+\.(css|less|scss|sass|styl|stylus))['"`]/g, // 样式文件
      /import\s+(?:\{[^}]*\}|\*\s+as\s+\w+|\w+)\s+from\s+['"`]([^'"`]+\.(png|jpg|jpeg|gif|svg|ico|webp|bmp))['"`]/g, // 图片
      /import\s+(?:\{[^}]*\}|\*\s+as\s+\w+|\w+)\s+from\s+['"`]([^'"`]+\.(woff|woff2|ttf|eot))['"`]/g, // 字体
      /import\s+(?:\{[^}]*\}|\*\s+as\s+\w+|\w+)\s+from\s+['"`]([^'"`]+\.(mp4|mp3|wav|ogg))['"`]/g, // 媒体文件

      // === 字符串中的路径模式 ===
      /['"`](\.\/.+?)['"`]/g, // 相对路径
      /['"`](@\/.+?)['"`]/g, // 别名路径
      /['"`](~\/.+?)['"`]/g, // 波浪号路径

      // === 特殊字符串模式 ===
      /path:\s*['"`]([^'"`]+)['"`]/g,
      /src:\s*['"`]([^'"`]+)['"`]/g,
      /url:\s*['"`]([^'"`]+)['"`]/g,
      /href:\s*['"`]([^'"`]+)['"`]/g,
      /component:\s*['"`]([^'"`]+)['"`]/g,

      // === 框架特定 ===
      // Next.js
      /next\/dynamic\s*\(\s*(?:\(\s*\)\s*=>\s*)?import\s*\(\s*['"`]([^'"`]+)['"`]\s*\)\s*\)/g,
      // Nuxt.js
      /\$nuxt\.\$router\.push\s*\(\s*['"`]([^'"`/]+)['"`]\s*\)/g,

      // === 构建工具特定 ===
      // Vite
      /import\.meta\.glob\s*\(\s*['"`]([^'"`]+)['"`]/g,
      /import\.meta\.globEager\s*\(\s*['"`]([^'"`]+)['"`]/g
    ];

    // 执行所有模式匹配
    importPatterns.forEach((pattern) => {
      let match;
      while ((match = pattern.exec(content)) !== null) {
        const importPath = match[1];
        if (importPath && this.isRelativeOrAliasPath(importPath)) {
          const resolvedPath = this.resolveImportPath(filePath, importPath);
          if (resolvedPath) {
            dependencies.add(resolvedPath);
          }
        }
      }
    });
  }

  /**
   * 分析 CSS 导入
   */
  analyzeCSSImports(cssContent, filePath, dependencies) {
    const importPattern = /@import\s+['"`]([^'"`]+)['"`]/g;
    let match;
    while ((match = importPattern.exec(cssContent)) !== null) {
      const importPath = match[1];
      if (this.isRelativeOrAliasPath(importPath)) {
        const resolvedPath = this.resolveImportPath(filePath, importPath);
        if (resolvedPath) {
          dependencies.add(resolvedPath);
        }
      }
    }
  }

  /**
   * 查找动态导入
   */
  findDynamicImports(filePath) {
    const imports = [];
    try {
      const content = fs.readFileSync(filePath, 'utf-8');
      const dynamicImportPattern = /import\s*\(\s*['"`]([^'"`]+)['"`]\s*\)/g;
      let match;
      while ((match = dynamicImportPattern.exec(content)) !== null) {
        imports.push(match[1]);
      }
    } catch (error) {
      // 忽略读取错误
    }
    return imports;
  }

  /**
   * 增强的路径解析
   */
  resolveImportPath(fromFile, importPath) {
    const cacheKey = `${fromFile}::${importPath}`;
    if (this.resolveCache.has(cacheKey)) {
      return this.resolveCache.get(cacheKey);
    }

    let result = null;

    try {
      const fromDir = path.dirname(fromFile);
      let resolvedPath = importPath;

      // 处理别名路径
      if (importPath.startsWith('@/')) {
        resolvedPath = importPath.replace('@/', 'src/');
        resolvedPath = path.resolve(this.options.context, resolvedPath);
      } else if (importPath.startsWith('~/')) {
        resolvedPath = importPath.replace('~/', '');
        resolvedPath = path.resolve(this.options.context, resolvedPath);
      } else if (importPath.startsWith('#/')) {
        resolvedPath = importPath.replace('#/', 'src/');
        resolvedPath = path.resolve(this.options.context, resolvedPath);
      } else if (importPath.startsWith('.')) {
        resolvedPath = path.resolve(fromDir, importPath);
      } else if (
        importPath.startsWith('src/') ||
        importPath.startsWith('components/') ||
        importPath.startsWith('pages/') ||
        importPath.startsWith('utils/') ||
        importPath.startsWith('assets/')
      ) {
        // 处理项目根目录相对路径
        resolvedPath = path.resolve(this.options.context, importPath);
      } else {
        // 忽略第三方模块
        this.resolveCache.set(cacheKey, null);
        return null;
      }

      resolvedPath = this.convertToUnixPath(resolvedPath);

      const extensions = [
        '.js',
        '.jsx',
        '.ts',
        '.tsx',
        '.vue',
        '.json',
        '.css',
        '.less',
        '.scss',
        '.sass'
      ];

      // 直接文件检查
      if (path.extname(resolvedPath)) {
        if (fs.existsSync(resolvedPath)) {
          result = resolvedPath;
        }
      } else {
        // 尝试添加扩展名
        for (const ext of extensions) {
          const fullPath = resolvedPath + ext;
          if (fs.existsSync(fullPath)) {
            result = this.convertToUnixPath(fullPath);
            break;
          }
        }

        // 尝试 index 文件
        if (!result) {
          for (const ext of extensions) {
            const indexPath = path.join(resolvedPath, 'index' + ext);
            if (fs.existsSync(indexPath)) {
              result = this.convertToUnixPath(indexPath);
              break;
            }
          }
        }

        // 检查 package.json
        if (
          !result &&
          fs.existsSync(resolvedPath) &&
          fs.statSync(resolvedPath).isDirectory()
        ) {
          const packageJsonPath = path.join(resolvedPath, 'package.json');
          if (fs.existsSync(packageJsonPath)) {
            try {
              const packageJson = JSON.parse(
                fs.readFileSync(packageJsonPath, 'utf-8')
              );
              const main = packageJson.main || packageJson.module;
              if (main) {
                const mainPath = path.resolve(resolvedPath, main);
                if (fs.existsSync(mainPath)) {
                  result = this.convertToUnixPath(mainPath);
                }
              }
            } catch (e) {
              // 忽略 package.json 解析错误
            }
          }
        }
      }
    } catch (error) {
      // 忽略路径解析错误
    }

    this.resolveCache.set(cacheKey, result);
    return result;
  }

  /**
   * 工具方法
   */
  getModuleId(module) {
    return (
      module.id ||
      module.identifier() ||
      module.resource ||
      Math.random().toString()
    );
  }

  isProjectFile(filePath) {
    if (!filePath) return false;
    const normalizedPath = this.convertToUnixPath(path.resolve(filePath));
    const projectRoot = this.convertToUnixPath(
      path.resolve(this.options.context)
    );
    return (
      normalizedPath.startsWith(projectRoot) &&
      !normalizedPath.includes('node_modules') &&
      !/\.(map|d\.ts)$/i.test(normalizedPath)
    );
  }

  isRelativeOrAliasPath(importPath) {
    if (!importPath) return false;

    // 相对路径
    if (importPath.startsWith('./') || importPath.startsWith('../')) {
      return true;
    }

    // 常见别名路径
    if (
      importPath.startsWith('@/') ||
      importPath.startsWith('~/') ||
      importPath.startsWith('#/') ||
      importPath.startsWith('src/') ||
      importPath.startsWith('components/') ||
      importPath.startsWith('pages/') ||
      importPath.startsWith('utils/') ||
      importPath.startsWith('assets/')
    ) {
      return true;
    }

    // 检查路径中是否包含文件扩展名（可能是本地文件）
    if (
      /\.(js|jsx|ts|tsx|vue|json|css|less|scss|sass|styl|png|jpg|jpeg|gif|svg|ico|woff|woff2|ttf|eot|mp4|mp3|wav|ogg)$/.test(
        importPath
      )
    ) {
      return true;
    }

    return false;
  }

  convertFilesToDict(assets) {
    return assets
      .filter((file) => file && file.indexOf('node_modules') === -1)
      .reduce((acc, file) => {
        const normalizedFile = this.convertToUnixPath(path.resolve(file));
        acc[normalizedFile] = true;
        return acc;
      }, {});
  }

  getPattern({ context, patterns, exclude }) {
    return patterns
      .map((pattern) => path.resolve(context, pattern))
      .concat(exclude.map((pattern) => `!${path.resolve(context, pattern)}`))
      .map(this.convertToUnixPath);
  }

  logUnusedFiles(unusedFiles) {
    console.log(
      chalk.yellow('\n--------------------- Unused Files ---------------------')
    );
    if (unusedFiles.length > 0) {
      unusedFiles.forEach((file) => console.log(`\n${chalk.yellow(file)}`));
      console.log(
        chalk.yellow(`\nFound ${unusedFiles.length} unused files.`),
        chalk.red.bold(`\n\nPlease review carefully before removing.\n`)
      );
    } else {
      console.log(chalk.green('\nNo unused files detected ✓'));
    }
  }

  /**
   * 输出模块的未使用导出映射（单个模块处理）
   *
   * 针对单个模块，检测其提供的导出中哪些没有被使用。
   * 兼容 Webpack 4/5 和 Rspack 的不同 API。
   *
   * @param {Object} compilation - 编译对象
   * @param {Object} chunk - 代码块对象
   * @param {Object} module - 模块对象
   * @param {Object} includedFileMap - 包含的文件映射
   * @param {Object} unusedExportMap - 未使用导出映射（输出参数）
   */
  outputUnusedExportMap(
    compilation,
    chunk,
    module,
    includedFileMap,
    unusedExportMap
  ) {
    if (!module.resource) return;

    // 检测使用的构建工具版本，使用对应的 API
    const isWebpack5 = compilation.chunkGraph ? true : false;
    const isRspack = typeof compilation.moduleGraph !== 'undefined';

    let providedExports;
    let usedExports;

    if (isRspack) {
      // Rspack API
      providedExports = compilation.moduleGraph.getProvidedExports(module);
      usedExports = compilation.moduleGraph.getUsedExports(
        module,
        chunk.runtime
      );
    } else if (isWebpack5) {
      // Webpack 5 API
      providedExports =
        compilation.chunkGraph.moduleGraph.getProvidedExports(module);
      usedExports = compilation.chunkGraph.moduleGraph.getUsedExports(
        module,
        chunk.runtime
      );
    } else {
      // Webpack 4 API
      providedExports =
        module.providedExports || module.buildMeta?.providedExports;
      usedExports = module.usedExports;
    }

    const path = this.convertToUnixPath(module.resource);
    let usedExportsArr = [];

    // 处理不同类型的 usedExports（null, boolean, Array<string>, Set<string>）
    if (usedExports instanceof Set) {
      usedExportsArr = Array.from(usedExports);
    } else if (Array.isArray(usedExports)) {
      usedExportsArr = usedExports;
    }

    // 过滤条件：非 node_modules 且在包含文件中
    if (
      usedExports !== true &&
      providedExports !== true &&
      /^((?!(node_modules)).)*$/.test(path) &&
      includedFileMap[path]
    ) {
      if (usedExports === false) {
        // 完全未使用
        unusedExportMap[path] = providedExports;
      } else if (providedExports instanceof Array) {
        // 部分未使用
        const unusedExports = providedExports.filter(
          (x) => Array.isArray(usedExportsArr) && !usedExportsArr.includes(x)
        );

        if (unusedExports.length > 0) {
          unusedExportMap[path] = unusedExports;
        }
      }
    }
  }

  /**
   * 获取未使用导出映射（主方法）
   *
   * 遍历所有编译的模块，收集未使用的导出信息。
   * 优先使用统计数据，回退到遍历模块的方式。
   *
   * @param {Object} includedFileMap - 包含的文件映射
   * @param {Object} compilation - 编译对象
   * @returns {Object} 未使用导出映射 {文件路径: [未使用导出列表]}
   */
  getUsedExportMap(includedFileMap, compilation) {
    const unusedExportMap = {};
    const isWebpack5 = compilation.chunkGraph ? true : false;
    const isRspack = typeof compilation.moduleGraph !== 'undefined';

    // 获取编译统计数据，可同时用于 Webpack 和 Rspack
    const statsJson = compilation.getStats().toJson();

    if (isRspack && statsJson.modules) {
      // === Rspack 处理路径：优先使用统计数据 ===
      // 当 Rspack 且有模块统计数据时，直接使用统计数据
      statsJson.modules.forEach((moduleStats) => {
        if (!moduleStats.name) return;

        const filePath = this.convertToUnixPath(
          path.join(process.cwd(), moduleStats.name)
        );

        // 过滤：非 node_modules 且在包含文件列表中
        if (
          !/^((?!(node_modules)).)*$/.test(filePath) ||
          !includedFileMap[filePath]
        ) {
          return;
        }

        const providedExports = moduleStats.providedExports;
        const usedExports = moduleStats.usedExports;

        if (usedExports === false) {
          // 完全未使用的模块
          unusedExportMap[filePath] = providedExports;
        } else if (
          Array.isArray(providedExports) &&
          Array.isArray(usedExports) &&
          providedExports.length > 0
        ) {
          // 查找部分未使用的导出
          const unusedExports = providedExports.filter(
            (exp) => !usedExports.includes(exp)
          );

          if (unusedExports.length > 0) {
            unusedExportMap[filePath] = unusedExports;
          }
        }
      });
    } else {
      // === Webpack 处理路径：遍历模块和代码块 ===
      // 当无法使用统计数据时，回退到传统的遍历方式
      compilation.chunks.forEach((chunk) => {
        let modules;

        // 根据不同版本获取模块列表
        if (isRspack) {
          modules = compilation.chunkGraph.getChunkModules(chunk);
        } else if (isWebpack5) {
          modules = compilation.chunkGraph.getChunkModules(chunk);
        } else {
          // Webpack 4
          modules = Array.from(chunk.modulesIterable || []);
        }

        // 为每个模块检测未使用的导出
        modules.forEach((module) => {
          this.outputUnusedExportMap(
            compilation,
            chunk,
            module,
            includedFileMap,
            unusedExportMap
          );
        });
      });
    }

    return unusedExportMap;
  }

  /**
   * 输出未使用导出的日志信息
   *
   * 以友好的格式显示每个文件的未使用导出列表。
   *
   * @param {Object} unusedExportMap - 未使用导出映射
   */
  logUnusedExportMap(unusedExportMap) {
    console.log(
      chalk.yellow(
        '\n--------------------- Unused Exports ---------------------'
      )
    );
    if (Object.keys(unusedExportMap).length > 0) {
      let numberOfUnusedExport = 0;

      Object.keys(unusedExportMap).forEach((modulePath) => {
        const unusedExports = unusedExportMap[modulePath];

        console.log(chalk.yellow(`\n${modulePath}`));
        console.log(chalk.yellow(`    ⟶   ${unusedExports.join(', ')}`));
        numberOfUnusedExport += unusedExports.length;
      });
      console.log(
        chalk.yellow(
          `\nThere are ${numberOfUnusedExport} unused exports (¬º-°)¬.\n`
        )
      );
    } else {
      console.log(chalk.green('\nPerfect, there is nothing to do ٩(◕‿◕｡)۶.'));
    }
  }

  /**
   * 导出检测结果到 JSON 文件
   *
   * 将未使用的文件和导出信息保存为 JSON 格式，便于后续处理。
   *
   * @param {string} exportPath - 导出文件路径
   * @param {Array<string>} unusedFiles - 未使用的文件列表
   * @param {Object} unusedExports - 未使用的导出映射
   */
  exportResultToJSON(exportPath, unusedFiles, unusedExports) {
    const data = {
      unusedFiles,
      unusedExports
    };
    fs.mkdir(getDirName(exportPath), { recursive: true }, (err) => {
      if (err) throw err;
      fs.writeFile(exportPath, JSON.stringify(data, null, 2), (err) => {
        if (err) throw err;
        console.info(path.resolve(exportPath) + ' is generated.');
      });
    });
  }

  /**
   * 主要的死代码检测方法 - 精确版本
   */
  detectDeadCode(compilation) {
    const startTime = Date.now();

    // 收集编译依赖
    const assets = this.getWebpackAssets(compilation);
    const compiledFiles = this.convertFilesToDict(assets);

    // 扫描项目源文件
    const rawIncludedFiles = fg.sync(this.getPattern(this.options));
    const includedFiles = rawIncludedFiles.map((file) =>
      this.convertToUnixPath(path.resolve(file))
    );

    let unusedFiles = [];
    let unusedExportMap = {};

    // 未使用文件检测
    if (this.options.detectUnusedFiles) {
      unusedFiles = includedFiles.filter((file) => {
        const normalizedFile = this.convertToUnixPath(path.resolve(file));
        return !compiledFiles[normalizedFile];
      });

      // 输出统计信息
      if (this.options.log === 'all') {
        const endTime = Date.now();
        console.log('\n=== 高精度死代码检测统计 ===');
        console.log(`扫描文件数: ${includedFiles.length}`);
        console.log(`编译依赖数: ${Object.keys(compiledFiles).length}`);
        console.log(`未使用文件数: ${unusedFiles.length}`);
        console.log(`检测耗时: ${endTime - startTime}ms`);
        console.log(
          `缓存命中: 路径解析 ${this.resolveCache.size}, 依赖分析 ${this.analysisCache.size}`
        );
      }

      if (
        (unusedFiles.length > 0 && this.options.log !== 'none') ||
        this.options.log === 'all'
      ) {
        this.logUnusedFiles(unusedFiles);
      }
    }

    // 未使用导出检测
    if (this.options.detectUnusedExport) {
      unusedExportMap = this.getUsedExportMap(
        this.convertFilesToDict(includedFiles),
        compilation
      );

      if (
        (Object.keys(unusedExportMap).length > 0 &&
          this.options.log !== 'none') ||
        this.options.log === 'all'
      ) {
        this.logUnusedExportMap(unusedExportMap);
      }
    }

    // JSON 结果导出
    if (this.options.exportJSON) {
      let exportPath = 'deadcode.json';
      if (typeof this.options.exportJSON === 'string') {
        exportPath = this.options.exportJSON + '/' + exportPath;
      }
      try {
        fs.stat(exportPath, (err) => {
          if (err == null) {
            fs.unlinkSync(exportPath);
          }
          this.exportResultToJSON(exportPath, unusedFiles, unusedExportMap);
        });
      } catch (error) {
        console.error('导出结果到 JSON 时出错: ', error);
      }
    }

    // 构建失败处理
    if (unusedFiles.length > 0 || Object.keys(unusedExportMap).length > 0) {
      if (this.options.failOnHint) {
        process.exit(2);
      }
    }
  }
}

/**
 * 导出高精度死代码检测插件
 *
 * 使用示例：
 *
 * // Webpack 配置
 * const DeadCodePlugin = require('./plugins/dead-code-plugin');
 *
 * module.exports = {
 *   // ... 其他配置
 *   plugins: [
 *     new DeadCodePlugin({
 *       patterns: ['src/**\/*.{js,jsx,ts,tsx,vue}'],
 *       exclude: ['node_modules/**\/*', 'dist/**\/*'],
 *       detectUnusedFiles: true,
 *       detectUnusedExport: true,
 *       log: 'warn',
 *       exportJSON: './build-analysis/',
 *       failOnHint: false
 *     })
 *   ]
 * };
 *
 * // Rspack 配置
 * module.exports = {
 *   // ... 其他配置
 *   plugins: [
 *     new DeadCodePlugin({
 *       context: __dirname,
 *       patterns: ['**\/*.js', '**\/*.jsx', '**\/*.ts', '**\/*.tsx', '**\/*.vue'],
 *       exclude: ['node_modules/**\/*', 'public/**\/*'],
 *       detectUnusedFiles: true,
 *       detectUnusedExport: true,
 *       log: 'all', // 'all', 'warn', 'none'
 *       exportJSON: true,
 *       failOnHint: false
 *     })
 *   ]
 * };
 *
 * // 高精度配置建议：
 * // 1. 包含所有可能的源文件类型
 * // 2. 使用 log: 'all' 查看详细统计信息
 * // 3. 启用 exportJSON 导出结果用于进一步分析
 * // 4. 初期设置 failOnHint: false 避免构建中断
 */
module.exports = DeadCodePlugin;
