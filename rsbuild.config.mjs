import { defineConfig } from '@rsbuild/core';
import { pluginVue2 } from '@rsbuild/plugin-vue2';
import { pluginLess } from '@rsbuild/plugin-less';
import { pluginBabel } from '@rsbuild/plugin-babel';
import { pluginVue2Jsx } from '@rsbuild/plugin-vue2-jsx';
import { pluginEslint } from '@rsbuild/plugin-eslint';
import { pluginBasicSsl } from '@rsbuild/plugin-basic-ssl';

import gitCommitInfoWebpackPlugin from '@quanzhiFE/git-commit-info-webpack-plugin';
import CompressionPlugin from 'compression-webpack-plugin'; // 兼容
import MonacoWebpackPlugin from 'monaco-editor-webpack-plugin'; // 兼容
import DeadCodePlugin from './plugins/dead-code-plugin';

import path from 'path';
import fs from 'fs';

function resolve(dir) {
  return path.resolve(__dirname, dir);
}

const hotServer = () => {
  const path = './server-config.json';
  const content = fs.readFileSync(path).toString();
  const { serverOrigin } = JSON.parse(content);
  return serverOrigin || '';
};

const fePort = 8081;

export default defineConfig({
  plugins: [
    pluginBabel(),
    pluginVue2({
      vueLoaderOptions: {
        compilerOptions: {
          directives: {
            html(node, directiveMeta) {
              (node.props || (node.props = [])).push({
                name: 'innerHTML',
                value: `xss(_s(${directiveMeta.value}))`
              });
            }
          }
        }
      }
    }),
    pluginVue2Jsx(),
    pluginLess({
      lessLoaderOptions: {
        additionalData: `@import "@/assets/css/mixins.less";@import "@/assets/css/global.less";`
      }
    }),
    pluginEslint(),
    /**
     * 支持本地使用https开发
     * 默认设置了 server.https 选项
     */
    pluginBasicSsl()
  ],
  // 与本地开发有关的选项
  dev: {
    progressBar: true // 在编译过程中展示进度条
    // lazyCompilation: true // 按需编译，从而提升启动时间，不建议开启，切换页面时很慢
  },
  // 与模块解析相关的选项
  resolve: {
    alias: {
      '@': resolve('src')
    }
  },
  // 与构建产物有关的选项
  output: {
    // publicPath: '/', // rspack中的publicPath配置项
    assetPrefix: '/', // publicPath与之效果一致
    /**
     * 浏览器兼容
     * usage: 注入的 polyfill 体积更小，适合对包体积有较高要求的项目使用
     * entry: 注入的 polyfill 较为全面，适合对兼容性要求较高的项目使用
     */
    polyfill: 'entry',
    // 修改静态资源输出路径，默认是在static目录下，且再根据文件类型划分文件夹
    distPath: {
      css: 'css',
      font: 'fonts',
      image: 'img',
      js: 'js',
      svg: 'img'
    }
  },
  // 与输入的源代码相关的选项
  source: {
    // 指定入口文件
    entry: {
      index: './src/main.js'
    }
  },
  // 与 HTML 生成有关的选项
  html: {
    template: './public/index.html'
  },
  // 与 Rsbuild 服务器有关的选项
  server: {
    port: fePort, // 端口号
    compress: true,
    host: 'localhost',
    open: true, //配置自动启动浏览器
    proxy: {
      '/dc': {
        secure: false,
        target: 'http://127.0.0.1:10086',
        router: () => hotServer(),
        onProxyReq(proxyReq) {
          // 绕过后端的csrf验证
          proxyReq.setHeader('referer', hotServer());
        }
      },
      '/wy': {
        target: 'http://*************:9527/rd-scaffold',
        pathRewrite: {
          '^/wy': '/'
        }
      }
    }
  },
  // 与构建性能、运行时性能有关的选项
  performance: {
    // bundleAnalyze: {}, // 开启webpack-bundle-analyzer分析产物体积
    removeConsole: ['log'] // 生产环境移除console.log
  },
  // 与底层工具有关的选项
  tools: {
    /**
     * tools.rspack修改Rspack的配置项
     */
    rspack: (config, { rspack }) => {
      config.cache = true;
      config.optimization.providedExports = true; // 允许 tree-shaking
      config.optimization.usedExports = true; // 允许 tree-shaking
      config.plugins.push(
        new CompressionPlugin({
          test: /\.(js|css)?$/i, // 压缩文件类型
          filename: '[path][base].gz', // 压缩后的文件名
          algorithm: 'gzip', // 使用 gzip 压缩
          minRatio: 0.8, // 压缩率小于 1 才会压缩
          threshold: 10240, // 对超过 10k 的数据压缩
          deleteOriginalAssets: false // 是否删除未压缩的文件(源文件)，不建议开启
        }),
        new gitCommitInfoWebpackPlugin(),
        // 检测循环依赖
        new rspack.CircularDependencyRspackPlugin({
          failOnError: true,
          exclude: /node_modules/
        })
      );

      config.ignoreWarnings = [
        /Critical dependency: require function is used in a way in which dependencies cannot be statically extracted/
      ];
      // 开发模式下才开启死代码检测
      if (process.env.NODE_ENV === 'development') {
        config.plugins.push(
          new DeadCodePlugin({
            patterns: ['./src/**/*.{js,jsx,ts,tsx,vue,css}'], // 要检查的文件模式
            exclude: ['node_modules/**/*'] // 排除的文件模式
          })
        );
      }
    },
    bundlerChain: (chain) => {
      /** 添加对react的支持 */
      chain.module
        .rule('jsx')
        .test(/components-react[\\/].*\.jsx$/)
        .use('builtin:swc-loader')
        .loader('builtin:swc-loader')
        .options({
          jsc: {
            parser: {
              syntax: 'ecmascript',
              jsx: true
            },
            transform: {
              react: {
                pragma: 'React.createElement' // 如果你用的是 React 17 的新 JSX 转换，可以去掉这行
              }
            }
          }
        })
        .end();

      chain.resolve.extensions.add('.tsx').add('.ts').add('.jsx').add('.js');
      chain.plugin('monaco-editor').use(
        new MonacoWebpackPlugin({
          languages: ['javascript']
        })
      );
      /** monaco-editor的代码用了很多新特性，需要babel转译 */
      chain.module
        .rule('monaco-editor')
        .test(/monaco-editor[\\/].*\.js$/)
        .include.add(resolve('node_modules/monaco-editor/esm/vs'))
        .end()
        .exclude.add(resolve('node_modules/monaco-editor/esm/vs/language'))
        .end()
        .use('builtin:swc-loader')
        .loader('builtin:swc-loader')
        .options({
          jsc: {
            parser: {
              syntax: 'ecmascript'
            },
            target: 'es5' // 将代码转译为 ES5
          }
        })
        .end();
      // 让其他 svg loader 不要对 src/assets/svgs 进行操作
      chain.module.rule('svg').exclude.add(resolve('src/assets/svg')).end();
      // 使用 svg-sprite-loader 对 src/assets/svgs下的svg进行操作
      chain.module
        .rule('icons')
        .test(/\.svg$/)
        .include.add(resolve('src/assets/svg'))
        .end()
        .use('svg-sprite')
        .loader('svg-sprite-loader')
        //定义规则 使用时 <svg><use xlink:href="#icon-filename"></use></svg>
        .options({
          symbolId: 'icon-[name]'
        })
        .end();
    }
  }
});
