<template>
  <div id="app" class="y-scroll">
    <router-view />
  </div>
</template>

<script>
import { mapMutations, mapState } from 'vuex';
import { getConfigInfo } from '@/service/basic-config-service';

export default {
  name: 'App',
  provide() {
    return {
      reload: this.reload
    };
  },
  data() {
    return {
      isRouterAlive: true,
      fontFamily: 'PingFangSC-Regular, MicrosoftYaHei, sans-serif'
    };
  },

  async mounted() {
    document.documentElement.style.setProperty(
      '--font-family',
      this.fontFamily
    );
    // 先使用默认的icon
    // 先使用默认的icon
    this.setIconImg(this.iconImg);

    // 获取产品Logo、Icon和名称
    let configInfo;
    getConfigInfo().then((res) => {
      configInfo = res.data || {};
      if (res.data) {
        if (configInfo.logoImg) {
          this.setLogoImg(configInfo.logoImg);
        } else {
          this.setLogoImg(this.logoImg);
        }
        if (configInfo.iconImg) {
          this.setIconImg(configInfo.iconImg);
        } else {
          this.setIconImg(this.iconImg);
        }
        if (configInfo.brandName) {
          this.setProductName(configInfo.brandName);
        } else {
          this.setProductName(this.productName);
        }
        if (configInfo.backImg || configInfo.brandBackgroundPicture) {
          const bgImg = configInfo.backImg || configInfo.brandBackgroundPicture;
          this.setBackImg(bgImg);
        } else {
          this.setBackImg(this.backImg);
        }
        if (configInfo.copyrightInformation) {
          this.setCopyright(configInfo.copyrightInformation);
        } else {
          this.setCopyright(this.copyright);
        }
        // this.setLogoImg(configInfo.logoImg);
        // this.setIconImg(configInfo.iconImg);
        // this.setProductName(configInfo.brandName);
        // this.setBackImg(configInfo.backImg);
        // this.setCopyright(configInfo.copyrightInformation);
      } else {
        this.setLogoImg(this.logoImg);
        this.setIconImg(this.iconImg);
        this.setProductName(this.productName);
        this.setBackImg(this.backImg);
        this.setCopyright(this.copyright);
      }
      const documentTitle = document.querySelector('title');
      documentTitle.innerText = this.productName;
    });
  },
  computed: {
    ...mapState(['iconImg', 'logoImg', 'productName', 'backImg', 'copyright'])
  },
  methods: {
    ...mapMutations([
      'setLogoImg',
      'setIconImg',
      'setProductName',
      'setBackImg',
      'setCopyright'
    ])
  }
};
</script>

<style lang="less">
:root {
  --theme-color: #47a5e7;
}
#app {
  flex: auto;
  font-family: var(--font-family);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  min-width: 1250px;
  display: flex;
  flex-direction: column;
  min-height: 100%;
  color: @text-primary-color;
}
html,
body {
  display: flex;
  flex-direction: column;
  font-size: 14px;
  height: 100%;
  margin: 0;
}
body {
  overflow-y: hidden;
}
</style>
