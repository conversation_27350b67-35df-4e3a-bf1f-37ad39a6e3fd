import { getMenuData, getLocalUserInfo } from '@/utils/storage-utils';
import { AESUtil } from '@/utils/AESUtil';
import {
  DATA_URL_LOGIN,
  DATA_URL_LOGIN_OUT,
  DATA_URL_SYSLOG_EXPORT,
  DATA_URL_ACCOUNT
} from '@/constant/data-url-constants';
//需要记录日志的code
const systemPageLogCode = [
  'scan_discovery',
  'scan_asset',
  'scan_classify',
  'scan_tag',
  'config_clean'
];

const initPageApi = [
  DATA_URL_LOGIN,
  DATA_URL_LOGIN_OUT,
  DATA_URL_SYSLOG_EXPORT,
  DATA_URL_ACCOUNT
];

const menuData = getMenuData();

const findPageByCode = (menuData, code) => {
  if (!menuData) return null;
  for (const menu of menuData) {
    if (menu.code === code) {
      return menu;
    }
    if (menu.children && menu.children.length > 0) {
      const found = findPageByCode(menu.children, code);
      if (found) return found;
    }
  }
  return null;
};

export const handerLogApi = (apiConfig) => {
  const logInfo = {
    userId: '',
    username: '',
    module: '',
    operationType: '',
    operationDesc: '',
    extraData: ''
  };
  const userInfo = getLocalUserInfo();
  if (Object.keys(userInfo).length > 0) {
    logInfo.userId = userInfo.userId;
    logInfo.username = userInfo.username;
  } else {
    //登录页面时从接口中获取用户信息
    logInfo.username = apiConfig.data.username;
  }
  const url = window.location.href;
  //acode说明是amis配置，code是配置中的
  if (
    apiConfig.url != DATA_URL_LOGIN_OUT &&
    (url.includes('acode') || url.includes('code'))
  ) {
    const params = new URLSearchParams(window.location.search);
    const code = params.get('acode') || params.get('code');
    if (!systemPageLogCode.includes(code)) {
      return;
    }
    const pageInfo = findPageByCode(menuData, code);
    if (pageInfo) {
      logInfo.module = pageInfo.name;
    } else {
      logInfo.module = '--';
    }
    if (
      apiConfig.url.includes('delete') ||
      apiConfig.url.includes('Delete') ||
      apiConfig.url.includes('DELETE') ||
      apiConfig.url.includes('del') ||
      apiConfig.url.includes('Del') ||
      apiConfig.url.includes('DEL') ||
      apiConfig.url.includes('remove') ||
      apiConfig.url.includes('Remove') ||
      apiConfig.url.includes('REMOVE')
    ) {
      logInfo.operationType = '删除';
    }
    if (
      apiConfig.url.includes('add') ||
      apiConfig.url.includes('Add') ||
      apiConfig.url.includes('ADD') ||
      apiConfig.url.includes('create') ||
      apiConfig.url.includes('Create') ||
      apiConfig.url.includes('CREATE') ||
      apiConfig.url.includes('insert') ||
      apiConfig.url.includes('Insert') ||
      apiConfig.url.includes('INSERT') ||
      apiConfig.url.includes('save') ||
      apiConfig.url.includes('Save') ||
      apiConfig.url.includes('SAVE')
    ) {
      logInfo.operationType = '新增';
    }
    if (
      apiConfig.url.includes('export') ||
      apiConfig.url.includes('Export') ||
      apiConfig.url.includes('EXPORT')
    ) {
      logInfo.operationType = '导出';
    }
  } else {
    //内置的页面api
    if (initPageApi.includes(apiConfig.url)) {
      if (apiConfig.url === DATA_URL_LOGIN) {
        logInfo.module = '用户';
        logInfo.operationType = '登录';
      }
      if (apiConfig.url === DATA_URL_LOGIN_OUT) {
        logInfo.module = '用户';
        logInfo.operationType = '登出';
      }
      if (apiConfig.url.includes(DATA_URL_ACCOUNT)) {
        logInfo.module = '用户';
        if (apiConfig.url.includes(`save`)) {
          const params = apiConfig.data;
          if (params.id) {
            logInfo.operationType = '修改';
          } else {
            logInfo.operationType = '新增';
          }
        }
        if (apiConfig.url.includes('delete')) {
          logInfo.operationType = '删除';
        }
        if (apiConfig.url.includes('modify')) {
          logInfo.operationType = '修改';
        }
      }
      if (apiConfig.url == DATA_URL_SYSLOG_EXPORT) {
        logInfo.module = '审计日志';
        logInfo.operationType = '导出';
      }
    }
  }
  if (!logInfo.operationType) {
    return null;
  }
  logInfo.operationDesc =
    logInfo.username + logInfo.operationType + logInfo.module;
  const str = JSON.stringify(logInfo);
  console.log(logInfo, 'logInfo');
  return btoa(unescape(encodeURIComponent(AESUtil.encrypt(str))));
};
