import { axios } from '@quanzhiFE/qz-frontend';
import router from '@/router/index.js';
import { PAGE_URL_LOGIN } from '@/constant/page-url-constants';
import { clearAllLocalStorage } from '@/utils/storage-utils';
import { handerLogApi } from './hand-log-api';
import { Message } from 'element-ui';
const handerResponse = (response) => {
  const LOGIN_ERROR_MSG = {
    401: '登录超时，请重新登录',
    402: '该账号已在其他终端登录',
    403: '该账号已被锁定，请联系管理员'
    // 404: '该账号已被禁用，请联系管理员'
  };
  const errorCodes = Object.keys(LOGIN_ERROR_MSG).map((item) => Number(item));
  if (
    errorCodes.includes(response.status) &&
    window.location.pathname !== '/login'
  ) {
    clearAllLocalStorage();
    router.push({
      name: PAGE_URL_LOGIN
    });
  }
};
axios.interceptors.request.use((config) => {
  if (handerLogApi(config)) {
    config.headers['Audit-Log'] = handerLogApi(config);
  }
  config.headers['X-Requested-With'] = 'AJAX';
  return config;
});

// 处理请求返回数据
axios.interceptors.response.use(
  (response) => {
    let data = response.data;
    try {
      if (response.config && response.config.loading) {
        response.config.loading.close();
      }
      if (
        response.headers['content-disposition']?.includes('attachment') ||
        data instanceof Blob
      ) {
        return response;
      }
      if (typeof data == 'string' && data) {
        data = JSON.parse(data);
      }
      if (data?.status === 0) {
        return data;
      } else if (data?.status === -1) {
        Message.error(data.msg || '操作失败');
        return Promise.reject(data);
      } else {
        handerResponse(data);
      }
    } catch (err) {
      return Promise.reject(data);
    }
  },
  (error) => {
    if (error.config && error.config.loading) {
      error.config.loading.close();
    }
    console.log(error, 'error');
    handerResponse(error.response);
    return Promise.reject();
  }
);
