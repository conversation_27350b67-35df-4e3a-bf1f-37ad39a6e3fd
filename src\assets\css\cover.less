// 覆盖使用的第三方库的样式
/* 
    将颜色抽出来，用变量控制
 */
@Color_main_1: #4a97eb;

@import './mixins.less';

.el-select .el-tag {
  max-width: 80%;
}
body{
  margin:0px;
  padding:0px;
}
.qz-pro-table
  .table-pagination
  .el-pagination.is-background
  .el-pager
  li:not(.disabled).active {
  border: none;
  cursor: pointer;
  background: @Color_main_1 !important;
  color: #fff;
}

.qz-panel {
  border: 1px solid #e4e7ed !important;
}

.qz-panel__header {
  background: #f5f7fa !important;
  border-bottom: 1px solid #e4e7ed !important;
  box-shadow: none !important;
  border-image: none !important;
}

.qz-pro-table .el-table th.is-leaf {
  background-color: #f5f7fa !important;
  box-shadow: 0px 1px 0px 0px rgba(228, 231, 237, 1);
}

.qz-table .el-table th.is-leaf {
  background-color: #f5f7fa !important;
  box-shadow: 0px 1px 0px 0px rgba(228, 231, 237, 1);
}

.qz-panel {
  border: 1px solid #e4e7ed !important;
}

// element-ui

// 多屏幕下，以下操作会使el-tab显示蓝色边框
// 点击tab标签->切换到其他屏幕并点击任意位置->切回含tab标签的屏幕，可以看到蓝色边框
//
// 下面的代码避免了这种情况
.el-tabs__item:focus.is-active.is-focus:not(:active) {
  box-shadow: none;
}

.qz-tabs-line-right {
  &.el-tabs--top {
    & .el-tabs__item.is-top:nth-child(2) {
      padding-left: 20px;
    }

    & .el-tabs__item.is-top:last-child {
      padding-right: 20px;
    }
  }

  & .el-tabs__nav-wrap::after {
    height: 1px;
    background: #e6e6e6;
  }

  & .el-tabs__item {
    height: 44px;
    line-height: 44px;
  }

  & .el-tabs__nav {
    float: right;
  }

  & .el-tabs__item.is-active {
    font-family: PingFangSC-Medium, Avenir, Helvetica, Arial, sans-serif;
    font-size: 14px;
    color: @Color_main_1;
    position: relative;

    &::after {
      content: '';
      border: 7px solid;
      border-color: transparent transparent @Color_main_1;
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
    }
  }

  & .el-tabs__active-bar {
    display: none;
  }

  & .el-tabs__item {
    font-family: PingFangSC-Regular, Avenir, Helvetica, Arial, sans-serif;
    font-size: 14px;
    color: #666;
  }
}

.qz-table-customed {
  background: #ffffff;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 0 20px 20px;

  & .el-table .el-table__body {
    border-collapse: separate;
    border-spacing: 0px 15px;

    & td:last-of-type i {
      color: @Color_main_1;
      font-size: 18px;
    }
  }

  & .el-table,
  .el-table td,
  .el-table th {
    border: none;
  }

  & .el-table--border::after,
  .el-table::before {
    display: none;
  }

  & .el-table--enable-row-hover .el-table__body tr:hover > td {
    background-color: transparent;
  }

  & .el-table tbody tr {
    background: #eff0f1;
    margin-bottom: 10px;

    & td:first-of-type {
      border-radius: 4px 0 0 4px;
    }

    & td:last-of-type {
      border-radius: 0 4px 4px 0;
    }
  }

  & .el-table th {
    padding-bottom: 0;
  }
}

.add-item-btn {
  background: #f9fcff;
  border: 1px dashed @Color_main_1;
  border-radius: 4px;
  height: 36px;
  font-family: MicrosoftYaHei, Avenir, Helvetica, Arial, sans-serif;
  font-size: 12px;
  color: @Color_main_1;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;

  & i {
    margin-right: 5px;
    font-size: 14px;
    vertical-align: middle;
  }
}

// form
.el-form-item {
  margin-bottom: 20px;
}

.el-form-item__label {
  padding-right: 7px;
  font-weight: normal;

  // &::after {
  //   content: '\ff1a';
  // }
}

input.el-upload__input {
  display: none;
}

// table
.el-table thead th {
  font-weight: 500;
}

.table-bordered > thead > tr > td,
.table-bordered > thead > tr > th {
  border-bottom-width: 1px;
}

// tooltip
.el-tooltip__popper.is-dark {
  max-width: 600px;
  max-height: 200px;
  overflow: hidden;
  background-color: #000;
  opacity: 0.7;
}

// button
.el-button:not(.el-button--text) {
  min-width: 60px;
  padding: 8px 10px;
  border-color: #c0c0c0;

  & i {
    margin-right: 5px;
  }

  &.el-button--primary {
    background-color: @Color_main_1;
    border-color: @Color_main_1;

    &.is-disabled {
      color: #c0c4cc;
      background-color: #fff;
      border-color: #ebeef5;
    }
  }

  & + .el-button {
    margin-left: 15px;
  }
}


.el-input .el-input__inner,
.el-select .el-input__inner {
  color: #2e3444;
  font-size: 14px;
}

.el-input-group__append {
  background-color: @Color_main_1;
  border-color: @Color_main_1;
  font-size: 12px;
  color: #ffffff;
}

.qz-upload-input {
  &:hover {
    & .el-input__suffix {
      display: flex;
    }
  }

  & .el-input__suffix {
    display: none;
    align-items: center;
    cursor: pointer;
  }
}

.el-tooltip__popper {
  max-width: 30%;
  word-break: break-all;
}

.dropdown-menu {
  left: -200px !important;
}

// 替换el-select和el-table组件中的箭头图标
.el-select,
.el-table {
  .el-icon-arrow-left:before {
    content: '\e792';
  }

  .el-icon-arrow-right:before {
    content: '\e791';
  }

  .el-icon-arrow-down:before {
    content: '\e790';
  }

  .el-icon-arrow-up:before {
    content: '\e78f';
  }
}

.el-select-dropdown .el-select-dropdown__item.selected {
  font-weight: normal;
}

.el-select-dropdown .el-select-dropdown__item.hover {
  background-color: #f4f9ff;
}

.qz-alert .alert-body {
  display: flex;
  flex-direction: column;

  .alert-body_title {
    flex: none;
    .ellipsis;
  }

  .alert-body_main {
    flex: auto;
    overflow: auto;
  }
}

.el-row.is-align-center {
  align-items: center;
}

.el-message-box .el-message-box__btns .el-button {
  min-width: 76px;
}

#nprogress .bar,
#nprogress .spinner,
.el-tooltip__popper,
.el-select-dropdown,
.el-cascader__dropdown,
.el-message-box__wrapper,
.el-message {
  z-index: 3000 !important;
}

.el-loading-mask {
  z-index: 1998;
}

label {
  margin-bottom: 0;
}

.qz-alert__body__title {
  position: relative;
  word-break: break-all;
  padding-right: 50px !important;

  .el-icon-close {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    right: 20px;
  }
}

.el-descriptions-item__content {
  flex: 1 1 0%;
}
//覆盖amis中的样式
.wrapper{
  padding: 0 !important;
}