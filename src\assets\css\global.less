// 主题色
@theme-blue: #47a5e7;

// 等级tag背景色
@high-level-color-bg: #fff1f0;
@mid-level-color-bg: #fff7e6;
@low-level-color-bg: #fffbe6;
@non-level-color-bg: #f0fde9;
@other-level-color-bg: #f4f4f5;

// 等级tag字体色
@high-level-color-font: #e84738;
@mid-level-color-font: #d48806;
@low-level-color-font: #d1b63b;
@non-level-color-font: #5eb023;
@other-level-color-font: #909399;

// 等级icon颜色
@high-level-color-icon: #e84738;
@mid-level-color-icon: #eb8e48;
@low-level-color-icon: #e8cf61;
@non-level-color-icon: #97d67a;
@other-level-color-icon: #909399;

// 等级数字色
@high-level-color-num: #e84738;
@mid-level-color-num: #f8851c;
@low-level-color-num: #f09e1c;

// 等级数字背景色
@high-level-color-num-bg: #fbdbd8;
@mid-level-color-num-bg: #ffe8ce;
@low-level-color-num-bg: #fff5cd;

// 通用红色
@red-color: #e84738;
// 成功绿色
@success-color: #70b603;

// 边框色
@border-color: #d3d4d5;
@shallow-border-color: #ebeef5;
@table-border-color: #e3e3e3;
@button-border-color: #c0c0c0;
// 边框阴影色
@border-box-shadow:0 0 6px 0 rgba(8, 61, 101, 0.15);
// panel顶部颜色
@panel-header-color: #f5f7fa;
// 树形悬停&点击背景色
@tree-hover-click-bg-color: #f4f9ff;

@grey-tag-color: #f4f5f9;
// input颜色
@input-font-color: #2e3444;
// 灰色字体2
@grey-font-color2: #c0c4cc;
// 禁用颜色
@disabled-color: #c9c8c8;
// 灰色字体
@grey-font-color: #999;
// tab灰色字体
@tab-grey-font-color: #666;
// 标题字体色
@title-color: #3d3d3d;
// 提示字体色
@tip-font-color: #999999;
// 表单标签色
@form-label-color: #606266;
// 抽屉标题色
@drawer-title-color: #606266;
// 抽屉关闭按钮色
@drawer-close-font-color: #979797;
// 抽屉底部背景色
@drawer-footer-bg-color: #f9f8f8;
// 抽屉底部阴影色
@drawer-footer-box-shadow:0 -1px 4px 1px rgba(0, 0, 0, 0.14);
// 描述底部颜色
@desc-grey-bg-color: #f8f9fb;
// 提示色
@warning-color: #f8a41f;
// 黄圆点标题色
@yellow-title-color: #f7b500;
// 高亮颜色
@highlight-color: #f77c00;

// 表格头颜色
@table-head-font-color: #909399;
// 表格头悬停深色背景
@table-hover-bg-color: #eaedf0;
// 表格头悬停边框颜色
@table-hover-border-color: #dbe0e4;
// 菜单头颜色
@menu-header-color: #242424;

// 滚动条颜色
@scrollbar-color: #eaecf1;
// 黑滚动条颜色
@dark-scrollbar-color: #595b5d;

// 除去页头页脚视口高度（含二级菜单）
@non-submenu-height: (calc(100vh - 133px));
// 除去页头页脚视口高度（不含二级菜单）
@non-menu-height: (calc(100vh - 93px));
// 配置模块树高度
@setting-tree-height: (calc(100vh - 300px));
// 普通模块树高度
@normal-tree-height: (calc(100vh - 200px));


// 主题色
@theme-color: @theme-blue;

// 通用红色
@red-color: #e84738;
// 成功绿色
@green-color: #67c23a;
// 通用墨绿色
@green-deep-color: #39a28b;
// 通用白色
@white-color: #fff;
// 通用警告黄色
@orange-color: #f8a41f;
// 通用橙色
@orange-deep-color: #f77c00;
// 通用粉色
@pink-color: #e65cad;
// 通用深粉色
@pink-deep-color: #a24c77;

// 等级tag背景色
@high-level-color-bg: #fff1f0;
@mid-level-color-bg: #fff7e6;
@low-level-color-bg: #fffbe6;
@non-level-color-bg: #f0fde9;
@other-level-color-bg: #f4f4f5;

// 等级tag字体色
@high-level-color-font: #e84738;
@mid-level-color-font: #d48806;
@low-level-color-font: #d1b63b;
@non-level-color-font: #5eb023;
@other-level-color-font: #909399;

// 等级icon颜色
@high-level-color-icon: #e84738;
@mid-level-color-icon: #eb8e48;
@low-level-color-icon: #e8cf61;
@non-level-color-icon: #97d67a;
@other-level-color-icon: #909399;

// 等级数字色
@high-level-color-num: #e84738;
@mid-level-color-num: #f8851c;
@low-level-color-num: #f09e1c;

// 等级数字背景色
@high-level-color-num-bg: #fbdbd8;
@mid-level-color-num-bg: #ffe8ce;
@low-level-color-num-bg: #fff5cd;

// 边框色
@border-base-color: #d3d4d5;
@border-light-color: #e3e3e3;
@border-lighter-color: #ebeef5;
@border-grey-color: #c0c0c0;
@border-blue-color: #90c4fd;
@border-deep-blue-color: #4a97eb;
@border-yellow-color: #ffe58f;
@border-red-color: #ffa39e;
@border-purple-color: rgb(109, 133, 225);
@border-black-color: #2b2f45;

// 阴影色
@box-shadow-border:0 0 6px 0 rgba(8, 61, 101, 0.15);
@box-shadow-drawer-footer:0 -1px 4px 1px rgba(0, 0, 0, 0.14);
@box-shadow-page-footer:2px 0 4px 0 rgba(208, 208, 208, 0.5);
@box-shadow-menu:0 2px 4px 0 rgba(208, 208, 208, 0.5);
@box-shadow-side-menu:0 2px 8px 0 rgba(0, 0, 0, 0.06);

// 文字颜色
@text-regular-color: #606266;
@text-dark-color: #151631;
@text-primary-color: #303133;
@text-grey-color: #c0c4cc;
@text-tip-color: #999999;
@text-disabled-color: #a4a5a7;
@text-light-blue-color: #50c3d1;
@text-purple-color: #787e98;
@text-lighter-blue-color: #3bfff9;

// 背景色
@bg-dark-color: #242424;
@bg-blue-deep-color: #031244;
@bg-disabled-color: #f3f6f9;
@bg-light-blue-color: #f4f9ff;
@bg-lighter-blue-color: #f8f9fb;
@bg-grey-light-color: #f5f7fa;
@bg-grey-color: #f9f8f8;
@bg-grey-deep-color: #eaedf0;
@bg-grey-deeper-color: #909399;
@bg-red-color: #d65046;
@bg-blue-color: #3876d0;
@bg-dark-hover-color: (rgba(255, 255, 255, 0.15));
@bg-purple-color: #f0f3ff;

// 滚动条颜色
@scrollbar-color: #eaecf1;
@scrollbar-dark-color: #595b5d;

// 除去页头页脚视口高度（含二级菜单）
@non-submenu-height: (calc(100vh - 134px));
// 除去页头页脚视口高度（不含二级菜单）
@non-menu-height: (calc(100vh - 92px));
// 配置模块树高度
@setting-tree-height: (calc(100vh - 300px));
// 普通模块树高度
@normal-tree-height: (calc(100vh - 200px));

