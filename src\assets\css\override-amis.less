@charset "UTF-8";
/* 此处放置需要override的变量，因为部分变量已经在variables.scss中定义 */
:root {
  --affix-offset-top: 0px;
  --affix-offset-bottom: 0px;
}
:root,
.AMISCSSWrapper {
  --white: var(--colors-neutral-text-11);
  --primary: var(--colors-brand-5);
  --primary-onHover: var(--colors-brand-6);
  --primary-onActive: var(--colors-brand-4);
  --secondary: var(--colors-neutral-text-4); /*  secondary 颜色需进一步确认 */
  --secondary-onHover: var(--colors-neutral-text-6);
  --secondary-onActive: var(--colors-neutral-text-4);
  --success: var(--colors-success-5);
  --success-onHover: var(--colors-success-6);
  --success-onActive: var(--colors-success-4);
  --info: var(--colors-info-5);
  --info-onHover: var(--colors-info-6);
  --info-onActive: var(--colors-info-4);
  --warning: var(--colors-warning-5);
  --warning-onHover: var(--colors-warning-6);
  --warning-onActive: var(--colors-warning-4);
  --danger: var(--colors-error-5);
  --danger-onHover: var(--colors-error-6);
  --danger-onActive: var(--colors-error-4);
  --light: var(--colors-neutral-fill-11);
  --dark: var(--colors-neutral-fill-3);
  --fontFamilyMonospace:
    SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace;
  --fontFamilyBase: var(--fonts-base-family);
  --fontSizeBase: var(--fonts-size-7);
  --fontSizeMd: var(--fonts-size-7);
  --fontSizeLg: var(--fonts-size-6);
  --fontSizeXl: var(--fonts-size-5);
  --fontSizeSm: var(--fonts-size-8);
  --fontSizeXs: var(--fonts-size-8);
  --text-color: var(--colors-neutral-text-2);
  --button-color: var(--colors-neutral-text-11);
  --animation-duration: 0.2s;
  --text--muted-color: var(--colors-neutral-text-6);
  --text--loud-color: var(--colors-neutral-text-2);
  --pre-color: var(--text-color);
  --borderColor: var(--colors-neutral-line-8);
  --borderColorLight: var(--colors-neutral-line-10);
  --borderColorDarken: var(--colors-neutral-line-8);
  --borderRadius: var(--borders-radius-3);
  --borderRadiusMd: var(--borders-radius-4);
  --borderRadiusLg: var(--borders-radius-5);
  --boxShadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  --boxShadowSm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  --boxTooltipShadow:
    0 4px 6px 1px rgb(8 14 26 / 6%), 0 1px 10px 0 rgb(8 14 26 / 5%),
    0 2px 4px -1px rgb(8 14 26 / 4%);
  --lineHeightBase: var(--fonts-lineHeight-2);
  --body-lineHeight: var(--lineHeightBase);
  --borderWidth: 0.0625rem;
  --fontWeightNormal: 400;
  --fontWeightBase: var(--fontWeightNormal);
  --fontWeightMd: 500;
  --fontWeightBold: 700;
  --background: var(--colors-neutral-fill-11);
  --code-color: var(--danger);
  --code-background: var(--background);
  --pre-background: var(--background);
  --body-bg: var(--light);
  --body-size: var(--fontSizeBase);
  --body-color: var(--text-color);
  --body-weight: var(--fontWeightBase);
  --gap-xs: var(--sizes-size-3);
  --gap-sm: var(--sizes-size-5);
  --gap-base: var(--sizes-size-7);
  --gap-md: var(--sizes-size-9);
  --gap-lg: var(--sizes-base-10);
  --gap-xl: var(--sizes-base-12);
  --icon-color: var(--colors-neutral-text-5);
  --icon-onHover-color: var(--colors-brand-5);
  --icon-onDisabled-color: var(--colors-neutral-text-10);
  --label--default-bg: var(--colors-neutral-fill-3);
  --label--primary-bg: var(--colors-brand-5);
  --label--success-bg: var(--success);
  --label--info-bg: var(--info);
  --label--warning-bg: var(--warning);
  --label--danger-bg: var(--danger);
  --label-color: #fff;
  --label-link--hover-color: #fff;
  --scrollbar-width: 1.0625rem;
  --Audio-border: 0.0625rem solid #dee2e6;
  --Audio-height: 3.125rem;
  --Audio-input-width: 5rem;
  --Audio-item-margin: 0.625rem;
  --Audio-lineHeight: 3.125rem;
  --Audio-play-top: var(--gap-xs);
  --Audio-play-width: var(--gap-md);
  --Audio-process-minWidth: 5rem;
  --Audio-rate-bg: #dee2e6;
  --Audio-rate-height: 3.125rem;
  --Audio-rate-lineHeight: 3.125rem;
  --Audio-rate-width: 2.5rem;
  --Audio-rateControlItem-bg: #dee2e6;
  --Audio-rateControlItem-borderRight: 0.0625rem solid #d3dae0;
  --Audio-svg-height: var(--gap-md);
  --Audio-svg-top: 0.375rem;
  --Audio-svg-width: var(--gap-md);
  --Audio-thumb-bg: #606670;
  --Audio-thumb-height: 0.875rem;
  --Audio-thumb-marginTop: -0.3125rem;
  --Audio-thumb-width: 0.875rem;
  --Audio-times-margin: 0 var(--gap-xs);
  --Audio-times-width: 4.6875rem;
  --Audio-track-bg: #d7dbdd;
  --Audio-track-border: 0.0625rem solid transparent;
  --Audio-track-borderRadius: 0.1875rem;
  --Audio-track-height: 0.375rem;
  --Audio-volume-height: 3.125rem;
  --Audio-volume-lineHeight: 3.125rem;
  --Audio-volume-width: var(--gap-md);
  --Audio-volumeControl-width: 6.875rem;
  --Avatar-bg: #d1d5db;
  --Avatar-width: 2.5rem;
  --Avatar-size-large: 3rem;
  --Avatar-size-default: var(--Avatar-width);
  --Avatar-size-small: 2rem;
  --Avatar-icon-size-large: 1.25rem;
  --Avatar-icon-size-default: var(--fontSizeLg);
  --Avatar-icon-size-small: 0.75rem;
  --Badge-size: var(--gap-md);
  --Badge-color: #fff;
  --Badge--success-bg: var(--success);
  --Badge--info-bg: var(--info);
  --Badge--warning-bg: var(--warning);
  --Badge--danger-bg: var(--danger);
  --Button--sm-fontSize: var(--fontSizeSm);
  --Button-transition:
    color var(--animation-duration) ease-in-out,
    background-color var(--animation-duration) ease-in-out,
    border-color var(--animation-duration) ease-in-out,
    box-shadow var(--animation-duration) ease-in-out;
  --ButtonGroup--primary-isActive-color: var(--colors-neutral-fill-11);
  --ButtonGroup--primary-isActive-bg: var(--colors-brand-5);
  --ButtonGroup-divider-width: 0.0625rem;
  --ButtonGroup-divider-color: #fff;
  --ButtonGroup-borderWidth: var(--borders-width-2);
  --Button-onDisabled-opacity: 0.3;
  --Breadcrumb-item-fontSize: var(--fontSizeMd);
  --Breadcrumb-item-default-color: var(--colors-neutral-text-5);
  --Breadcrumb-item-hover-color: var(--colors-brand-5);
  --Breadcrumb-item-active-color: var(--colors-brand-4);
  --Breadcrumb-item-last-color: var(--colors-neutral-text-2);
  --BreadcrumbDropdown-item-default-color: var(--colors-neutral-text-2);
  --BreadcrumbDropdown-item-default-bg: var(--colors-neutral-text-11);
  --BreadcrumbDropdown-item-hover-bg: var(--colors-brand-10);
  --BreadcrumbDropdown-item-fontSize: var(--fontSizeSm);
  --BreadcrumbDropdown-item-height: 2rem;
  --BreadcrumbDropdown-item-paddingX: var(--gap-sm);
  --BreadcrumbDropdown-item-paddingY: calc(
    (var(--BreadcrumbDropdown-item-height) - var(--fontSizeSm)) / 2
  );
  --Breadcrumb-item-disabled-color: var(--colors-neutral-text-6);
  --Card-actions-borderColor: var(--colors-neutral-line-10);
  --Card-actions-fontSize: var(--fonts-size-8);
  --Card-actions-onChecked-onHover-bg: var(--colors-neutral-fill-11);
  --Card-actions-onChecked-onHover-color: var(--colors-neutral-fill-11);
  --Card-actions-onHover-bg: var(--colors-neutral-fill-11);
  --Card-actions-onHover-color: var(--colors-brand-5);
  --Card-bg: var(--colors-neutral-fill-11);
  --Card-borderColor: var(--borderColor);
  --Card-borderRadius: var(--borderRadius);
  --Card-borderWidth: var(--borderWidth);
  --Card-secondary-color: var(--colors-neutral-fill-5);
  --Card-onChecked-bg: var(--colors-neutral-fill-11);
  --Card-onChecked-borderColor: var(--colors-brand-5);
  --Card-onChecked-color: var(--colors-brand-5);
  --Card-onChecked-fieldLabel-color: rgb(124.7142857143, 213.8571428571, 241.2857142857);
  --Card-onDragging-opacity: 0.1;
  --Card-onModified-bg: var(--colors-neutral-fill-8);
  --Card-onModified-borderColor: var(--colors-brand-5);
  --Card-onModified-color: var(--colors-brand-6);
  --Card-onModified-fieldLabel-color: var(--colors-brand-8);
  --Card-onModified-onHover-bg: rgb(207.5625, 224.0625, 252.9375);
  --Card-onModified-onHover-color: rgb(17.805, 101.89, 241.195);
  --Cards--unsaved-heading-bg: #e8f0fe;
  --Cards--unsaved-heading-color: #4285f4;
  --Cards-fixedTop-boxShadow: var(--shadows-shadow-normal);
  --Cards-placeholder-height: 6.25rem;
  --Cards-toolbar-marginX: 0;
  --Cards-toolbar-marginY: var(--gap-base);
  --Carousel--dark-control: black;
  --Carousel--light-control: white;
  --Carousel-arrowControl-height: var(--gap-lg);
  --Carousel-arrowControl-width: var(--gap-lg);
  --Carousel-bg: var(--colors-neutral-fill-10);
  --Carousel-dot-borderRadius: 0.25rem;
  --Carousel-dot-height: 0.5rem;
  --Carousel-dot-margin: 0.4375rem var(--gap-xs);
  --Carousel-dot-width: 0.5rem;
  --Carousel-height: 12.5rem;
  --Carousel-imageDescription-bottom: 1.5625rem;
  --Carousel-imageTitle-bottom: 2.8125rem;
  --Carousel-minWidth: 6.25rem;
  --Carousel-svg-height: var(--gap-lg);
  --Carousel-svg-width: var(--gap-lg);
  --Carousel-transitionDuration: var(--animation-duration);
  --ColorPicker-bg: var(--colors-neutral-fill-11);
  --ColorPicker-borderColor: var(--Form-input-borderColor);
  --ColorPicker-borderRadius: var(--borders-radius-3);
  --ColorPicker-borderWidth: var(--borders-width-2);
  --ColorPicker-color: var(--colors-neutral-text-2);
  --ColorPicker-fontSize: var(--Form-input-fontSize);
  --ColorPicker-height: var(--Form-input-height);
  --ColorPicker-lineHeight: var(--Form-input-lineHeight);
  --ColorPicker-height: var(--sizes-base-16);
  --ColorPicker-onDisabled-bg: var(--colors-neutral-fill-8);
  --ColorPicker-onDisabled-color: var(--text--muted-color);
  --ColorPicker-onFocused-borderColor: var(--Form-input-onFocused-borderColor);
  --ColorPicker-onHover-bg: var(--colors-neutral-fill-11);
  --ColorPicker-onHover-borderColor: var(--colors-brand-5);
  --ColorPicker-paddingX: var(--sizes-size-6);
  --ColorPicker-paddingY: var(--sizes-size-3);
  --ColorPicker-placeholderColor: var(--colors-neutral-text-6);
  --ColorPicker-boxShadow: var(--shadows-shadow-normal);
  --Copyable-iconColor: var(--icon-color);
  --Copyable-onHover-iconColor: var(--icon-onHover-color);
  --Crud-toolbar-gap: var(--sizes-size-6);
  --Crud-toolbar-height: 1.875rem;
  --Crud-toolbar-lineHeight: var(--lineHeightBase);
  --LocationPicker-borderRadius: var(--borders-radius-3);
  --DropDown-caret-marginLeft: var(--gap-sm);
  --DropDown-menu-bg: var(--colors-neutral-fill-11);
  --DropDown-menu-borderColor: var(--borderColor);
  --DropDown-menu-borderRadius: var(--borderRadius);
  --DropDown-menu-borderWidth: var(--borderWidth);
  --DropDown-menu-boxShadow: var(--Form-select-outer-boxShadow);
  --DropDown-menu-height: 2rem;
  --DropDown-menu-minWidth: 10rem;
  --DropDown-menu-paddingX: 0;
  --DropDown-menu-paddingY: var(--gap-xs);
  --DropDown-menuItem-onHover-bg: var(--Form-select-menu-onHover-bg);
  --DropDown-group-color: #848b99;
  --DropDown-menuItem-color: var(--colors-neutral-text-2);
  --DropDown-menuItem-onHover-color: var(--Form-select-menu-onHover-color);
  --DropDown-menuItem-onActive-color: var(--colors-brand-5);
  --DropDown-menuItem-onDisabled-color: #b4b6ba;
  --DropDown-menuItem-paddingX: var(--select-base-default-option-paddingRight);
  --Fieldset-legend-bgColor: var(--colors-neutral-fill-11);
  --Form--horizontal-gutterWidth: var(--gap-md);
  --Form--horizontal-label-align: right;
  --Form--horizontal-label-whiteSpace: normal;
  --Form--horizontal-justify-label-align: left;
  --Form--horizontal-justify-value-align: right;
  --Form-control-widthBase: 12.5rem;
  --Form-control-widthLg: 20rem;
  --Form-control-widthMd: 15rem;
  --Form-control-widthSm: 10rem;
  --Form-control-widthXs: 5rem;
  --Form-fontSize: var(--fontSizeBase);
  --Form-group--lg-gutterWidth: 2.5rem;
  --Form-group--md-gutterWidth: 1.875rem;
  --Form-group--sm-gutterWidth: var(--gap-md);
  --Form-group--xs-gutterWidth: 0.625rem;
  --Form-group-gutterWidth: var(--Form--horizontal-gutterWidth);
  --Form-input-addOnBg: var(--colors-neutral-fill-11);
  --Form-input-addOnColor: var(--text-color);
  --Form-input-addOnDividerBorderWidth: var(--borders-width-2);
  --Form-input-bg: var(--colors-neutral-fill-11);
  --Form-input-borderColor: var(--borderColor);
  --Form-input-borderRadius: var(--borders-radius-3);
  --Form-input-borderWidth: 0.0625rem;
  --Form-input-boxShadow: none;
  --Form-input-color: var(--colors-neutral-text-2);
  --Form-input-fontSize: var(--Form-fontSize);
  --Form-input-height: var(--sizes-base-16);
  --Form-input-iconColor: var(--colors-neutral-text-5);
  --Form-input-lineHeight: var(--fonts-lineHeight-2);
  --Form-input-marginBottom: var(--sizes-size-3);
  --Form-input-onActive-color: var(--info);
  --Form-input-onDisabled-bg: var(--colors-neutral-fill-10);
  --Form-input-onDisabled-borderColor: var(--colors-neutral-line-8);
  --Form-input-onError-bg: var(--colors-neutral-fill-11);
  --Form-input-onError-borderColor: var(--colors-error-5);
  --Form-input-onFocus-addOnColor: var(--colors-brand-5);
  --Form-input-onFocused-bg: var(--colors-neutral-fill-11);
  --Form-input-onFocused-borderColor: var(--colors-brand-4);
  --Form-input-onHover-iconColor: var(--colors-neutral-text-4);
  --Form-input-onHover-bg: rgba(255, 255, 255, 0.6);
  --Form-input-onHover-borderColor: var(--colors-brand-5);
  --Form-input-paddingX: var(--sizes-size-6);
  --Form-input-password-icon-size: var(--sizes-size-9);
  --Form-input-password-icon-color: var(--colors-neutral-text-5);
  --Form-input-paddingY: var(--sizes-size-3);
  --Form-input-placeholderColor: var(--text--muted-color);
  --Form-input-onDisabled-color: var(--colors-neutral-text-5);
  --Form-input-clearBtn-size: var(--fontSizeMd);
  --Form-input-clearBtn-padding: 0.1875rem;
  --Form-input-clearBtn-color: var(--colors-neutral-text-7);
  --Form-input-clearBtn-color-onHover: var(--colors-neutral-text-4);
  --Form-input-clearBtn-color-onActive: var(--colors-neutral-text-3);
  --Form-label-paddingTop: calc(
    (var(--Form-input-height) - var(--Form-input-lineHeight) * var(--Form-input-fontSize)) / 2
  );
  --Form-row-gutterWidth: 0.625rem;
  --IconPicker-content-maxHeight: 21.875rem;
  --IconPicker-padding: var(--gap-xs);
  --IconPicker-selectedIcon-marginRight: var(--gap-xs);
  --IconPicker-sugItem-height: 1.75rem;
  --IconPicker-sugItem-lineHeight: 1.75rem;
  --IconPicker-sugItem-width: 1.75rem;
  --IconPicker-tab-height: 1.875rem;
  --IconPicker-tab-lineHeight: 1.875rem;
  --IconPicker-tab-onActive-bg: var(--colors-neutral-fill-11);
  --IconPicker-tab-padding: 0 0.625rem;
  --IconPicker-tabs-bg: #f0f3f4;
  --InputGroup-addOn-bg: var(--Form-input-addOnBg);
  --InputGroup-addOn-borderColor: var(--Form-input-borderColor);
  --InputGroup-addOn-borderRadius: var(--Form-input-borderRadius);
  --InputGroup-addOn-borderWidth: var(--Form-input-borderWidth);
  --InputGroup-addOn-onFocused-borderColor: var(--Form-input-onFocused-borderColor);
  --InputGroup-button-borderColor: var(--Form-input-borderColor);
  --InputGroup-button-borderRadius: var(--borders-radius-3);
  --InputGroup-button-borderWidth: var(--borders-width-2);
  --InputGroup-height: var(--Form-input-height);
  --InputGroup-paddingX: 0.625rem;
  --InputGroup-paddingY: calc(
    (
        var(--InputGroup-height) - var(--Form-input-lineHeight) * var(--Form-input-fontSize) -
          0.125rem
      ) /
      2
  );
  --InputGroup-select-arrowColor: var(--colors-neutral-text-5);
  --InputGroup-select-bg: var(--colors-neutral-fill-11);
  --InputGroup-select-borderColor: var(--borderColor);
  --InputGroup-select-borderRadius: var(--borders-radius-3);
  --InputGroup-select-borderWidth: var(--borders-width-2);
  --InputGroup-select-color: var(--colors-neutral-text-2);
  --InputGroup-select-onFocused-arrowColor: var(--colors-brand-5);
  --InputGroup-select-onFocused-bg: var(--colors-brand-10);
  --InputGroup-select-onFocused-color: var(--colors-brand-5);
  --Layout--offscreen-width: 75%;
  --Layout-aside--folded-width: var(--sizes-base-30);
  --Layout-aside--lg-width: 18.75rem;
  --Layout-aside--md-width: 15.625rem;
  --Layout-aside--sm-width: 9.375rem;
  --Layout-aside-bg: var(--colors-neutral-fill-2);
  --Layout-aside-color: rgb(159.6724137931, 160, 160.3275862069);
  --Layout-aside-onAcitve-bg: rgb(39.4377155172, 45.25, 51.0622844828);
  --Layout-aside-onHover-bg: rgb(43.8826293103, 50.35, 56.8173706897);
  --Layout-aside-subList-bg: var(--colors-neutral-fill-2);
  --Layout-aside-onAcitve-onHover-bg: var(--Layout-aside-onAcitve-bg);
  --Layout-aside-width: 11.25rem;
  --Layout-aside-width-collapsed: 3.75rem;
  --Layout-asideDivider-bg: var(--colors-neutral-line-3);
  --Layout-asideDivider-margin: 0 var(--sizes-size-6);
  --Layout-asideLabel-color: rgb(134.0844827586, 134.5, 134.9155172414);
  --Layout-asideLink-color: var(--colors-neutral-text-11);
  --Layout-asideLink-fontSize: var(--fonts-size-8);
  --Layout-asideLink-arrowFontSize: var(--fonts-size-8);
  --Layout-asideLink-arrowColor: var(--colors-neutral-text-5);
  --Layout-asideLink-iconColor: inherit;
  --Layout-asideLink-onActive-arrowColor: var(--Layout-asideLink-onActive-color);
  --Layout-asideLink-onActive-color: var(--colors-brand-6);
  --Layout-asideLink-onHover-color: var(--colors-brand-6);
  --Layout-asideLink-onHover-iconColor: var(--colors-brand-6);
  --Layout-asideLink-onHover-iconSize: var(--sizes-size-9);
  --Layout-asideLink-onHover-arrowColor: var(--colors-neutral-text-11);
  --Layout-brand-bg: var(--colors-neutral-fill-2);
  --Layout-brand-color: var(--colors-neutral-text-11);
  --Layout-brandBar-color: rgb(159.6724137931, 160, 160.3275862069);
  --Layout-header-bg: var(--colors-neutral-fill-10);
  --Layout-header-boxShadow: none;
  --Layout-header-height: 3.125rem;
  --Layout-headerBar-borderBottom: none;
  --Layout-footer-height: 3.125rem;
  --Layout-nav--folded-height: var(--sizes-base-20);
  --Layout-nav-height: 2.5rem;
  --Layout-nav-lgHeight: 3.125rem;
  --Layout-body-bg: var(--body-bg);
  --Layout-paddingX: 1.5625rem;
  --Layout-icon-size: 0.875rem;
  --Layout-light-backgroundColor: var(--colors-neutral-fill-11);
  --Layout-light-bgColor-onHover: var(--colors-brand-10);
  --Layout-light-fontColor: var(--colors-neutral-text-2);
  --Layout-fontColor--onHover: var(--colors-brand-5);
  --Layout-dark-fontColor: var(--colors-neutral-text-11);
  --Layout-fontColor--onActive: var(--colors-brand-4);
  --Layout-fontColor--info: var(--colors-brand-5);
  --Layout-dark-backgroundColor: var(--colors-neutral-fill-2);
  --Layout-dark-selected-color: var(--colors-brand-6);
  --Layout-tooltip-fontSize: var(--fonts-size-8);
  --Layout-dark-tooltip-backgroundColor: var(--colors-brand-1);
  --List--unsaved-heading-bg: #e8f0fe;
  --List--unsaved-heading-color: #4285f4;
  --List-bg: var(--colors-neutral-fill-11);
  --List-borderColor: var(--borderColor);
  --List-borderRadius: var(--borderRadius);
  --List-borderWidth: var(--borderWidth);
  --List-fixedTop-boxShadow: var(--shadows-shadow-normal);
  --List-placeholder-height: 1.875rem;
  --List-toolbar-marginX: 0;
  --List-toolbar-marginY: var(--gap-base);
  --ListControl-fontSize: var(--Form-fontSize);
  --ListControl-gutterWidth: 0.625rem;
  --ListControl-item-bg: var(--colors-neutral-fill-11);
  --ListControl-item-borderColor: var(--borderColor);
  --ListControl-item-borderWidth: var(--borders-width-2);
  --ListControl-item-borderRadius: var(--borders-radius-3);
  --ListControl-item-color: var(--colors-neutral-text-2);
  --ListControl-item-onActive-after-borderColor: #fff;
  --ListControl-item-onActive-before-bg: var(--colors-brand-5);
  --ListControl-item-onActive-bg: var(--colors-neutral-fill-11);
  --ListControl-item-onActive-borderColor: var(--colors-brand-4);
  --ListControl-item-onActive-color: var(--colors-brand-4);
  --ListControl-item-onActive-onHover-bg: var(--colors-neutral-fill-11);
  --ListControl-item-onDisabled-bg: var(--colors-neutral-fill-9);
  --ListControl-item-onDisabled-borderColor: var(--colors-neutral-line-9);
  --ListControl-item-onDisabled-color: var(--colors-neutral-text-6);
  --ListControl-item-onDisabled-opacity: 1;
  --ListControl-item-onHover-bg: var(--colors-neutral-fill-11);
  --ListControl-item-onHover-borderColor: var(--colors-brand-5);
  --ListControl-item-onHover-color: var(--colors-brand-5);
  --ListControl-item-paddingX: var(--sizes-size-6);
  --ListControl-item-paddingY: 0.375rem;
  --ListControl-item-transition: none;
  --ListItem--strip-bg: var(--colors-neutral-fill-10);
  --ListItem-borderColor: var(--colors-neutral-line-10);
  --ListItem-borderWidth: var(--List-borderWidth);
  --ListItem-onChecked-bg: var(--colors-brand-10);
  --ListItem-onChecked-borderColor: var(--colors-brand-4);
  --ListItem-onChecked-color: var(--colors-brand-4);
  --ListItem-onChecked-fieldLabel-color: var(--colors-brand-4);
  --ListItem-onDragging-opacity: 0.1;
  --ListItem-onModified-bg: #e8f0fe;
  --ListItem-onModified-borderColor: rgb(183.125, 208.125, 251.875);
  --ListItem-onModified-color: #4285f4;
  --ListItem-onModified-fieldLabel-color: rgb(162.39, 195.22, 249.61);
  --ListItem-paddingX: var(--gap-base);
  --ListItem-paddingY: var(--gap-sm);
  --ListItem--onHover-bg: rgba(0, 126, 255, 0.08);
  --ListItem--onHover-color: var(--info);
  --listMenu--onActive-borderColor: var(--info);
  --ListMenu-borderRadius: var(--borders-radius-1);
  --ListMenu-borderWidth: var(--borders-width-1);
  --ListMenu-bordrColor: var(--borderColor);
  --ListMenu-divider-color: var(--borderColorLight);
  --ListMenu-item--onActive-bg: transparent;
  --ListMenu-item--onActive-color: var(--info);
  --ListMenu-item--onDisabled-bg: transparent;
  --ListMenu-item--onDisabled-color: var(--text--muted-color);
  --ListMenu-item--onHover-bg: var(--colors-neutral-fill-8);
  --ListMenu-item--onHover-color: var(--colors-neutral-text-2);
  --ListMenu-item-bg: var(--colors-neutral-fill-11);
  --ListMenu-item-color: var(--colors-neutral-text-2);
  --ListMenu-item-height: var(--sizes-base-15);
  --ListMenu-item-mobile-margin: 0.3125rem;
  --ListMenu-item-mobile-width: 5.625rem;
  --ListMenu-item-mobile-bg: #f5f5f5;
  --ListMenu-item-mobile-active-bg: #e7f1ff;
  --Log-bg: #222;
  --Log-padding: var(--gap-sm) 0;
  --Log-line-padding: 0 var(--gap-sm);
  --Log-color: #f1f1f1;
  --Log-line--onHover-bg: #444;
  --Nav-item-bg: transparent;
  --Nav-item-borderRadius: var(--borders-radius-1);
  --Nav-item-color: var(--text-color);
  --Nav-item-fontSize: var(--fonts-size-7);
  --Nav-item-collapsed-fontSize: var(--fonts-size-6);
  --Nav-item-fontWeight: var(--fonts-weight-6);
  --Nav-item-fontColor-onDisabled: var(--colors-neutral-fill-6);
  --Nav-item-onActive-bg: var(--colors-neutral-fill-10);
  --Nav-item-onActive-backgroundColor: var(--colors-neutral-fill-12);
  --Nav-item-onActive-borderLeft: var(--borders-width-4) var(--borders-style-2)
    var(--colors-brand-5);
  --Nav-item-onActive-borderColor: var(--colors-link-5);
  --Nav-item-onActive-color: var(--colors-brand-5);
  --Nav-item-onDisabled-color: var(--text--muted-color);
  --Nav-item-onHover-bg: rgba(0, 0, 0, 0.05);
  --Nav-item-onHover-color: var(--text--loud-color);
  --Nav-subItem-fontSize: var(--fonts-size-8);
  --Nav-subItem-onActiveBeforeBg: var(--colors-brand-5);
  --Nav-Item-maxWidth--tabs: 10rem;
  --Nav-Item-height: 2.5rem;
  --Nav-Item-height--horizontal: 3.125rem;
  --Nav-Item-Badge-paddingRight: 0.625rem;
  --Nav-Item-paddingX: 1.25rem;
  --Nav-Item-Drag-color: var(--colors-neutral-text-5);
  --Number-bg: var(--Form-input-bg);
  --Number-borderColor: var(--colors-neutral-line-7);
  --Number-borderRadius: var(--Form-input-borderRadius);
  --Number-borderWidth: var(--borders-width-2);
  --Number-handler--down-content: '';
  --Number-handler--up-content: '';
  --Number-handler--up-transform: rotate(180deg);
  --Number-handler-bg: var(--colors-neutral-fill-11);
  --Number-handler-borderBottom: var(--borders-width-1) solid var(--Form-input-borderColor);
  --Number-handler-color: var(--Form-input-color);
  --Number-handler-fontFamily: 'iconfont';
  --Number-handler-fontSize: var(--fonts-size-8);
  --Number-handler-onActive-bg: var(--Number-handler-onHover-bg);
  --Number-handler-onDisabled-bg: var(--Form-input-onDisabled-bg);
  --Number-handler-onDisabled-color: var(--text--muted-color);
  --Number-handler-onHover-bg: var(--colors-neutral-fill-11);
  --Number-handler-onHover-color: var(--colors-brand-5);
  --Number-handler-width: var(--sizes-base-12);
  --Number-onDisabled-bg: var(--Form-input-bg);
  --Page-aside-bg: var(--colors-neutral-fill-11);
  --Page-aside-maxWidth: 18.75rem;
  --Page-aside-width: 10rem;
  --Page-body-padding: var(--gap-base);
  --Page-content-paddingX: var(--sizes-size-0);
  --Page-content-paddingY: var(--sizes-size-0);
  --Page-header-paddingX: var(--sizes-size-9);
  --Page-header-paddingY: var(--sizes-size-6);
  --Page-header-bg: transparent;
  --Page-main-bg: var(--colors-neutral-fill-11);
  --Page-title-color: var(--colors-neutral-text-2);
  --Page-title-fontSize: var(--fontSizeLg);
  --Page-title-fontWeight: var(--fontWeightNormal);
  --Page-title-lineHeight: 1.5;
  --Pagination-fontSize: var(--fonts-size-8);
  --Pagination-height: 2rem;
  --Pagination-height-sm: 1.5rem;
  --Pagination-minWidth: 2rem;
  --Pagination-minWidth-sm: 1.5rem;
  --Pagination-onActive-backgroundColor: var(--colors-neutral-fill-11);
  --Pagination-onActive-border: var(--borders-width-2) var(--borders-style-2) var(--colors-brand-5);
  --Pagination-onActive-color: var(--colors-brand-5);
  --Pagination-onDisabled-color: var(--colors-neutral-text-6);
  --Pagination-onDisabled-backgroundColor: var(--colors-neutral-fill-10);
  --Pagination-padding: 0 0.5rem;
  --Pagination-padding-sm: 0 0.25rem;
  --Pagination-light-color: var(--colors-neutral-text-4);
  --Pagination-border: var(--borderWidth) var(--borders-style-2) var(--colors-neutral-line-7);
  --Panel--default-bg: var(--colors-neutral-fill-11);
  --Panel--default-badgeBg: var(--colors-neutral-fill-3);
  --Panel--default-badgeColor: var(--colors-neutral-fill-10);
  --Picker-onHover-iconColor: var(--icon-onHover-color);
  --PickerColumns-bg: white;
  --PickerColumns-toolbar-height: 3.125rem;
  --PickerColumns-title-fontSize: var(--fontSizeLg);
  --PickerColumns-title-color: #222;
  --PickerColumns-title-lineHeight: 1.5;
  --PickerColumns-action-padding: 0 var(--gap-md);
  --PickerColumns-action-fontSize: var(--fontSizeLg);
  --PickerColumns-confirmAction-color: var(--colors-brand-5);
  --PickerColumns-cancelAction-color: #666;
  --PickerColumns-option-fontSize: var(--fontSizeLg);
  --PickerColumns-optionText-color: var(--text-color);
  --PickerColumns-optionDisabled-opacity: 0.3;
  --PickerColumns-loadingIcon-color: var(--icon-color);
  --PickerColumns-loadingMask-Color: rgba(255, 255, 255, 0.9);
  --PopOver-bg: var(--colors-neutral-fill-11);
  --PopOverAble-iconColor: inherit;
  --PopOverAble-onHover-iconColor: inherit;
  --PopUp-cancelAction-color: #666;
  --PopUp-confirmAction-color: var(--primary);
  --Property-title-bg: var(--colors-neutral-fill-10);
  --Property-label-bg: var(--colors-neutral-fill-10);
  --Portlet-borderColor: var(--borderColor);
  --Portlet-borderStyle: solid;
  --Portlet-borderWidth: var(--borderWidth);
  --Portlet-borderRadius: var(--borderRadius);
  --QuickEdit-iconSize: 0.875rem;
  --QuickEdit-onFocus-borderColor: var(--info);
  --QuickEdit-onFocus-borderWidth: var(--borderWidth);
  --Remark-bg: var(--colors-neutral-text-11);
  --Remark-borderColor: var(--colors-neutral-line-8);
  --Remark-borderWidth: var(--borders-width-2);
  --Remark-icon-fontSize: var(--fonts-size-8);
  --Remark-iconColor: var(--colors-neutral-text-5);
  --Remark-marginLeft: var(--gap-sm);
  --Remark-onHover-bg: var(--colors-warning-5);
  --Remark-onHover-borderColor: var(--colors-warning-5);
  --Remark-onHover-iconColor: var(--colors-neutral-text-11);
  --Remark-width: var(--sizes-size-9);
  --Sparkline-line-color: var(--info);
  --Sparkline-area-color: rgba(0, 123, 255, 0.1);
  --Table-fixedLeftLast-boxShadow: inset 10px 0 8px -8px #00000026;
  --Table-fixedRightFirst-boxShadow: inset -10px 0 8px -8px #00000026;
  --Table-loading-padding: 30px 0px;
  --TagControl-sugBtn-bg: transparent;
  --TagControl-sugBtn-border: var(--colors-neutral-line-7);
  --TagControl-sugBtn-borderRadius: var(--borders-radius-2);
  --TagControl-sugBtn-borderWidth: var(--borders-width-2);
  --TagControl-sugBtn-color: var(--colors-neutral-text-2);
  --TagControl-sugBtn-fontSize: var(--fonts-size-8);
  --TagControl-sugBtn-fontWeight: var(--fonts-weight-6);
  --TagControl-sugBtn-height: var(--sizes-base-15);
  --TagControl-sugBtn-lineHeight: var(--fonts-lineHeight-2);
  --TagControl-sugBtn-onActive-bg: var(--colors-neutral-fill-8);
  --TagControl-sugBtn-onActive-border: var(--colors-neutral-line-5);
  --TagControl-sugBtn-onActive-color: var(--TagControl-sugBtn-color);
  --TagControl-sugBtn-onHover-bg: var(--colors-neutral-fill-8);
  --TagControl-sugBtn-onHover-border: var(--colors-neutral-line-6);
  --TagControl-sugBtn-onHover-color: var(--colors-neutral-text-2);
  --TagControl-sugBtn-paddingX: var(--sizes-size-6);
  --TagControl-sugBtn-paddingY: calc(
    (
        var(--TagControl-sugBtn-height) - var(--borders-width-2) * 2 -
          var(--TagControl-sugBtn-lineHeight) * var(--TagControl-sugBtn-fontSize)
      ) /
      2
  );
  --TagControl-sugTip-color: var(--colors-brand-5);
  --Tooltip--attr-bg: rgba(0, 0, 0, 0.7);
  --Tooltip--attr-borderColor: var(--borderColor);
  --Tooltip--attr-borderRadius: var(--borderRadius);
  --Tooltip--attr-borderWidth: 0;
  --Tooltip--attr-boxShadow: var(--shadows-shadow-normal);
  --Tooltip--attr-color: #fff;
  --Tooltip--attr-fontSize: var(--fontSizeSm);
  --Tooltip--attr-gap: var(--gap-sm);
  --Tooltip--attr-lineHeigt: var(--lineHeightBase);
  --Tooltip--attr-paddingX: 0.625rem;
  --Tooltip--attr-paddingY: 0.125rem;
  --Tooltip--attr-transition: all var(--animation-duration) ease-in-out;
  --Tooltip-arrow-color: var(--Tooltip-bg);
  --Tooltip-arrow-color--dark: rgba(7, 12, 20, 0.85);
  --Tooltip-arrow-height: 0.25rem;
  --Tooltip-arrow-outerColor: #d1d5db;
  --Tooltip-arrow-width: 0.5rem;
  --Tooltip-bg: var(--background);
  --Tooltip-bg--dark: rgba(7, 12, 20, 0.85);
  --Tooltip-body-color: var(--text-color);
  --Tooltip-body-color--dark: #fff;
  --Tooltip-body-paddingX: var(--gap-base);
  --Tooltip-body-paddingY: var(--gap-sm);
  --Tooltip-borderColor: var(--borderColor);
  --Tooltip-borderRadius: var(--borderRadiusLg);
  --Tooltip-borderWidth: var(--borderWidth);
  --Tooltip-boxShadow: var(--boxTooltipShadow);
  --Tooltip-boxShadow--dark: 0 2px 8px 0 rgba(7, 12, 20, 0.12);
  --Tooltip-fontSize: var(--fontSizeSm);
  --Tooltip-fontWeight: var(--fonts-weight-5);
  --Tooltip-maxWidth: 15rem;
  --Tooltip-minWidth: auto;
  --Tooltip-title-fontWeight: bold;
  --Tooltip-title-bg: rgb(247.35, 247.35, 247.35);
  --Tooltip-title-borderBottom-color: rgb(234.6, 234.6, 234.6);
  --Tooltip-title-color: var(--text--loud-color);
  --Tooltip-title-color--dark: #fff;
  --Tooltip-title-paddingX: var(--gap-base);
  --Tooltip-title-paddingY: var(--gap-sm);
  --Transfer-title-bg: var(--colors-neutral-fill-10);
  --Transfer-selection-maxHeight: 21.875rem;
  --TransferSelect--table-heading-bg: var(--colors-neutral-fill-11);
  --TransferSelect--normal-heading-bg: var(--colors-neutral-fill-10);
  --TransferSelect-heading-borderBottom: var(--borders-width-1);
  --TabsTransfer-title-bg: var(--colors-neutral-fill-10);
  --TabsTransfer-border-color: var(--colors-neutral-fill-8);
  --AnchorNav-links-container-borderRight: 0.125rem solid #d3dae0;
  --AnchorNav-onActive-borderWidth: 0 0 0 0.125rem;
  --ColumnToggler-backgroundColor: var(--colors-neutral-fill-11);
  --ColumnToggler-borderRadius: 0.25rem;
  --ColumnToggler-lineHeight: 1.5rem;
  --ColumnToggler-title-fontColor: #080e1a;
  --ColumnToggler-fontColor: #151a26;
  --ColumnToggler-item-backgroundColor: #f6f7f8;
  --ColumnToggler-item-backgroundColor-onHover: rgba(36, 104, 242, 0.1);
  --ColumnToggler-item-paddingX: 0.5rem;
  --ColumnToggler-item-paddingY: 0.25rem;
  --ColumnToggler-item-margin: 0.25rem;
  --ColumnToggler-item-dragBar-color: #d8d8d8;
  --InputFormula-header-bgColor: var(--colors-neutral-fill-10);
  --InputFormula-icon-size: 1.125rem;
  --InputFormula-icon-color-onActive: var(--colors-brand-5);
  --InputFormula-code-bgColor: var(--colors-neutral-fill-10);
  --InputFormula-input-schema-height: var(--sizes-base-12);
  --UserSelect--post-bg: var(--colors-brand-6);
  --UserSelect--department-bg: #ffab52;
  --UserSelect--role-bg: #0bc286;
  --UserSelect--border-color: #f7f7f9;
  --UserSelect--content-bg: #f5f7f8;
  --UserSelect--bread-color: #5e626a;
  --Cascader-border-color: #f7f7f9;
  --Cascader-border-active-bg-color: #f7f7f9;
  --Cascader-option-disable-color: #b8babf;
  --Tag-content-fontSize: var(--fontSizeSm);
  --Tag-height: 1.5rem;
  --Tag-borderRadius: 0.125rem;
  --Tag-fontColor: #151a26;
  --Tag-default-color: #f2f2f4;
  --Tag-inactive-color: #b8babf;
  --Tag-active-color: var(--colors-brand-5);
  --Tag-processing-color: var(--colors-brand-6);
  --Tag-success-color: var(--colors-success-5);
  --Tag-error-color: var(--colors-error-5);
  --Tag-warning-color: var(--colors-warning-5);
  --Tag-checkable-bgColor: #f2f2f4;
  --Tag-checkable-bgColor-onDisable: #e6e6e8;
  --Tag-checkable-bgColor-onHover: #e6e6e8;
  --Tag-checkable-bgColor-onActive: #e6e6e8;
  --Tag-checkable-bgColor-onHover-onChecked: var(--colors-link-6);
  --Tag-checkable-bgColor-onActive-onChecked: var(--colors-link-4);
  --Tag-checkable-bgColor-onChecked: var(--colors-brand-5);
  --Tag-checkable-fontColor-onDisable: #b4b6ba;
  --menu-background: #fff;
  --menu-box-shadow: 0 2px 8px 0 rgba(7, 12, 20, 0.12);
  --menu-border-radius: 4px;
  --menu-font-color: #151b26;
  --menu-font-family: PingFangSC-Regular;
  --menu-border-color: #e8e9eb;
  --menu-active-color: var(--colors-brand-5);
  --menu-hover-bg-color: #e6f0ff;
  --menu-disabled-color: #b8babf;
  --default-icon-color: #84868c;
  --default-padding: 4px 12px;
  --menu-min-width: 150px;
  --menu-sub-min-width: 100px;
  --SearchBox-width: 9.375rem;
  --SearchBox-history-dropdown-maxWidth: calc(var(--SearchBox-width) * 2);
  --SearchBox-history-dropdown-maxHeight: 12.5rem;
  --SearchBox-hover-color: var(--colors-brand-5);
  --SearchBox-focus-color: var(--colors-brand-5);
  --SearchBox-search-icon-color: var(--colors-neutral-text-5);
  --SearchBox-enhonce-icon-color: var(--button-primary-default-font-color);
  --SearchBox-clearable-icon-color: var(--colors-neutral-text-7);
  --SearchBox-clearable-icon-size: var(--sizes-size-9);
  --SearchBox-height: var(--sizes-base-15);
  --SearchBox-disabled-color: var(--colors-neutral-text-10);
  --SearchBox-enhonce-disabled-color: var(--colors-neutral-text-9);
  --SearchBox-enhonce-disabled-search-color: var(--colors-neutral-text-6);
  --SearchBox-enhonce-clearable-gap: var(--borders-radius-4);
  --SearchBox-search-btn-color--disabled: var(--colors-neutral-fill-6);
  --IconSelect-searchBox-width: 15.375rem;
  --IconSelect-type-item-height: 3rem;
  --IconSelect-dialog-height: 50vh;
  --IconSelect-base-margin: var(--gap-base);
  --IconSelect-xs-margin: var(--gap-xs);
  --IconSelect-sm-padding: 0.375rem;
  --IconSelect-base-border-radius: var(--borderRadiusMd);
  --IconSelect-border-color: var(--menu-border-color);
  --IconSelect-preview-icon-size: 1rem;
  --IconSelect-list-icon-size: 1.5rem;
  --IconSelect-type-font-size: var(--fontSizeSm);
  --IconSelect-active-badge-color: var(--colors-neutral-fill-11);
  --IconSelect-active-bg-color: var(--colors-brand-5);
  --IconSelect-icon-name-color: var(--colors-neutral-text-2);
  --IconSelect-icon-id-color: var(--colors-neutral-text-5);
  --IconSelect-icon-placeholder-color: var(--colors-neutral-text-6);
  --IconSelect-type-width: 5.5rem;
  --IconSelect-type-li-height: 2rem;
  --IconSelect-type-li-padding: var(--gap-md);
}
@media (max-width: 767px) {
  :root {
    --fontSizeBase: var(--fontSizeLg);
  }
  :root,
  .AMISCSSWrapper {
    --Page-body-padding: var(--gap-md);
  }
}
:root,
.AMISCSSWrapper {
  --button-default-default-top-border-color: var(--colors-neutral-line-8);
  --button-default-default-top-border-style: var(--borders-style-2);
  --button-default-default-top-border-width: var(--borders-width-2);
  --button-default-default-left-border-color: var(--colors-neutral-line-8);
  --button-default-default-left-border-style: var(--borders-style-2);
  --button-default-default-left-border-width: var(--borders-width-2);
  --button-default-default-right-border-color: var(--colors-neutral-line-8);
  --button-default-default-right-border-style: var(--borders-style-2);
  --button-default-default-right-border-width: var(--borders-width-2);
  --button-default-default-bottom-border-color: var(--colors-neutral-line-8);
  --button-default-default-bottom-border-style: var(--borders-style-2);
  --button-default-default-bottom-border-width: var(--borders-width-2);
  --button-default-default-shadow: var(--shadows-shadow-none);
  --button-default-default-bg-color: var(--colors-neutral-fill-11);
  --button-default-default-font-color: var(--colors-neutral-text-2);
  --button-default-hover-top-border-color: var(--colors-brand-5);
  --button-default-hover-top-border-style: var(--borders-style-2);
  --button-default-hover-top-border-width: var(--borders-width-2);
  --button-default-hover-left-border-color: var(--colors-brand-5);
  --button-default-hover-left-border-style: var(--borders-style-2);
  --button-default-hover-left-border-width: var(--borders-width-2);
  --button-default-hover-right-border-color: var(--colors-brand-5);
  --button-default-hover-right-border-style: var(--borders-style-2);
  --button-default-hover-right-border-width: var(--borders-width-2);
  --button-default-hover-bottom-border-color: var(--colors-brand-5);
  --button-default-hover-bottom-border-style: var(--borders-style-2);
  --button-default-hover-bottom-border-width: var(--borders-width-2);
  --button-default-hover-shadow: var(--shadows-shadow-none);
  --button-default-hover-bg-color: var(--colors-neutral-fill-11);
  --button-default-hover-font-color: var(--colors-brand-5);
  --button-default-active-top-border-color: var(--colors-brand-4);
  --button-default-active-top-border-style: var(--borders-style-2);
  --button-default-active-top-border-width: var(--borders-width-2);
  --button-default-active-left-border-color: var(--colors-brand-4);
  --button-default-active-left-border-style: var(--borders-style-2);
  --button-default-active-left-border-width: var(--borders-width-2);
  --button-default-active-right-border-color: var(--colors-brand-4);
  --button-default-active-right-border-style: var(--borders-style-2);
  --button-default-active-right-border-width: var(--borders-width-2);
  --button-default-active-bottom-border-color: var(--colors-brand-4);
  --button-default-active-bottom-border-style: var(--borders-style-2);
  --button-default-active-bottom-border-width: var(--borders-width-2);
  --button-default-active-shadow: var(--shadows-shadow-none);
  --button-default-active-bg-color: var(--colors-neutral-fill-11);
  --button-default-active-font-color: var(--colors-brand-4);
  --button-default-disabled-top-border-color: var(--colors-neutral-line-10);
  --button-default-disabled-top-border-style: var(--borders-style-2);
  --button-default-disabled-top-border-width: var(--borders-width-2);
  --button-default-disabled-left-border-color: var(--colors-neutral-line-10);
  --button-default-disabled-left-border-style: var(--borders-style-2);
  --button-default-disabled-left-border-width: var(--borders-width-2);
  --button-default-disabled-right-border-color: var(--colors-neutral-line-10);
  --button-default-disabled-right-border-style: var(--borders-style-2);
  --button-default-disabled-right-border-width: var(--borders-width-2);
  --button-default-disabled-bottom-border-color: var(--colors-neutral-line-10);
  --button-default-disabled-bottom-border-style: var(--borders-style-2);
  --button-default-disabled-bottom-border-width: var(--borders-width-2);
  --button-default-disabled-shadow: var(--shadows-shadow-none);
  --button-default-disabled-bg-color: var(--colors-neutral-fill-10);
  --button-default-disabled-font-color: var(--colors-neutral-text-6);
  --button-primary-default-top-border-color: var(--colors-brand-5);
  --button-primary-default-top-border-style: var(--borders-style-2);
  --button-primary-default-top-border-width: var(--borders-width-2);
  --button-primary-default-left-border-color: var(--colors-brand-5);
  --button-primary-default-left-border-style: var(--borders-style-2);
  --button-primary-default-left-border-width: var(--borders-width-2);
  --button-primary-default-right-border-color: var(--colors-brand-5);
  --button-primary-default-right-border-style: var(--borders-style-2);
  --button-primary-default-right-border-width: var(--borders-width-2);
  --button-primary-default-bottom-border-color: var(--colors-brand-5);
  --button-primary-default-bottom-border-style: var(--borders-style-2);
  --button-primary-default-bottom-border-width: var(--borders-width-2);
  --button-primary-default-shadow: var(--shadows-shadow-none);
  --button-primary-default-bg-color: var(--colors-brand-5);
  --button-primary-default-font-color: var(--colors-neutral-text-11);
  --button-primary-hover-top-border-color: var(--colors-brand-6);
  --button-primary-hover-top-border-style: var(--borders-style-2);
  --button-primary-hover-top-border-width: var(--borders-width-2);
  --button-primary-hover-left-border-color: var(--colors-brand-6);
  --button-primary-hover-left-border-style: var(--borders-style-2);
  --button-primary-hover-left-border-width: var(--borders-width-2);
  --button-primary-hover-right-border-color: var(--colors-brand-6);
  --button-primary-hover-right-border-style: var(--borders-style-2);
  --button-primary-hover-right-border-width: var(--borders-width-2);
  --button-primary-hover-bottom-border-color: var(--colors-brand-6);
  --button-primary-hover-bottom-border-style: var(--borders-style-2);
  --button-primary-hover-bottom-border-width: var(--borders-width-2);
  --button-primary-hover-shadow: var(--shadows-shadow-none);
  --button-primary-hover-bg-color: var(--colors-brand-6);
  --button-primary-hover-font-color: var(--colors-neutral-text-11);
  --button-primary-active-top-border-color: var(--colors-brand-4);
  --button-primary-active-top-border-style: var(--borders-style-2);
  --button-primary-active-top-border-width: var(--borders-width-2);
  --button-primary-active-left-border-color: var(--colors-brand-4);
  --button-primary-active-left-border-style: var(--borders-style-2);
  --button-primary-active-left-border-width: var(--borders-width-2);
  --button-primary-active-right-border-color: var(--colors-brand-4);
  --button-primary-active-right-border-style: var(--borders-style-2);
  --button-primary-active-right-border-width: var(--borders-width-2);
  --button-primary-active-bottom-border-color: var(--colors-brand-4);
  --button-primary-active-bottom-border-style: var(--borders-style-2);
  --button-primary-active-bottom-border-width: var(--borders-width-2);
  --button-primary-active-shadow: var(--shadows-shadow-none);
  --button-primary-active-bg-color: var(--colors-brand-4);
  --button-primary-active-font-color: var(--colors-neutral-text-11);
  --button-primary-disabled-top-border-color: var(--colors-neutral-line-10);
  --button-primary-disabled-top-border-style: var(--borders-style-2);
  --button-primary-disabled-top-border-width: var(--borders-width-2);
  --button-primary-disabled-left-border-color: var(--colors-neutral-line-10);
  --button-primary-disabled-left-border-style: var(--borders-style-2);
  --button-primary-disabled-left-border-width: var(--borders-width-2);
  --button-primary-disabled-right-border-color: var(--colors-neutral-line-10);
  --button-primary-disabled-right-border-style: var(--borders-style-2);
  --button-primary-disabled-right-border-width: var(--borders-width-2);
  --button-primary-disabled-bottom-border-color: var(--colors-neutral-line-10);
  --button-primary-disabled-bottom-border-style: var(--borders-style-2);
  --button-primary-disabled-bottom-border-width: var(--borders-width-2);
  --button-primary-disabled-shadow: var(--shadows-shadow-none);
  --button-primary-disabled-bg-color: var(--colors-neutral-fill-10);
  --button-primary-disabled-font-color: var(--colors-neutral-text-6);
  --button-secondary-default-top-border-color: var(--colors-neutral-line-6);
  --button-secondary-default-top-border-style: var(--borders-style-2);
  --button-secondary-default-top-border-width: var(--borders-width-2);
  --button-secondary-default-left-border-color: var(--colors-neutral-line-6);
  --button-secondary-default-left-border-style: var(--borders-style-2);
  --button-secondary-default-left-border-width: var(--borders-width-2);
  --button-secondary-default-right-border-color: var(--colors-neutral-line-6);
  --button-secondary-default-right-border-style: var(--borders-style-2);
  --button-secondary-default-right-border-width: var(--borders-width-2);
  --button-secondary-default-bottom-border-color: var(--colors-neutral-line-6);
  --button-secondary-default-bottom-border-style: var(--borders-style-2);
  --button-secondary-default-bottom-border-width: var(--borders-width-2);
  --button-secondary-default-shadow: var(--shadows-shadow-none);
  --button-secondary-default-bg-color: var(--colors-neutral-fill-6);
  --button-secondary-default-font-color: var(--colors-neutral-text-11);
  --button-secondary-hover-top-border-color: var(--colors-neutral-line-5);
  --button-secondary-hover-top-border-style: var(--borders-style-2);
  --button-secondary-hover-top-border-width: var(--borders-width-2);
  --button-secondary-hover-left-border-color: var(--colors-neutral-line-5);
  --button-secondary-hover-left-border-style: var(--borders-style-2);
  --button-secondary-hover-left-border-width: var(--borders-width-2);
  --button-secondary-hover-right-border-color: var(--colors-neutral-line-5);
  --button-secondary-hover-right-border-style: var(--borders-style-2);
  --button-secondary-hover-right-border-width: var(--borders-width-2);
  --button-secondary-hover-bottom-border-color: var(--colors-neutral-line-5);
  --button-secondary-hover-bottom-border-style: var(--borders-style-2);
  --button-secondary-hover-bottom-border-width: var(--borders-width-2);
  --button-secondary-hover-shadow: var(--shadows-shadow-none);
  --button-secondary-hover-bg-color: var(--colors-neutral-fill-5);
  --button-secondary-hover-font-color: var(--colors-neutral-text-11);
  --button-secondary-active-top-border-color: var(--colors-neutral-line-4);
  --button-secondary-active-top-border-style: var(--borders-style-2);
  --button-secondary-active-top-border-width: var(--borders-width-2);
  --button-secondary-active-left-border-color: var(--colors-neutral-line-4);
  --button-secondary-active-left-border-style: var(--borders-style-2);
  --button-secondary-active-left-border-width: var(--borders-width-2);
  --button-secondary-active-right-border-color: var(--colors-neutral-line-4);
  --button-secondary-active-right-border-style: var(--borders-style-2);
  --button-secondary-active-right-border-width: var(--borders-width-2);
  --button-secondary-active-bottom-border-color: var(--colors-neutral-line-4);
  --button-secondary-active-bottom-border-style: var(--borders-style-2);
  --button-secondary-active-bottom-border-width: var(--borders-width-2);
  --button-secondary-active-shadow: var(--shadows-shadow-none);
  --button-secondary-active-bg-color: var(--colors-neutral-fill-4);
  --button-secondary-active-font-color: var(--colors-neutral-text-11);
  --button-secondary-disabled-top-border-color: var(--colors-neutral-line-10);
  --button-secondary-disabled-top-border-style: var(--borders-style-2);
  --button-secondary-disabled-top-border-width: var(--borders-width-2);
  --button-secondary-disabled-left-border-color: var(--colors-neutral-line-10);
  --button-secondary-disabled-left-border-style: var(--borders-style-2);
  --button-secondary-disabled-left-border-width: var(--borders-width-2);
  --button-secondary-disabled-right-border-color: var(--colors-neutral-line-10);
  --button-secondary-disabled-right-border-style: var(--borders-style-2);
  --button-secondary-disabled-right-border-width: var(--borders-width-2);
  --button-secondary-disabled-bottom-border-color: var(--colors-neutral-line-10);
  --button-secondary-disabled-bottom-border-style: var(--borders-style-2);
  --button-secondary-disabled-bottom-border-width: var(--borders-width-2);
  --button-secondary-disabled-shadow: var(--shadows-shadow-none);
  --button-secondary-disabled-bg-color: var(--colors-neutral-fill-10);
  --button-secondary-disabled-font-color: var(--colors-neutral-text-6);
  --button-enhance-default-top-border-color: var(--colors-brand-5);
  --button-enhance-default-top-border-style: var(--borders-style-2);
  --button-enhance-default-top-border-width: var(--borders-width-2);
  --button-enhance-default-left-border-color: var(--colors-brand-5);
  --button-enhance-default-left-border-style: var(--borders-style-2);
  --button-enhance-default-left-border-width: var(--borders-width-2);
  --button-enhance-default-right-border-color: var(--colors-brand-5);
  --button-enhance-default-right-border-style: var(--borders-style-2);
  --button-enhance-default-right-border-width: var(--borders-width-2);
  --button-enhance-default-bottom-border-color: var(--colors-brand-5);
  --button-enhance-default-bottom-border-style: var(--borders-style-2);
  --button-enhance-default-bottom-border-width: var(--borders-width-2);
  --button-enhance-default-shadow: var(--shadows-shadow-none);
  --button-enhance-default-bg-color: var(--colors-neutral-fill-11);
  --button-enhance-default-font-color: var(--colors-brand-5);
  --button-enhance-hover-top-border-color: var(--colors-brand-6);
  --button-enhance-hover-top-border-style: var(--borders-style-2);
  --button-enhance-hover-top-border-width: var(--borders-width-2);
  --button-enhance-hover-left-border-color: var(--colors-brand-6);
  --button-enhance-hover-left-border-style: var(--borders-style-2);
  --button-enhance-hover-left-border-width: var(--borders-width-2);
  --button-enhance-hover-right-border-color: var(--colors-brand-6);
  --button-enhance-hover-right-border-style: var(--borders-style-2);
  --button-enhance-hover-right-border-width: var(--borders-width-2);
  --button-enhance-hover-bottom-border-color: var(--colors-brand-6);
  --button-enhance-hover-bottom-border-style: var(--borders-style-2);
  --button-enhance-hover-bottom-border-width: var(--borders-width-2);
  --button-enhance-hover-shadow: var(--shadows-shadow-none);
  --button-enhance-hover-bg-color: var(--colors-neutral-fill-11);
  --button-enhance-hover-font-color: var(--colors-brand-6);
  --button-enhance-active-top-border-color: var(--colors-brand-4);
  --button-enhance-active-top-border-style: var(--borders-style-2);
  --button-enhance-active-top-border-width: var(--borders-width-2);
  --button-enhance-active-left-border-color: var(--colors-brand-4);
  --button-enhance-active-left-border-style: var(--borders-style-2);
  --button-enhance-active-left-border-width: var(--borders-width-2);
  --button-enhance-active-right-border-color: var(--colors-brand-4);
  --button-enhance-active-right-border-style: var(--borders-style-2);
  --button-enhance-active-right-border-width: var(--borders-width-2);
  --button-enhance-active-bottom-border-color: var(--colors-brand-4);
  --button-enhance-active-bottom-border-style: var(--borders-style-2);
  --button-enhance-active-bottom-border-width: var(--borders-width-2);
  --button-enhance-active-shadow: var(--shadows-shadow-none);
  --button-enhance-active-bg-color: var(--colors-neutral-fill-11);
  --button-enhance-active-font-color: var(--colors-brand-4);
  --button-enhance-disabled-top-border-color: var(--colors-neutral-line-10);
  --button-enhance-disabled-top-border-style: var(--borders-style-2);
  --button-enhance-disabled-top-border-width: var(--borders-width-2);
  --button-enhance-disabled-left-border-color: var(--colors-neutral-line-10);
  --button-enhance-disabled-left-border-style: var(--borders-style-2);
  --button-enhance-disabled-left-border-width: var(--borders-width-2);
  --button-enhance-disabled-right-border-color: var(--colors-neutral-line-10);
  --button-enhance-disabled-right-border-style: var(--borders-style-2);
  --button-enhance-disabled-right-border-width: var(--borders-width-2);
  --button-enhance-disabled-bottom-border-color: var(--colors-neutral-line-10);
  --button-enhance-disabled-bottom-border-style: var(--borders-style-2);
  --button-enhance-disabled-bottom-border-width: var(--borders-width-2);
  --button-enhance-disabled-shadow: var(--shadows-shadow-none);
  --button-enhance-disabled-bg-color: var(--colors-neutral-fill-10);
  --button-enhance-disabled-font-color: var(--colors-neutral-text-6);
  --button-info-default-top-border-color: var(--colors-info-5);
  --button-info-default-top-border-style: var(--borders-style-2);
  --button-info-default-top-border-width: var(--borders-width-2);
  --button-info-default-left-border-color: var(--colors-info-5);
  --button-info-default-left-border-style: var(--borders-style-2);
  --button-info-default-left-border-width: var(--borders-width-2);
  --button-info-default-right-border-color: var(--colors-info-5);
  --button-info-default-right-border-style: var(--borders-style-2);
  --button-info-default-right-border-width: var(--borders-width-2);
  --button-info-default-bottom-border-color: var(--colors-info-5);
  --button-info-default-bottom-border-style: var(--borders-style-2);
  --button-info-default-bottom-border-width: var(--borders-width-2);
  --button-info-default-shadow: var(--shadows-shadow-none);
  --button-info-default-bg-color: var(--colors-info-5);
  --button-info-default-font-color: var(--colors-neutral-text-11);
  --button-info-hover-top-border-color: var(--colors-info-6);
  --button-info-hover-top-border-style: var(--borders-style-2);
  --button-info-hover-top-border-width: var(--borders-width-2);
  --button-info-hover-left-border-color: var(--colors-info-6);
  --button-info-hover-left-border-style: var(--borders-style-2);
  --button-info-hover-left-border-width: var(--borders-width-2);
  --button-info-hover-right-border-color: var(--colors-info-6);
  --button-info-hover-right-border-style: var(--borders-style-2);
  --button-info-hover-right-border-width: var(--borders-width-2);
  --button-info-hover-bottom-border-color: var(--colors-info-6);
  --button-info-hover-bottom-border-style: var(--borders-style-2);
  --button-info-hover-bottom-border-width: var(--borders-width-2);
  --button-info-hover-shadow: var(--shadows-shadow-none);
  --button-info-hover-bg-color: var(--colors-info-6);
  --button-info-hover-font-color: var(--colors-neutral-text-11);
  --button-info-active-top-border-color: var(--colors-info-4);
  --button-info-active-top-border-style: var(--borders-style-2);
  --button-info-active-top-border-width: var(--borders-width-2);
  --button-info-active-left-border-color: var(--colors-info-4);
  --button-info-active-left-border-style: var(--borders-style-2);
  --button-info-active-left-border-width: var(--borders-width-2);
  --button-info-active-right-border-color: var(--colors-info-4);
  --button-info-active-right-border-style: var(--borders-style-2);
  --button-info-active-right-border-width: var(--borders-width-2);
  --button-info-active-bottom-border-color: var(--colors-info-4);
  --button-info-active-bottom-border-style: var(--borders-style-2);
  --button-info-active-bottom-border-width: var(--borders-width-2);
  --button-info-active-shadow: var(--shadows-shadow-none);
  --button-info-active-bg-color: var(--colors-info-4);
  --button-info-active-font-color: var(--colors-neutral-text-11);
  --button-info-disabled-top-border-color: var(--colors-neutral-line-10);
  --button-info-disabled-top-border-style: var(--borders-style-2);
  --button-info-disabled-top-border-width: var(--borders-width-2);
  --button-info-disabled-left-border-color: var(--colors-neutral-line-10);
  --button-info-disabled-left-border-style: var(--borders-style-2);
  --button-info-disabled-left-border-width: var(--borders-width-2);
  --button-info-disabled-right-border-color: var(--colors-neutral-line-10);
  --button-info-disabled-right-border-style: var(--borders-style-2);
  --button-info-disabled-right-border-width: var(--borders-width-2);
  --button-info-disabled-bottom-border-color: var(--colors-neutral-line-10);
  --button-info-disabled-bottom-border-style: var(--borders-style-2);
  --button-info-disabled-bottom-border-width: var(--borders-width-2);
  --button-info-disabled-shadow: var(--shadows-shadow-none);
  --button-info-disabled-bg-color: var(--colors-neutral-fill-10);
  --button-info-disabled-font-color: var(--colors-neutral-text-6);
  --button-success-default-top-border-color: var(--colors-success-5);
  --button-success-default-top-border-style: var(--borders-style-2);
  --button-success-default-top-border-width: var(--borders-width-2);
  --button-success-default-left-border-color: var(--colors-success-5);
  --button-success-default-left-border-style: var(--borders-style-2);
  --button-success-default-left-border-width: var(--borders-width-2);
  --button-success-default-right-border-color: var(--colors-success-5);
  --button-success-default-right-border-style: var(--borders-style-2);
  --button-success-default-right-border-width: var(--borders-width-2);
  --button-success-default-bottom-border-color: var(--colors-success-5);
  --button-success-default-bottom-border-style: var(--borders-style-2);
  --button-success-default-bottom-border-width: var(--borders-width-2);
  --button-success-default-shadow: var(--shadows-shadow-none);
  --button-success-default-bg-color: var(--colors-success-5);
  --button-success-default-font-color: var(--colors-neutral-text-11);
  --button-success-hover-top-border-color: var(--colors-success-6);
  --button-success-hover-top-border-style: var(--borders-style-2);
  --button-success-hover-top-border-width: var(--borders-width-2);
  --button-success-hover-left-border-color: var(--colors-success-6);
  --button-success-hover-left-border-style: var(--borders-style-2);
  --button-success-hover-left-border-width: var(--borders-width-2);
  --button-success-hover-right-border-color: var(--colors-success-6);
  --button-success-hover-right-border-style: var(--borders-style-2);
  --button-success-hover-right-border-width: var(--borders-width-2);
  --button-success-hover-bottom-border-color: var(--colors-success-6);
  --button-success-hover-bottom-border-style: var(--borders-style-2);
  --button-success-hover-bottom-border-width: var(--borders-width-2);
  --button-success-hover-shadow: var(--shadows-shadow-none);
  --button-success-hover-bg-color: var(--colors-success-6);
  --button-success-hover-font-color: var(--colors-neutral-text-11);
  --button-success-active-top-border-color: var(--colors-success-4);
  --button-success-active-top-border-style: var(--borders-style-2);
  --button-success-active-top-border-width: var(--borders-width-2);
  --button-success-active-left-border-color: var(--colors-success-4);
  --button-success-active-left-border-style: var(--borders-style-2);
  --button-success-active-left-border-width: var(--borders-width-2);
  --button-success-active-right-border-color: var(--colors-success-4);
  --button-success-active-right-border-style: var(--borders-style-2);
  --button-success-active-right-border-width: var(--borders-width-2);
  --button-success-active-bottom-border-color: var(--colors-success-4);
  --button-success-active-bottom-border-style: var(--borders-style-2);
  --button-success-active-bottom-border-width: var(--borders-width-2);
  --button-success-active-shadow: var(--shadows-shadow-none);
  --button-success-active-bg-color: var(--colors-success-4);
  --button-success-active-font-color: var(--colors-neutral-text-11);
  --button-success-disabled-top-border-color: var(--colors-neutral-line-10);
  --button-success-disabled-top-border-style: var(--borders-style-2);
  --button-success-disabled-top-border-width: var(--borders-width-2);
  --button-success-disabled-left-border-color: var(--colors-neutral-line-10);
  --button-success-disabled-left-border-style: var(--borders-style-2);
  --button-success-disabled-left-border-width: var(--borders-width-2);
  --button-success-disabled-right-border-color: var(--colors-neutral-line-10);
  --button-success-disabled-right-border-style: var(--borders-style-2);
  --button-success-disabled-right-border-width: var(--borders-width-2);
  --button-success-disabled-bottom-border-color: var(--colors-neutral-line-10);
  --button-success-disabled-bottom-border-style: var(--borders-style-2);
  --button-success-disabled-bottom-border-width: var(--borders-width-2);
  --button-success-disabled-shadow: var(--shadows-shadow-none);
  --button-success-disabled-bg-color: var(--colors-neutral-fill-10);
  --button-success-disabled-font-color: var(--colors-neutral-text-6);
  --button-warning-default-top-border-color: var(--colors-warning-5);
  --button-warning-default-top-border-style: var(--borders-style-2);
  --button-warning-default-top-border-width: var(--borders-width-2);
  --button-warning-default-left-border-color: var(--colors-warning-5);
  --button-warning-default-left-border-style: var(--borders-style-2);
  --button-warning-default-left-border-width: var(--borders-width-2);
  --button-warning-default-right-border-color: var(--colors-warning-5);
  --button-warning-default-right-border-style: var(--borders-style-2);
  --button-warning-default-right-border-width: var(--borders-width-2);
  --button-warning-default-bottom-border-color: var(--colors-warning-5);
  --button-warning-default-bottom-border-style: var(--borders-style-2);
  --button-warning-default-bottom-border-width: var(--borders-width-2);
  --button-warning-default-shadow: var(--shadows-shadow-none);
  --button-warning-default-bg-color: var(--colors-warning-5);
  --button-warning-default-font-color: var(--colors-neutral-text-11);
  --button-warning-hover-top-border-color: var(--colors-warning-6);
  --button-warning-hover-top-border-style: var(--borders-style-2);
  --button-warning-hover-top-border-width: var(--borders-width-2);
  --button-warning-hover-left-border-color: var(--colors-warning-6);
  --button-warning-hover-left-border-style: var(--borders-style-2);
  --button-warning-hover-left-border-width: var(--borders-width-2);
  --button-warning-hover-right-border-color: var(--colors-warning-6);
  --button-warning-hover-right-border-style: var(--borders-style-2);
  --button-warning-hover-right-border-width: var(--borders-width-2);
  --button-warning-hover-bottom-border-color: var(--colors-warning-6);
  --button-warning-hover-bottom-border-style: var(--borders-style-2);
  --button-warning-hover-bottom-border-width: var(--borders-width-2);
  --button-warning-hover-shadow: var(--shadows-shadow-none);
  --button-warning-hover-bg-color: var(--colors-warning-6);
  --button-warning-hover-font-color: var(--colors-neutral-text-11);
  --button-warning-active-top-border-color: var(--colors-warning-4);
  --button-warning-active-top-border-style: var(--borders-style-2);
  --button-warning-active-top-border-width: var(--borders-width-2);
  --button-warning-active-left-border-color: var(--colors-warning-4);
  --button-warning-active-left-border-style: var(--borders-style-2);
  --button-warning-active-left-border-width: var(--borders-width-2);
  --button-warning-active-right-border-color: var(--colors-warning-4);
  --button-warning-active-right-border-style: var(--borders-style-2);
  --button-warning-active-right-border-width: var(--borders-width-2);
  --button-warning-active-bottom-border-color: var(--colors-warning-4);
  --button-warning-active-bottom-border-style: var(--borders-style-2);
  --button-warning-active-bottom-border-width: var(--borders-width-2);
  --button-warning-active-shadow: var(--shadows-shadow-none);
  --button-warning-active-bg-color: var(--colors-warning-4);
  --button-warning-active-font-color: var(--colors-neutral-text-11);
  --button-warning-disabled-top-border-color: var(--colors-neutral-line-10);
  --button-warning-disabled-top-border-style: var(--borders-style-2);
  --button-warning-disabled-top-border-width: var(--borders-width-2);
  --button-warning-disabled-left-border-color: var(--colors-neutral-line-10);
  --button-warning-disabled-left-border-style: var(--borders-style-2);
  --button-warning-disabled-left-border-width: var(--borders-width-2);
  --button-warning-disabled-right-border-color: var(--colors-neutral-line-10);
  --button-warning-disabled-right-border-style: var(--borders-style-2);
  --button-warning-disabled-right-border-width: var(--borders-width-2);
  --button-warning-disabled-bottom-border-color: var(--colors-neutral-line-10);
  --button-warning-disabled-bottom-border-style: var(--borders-style-2);
  --button-warning-disabled-bottom-border-width: var(--borders-width-2);
  --button-warning-disabled-shadow: var(--shadows-shadow-none);
  --button-warning-disabled-bg-color: var(--colors-neutral-fill-10);
  --button-warning-disabled-font-color: var(--colors-neutral-text-6);
  --button-danger-default-top-border-color: var(--colors-error-5);
  --button-danger-default-top-border-style: var(--borders-style-1);
  --button-danger-default-top-border-width: var(--borders-width-1);
  --button-danger-default-left-border-color: var(--colors-error-5);
  --button-danger-default-left-border-style: var(--borders-style-1);
  --button-danger-default-left-border-width: var(--borders-width-1);
  --button-danger-default-right-border-color: var(--colors-error-5);
  --button-danger-default-right-border-style: var(--borders-style-1);
  --button-danger-default-right-border-width: var(--borders-width-1);
  --button-danger-default-bottom-border-color: var(--colors-error-5);
  --button-danger-default-bottom-border-style: var(--borders-style-1);
  --button-danger-default-bottom-border-width: var(--borders-width-1);
  --button-danger-default-shadow: var(--shadows-shadow-none);
  --button-danger-default-bg-color: var(--colors-error-5);
  --button-danger-default-font-color: var(--colors-neutral-text-11);
  --button-danger-hover-top-border-color: var(--colors-error-6);
  --button-danger-hover-top-border-style: var(--borders-style-1);
  --button-danger-hover-top-border-width: var(--borders-width-1);
  --button-danger-hover-left-border-color: var(--colors-error-6);
  --button-danger-hover-left-border-style: var(--borders-style-1);
  --button-danger-hover-left-border-width: var(--borders-width-1);
  --button-danger-hover-right-border-color: var(--colors-error-6);
  --button-danger-hover-right-border-style: var(--borders-style-1);
  --button-danger-hover-right-border-width: var(--borders-width-1);
  --button-danger-hover-bottom-border-color: var(--colors-error-6);
  --button-danger-hover-bottom-border-style: var(--borders-style-1);
  --button-danger-hover-bottom-border-width: var(--borders-width-1);
  --button-danger-hover-shadow: var(--shadows-shadow-none);
  --button-danger-hover-bg-color: var(--colors-error-6);
  --button-danger-hover-font-color: var(--colors-neutral-text-11);
  --button-danger-active-top-border-color: var(--colors-error-4);
  --button-danger-active-top-border-style: var(--borders-style-1);
  --button-danger-active-top-border-width: var(--borders-width-1);
  --button-danger-active-left-border-color: var(--colors-error-4);
  --button-danger-active-left-border-style: var(--borders-style-1);
  --button-danger-active-left-border-width: var(--borders-width-1);
  --button-danger-active-right-border-color: var(--colors-error-4);
  --button-danger-active-right-border-style: var(--borders-style-1);
  --button-danger-active-right-border-width: var(--borders-width-1);
  --button-danger-active-bottom-border-color: var(--colors-error-4);
  --button-danger-active-bottom-border-style: var(--borders-style-1);
  --button-danger-active-bottom-border-width: var(--borders-width-1);
  --button-danger-active-shadow: var(--shadows-shadow-none);
  --button-danger-active-bg-color: var(--colors-error-4);
  --button-danger-active-font-color: var(--colors-neutral-text-11);
  --button-danger-disabled-top-border-color: var(--colors-neutral-line-10);
  --button-danger-disabled-top-border-style: var(--borders-style-2);
  --button-danger-disabled-top-border-width: var(--borders-width-2);
  --button-danger-disabled-left-border-color: var(--colors-neutral-line-10);
  --button-danger-disabled-left-border-style: var(--borders-style-2);
  --button-danger-disabled-left-border-width: var(--borders-width-2);
  --button-danger-disabled-right-border-color: var(--colors-neutral-line-10);
  --button-danger-disabled-right-border-style: var(--borders-style-2);
  --button-danger-disabled-right-border-width: var(--borders-width-2);
  --button-danger-disabled-bottom-border-color: var(--colors-neutral-line-10);
  --button-danger-disabled-bottom-border-style: var(--borders-style-2);
  --button-danger-disabled-bottom-border-width: var(--borders-width-2);
  --button-danger-disabled-shadow: var(--shadows-shadow-none);
  --button-danger-disabled-bg-color: var(--colors-neutral-fill-10);
  --button-danger-disabled-font-color: var(--colors-neutral-text-6);
  --button-light-default-top-border-color: var(--colors-brand-10);
  --button-light-default-top-border-style: var(--borders-style-2);
  --button-light-default-top-border-width: var(--borders-width-2);
  --button-light-default-left-border-color: var(--colors-brand-10);
  --button-light-default-left-border-style: var(--borders-style-2);
  --button-light-default-left-border-width: var(--borders-width-2);
  --button-light-default-right-border-color: var(--colors-brand-10);
  --button-light-default-right-border-style: var(--borders-style-2);
  --button-light-default-right-border-width: var(--borders-width-2);
  --button-light-default-bottom-border-color: var(--colors-brand-10);
  --button-light-default-bottom-border-style: var(--borders-style-2);
  --button-light-default-bottom-border-width: var(--borders-width-2);
  --button-light-default-shadow: var(--shadows-shadow-none);
  --button-light-default-bg-color: var(--colors-brand-10);
  --button-light-default-font-color: var(--colors-neutral-text-2);
  --button-light-hover-top-border-color: var(--colors-brand-9);
  --button-light-hover-top-border-style: var(--borders-style-2);
  --button-light-hover-top-border-width: var(--borders-width-2);
  --button-light-hover-left-border-color: var(--colors-brand-9);
  --button-light-hover-left-border-style: var(--borders-style-2);
  --button-light-hover-left-border-width: var(--borders-width-2);
  --button-light-hover-right-border-color: var(--colors-brand-9);
  --button-light-hover-right-border-style: var(--borders-style-2);
  --button-light-hover-right-border-width: var(--borders-width-2);
  --button-light-hover-bottom-border-color: var(--colors-brand-9);
  --button-light-hover-bottom-border-style: var(--borders-style-2);
  --button-light-hover-bottom-border-width: var(--borders-width-2);
  --button-light-hover-shadow: var(--shadows-shadow-none);
  --button-light-hover-bg-color: var(--colors-brand-9);
  --button-light-hover-font-color: var(--colors-neutral-text-2);
  --button-light-active-top-border-color: var(--colors-brand-7);
  --button-light-active-top-border-style: var(--borders-style-2);
  --button-light-active-top-border-width: var(--borders-width-2);
  --button-light-active-left-border-color: var(--colors-brand-7);
  --button-light-active-left-border-style: var(--borders-style-2);
  --button-light-active-left-border-width: var(--borders-width-2);
  --button-light-active-right-border-color: var(--colors-brand-7);
  --button-light-active-right-border-style: var(--borders-style-2);
  --button-light-active-right-border-width: var(--borders-width-2);
  --button-light-active-bottom-border-color: var(--colors-brand-7);
  --button-light-active-bottom-border-style: var(--borders-style-2);
  --button-light-active-bottom-border-width: var(--borders-width-2);
  --button-light-active-shadow: var(--shadows-shadow-none);
  --button-light-active-bg-color: var(--colors-brand-7);
  --button-light-active-font-color: var(--colors-neutral-text-2);
  --button-light-disabled-top-border-color: var(--colors-neutral-line-10);
  --button-light-disabled-top-border-style: var(--borders-style-2);
  --button-light-disabled-top-border-width: var(--borders-width-2);
  --button-light-disabled-left-border-color: var(--colors-neutral-line-10);
  --button-light-disabled-left-border-style: var(--borders-style-2);
  --button-light-disabled-left-border-width: var(--borders-width-2);
  --button-light-disabled-right-border-color: var(--colors-neutral-line-10);
  --button-light-disabled-right-border-style: var(--borders-style-2);
  --button-light-disabled-right-border-width: var(--borders-width-2);
  --button-light-disabled-bottom-border-color: var(--colors-neutral-line-10);
  --button-light-disabled-bottom-border-style: var(--borders-style-2);
  --button-light-disabled-bottom-border-width: var(--borders-width-2);
  --button-light-disabled-shadow: var(--shadows-shadow-none);
  --button-light-disabled-bg-color: var(--colors-neutral-fill-10);
  --button-light-disabled-font-color: var(--colors-neutral-text-6);
  --button-dark-default-top-border-color: var(--colors-neutral-line-3);
  --button-dark-default-top-border-style: var(--borders-style-2);
  --button-dark-default-top-border-width: var(--borders-width-2);
  --button-dark-default-left-border-color: var(--colors-neutral-line-3);
  --button-dark-default-left-border-style: var(--borders-style-2);
  --button-dark-default-left-border-width: var(--borders-width-2);
  --button-dark-default-right-border-color: var(--colors-neutral-line-3);
  --button-dark-default-right-border-style: var(--borders-style-2);
  --button-dark-default-right-border-width: var(--borders-width-2);
  --button-dark-default-bottom-border-color: var(--colors-neutral-line-3);
  --button-dark-default-bottom-border-style: var(--borders-style-2);
  --button-dark-default-bottom-border-width: var(--borders-width-2);
  --button-dark-default-shadow: var(--shadows-shadow-none);
  --button-dark-default-bg-color: var(--colors-neutral-fill-3);
  --button-dark-default-font-color: var(--colors-neutral-text-11);
  --button-dark-hover-top-border-color: var(--colors-neutral-line-4);
  --button-dark-hover-top-border-style: var(--borders-style-2);
  --button-dark-hover-top-border-width: var(--borders-width-2);
  --button-dark-hover-left-border-color: var(--colors-neutral-line-4);
  --button-dark-hover-left-border-style: var(--borders-style-2);
  --button-dark-hover-left-border-width: var(--borders-width-2);
  --button-dark-hover-right-border-color: var(--colors-neutral-line-4);
  --button-dark-hover-right-border-style: var(--borders-style-2);
  --button-dark-hover-right-border-width: var(--borders-width-2);
  --button-dark-hover-bottom-border-color: var(--colors-neutral-line-4);
  --button-dark-hover-bottom-border-style: var(--borders-style-2);
  --button-dark-hover-bottom-border-width: var(--borders-width-2);
  --button-dark-hover-shadow: var(--shadows-shadow-none);
  --button-dark-hover-bg-color: var(--colors-neutral-fill-4);
  --button-dark-hover-font-color: var(--colors-neutral-text-11);
  --button-dark-active-top-border-color: var(--colors-neutral-line-5);
  --button-dark-active-top-border-style: var(--borders-style-2);
  --button-dark-active-top-border-width: var(--borders-width-2);
  --button-dark-active-left-border-color: var(--colors-neutral-line-5);
  --button-dark-active-left-border-style: var(--borders-style-2);
  --button-dark-active-left-border-width: var(--borders-width-2);
  --button-dark-active-right-border-color: var(--colors-neutral-line-5);
  --button-dark-active-right-border-style: var(--borders-style-2);
  --button-dark-active-right-border-width: var(--borders-width-2);
  --button-dark-active-bottom-border-color: var(--colors-neutral-line-5);
  --button-dark-active-bottom-border-style: var(--borders-style-2);
  --button-dark-active-bottom-border-width: var(--borders-width-2);
  --button-dark-active-shadow: var(--shadows-shadow-none);
  --button-dark-active-bg-color: var(--colors-neutral-fill-5);
  --button-dark-active-font-color: var(--colors-neutral-text-11);
  --button-dark-disabled-top-border-color: var(--colors-neutral-line-10);
  --button-dark-disabled-top-border-style: var(--borders-style-2);
  --button-dark-disabled-top-border-width: var(--borders-width-2);
  --button-dark-disabled-left-border-color: var(--colors-neutral-line-10);
  --button-dark-disabled-left-border-style: var(--borders-style-2);
  --button-dark-disabled-left-border-width: var(--borders-width-2);
  --button-dark-disabled-right-border-color: var(--colors-neutral-line-10);
  --button-dark-disabled-right-border-style: var(--borders-style-2);
  --button-dark-disabled-right-border-width: var(--borders-width-2);
  --button-dark-disabled-bottom-border-color: var(--colors-neutral-line-10);
  --button-dark-disabled-bottom-border-style: var(--borders-style-2);
  --button-dark-disabled-bottom-border-width: var(--borders-width-2);
  --button-dark-disabled-shadow: var(--shadows-shadow-none);
  --button-dark-disabled-bg-color: var(--colors-neutral-fill-10);
  --button-dark-disabled-font-color: var(--colors-neutral-text-6);
  --button-link-default-top-border-color: transparent;
  --button-link-default-top-border-style: var(--borders-style-1);
  --button-link-default-top-border-width: var(--borders-width-1);
  --button-link-default-left-border-color: transparent;
  --button-link-default-left-border-style: var(--borders-style-1);
  --button-link-default-left-border-width: var(--borders-width-1);
  --button-link-default-right-border-color: transparent;
  --button-link-default-right-border-style: var(--borders-style-1);
  --button-link-default-right-border-width: var(--borders-width-1);
  --button-link-default-bottom-border-color: transparent;
  --button-link-default-bottom-border-style: var(--borders-style-1);
  --button-link-default-bottom-border-width: var(--borders-width-1);
  --button-link-default-shadow: var(--shadows-shadow-none);
  --button-link-default-bg-color: transparent;
  --button-link-default-font-color: var(--colors-link-5);
  --button-link-hover-top-border-color: transparent;
  --button-link-hover-top-border-style: var(--borders-style-1);
  --button-link-hover-top-border-width: var(--borders-width-1);
  --button-link-hover-left-border-color: transparent;
  --button-link-hover-left-border-style: var(--borders-style-1);
  --button-link-hover-left-border-width: var(--borders-width-1);
  --button-link-hover-right-border-color: transparent;
  --button-link-hover-right-border-style: var(--borders-style-1);
  --button-link-hover-right-border-width: var(--borders-width-1);
  --button-link-hover-bottom-border-color: transparent;
  --button-link-hover-bottom-border-style: var(--borders-style-1);
  --button-link-hover-bottom-border-width: var(--borders-width-1);
  --button-link-hover-shadow: var(--shadows-shadow-none);
  --button-link-hover-bg-color: transparent;
  --button-link-hover-font-color: var(--colors-link-6);
  --button-link-active-top-border-color: transparent;
  --button-link-active-top-border-style: var(--borders-style-1);
  --button-link-active-top-border-width: var(--borders-width-1);
  --button-link-active-left-border-color: transparent;
  --button-link-active-left-border-style: var(--borders-style-1);
  --button-link-active-left-border-width: var(--borders-width-1);
  --button-link-active-right-border-color: transparent;
  --button-link-active-right-border-style: var(--borders-style-1);
  --button-link-active-right-border-width: var(--borders-width-1);
  --button-link-active-bottom-border-color: transparent;
  --button-link-active-bottom-border-style: var(--borders-style-1);
  --button-link-active-bottom-border-width: var(--borders-width-1);
  --button-link-active-shadow: var(--shadows-shadow-none);
  --button-link-active-bg-color: transparent;
  --button-link-active-font-color: var(--colors-link-4);
  --button-link-disabled-top-border-color: transparent;
  --button-link-disabled-top-border-style: var(--borders-style-1);
  --button-link-disabled-top-border-width: var(--borders-width-1);
  --button-link-disabled-left-border-color: transparent;
  --button-link-disabled-left-border-style: var(--borders-style-1);
  --button-link-disabled-left-border-width: var(--borders-width-1);
  --button-link-disabled-right-border-color: transparent;
  --button-link-disabled-right-border-style: var(--borders-style-1);
  --button-link-disabled-right-border-width: var(--borders-width-1);
  --button-link-disabled-bottom-border-color: transparent;
  --button-link-disabled-bottom-border-style: var(--borders-style-1);
  --button-link-disabled-bottom-border-width: var(--borders-width-1);
  --button-link-disabled-shadow: var(--shadows-shadow-none);
  --button-link-disabled-bg-color: transparent;
  --button-link-disabled-font-color: var(--colors-neutral-text-6);
  --button-size-default-top-left-border-radius: var(--borders-radius-3);
  --button-size-default-top-right-border-radius: var(--borders-radius-3);
  --button-size-default-bottom-left-border-radius: var(--borders-radius-3);
  --button-size-default-bottom-right-border-radius: var(--borders-radius-3);
  --button-size-default-height: var(--sizes-base-16);
  --button-size-default-fontSize: var(--fonts-size-7);
  --button-size-default-minWidth: var(--sizes-size-1);
  --button-size-default-icon-size: var(--sizes-size-8);
  --button-size-default-fontWeight: var(--fonts-weight-6);
  --button-size-default-lineHeight: var(--fonts-lineHeight-2);
  --button-size-default-icon-margin: var(--sizes-size-3);
  --button-size-default-marginTop: var(--sizes-size-0);
  --button-size-default-marginLeft: var(--sizes-size-0);
  --button-size-default-paddingTop: var(--sizes-size-3);
  --button-size-default-marginRight: var(--sizes-size-0);
  --button-size-default-paddingLeft: var(--sizes-size-7);
  --button-size-default-marginBottom: var(--sizes-size-0);
  --button-size-default-paddingRight: var(--sizes-size-7);
  --button-size-default-paddingBottom: var(--sizes-size-3);
  --button-size-xs-top-left-border-radius: var(--borders-radius-3);
  --button-size-xs-top-right-border-radius: var(--borders-radius-3);
  --button-size-xs-bottom-left-border-radius: var(--borders-radius-3);
  --button-size-xs-bottom-right-border-radius: var(--borders-radius-3);
  --button-size-xs-height: var(--sizes-base-11);
  --button-size-xs-fontSize: var(--fonts-size-8);
  --button-size-xs-minWidth: var(--sizes-size-1);
  --button-size-xs-icon-size: var(--sizes-size-8);
  --button-size-xs-fontWeight: var(--fonts-weight-6);
  --button-size-xs-lineHeight: var(--fonts-lineHeight-2);
  --button-size-xs-icon-margin: var(--sizes-size-3);
  --button-size-xs-marginTop: var(--sizes-size-0);
  --button-size-xs-marginLeft: var(--sizes-size-0);
  --button-size-xs-paddingTop: var(--sizes-size-2);
  --button-size-xs-marginRight: var(--sizes-size-0);
  --button-size-xs-paddingLeft: var(--sizes-size-3);
  --button-size-xs-marginBottom: var(--sizes-size-0);
  --button-size-xs-paddingRight: var(--sizes-size-3);
  --button-size-xs-paddingBottom: var(--sizes-size-2);
  --button-size-sm-top-left-border-radius: var(--borders-radius-3);
  --button-size-sm-top-right-border-radius: var(--borders-radius-3);
  --button-size-sm-bottom-left-border-radius: var(--borders-radius-3);
  --button-size-sm-bottom-right-border-radius: var(--borders-radius-3);
  --button-size-sm-height: var(--sizes-base-15);
  --button-size-sm-fontSize: var(--fonts-size-8);
  --button-size-sm-minWidth: var(--sizes-size-1);
  --button-size-sm-icon-size: var(--sizes-size-8);
  --button-size-sm-fontWeight: var(--fonts-weight-6);
  --button-size-sm-lineHeight: var(--fonts-lineHeight-2);
  --button-size-sm-icon-margin: var(--sizes-size-3);
  --button-size-sm-marginTop: var(--sizes-size-0);
  --button-size-sm-marginLeft: var(--sizes-size-0);
  --button-size-sm-paddingTop: var(--sizes-size-3);
  --button-size-sm-marginRight: var(--sizes-size-0);
  --button-size-sm-paddingLeft: var(--sizes-size-6);
  --button-size-sm-marginBottom: var(--sizes-size-0);
  --button-size-sm-paddingRight: var(--sizes-size-6);
  --button-size-sm-paddingBottom: var(--sizes-size-3);
  --button-size-md-top-left-border-radius: var(--borders-radius-3);
  --button-size-md-top-right-border-radius: var(--borders-radius-3);
  --button-size-md-bottom-left-border-radius: var(--borders-radius-3);
  --button-size-md-bottom-right-border-radius: var(--borders-radius-3);
  --button-size-md-height: var(--sizes-base-16);
  --button-size-md-fontSize: var(--fonts-size-7);
  --button-size-md-minWidth: var(--sizes-size-1);
  --button-size-md-icon-size: var(--sizes-size-8);
  --button-size-md-fontWeight: var(--fonts-weight-6);
  --button-size-md-lineHeight: var(--fonts-lineHeight-2);
  --button-size-md-icon-margin: var(--sizes-size-3);
  --button-size-md-marginTop: var(--sizes-size-0);
  --button-size-md-marginLeft: var(--sizes-size-0);
  --button-size-md-paddingTop: var(--sizes-size-3);
  --button-size-md-marginRight: var(--sizes-size-0);
  --button-size-md-paddingLeft: var(--sizes-size-7);
  --button-size-md-marginBottom: var(--sizes-size-0);
  --button-size-md-paddingRight: var(--sizes-size-7);
  --button-size-md-paddingBottom: var(--sizes-size-3);
  --button-size-lg-top-left-border-radius: var(--borders-radius-3);
  --button-size-lg-top-right-border-radius: var(--borders-radius-3);
  --button-size-lg-bottom-left-border-radius: var(--borders-radius-3);
  --button-size-lg-bottom-right-border-radius: var(--borders-radius-3);
  --button-size-lg-height: var(--sizes-base-19);
  --button-size-lg-fontSize: var(--fonts-size-7);
  --button-size-lg-minWidth: var(--sizes-size-1);
  --button-size-lg-icon-size: var(--sizes-size-8);
  --button-size-lg-fontWeight: var(--fonts-weight-6);
  --button-size-lg-lineHeight: var(--fonts-lineHeight-2);
  --button-size-lg-icon-margin: var(--sizes-size-3);
  --button-size-lg-marginTop: var(--sizes-size-0);
  --button-size-lg-marginLeft: var(--sizes-size-0);
  --button-size-lg-paddingTop: var(--sizes-size-6);
  --button-size-lg-marginRight: var(--sizes-size-0);
  --button-size-lg-paddingLeft: var(--sizes-size-9);
  --button-size-lg-marginBottom: var(--sizes-size-0);
  --button-size-lg-paddingRight: var(--sizes-size-9);
  --button-size-lg-paddingBottom: var(--sizes-size-6);
  --transfer-base-title-bg: var(--colors-neutral-fill-10);
  --transfer-base-title-color: var(--colors-neutral-text-2);
  --transfer-base-title-fontSize: var(--fonts-size-7);
  --transfer-base-title-fontWeight: var(--fonts-weight-6);
  --transfer-base-title-lineHeight: var(--fonts-lineHeight-2);
  --transfer-base-content-color: var(--colors-neutral-text-2);
  --transfer-base-content-fontSize: var(--fonts-size-7);
  --transfer-base-content-fontWeight: var(--fonts-weight-6);
  --transfer-base-content-lineHeight: var(--fonts-lineHeight-2);
  --transfer-base-top-border-color: var(--colors-neutral-line-8);
  --transfer-base-top-border-width: var(--borders-width-2);
  --transfer-base-top-border-style: var(--borders-style-2);
  --transfer-base-right-border-color: var(--colors-neutral-line-8);
  --transfer-base-right-border-width: var(--borders-width-2);
  --transfer-base-right-border-style: var(--borders-style-2);
  --transfer-base-bottom-border-color: var(--colors-neutral-line-8);
  --transfer-base-bottom-border-width: var(--borders-width-2);
  --transfer-base-bottom-border-style: var(--borders-style-2);
  --transfer-base-left-border-color: var(--colors-neutral-line-8);
  --transfer-base-left-border-width: var(--borders-width-2);
  --transfer-base-left-border-style: var(--borders-style-2);
  --transfer-base-top-right-border-radius: var(--borders-radius-3);
  --transfer-base-top-left-border-radius: var(--borders-radius-3);
  --transfer-base-bottom-right-border-radius: var(--borders-radius-2);
  --transfer-base-bottom-left-border-radius: var(--borders-radius-2);
  --transfer-base-header-paddingTop: var(--sizes-size-5);
  --transfer-base-header-paddingBottom: var(--sizes-size-5);
  --transfer-base-header-paddingLeft: var(--sizes-size-8);
  --transfer-base-header-paddingRight: var(--sizes-size-8);
  --transfer-base-footer-border-color: var(--colors-neutral-line-8);
  --transfer-base-body-paddingTop: var(--sizes-size-0);
  --transfer-base-body-paddingBottom: var(--sizes-size-0);
  --transfer-base-body-paddingLeft: var(--sizes-size-0);
  --transfer-base-body-paddingRight: var(--sizes-size-0);
  --transfer-base-option-paddingTop: var(--sizes-size-5);
  --transfer-base-option-paddingBottom: var(--sizes-size-5);
  --transfer-base-option-paddingLeft: var(--sizes-size-8);
  --transfer-base-option-paddingRight: var(--sizes-size-8);
  --transfer-base-option-marginTop: var(--sizes-size-0);
  --transfer-base-option-marginBottom: var(--sizes-size-0);
  --transfer-base-option-marginLeft: var(--sizes-size-0);
  --transfer-base-option-marginRight: var(--sizes-size-0);
  --transfer-base-shadow: var(--shadows-shadow-none);
  --transfer-search-color: var(--colors-neutral-text-2);
  --transfer-search-fontSize: var(--fonts-size-7);
  --transfer-search-fontWeight: var(--fonts-weight-6);
  --transfer-search-lineHeight: var(--fonts-lineHeight-2);
  --transfer-search-placeholder-font-color: var(--colors-neutral-text-6);
  --transfer-search-top-border-color: var(--colors-neutral-line-8);
  --transfer-search-top-border-width: var(--borders-width-2);
  --transfer-search-top-border-style: var(--borders-style-2);
  --transfer-search-right-border-color: var(--colors-neutral-line-8);
  --transfer-search-right-border-width: var(--borders-width-2);
  --transfer-search-right-border-style: var(--borders-style-2);
  --transfer-search-bottom-border-color: var(--colors-neutral-line-8);
  --transfer-search-bottom-border-width: var(--borders-width-2);
  --transfer-search-bottom-border-style: var(--borders-style-2);
  --transfer-search-left-border-color: var(--colors-neutral-line-8);
  --transfer-search-left-border-width: var(--borders-width-2);
  --transfer-search-left-border-style: var(--borders-style-2);
  --transfer-search-top-right-border-radius: var(--borders-radius-3);
  --transfer-search-top-left-border-radius: var(--borders-radius-3);
  --transfer-search-bottom-right-border-radius: var(--borders-radius-3);
  --transfer-search-bottom-left-border-radius: var(--borders-radius-3);
  --transfer-search-border-hover-color: var(--colors-brand-4);
  --transfer-search-border-active-color: var(--colors-brand-4);
  --transfer-search-paddingTop: var(--sizes-size-6);
  --transfer-search-paddingBottom: var(--sizes-size-6);
  --transfer-search-paddingLeft: var(--sizes-size-6);
  --transfer-search-paddingRight: var(--sizes-size-6);
  --transfer-search-input-paddingTop: var(--sizes-size-4);
  --transfer-search-input-paddingBottom: var(--sizes-size-4);
  --transfer-search-input-paddingLeft: var(--sizes-size-7);
  --transfer-search-input-paddingRight: var(--sizes-size-7);
  --transfer-search-shadow: var(--shadows-shadow-none);
  --transfer-group-color: var(--colors-neutral-text-5);
  --transfer-group-fontSize: var(--fonts-size-7);
  --transfer-group-fontWeight: var(--fonts-weight-6);
  --transfer-group-lineHeight: var(--fonts-lineHeight-2);
  --transfer-table-header-paddingTop: var(--sizes-size-5);
  --transfer-table-header-paddingBottom: var(--sizes-size-5);
  --transfer-table-header-paddingLeft: var(--sizes-size-7);
  --transfer-table-header-paddingRight: var(--sizes-size-7);
  --transfer-table-option-paddingTop: var(--sizes-size-4);
  --transfer-table-option-paddingBottom: var(--sizes-size-5);
  --transfer-table-option-paddingLeft: var(--sizes-size-7);
  --transfer-table-option-paddingRight: var(--sizes-size-7);
  --transfer-table-last-paddingRight: var(--sizes-base-9);
  --transfer-tree-bg-hover-color: var(--colors-neutral-fill-10);
  --transfer-tree-bg-active-color: var(--colors-brand-10);
  --transfer-tree-top-right-border-radius: var(--borders-radius-2);
  --transfer-tree-top-left-border-radius: var(--borders-radius-2);
  --transfer-tree-bottom-right-border-radius: var(--borders-radius-2);
  --transfer-tree-bottom-left-border-radius: var(--borders-radius-2);
  --transfer-tree-paddingTop: var(--sizes-size-3);
  --transfer-tree-paddingBottom: var(--sizes-size-3);
  --transfer-tree-paddingLeft: var(--sizes-size-7);
  --transfer-tree-paddingRight: var(--sizes-size-7);
  --transfer-tree-marginTop: var(--sizes-size-0);
  --transfer-tree-marginBottom: var(--sizes-size-2);
  --transfer-tree-marginLeft: var(--sizes-size-0);
  --transfer-tree-marginRight: var(--sizes-size-0);
  --transfer-tree-option-paddingTop: var(--sizes-size-0);
  --transfer-tree-option-paddingBottom: var(--sizes-size-0);
  --transfer-tree-option-paddingLeft: var(--sizes-size-6);
  --transfer-tree-option-paddingRight: var(--sizes-size-0);
  --transfer-tree-option-marginTop: var(--sizes-size-0);
  --transfer-tree-option-marginBottom: var(--sizes-size-4);
  --transfer-tree-option-marginLeft: var(--sizes-size-0);
  --transfer-tree-option-marginRight: var(--sizes-size-0);
  --transfer-chained-paddingTop: var(--sizes-size-5);
  --transfer-chained-paddingBottom: var(--sizes-size-5);
  --transfer-chained-paddingLeft: var(--sizes-size-6);
  --transfer-chained-paddingRight: var(--sizes-size-6);
  --transfer-chained-marginTop: var(--sizes-size-0);
  --transfer-chained-marginBottom: var(--sizes-size-0);
  --transfer-chained-marginLeft: var(--sizes-size-0);
  --transfer-chained-marginRight: var(--sizes-size-0);
  --input-default-default-top-border-color: var(--colors-neutral-line-8);
  --input-default-default-top-border-width: var(--borders-width-2);
  --input-default-default-top-border-style: var(--borders-style-2);
  --input-default-default-right-border-color: var(--colors-neutral-line-8);
  --input-default-default-right-border-width: var(--borders-width-2);
  --input-default-default-right-border-style: var(--borders-style-2);
  --input-default-default-bottom-border-color: var(--colors-neutral-line-8);
  --input-default-default-bottom-border-width: var(--borders-width-2);
  --input-default-default-bottom-border-style: var(--borders-style-2);
  --input-default-default-left-border-color: var(--colors-neutral-line-8);
  --input-default-default-left-border-width: var(--borders-width-2);
  --input-default-default-left-border-style: var(--borders-style-2);
  --input-default-default-top-right-border-radius: var(--borders-radius-3);
  --input-default-default-top-left-border-radius: var(--borders-radius-3);
  --input-default-default-bottom-right-border-radius: var(--borders-radius-3);
  --input-default-default-bottom-left-border-radius: var(--borders-radius-3);
  --input-default-default-color: var(--colors-neutral-fill-2);
  --input-default-default-fontSize: var(--fonts-size-7);
  --input-default-default-fontWeight: var(--fonts-weight-6);
  --input-default-default-lineHeight: var(--fonts-lineHeight-2);
  --input-default-default-paddingTop: var(--sizes-size-3);
  --input-default-default-paddingBottom: var(--sizes-size-3);
  --input-default-default-paddingLeft: var(--sizes-size-6);
  --input-default-default-paddingRight: var(--sizes-size-6);
  --input-default-default-bg-color: var(--colors-neutral-fill-11);
  --input-default-hover-top-border-color: var(--colors-brand-5);
  --input-default-hover-top-border-width: var(--borders-width-2);
  --input-default-hover-top-border-style: var(--borders-style-2);
  --input-default-hover-right-border-color: var(--colors-brand-5);
  --input-default-hover-right-border-width: var(--borders-width-2);
  --input-default-hover-right-border-style: var(--borders-style-2);
  --input-default-hover-bottom-border-color: var(--colors-brand-5);
  --input-default-hover-bottom-border-width: var(--borders-width-2);
  --input-default-hover-bottom-border-style: var(--borders-style-2);
  --input-default-hover-left-border-color: var(--colors-brand-5);
  --input-default-hover-left-border-width: var(--borders-width-2);
  --input-default-hover-left-border-style: var(--borders-style-2);
  --input-default-hover-top-right-border-radius: var(--borders-radius-3);
  --input-default-hover-top-left-border-radius: var(--borders-radius-3);
  --input-default-hover-bottom-right-border-radius: var(--borders-radius-3);
  --input-default-hover-bottom-left-border-radius: var(--borders-radius-3);
  --input-default-hover-paddingTop: var(--sizes-size-3);
  --input-default-hover-paddingBottom: var(--sizes-size-3);
  --input-default-hover-paddingLeft: var(--sizes-size-6);
  --input-default-hover-paddingRight: var(--sizes-size-6);
  --input-default-hover-bg-color: var(--colors-neutral-fill-11);
  --input-default-active-top-border-color: var(--colors-brand-5);
  --input-default-active-top-border-width: var(--borders-width-2);
  --input-default-active-top-border-style: var(--borders-style-2);
  --input-default-active-right-border-color: var(--colors-brand-5);
  --input-default-active-right-border-width: var(--borders-width-2);
  --input-default-active-right-border-style: var(--borders-style-2);
  --input-default-active-bottom-border-color: var(--colors-brand-5);
  --input-default-active-bottom-border-width: var(--borders-width-2);
  --input-default-active-bottom-border-style: var(--borders-style-2);
  --input-default-active-left-border-color: var(--colors-brand-5);
  --input-default-active-left-border-width: var(--borders-width-2);
  --input-default-active-left-border-style: var(--borders-style-2);
  --input-default-active-top-right-border-radius: var(--borders-radius-3);
  --input-default-active-top-left-border-radius: var(--borders-radius-3);
  --input-default-active-bottom-right-border-radius: var(--borders-radius-3);
  --input-default-active-bottom-left-border-radius: var(--borders-radius-3);
  --input-default-active-paddingTop: var(--sizes-size-3);
  --input-default-active-paddingBottom: var(--sizes-size-3);
  --input-default-active-paddingLeft: var(--sizes-size-6);
  --input-default-active-paddingRight: var(--sizes-size-6);
  --input-default-active-shadow: var(--shadows-shadow-none);
  --input-default-active-bg-color: var(--colors-neutral-fill-11);
  --input-default-disabled-top-border-color: var(--colors-neutral-line-8);
  --input-default-disabled-top-border-width: var(--borders-width-2);
  --input-default-disabled-top-border-style: var(--borders-style-2);
  --input-default-disabled-right-border-color: var(--colors-neutral-line-8);
  --input-default-disabled-right-border-width: var(--borders-width-2);
  --input-default-disabled-right-border-style: var(--borders-style-2);
  --input-default-disabled-bottom-border-color: var(--colors-neutral-line-8);
  --input-default-disabled-bottom-border-width: var(--borders-width-2);
  --input-default-disabled-bottom-border-style: var(--borders-style-2);
  --input-default-disabled-left-border-color: var(--colors-neutral-line-8);
  --input-default-disabled-left-border-width: var(--borders-width-2);
  --input-default-disabled-left-border-style: var(--borders-style-2);
  --input-default-disabled-top-right-border-radius: var(--borders-radius-3);
  --input-default-disabled-top-left-border-radius: var(--borders-radius-3);
  --input-default-disabled-bottom-right-border-radius: var(--borders-radius-3);
  --input-default-disabled-bottom-left-border-radius: var(--borders-radius-3);
  --input-default-disabled-paddingTop: var(--sizes-size-3);
  --input-default-disabled-paddingBottom: var(--sizes-size-3);
  --input-default-disabled-paddingLeft: var(--sizes-size-6);
  --input-default-disabled-paddingRight: var(--sizes-size-6);
  --input-default-disabled-bg-color: var(--colors-neutral-fill-10);
  --input-clearable-icon: '<svg t="1642652418667" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3606"><path d="M512 39.384615C250.092308 39.384615 39.384615 250.092308 39.384615 512s210.707692 472.615385 472.615385 472.615385 472.615385-210.707692 472.615385-472.615385S773.907692 39.384615 512 39.384615z m96.492308 488.369231l153.6 153.6c7.876923 7.876923 7.876923 19.692308 0 27.569231l-55.138462 55.138461c-7.876923 7.876923-19.692308 7.876923-27.569231 0L525.784615 610.461538c-7.876923-7.876923-19.692308-7.876923-27.56923 0l-153.6 153.6c-7.876923 7.876923-19.692308 7.876923-27.569231 0L261.907692 708.923077c-7.876923-7.876923-7.876923-19.692308 0-27.569231l153.6-153.6c7.876923-7.876923 7.876923-19.692308 0-27.569231l-155.56923-155.56923c-7.876923-7.876923-7.876923-19.692308 0-27.569231l55.138461-55.138462c7.876923-7.876923 19.692308-7.876923 27.569231 0l155.569231 155.569231c7.876923 7.876923 19.692308 7.876923 27.56923 0l153.6-153.6c7.876923-7.876923 19.692308-7.876923 27.569231 0l55.138462 55.138462c7.876923 7.876923 7.876923 19.692308 0 27.56923l-153.6 153.6c-5.907692 7.876923-5.907692 19.692308 0 27.569231z" p-id="3607"></path></svg>';
  --input-clearable-icon-size: var(--sizes-size-8);
  --input-clearable-default-color: var(--colors-neutral-text-7);
  --input-clearable-hover-color: var(--colors-neutral-text-4);
  --input-clearable-active-color: var(--colors-neutral-text-4);
  --input-count-single-fontSize: var(--fonts-size-7);
  --input-count-single-color: var(--colors-neutral-text-6);
  --input-count-multi-fontSize: var(--fonts-size-7);
  --input-count-multi-color: var(--colors-neutral-text-5);
  --input-prefix-fontSize: var(--fonts-size-7);
  --input-prefix-color: var(--colors-neutral-text-2);
  --input-password-invisible-icon: '<svg viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><title>不可见</title><g id="不可见" stroke="none" stroke-width="1" fill="currentColor" fill-rule="evenodd"><g id="编组"><rect id="矩形" stroke="none" fill="currentColor" opacity="0" x="0.5" y="0.5" width="15" height="15"></rect><path d="M2.91972703,5.00035128 C3.15932221,5.56062137 3.48954828,6.0784548 3.89678565,6.53485922 C4.96673844,7.73914737 6.49518581,8.43995 8.14,8.43995 C9.76664693,8.43995 11.2813058,7.75315198 12.3523764,6.57033395 C12.6920742,6.19520277 12.9803798,5.7761243 13.209327,5.32420638 L13.3395085,5.04920376 L14.2544915,5.45269624 C13.9653387,6.10839593 13.572991,6.71219666 13.0936273,7.24156203 C12.7623988,7.60734835 12.3948705,7.93285848 11.9982387,8.21395897 L12.9566,9.87395 L12.0906,10.37395 L11.1412434,8.72942071 C10.3784723,9.11337429 9.54082663,9.35086388 8.66757967,9.41933209 L8.668,10.97185 L7.668,10.97185 L7.66735222,9.42343888 C6.75745885,9.35969244 5.88560233,9.11282413 5.09602954,8.70830726 L4.1485,10.34855 L3.2825,9.84855 L4.2424457,8.18636156 C3.84593988,7.9008387 3.4793171,7.57058753 3.14992355,7.19983732 C2.73988365,6.74029373 2.39560013,6.22662333 2.12776836,5.67339306 L2.00027297,5.39354872 L2.91972703,5.00035128 Z" id="形状结合"></path></g></g></svg>';
  --input-password-invisible-icon-size: var(--sizes-size-8);
  --input-password-invisible-icon-color: var(--colors-neutral-text-5);
  --input-password-view-icon: '<svg viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><title>可见</title><g id="可见" stroke="none" stroke-width="1" fill="currentColor" fill-rule="evenodd"><g id="编组"><rect id="矩形" opacity="0" x="0.5" y="0.5" width="15" height="15"></rect><path d="M7.9999,3.0001 C11.9889,3.0001 14.9999,6.8731 14.9999,8.0001 C14.9999,8.8831 11.9889,13.0001 7.9999,13.0001 C3.9609,13.0001 0.9999,8.8831 0.9999,8.0001 C0.9999,6.8731 3.9609,3.0001 7.9999,3.0001 Z M7.9999,4.0001 C4.7329,4.0001 2.2179,7.0861 2.0089,7.9731 C2.2749,8.7711 4.7189,12.0001 7.9999,12.0001 C11.2099,12.0001 13.7339,8.7311 13.9929,7.9631 C13.8069,7.1261 11.2709,4.0001 7.9999,4.0001 Z M7.975,5.879 C9.08,5.879 9.975,6.775 9.975,7.879 C9.975,8.983 9.08,9.879 7.975,9.879 C6.871,9.879 5.975,8.983 5.975,7.879 C5.975,6.775 6.871,5.879 7.975,5.879 Z M7.975,6.879 C7.424,6.879 6.975,7.327 6.975,7.879 C6.975,8.43 7.424,8.879 7.975,8.879 C8.527,8.879 8.975,8.43 8.975,7.879 C8.975,7.327 8.527,6.879 7.975,6.879 Z" id="图标"></path></g</g></svg>';
  --input-password-view-icon-size: var(--sizes-size-8);
  --input-password-view-icon-color: var(--colors-neutral-text-5);
  --input-textarea-paddingTop: var(--sizes-size-3);
  --input-textarea-paddingBottom: var(--sizes-size-3);
  --input-textarea-paddingLeft: var(--sizes-size-6);
  --input-textarea-paddingRight: var(--sizes-base-11);
  --input-addon-text-bg-color-default: var(--colors-neutral-text-11);
  --input-addon-text-bg-color-hover: var(--colors-neutral-text-11);
  --input-addon-text-top-border-color: var(--colors-neutral-line-8);
  --input-addon-text-top-border-width: var(--borders-width-2);
  --input-addon-text-top-border-style: var(--borders-style-2);
  --input-addon-text-right-border-color: var(--colors-neutral-line-8);
  --input-addon-text-right-border-width: var(--borders-width-2);
  --input-addon-text-right-border-style: var(--borders-style-2);
  --input-addon-text-bottom-border-color: var(--colors-neutral-line-8);
  --input-addon-text-bottom-border-width: var(--borders-width-2);
  --input-addon-text-bottom-border-style: var(--borders-style-2);
  --input-addon-text-left-border-color: var(--colors-neutral-line-8);
  --input-addon-text-left-border-width: var(--borders-width-2);
  --input-addon-text-left-border-style: var(--borders-style-2);
  --input-size-sm-height: var(--sizes-base-15);
  --input-size-default-height: var(--sizes-base-16);
  --input-size-md-height: var(--sizes-base-16);
  --input-size-lg-height: var(--sizes-base-19);
  --Form-inputNumber-base-width: var(--sizes-base-12);
  --Form-inputNumber-base-height: var(--sizes-base-16);
  --inputNumber-base-default-top-border-color: var(--colors-neutral-line-8);
  --inputNumber-base-default-top-border-width: var(--borders-width-2);
  --inputNumber-base-default-top-border-style: var(--borders-style-2);
  --inputNumber-base-default-right-border-color: var(--colors-neutral-line-8);
  --inputNumber-base-default-right-border-width: var(--borders-width-2);
  --inputNumber-base-default-right-border-style: var(--borders-style-2);
  --inputNumber-base-default-bottom-border-color: var(--colors-neutral-line-8);
  --inputNumber-base-default-bottom-border-width: var(--borders-width-2);
  --inputNumber-base-default-bottom-border-style: var(--borders-style-2);
  --inputNumber-base-default-left-border-color: var(--colors-neutral-line-8);
  --inputNumber-base-default-left-border-width: var(--borders-width-2);
  --inputNumber-base-default-left-border-style: var(--borders-style-2);
  --inputNumber-base-default-top-right-border-radius: var(--borders-radius-3);
  --inputNumber-base-default-top-left-border-radius: var(--borders-radius-3);
  --inputNumber-base-default-bottom-right-border-radius: var(--borders-radius-3);
  --inputNumber-base-default-bottom-left-border-radius: var(--borders-radius-3);
  --inputNumber-base-default-paddingTop: var(--sizes-size-3);
  --inputNumber-base-default-paddingBottom: var(--sizes-size-3);
  --inputNumber-base-default-paddingLeft: var(--sizes-size-6);
  --inputNumber-base-default-paddingRight: var(--sizes-size-6);
  --inputNumber-base-default-bg-color: var(--colors-neutral-fill-11);
  --inputNumber-base-hover-top-border-color: var(--colors-brand-5);
  --inputNumber-base-hover-top-border-width: var(--borders-width-2);
  --inputNumber-base-hover-top-border-style: var(--borders-style-2);
  --inputNumber-base-hover-right-border-color: var(--colors-brand-5);
  --inputNumber-base-hover-right-border-width: var(--borders-width-2);
  --inputNumber-base-hover-right-border-style: var(--borders-style-2);
  --inputNumber-base-hover-bottom-border-color: var(--colors-brand-5);
  --inputNumber-base-hover-bottom-border-width: var(--borders-width-2);
  --inputNumber-base-hover-bottom-border-style: var(--borders-style-2);
  --inputNumber-base-hover-left-border-color: var(--colors-brand-5);
  --inputNumber-base-hover-left-border-width: var(--borders-width-2);
  --inputNumber-base-hover-left-border-style: var(--borders-style-2);
  --inputNumber-base-hover-top-right-border-radius: var(--borders-radius-3);
  --inputNumber-base-hover-top-left-border-radius: var(--borders-radius-3);
  --inputNumber-base-hover-bottom-right-border-radius: var(--borders-radius-3);
  --inputNumber-base-hover-bottom-left-border-radius: var(--borders-radius-3);
  --inputNumber-base-hover-paddingTop: var(--sizes-size-3);
  --inputNumber-base-hover-paddingBottom: var(--sizes-size-3);
  --inputNumber-base-hover-paddingLeft: var(--sizes-size-7);
  --inputNumber-base-hover-paddingRight: var(--sizes-size-7);
  --inputNumber-base-hover-bg-color: var(--colors-neutral-fill-11);
  --inputNumber-base-active-top-border-color: var(--colors-brand-5);
  --inputNumber-base-active-top-border-width: var(--borders-width-2);
  --inputNumber-base-active-top-border-style: var(--borders-style-2);
  --inputNumber-base-active-right-border-color: var(--colors-brand-5);
  --inputNumber-base-active-right-border-width: var(--borders-width-2);
  --inputNumber-base-active-right-border-style: var(--borders-style-2);
  --inputNumber-base-active-bottom-border-color: var(--colors-brand-5);
  --inputNumber-base-active-bottom-border-width: var(--borders-width-2);
  --inputNumber-base-active-bottom-border-style: var(--borders-style-2);
  --inputNumber-base-active-left-border-color: var(--colors-brand-5);
  --inputNumber-base-active-left-border-width: var(--borders-width-2);
  --inputNumber-base-active-left-border-style: var(--borders-style-2);
  --inputNumber-base-active-top-right-border-radius: var(--borders-radius-3);
  --inputNumber-base-active-top-left-border-radius: var(--borders-radius-3);
  --inputNumber-base-active-bottom-right-border-radius: var(--borders-radius-3);
  --inputNumber-base-active-bottom-left-border-radius: var(--borders-radius-3);
  --inputNumber-base-active-paddingTop: var(--sizes-size-3);
  --inputNumber-base-active-paddingBottom: var(--sizes-size-3);
  --inputNumber-base-active-paddingLeft: var(--sizes-size-7);
  --inputNumber-base-active-paddingRight: var(--sizes-size-7);
  --inputNumber-base-active-shadow: var(--Form-input-boxShadow);
  --inputNumber-base-active-bg-color: var(--colors-neutral-fill-11);
  --inputNumber-base-disabled-top-border-color: var(--colors-neutral-line-8);
  --inputNumber-base-disabled-top-border-width: var(--borders-width-2);
  --inputNumber-base-disabled-top-border-style: var(--borders-style-2);
  --inputNumber-base-disabled-right-border-color: var(--colors-neutral-line-8);
  --inputNumber-base-disabled-right-border-width: var(--borders-width-2);
  --inputNumber-base-disabled-right-border-style: var(--borders-style-2);
  --inputNumber-base-disabled-bottom-border-color: var(--colors-neutral-line-8);
  --inputNumber-base-disabled-bottom-border-width: var(--borders-width-2);
  --inputNumber-base-disabled-bottom-border-style: var(--borders-style-2);
  --inputNumber-base-disabled-left-border-color: var(--colors-neutral-line-8);
  --inputNumber-base-disabled-left-border-width: var(--borders-width-2);
  --inputNumber-base-disabled-left-border-style: var(--borders-style-2);
  --inputNumber-base-disabled-top-right-border-radius: var(--borders-radius-3);
  --inputNumber-base-disabled-top-left-border-radius: var(--borders-radius-3);
  --inputNumber-base-disabled-bottom-right-border-radius: var(--borders-radius-3);
  --inputNumber-base-disabled-bottom-left-border-radius: var(--borders-radius-3);
  --inputNumber-base-disabled-paddingTop: var(--sizes-size-3);
  --inputNumber-base-disabled-paddingBottom: var(--sizes-size-3);
  --inputNumber-base-disabled-paddingLeft: var(--sizes-size-7);
  --inputNumber-base-disabled-paddingRight: var(--sizes-size-7);
  --inputNumber-base-disabled-bg-color: var(--colors-neutral-fill-10);
  --inputNumber-base-default-icon-fontSize: var(--fonts-size-8);
  --inputNumber-base-default-icon-color: var(--colors-neutral-text-2);
  --inputNumber-base-hover-icon-color: var(--colors-brand-5);
  --inputNumber-base-active-icon-color: var(--colors-brand-5);
  --inputNumber-base-default-step-bg: var(--colors-neutral-fill-11);
  --inputNumber-base-hover-step-bg: var(--colors-neutral-fill-11);
  --inputNumber-base-active-step-bg: var(--colors-neutral-fill-11);
  --inputNumber-base-unit-bg-color: var(--colors-neutral-fill-10);
  --inputNumber-base-default-unit-width: var(--sizes-base-28);
  --inputNumber-base-default-unit-paddingTop: calc(
    (
        var(--Form-selectOption-height) - var(--Form-input-lineHeight) *
          var(--Form-input-fontSize) - var(--Form-input-borderWidth) * 2
      ) /
      2
  );
  --inputNumber-base-default-unit-paddingBottom: calc(
    (
        var(--Form-selectOption-height) - var(--Form-input-lineHeight) *
          var(--Form-input-fontSize) - var(--Form-input-borderWidth) * 2
      ) /
      2
  );
  --inputNumber-base-default-unit-paddingLeft: var(--sizes-size-6);
  --inputNumber-base-default-unit-paddingRight: var(--sizes-size-6);
  --inputNumber-size-sm-height: var(--sizes-base-16);
  --inputNumber-size-default-height: var(--sizes-base-16);
  --inputNumber-size-md-height: var(--sizes-base-16);
  --inputNumber-size-lg-height: var(--sizes-base-16);
  --Number-handler-bg: var(--inputNumber-base-default-step-bg);
  --Number-handler-fontSize: var(--inputNumber-base-default-icon-fontSize);
  --Number-handler-color: var(--inputNumber-base-default-icon-color);
  --Number-handler-onHover-bg: var(--inputNumber-base-hover-step-bg);
  --Number-handler-onHover-color: var(--inputNumber-base-hover-icon-color);
  --Number-handler-onActive-bg: var(--inputNumber-base-active-step-bg);
  --inputNumber-enhance-default-top-border-color: var(--colors-neutral-line-7);
  --inputNumber-enhance-default-top-border-width: var(--borders-width-2);
  --inputNumber-enhance-default-top-border-style: var(--borders-style-2);
  --inputNumber-enhance-default-right-border-color: var(--colors-neutral-line-7);
  --inputNumber-enhance-default-right-border-width: var(--borders-width-2);
  --inputNumber-enhance-default-right-border-style: var(--borders-style-2);
  --inputNumber-enhance-default-bottom-border-color: var(--colors-neutral-line-7);
  --inputNumber-enhance-default-bottom-border-width: var(--borders-width-2);
  --inputNumber-enhance-default-bottom-border-style: var(--borders-style-2);
  --inputNumber-enhance-default-left-border-color: var(--colors-neutral-line-7);
  --inputNumber-enhance-default-left-border-width: var(--borders-width-2);
  --inputNumber-enhance-default-left-border-style: var(--borders-style-2);
  --inputNumber-enhance-default-top-right-border-radius: var(--borders-radius-3);
  --inputNumber-enhance-default-top-left-border-radius: var(--borders-radius-3);
  --inputNumber-enhance-default-bottom-right-border-radius: var(--borders-radius-3);
  --inputNumber-enhance-default-bottom-left-border-radius: var(--borders-radius-3);
  --inputNumber-enhance-default-paddingTop: var(--sizes-size-3);
  --inputNumber-enhance-default-paddingBottom: var(--sizes-size-3);
  --inputNumber-enhance-default-paddingLeft: var(--sizes-size-5);
  --inputNumber-enhance-default-paddingRight: var(--sizes-size-5);
  --inputNumber-enhance-default-bg-color: var(--colors-neutral-fill-11);
  --inputNumber-enhance-hover-top-border-color: var(--colors-brand-5);
  --inputNumber-enhance-hover-top-border-width: var(--borders-width-2);
  --inputNumber-enhance-hover-top-border-style: var(--borders-style-2);
  --inputNumber-enhance-hover-right-border-color: var(--colors-brand-5);
  --inputNumber-enhance-hover-right-border-width: var(--borders-width-2);
  --inputNumber-enhance-hover-right-border-style: var(--borders-style-2);
  --inputNumber-enhance-hover-bottom-border-color: var(--colors-brand-5);
  --inputNumber-enhance-hover-bottom-border-width: var(--borders-width-2);
  --inputNumber-enhance-hover-bottom-border-style: var(--borders-style-2);
  --inputNumber-enhance-hover-left-border-color: var(--colors-brand-5);
  --inputNumber-enhance-hover-left-border-width: var(--borders-width-2);
  --inputNumber-enhance-hover-left-border-style: var(--borders-style-2);
  --inputNumber-enhance-hover-top-right-border-radius: var(--borders-radius-3);
  --inputNumber-enhance-hover-top-left-border-radius: var(--borders-radius-3);
  --inputNumber-enhance-hover-bottom-right-border-radius: var(--borders-radius-3);
  --inputNumber-enhance-hover-bottom-left-border-radius: var(--borders-radius-3);
  --inputNumber-enhance-hover-paddingTop: var(--sizes-size-3);
  --inputNumber-enhance-hover-paddingBottom: var(--sizes-size-3);
  --inputNumber-enhance-hover-paddingLeft: var(--sizes-size-7);
  --inputNumber-enhance-hover-paddingRight: var(--sizes-size-7);
  --inputNumber-enhance-hover-bg-color: var(--colors-neutral-fill-11);
  --inputNumber-enhance-active-top-border-color: var(--colors-brand-5);
  --inputNumber-enhance-active-top-border-width: var(--borders-width-2);
  --inputNumber-enhance-active-top-border-style: var(--borders-style-2);
  --inputNumber-enhance-active-right-border-color: var(--colors-brand-5);
  --inputNumber-enhance-active-right-border-width: var(--borders-width-2);
  --inputNumber-enhance-active-right-border-style: var(--borders-style-2);
  --inputNumber-enhance-active-bottom-border-color: var(--colors-brand-5);
  --inputNumber-enhance-active-bottom-border-width: var(--borders-width-2);
  --inputNumber-enhance-active-bottom-border-style: var(--borders-style-2);
  --inputNumber-enhance-active-left-border-color: var(--colors-brand-5);
  --inputNumber-enhance-active-left-border-width: var(--borders-width-2);
  --inputNumber-enhance-active-left-border-style: var(--borders-style-2);
  --inputNumber-enhance-active-top-right-border-radius: var(--borders-radius-3);
  --inputNumber-enhance-active-top-left-border-radius: var(--borders-radius-3);
  --inputNumber-enhance-active-bottom-right-border-radius: var(--borders-radius-3);
  --inputNumber-enhance-active-bottom-left-border-radius: var(--borders-radius-3);
  --inputNumber-enhance-active-paddingTop: var(--sizes-size-3);
  --inputNumber-enhance-active-paddingBottom: var(--sizes-size-3);
  --inputNumber-enhance-active-paddingLeft: var(--sizes-size-7);
  --inputNumber-enhance-active-paddingRight: var(--sizes-size-7);
  --inputNumber-enhance-active-shadow: var(--shadows-shadow-none);
  --inputNumber-enhance-active-bg-color: var(--colors-neutral-fill-11);
  --inputNumber-enhance-disabled-top-border-color: var(--colors-neutral-line-8);
  --inputNumber-enhance-disabled-top-border-width: var(--borders-width-2);
  --inputNumber-enhance-disabled-top-border-style: var(--borders-style-2);
  --inputNumber-enhance-disabled-right-border-color: var(--colors-neutral-line-8);
  --inputNumber-enhance-disabled-right-border-width: var(--borders-width-2);
  --inputNumber-enhance-disabled-right-border-style: var(--borders-style-2);
  --inputNumber-enhance-disabled-bottom-border-color: var(--colors-neutral-line-8);
  --inputNumber-enhance-disabled-bottom-border-width: var(--borders-width-2);
  --inputNumber-enhance-disabled-bottom-border-style: var(--borders-style-2);
  --inputNumber-enhance-disabled-left-border-color: var(--colors-neutral-line-8);
  --inputNumber-enhance-disabled-left-border-width: var(--borders-width-2);
  --inputNumber-enhance-disabled-left-border-style: var(--borders-style-2);
  --inputNumber-enhance-disabled-top-right-border-radius: var(--borders-radius-3);
  --inputNumber-enhance-disabled-top-left-border-radius: var(--borders-radius-3);
  --inputNumber-enhance-disabled-bottom-right-border-radius: var(--borders-radius-3);
  --inputNumber-enhance-disabled-bottom-left-border-radius: var(--borders-radius-3);
  --inputNumber-enhance-disabled-paddingTop: var(--sizes-size-3);
  --inputNumber-enhance-disabled-paddingBottom: var(--sizes-size-3);
  --inputNumber-enhance-disabled-paddingLeft: var(--sizes-size-7);
  --inputNumber-enhance-disabled-paddingRight: var(--sizes-size-7);
  --inputNumber-enhance-disabled-bg-color: var(--colors-neutral-fill-10);
  --inputNumber-enhance-default-icon-fontSize: var(--fonts-size-8);
  --inputNumber-enhance-leftIcon-default-height: var(--sizes-size-7);
  --inputNumber-enhance-leftIcon-default-width: var(--sizes-size-7);
  --inputNumber-enhance-leftIcon-default-color: var(--colors-neutral-text-2);
  --inputNumber-enhance-leftIcon-hover-color: var(--colors-brand-5);
  --inputNumber-enhance-leftIcon-active-color: var(--colors-brand-5);
  --inputNumber-enhance-leftIcon-default-bg-color: var(--colors-neutral-fill-11);
  --inputNumber-enhance-leftIcon-hover-bg-color: var(--colors-neutral-fill-11);
  --inputNumber-enhance-leftIcon-active-bg-color: var(--colors-neutral-fill-11);
  --inputNumber-enhance-rightIcon-default-height: var(--sizes-size-7);
  --inputNumber-enhance-rightIcon-default-width: var(--sizes-size-7);
  --inputNumber-enhance-rightIcon-default-color: var(--colors-neutral-text-2);
  --inputNumber-enhance-rightIcon-hover-color: var(--colors-brand-5);
  --inputNumber-enhance-rightIcon-active-color: var(--colors-brand-5);
  --inputNumber-enhance-rightIcon-default-bg-color: var(--colors-neutral-fill-11);
  --inputNumber-enhance-rightIcon-hover-bg-color: var(--colors-neutral-fill-11);
  --inputNumber-enhance-rightIcon-active-bg-color: var(--colors-neutral-fill-11);
  --inputNumber-enhance-leftIcon-default-icon: '<svg viewBox="0 0 12 2" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" class="icon icon-minus"><g id="minus" fill="currentColor" fill-rule="nonzero"><polygon id="path-1" points="0 1.6 0 0.4 12 0.4 12 1.6"></polygon></g></svg>';
  --inputNumber-enhance-rightIcon-default-icon: '<svg viewBox="0 0 12 12" xmlns="http://www.w3.org/2000/svg" class="icon  icon-plus"><path d="M6.6 6.6V12H5.4V6.6H0V5.4h5.4V0h1.2v5.4H12v1.2z" fill="currentColor" fill-rule="nonzero"></path></svg>';
  --inputNumber-enhance-mobile-input-width: 3.5rem;
  --inputNumber-enhance-mobile-icon-width: 1.75rem;
  --inputNumber-enhance-mobile-icon-height: 1.75rem;
  --inputNumber-enhance-mobile-icon-inner-width: 1.6875rem;
  --inputNumber-enhance-mobile-icon-inner-height: 1.6875rem;
  --Form-input-onHover-borderColor: var(--colors-brand-5);
  --Form-input-onFocused-borderColor: var(--colors-brand-5);
  --checkbox-default-default-height: var(--sizes-size-9);
  --checkbox-default-default-bg-color: var(--colors-neutral-fill-11);
  --checkbox-default-default-text-color: var(--colors-neutral-text-2);
  --checkbox-default-default-fontWeight: var(--fonts-weight-6);
  --checkbox-default-default-fontSize: var(--fonts-size-7);
  --checkbox-default-default-distance: var(--sizes-size-5);
  --checkbox-default-default-top-border-color: var(--colors-neutral-line-8);
  --checkbox-default-default-right-border-color: var(--colors-neutral-line-8);
  --checkbox-default-default-bottom-border-color: var(--colors-neutral-line-8);
  --checkbox-default-default-left-border-color: var(--colors-neutral-line-8);
  --checkbox-default-default-top-border-width: var(--borders-width-2);
  --checkbox-default-default-right-border-width: var(--borders-width-2);
  --checkbox-default-default-bottom-border-width: var(--borders-width-2);
  --checkbox-default-default-left-border-width: var(--borders-width-2);
  --checkbox-default-default-top-border-style: var(--borders-style-2);
  --checkbox-default-default-right-border-style: var(--borders-style-2);
  --checkbox-default-default-bottom-border-style: var(--borders-style-2);
  --checkbox-default-default-left-border-style: var(--borders-style-2);
  --checkbox-default-default-top-left-border-radius: var(--borders-radius-2);
  --checkbox-default-default-top-right-border-radius: var(--borders-radius-2);
  --checkbox-default-default-bottom-right-border-radius: var(--borders-radius-2);
  --checkbox-default-default-bottom-left-border-radius: var(--borders-radius-2);
  --checkbox-default-hover-height: var(--sizes-size-9);
  --checkbox-default-hover-bg-color: var(--colors-neutral-fill-11);
  --checkbox-default-hover-text-color: var(--colors-neutral-text-2);
  --checkbox-default-hover-fontSize: var(--fonts-size-7);
  --checkbox-default-hover-top-border-color: var(--colors-brand-5);
  --checkbox-default-hover-right-border-color: var(--colors-brand-5);
  --checkbox-default-hover-bottom-border-color: var(--colors-brand-5);
  --checkbox-default-hover-left-border-color: var(--colors-brand-5);
  --checkbox-default-hover-top-border-width: var(--borders-width-2);
  --checkbox-default-hover-right-border-width: var(--borders-width-2);
  --checkbox-default-hover-bottom-border-width: var(--borders-width-2);
  --checkbox-default-hover-left-border-width: var(--borders-width-2);
  --checkbox-default-hover-top-border-style: var(--borders-style-2);
  --checkbox-default-hover-right-border-style: var(--borders-style-2);
  --checkbox-default-hover-bottom-border-style: var(--borders-style-2);
  --checkbox-default-hover-left-border-style: var(--borders-style-2);
  --checkbox-default-hover-top-left-border-radius: var(--borders-radius-2);
  --checkbox-default-hover-top-right-border-radius: var(--borders-radius-2);
  --checkbox-default-hover-bottom-right-border-radius: var(--borders-radius-2);
  --checkbox-default-hover-bottom-left-border-radius: var(--borders-radius-2);
  --checkbox-default-active-height: var(--sizes-size-9);
  --checkbox-default-active-bg-color: var(--colors-neutral-fill-11);
  --checkbox-default-active-text-color: var(--colors-neutral-text-2);
  --checkbox-default-active-fontSize: var(--fonts-size-7);
  --checkbox-default-active-top-border-color: var(--colors-brand-5);
  --checkbox-default-active-right-border-color: var(--colors-brand-5);
  --checkbox-default-active-bottom-border-color: var(--colors-brand-5);
  --checkbox-default-active-left-border-color: var(--colors-brand-5);
  --checkbox-default-active-top-border-width: var(--borders-width-2);
  --checkbox-default-active-right-border-width: var(--borders-width-2);
  --checkbox-default-active-bottom-border-width: var(--borders-width-2);
  --checkbox-default-active-left-border-width: var(--borders-width-2);
  --checkbox-default-active-top-border-style: var(--borders-style-2);
  --checkbox-default-active-right-border-style: var(--borders-style-2);
  --checkbox-default-active-bottom-border-style: var(--borders-style-2);
  --checkbox-default-active-left-border-style: var(--borders-style-2);
  --checkbox-default-active-top-left-border-radius: var(--borders-radius-2);
  --checkbox-default-active-top-right-border-radius: var(--borders-radius-2);
  --checkbox-default-active-bottom-right-border-radius: var(--borders-radius-2);
  --checkbox-default-active-bottom-left-border-radius: var(--borders-radius-2);
  --checkbox-default-disabled-height: var(--sizes-size-9);
  --checkbox-default-disabled-bg-color: var(--colors-neutral-fill-10);
  --checkbox-default-disabled-text-color: var(--colors-neutral-text-6);
  --checkbox-default-disabled-fontSize: var(--fonts-size-7);
  --checkbox-default-disabled-top-border-color: var(--colors-neutral-line-8);
  --checkbox-default-disabled-right-border-color: var(--colors-neutral-line-8);
  --checkbox-default-disabled-bottom-border-color: var(--colors-neutral-line-8);
  --checkbox-default-disabled-left-border-color: var(--colors-neutral-line-8);
  --checkbox-default-disabled-top-border-width: var(--borders-width-2);
  --checkbox-default-disabled-right-border-width: var(--borders-width-2);
  --checkbox-default-disabled-bottom-border-width: var(--borders-width-2);
  --checkbox-default-disabled-left-border-width: var(--borders-width-2);
  --checkbox-default-disabled-top-border-style: var(--borders-style-2);
  --checkbox-default-disabled-right-border-style: var(--borders-style-2);
  --checkbox-default-disabled-bottom-border-style: var(--borders-style-2);
  --checkbox-default-disabled-left-border-style: var(--borders-style-2);
  --checkbox-default-disabled-top-left-border-radius: var(--borders-radius-2);
  --checkbox-default-disabled-top-right-border-radius: var(--borders-radius-2);
  --checkbox-default-disabled-bottom-right-border-radius: var(--borders-radius-2);
  --checkbox-default-disabled-bottom-left-border-radius: var(--borders-radius-2);
  --checkbox-default-checked-default-height: var(--sizes-size-9);
  --checkbox-default-checked-default-bg-color: var(--colors-brand-5);
  --checkbox-default-checked-default-text-color: var(--colors-neutral-text-2);
  --checkbox-default-checked-default-fontSize: var(--fonts-size-7);
  --checkbox-default-checked-default-icon-size: var(--sizes-size-8);
  --checkbox-default-checked-default-icon-color: var(--colors-neutral-fill-11);
  --checkbox-default-checked-default-icon: '<svg viewBox="0 0 1024 1024"><path d="M806.784 295.744l45.248 45.184-362.112 362.048-45.184 45.312-226.24-226.24 45.184-45.184 180.992 180.928z" fill="currentColor"></path></svg>';
  --checkbox-default-checked-default-top-border-color: var(--colors-brand-5);
  --checkbox-default-checked-default-right-border-color: var(--colors-brand-5);
  --checkbox-default-checked-default-bottom-border-color: var(--colors-brand-5);
  --checkbox-default-checked-default-left-border-color: var(--colors-brand-5);
  --checkbox-default-checked-default-top-border-width: var(--borders-width-2);
  --checkbox-default-checked-default-right-border-width: var(--borders-width-2);
  --checkbox-default-checked-default-bottom-border-width: var(--borders-width-2);
  --checkbox-default-checked-default-left-border-width: var(--borders-width-2);
  --checkbox-default-checked-default-top-border-style: var(--borders-style-2);
  --checkbox-default-checked-default-right-border-style: var(--borders-style-2);
  --checkbox-default-checked-default-bottom-border-style: var(--borders-style-2);
  --checkbox-default-checked-default-left-border-style: var(--borders-style-2);
  --checkbox-default-checked-default-top-left-border-radius: var(--borders-radius-2);
  --checkbox-default-checked-default-top-right-border-radius: var(--borders-radius-2);
  --checkbox-default-checked-default-bottom-right-border-radius: var(--borders-radius-2);
  --checkbox-default-checked-default-bottom-left-border-radius: var(--borders-radius-2);
  --checkbox-default-checked-hover-height: var(--sizes-size-9);
  --checkbox-default-checked-hover-bg-color: var(--colors-brand-6);
  --checkbox-default-checked-hover-text-color: var(--colors-neutral-text-2);
  --checkbox-default-checked-hover-fontSize: var(--fonts-size-7);
  --checkbox-default-checked-hover-top-border-color: var(--colors-brand-6);
  --checkbox-default-checked-hover-right-border-color: var(--colors-brand-6);
  --checkbox-default-checked-hover-bottom-border-color: var(--colors-brand-6);
  --checkbox-default-checked-hover-left-border-color: var(--colors-brand-6);
  --checkbox-default-checked-hover-top-border-width: var(--borders-width-2);
  --checkbox-default-checked-hover-right-border-width: var(--borders-width-2);
  --checkbox-default-checked-hover-bottom-border-width: var(--borders-width-2);
  --checkbox-default-checked-hover-left-border-width: var(--borders-width-2);
  --checkbox-default-checked-hover-top-border-style: var(--borders-style-2);
  --checkbox-default-checked-hover-right-border-style: var(--borders-style-2);
  --checkbox-default-checked-hover-bottom-border-style: var(--borders-style-2);
  --checkbox-default-checked-hover-left-border-style: var(--borders-style-2);
  --checkbox-default-checked-hover-top-left-border-radius: var(--borders-radius-2);
  --checkbox-default-checked-hover-top-right-border-radius: var(--borders-radius-2);
  --checkbox-default-checked-hover-bottom-right-border-radius: var(--borders-radius-2);
  --checkbox-default-checked-hover-bottom-left-border-radius: var(--borders-radius-2);
  --checkbox-default-checked-active-height: var(--sizes-size-9);
  --checkbox-default-checked-active-bg-color: var(--colors-brand-4);
  --checkbox-default-checked-active-text-color: var(--colors-neutral-text-2);
  --checkbox-default-checked-active-fontSize: var(--fonts-size-7);
  --checkbox-default-checked-active-top-border-color: var(--colors-brand-5);
  --checkbox-default-checked-active-right-border-color: var(--colors-brand-5);
  --checkbox-default-checked-active-bottom-border-color: var(--colors-brand-5);
  --checkbox-default-checked-active-left-border-color: var(--colors-brand-5);
  --checkbox-default-checked-active-top-border-width: var(--borders-width-2);
  --checkbox-default-checked-active-right-border-width: var(--borders-width-2);
  --checkbox-default-checked-active-bottom-border-width: var(--borders-width-2);
  --checkbox-default-checked-active-left-border-width: var(--borders-width-2);
  --checkbox-default-checked-active-top-border-style: var(--borders-style-2);
  --checkbox-default-checked-active-right-border-style: var(--borders-style-2);
  --checkbox-default-checked-active-bottom-border-style: var(--borders-style-2);
  --checkbox-default-checked-active-left-border-style: var(--borders-style-2);
  --checkbox-default-checked-active-top-left-border-radius: var(--borders-radius-2);
  --checkbox-default-checked-active-top-right-border-radius: var(--borders-radius-2);
  --checkbox-default-checked-active-bottom-right-border-radius: var(--borders-radius-2);
  --checkbox-default-checked-active-bottom-left-border-radius: var(--borders-radius-2);
  --checkbox-default-checked-disabled-height: var(--sizes-size-9);
  --checkbox-default-checked-disabled-bg-color: var(--colors-neutral-fill-10);
  --checkbox-default-checked-disabled-text-color: var(--colors-neutral-text-6);
  --checkbox-default-checked-disabled-fontSize: var(--fonts-size-7);
  --checkbox-default-checked-disabled-icon-color: var(--colors-neutral-fill-6);
  --checkbox-default-checked-disabled-top-border-color: var(--colors-neutral-line-8);
  --checkbox-default-checked-disabled-right-border-color: var(--colors-neutral-line-8);
  --checkbox-default-checked-disabled-bottom-border-color: var(--colors-neutral-line-8);
  --checkbox-default-checked-disabled-left-border-color: var(--colors-neutral-line-8);
  --checkbox-default-checked-disabled-top-border-width: var(--borders-width-2);
  --checkbox-default-checked-disabled-right-border-width: var(--borders-width-2);
  --checkbox-default-checked-disabled-bottom-border-width: var(--borders-width-2);
  --checkbox-default-checked-disabled-left-border-width: var(--borders-width-2);
  --checkbox-default-checked-disabled-top-border-style: var(--borders-style-2);
  --checkbox-default-checked-disabled-right-border-style: var(--borders-style-2);
  --checkbox-default-checked-disabled-bottom-border-style: var(--borders-style-2);
  --checkbox-default-checked-disabled-left-border-style: var(--borders-style-2);
  --checkbox-default-checked-disabled-top-left-border-radius: var(--borders-radius-2);
  --checkbox-default-checked-disabled-top-right-border-radius: var(--borders-radius-2);
  --checkbox-default-checked-disabled-bottom-right-border-radius: var(--borders-radius-2);
  --checkbox-default-checked-disabled-bottom-left-border-radius: var(--borders-radius-2);
  --checkbox-button-default-height: var(--sizes-base-16);
  --checkbox-button-default-fontSize: var(--fonts-size-7);
  --checkbox-button-default-text-color: var(--colors-neutral-text-2);
  --checkbox-button-default-lineHeight: var(--fonts-lineHeight-2);
  --checkbox-button-default-paddingTop: var(--sizes-size-3);
  --checkbox-button-default-paddingRight: var(--sizes-size-7);
  --checkbox-button-default-paddingBottom: var(--sizes-size-3);
  --checkbox-button-default-paddingLeft: var(--sizes-size-7);
  --checkbox-button-default-top-left-border-radius: var(--borders-radius-3);
  --checkbox-button-default-top-right-border-radius: var(--borders-radius-3);
  --checkbox-button-default-bottom-right-border-radius: var(--borders-radius-3);
  --checkbox-button-default-bottom-left-border-radius: var(--borders-radius-3);
  --checkbox-button-default-top-border-color: var(--colors-neutral-line-8);
  --checkbox-button-default-right-border-color: var(--colors-neutral-line-8);
  --checkbox-button-default-bottom-border-color: var(--colors-neutral-line-8);
  --checkbox-button-default-left-border-color: var(--colors-neutral-line-8);
  --checkbox-button-default-top-border-width: var(--borders-width-2);
  --checkbox-button-default-right-border-width: var(--borders-width-2);
  --checkbox-button-default-bottom-border-width: var(--borders-width-2);
  --checkbox-button-default-left-border-width: var(--borders-width-2);
  --checkbox-button-default-top-border-style: var(--borders-style-2);
  --checkbox-button-default-right-border-style: var(--borders-style-2);
  --checkbox-button-default-bottom-border-style: var(--borders-style-2);
  --checkbox-button-default-left-border-style: var(--borders-style-2);
  --checkbox-button-hover-text-color: var(--colors-brand-5);
  --checkbox-button-hover-top-border-color: var(--colors-brand-5);
  --checkbox-button-hover-right-border-color: var(--colors-brand-5);
  --checkbox-button-hover-bottom-border-color: var(--colors-brand-5);
  --checkbox-button-hover-left-border-color: var(--colors-brand-5);
  --checkbox-button-checked-default-text-color: var(--colors-brand-5);
  --checkbox-button-checked-default-icon-bg-size: var(--sizes-size-9);
  --checkbox-button-checked-default-icon-bg-color: var(--colors-brand-5);
  --checkbox-button-checked-default-icon-size: var(--sizes-size-8);
  --checkbox-button-checked-default-icon-color: var(--colors-neutral-fill-11);
  --checkbox-button-checked-default-top-border-color: var(--colors-brand-5);
  --checkbox-button-checked-default-right-border-color: var(--colors-brand-5);
  --checkbox-button-checked-default-bottom-border-color: var(--colors-brand-5);
  --checkbox-button-checked-default-left-border-color: var(--colors-brand-5);
  --checkbox-button-checked-hover-text-color: var(--colors-brand-6);
  --checkbox-button-checked-hover-icon-bg-size: var(--sizes-size-9);
  --checkbox-button-checked-hover-icon-bg-color: var(--colors-brand-6);
  --checkbox-button-checked-hover-icon-size: var(--sizes-size-8);
  --checkbox-button-checked-hover-icon-color: var(--colors-neutral-fill-11);
  --checkbox-button-checked-hover-top-border-color: var(--colors-brand-6);
  --checkbox-button-checked-hover-right-border-color: var(--colors-brand-6);
  --checkbox-button-checked-hover-bottom-border-color: var(--colors-brand-6);
  --checkbox-button-checked-hover-left-border-color: var(--colors-brand-6);
  --checkbox-button-checked-active-text-color: var(--colors-brand-4);
  --checkbox-button-checked-active-icon-bg-size: var(--sizes-size-9);
  --checkbox-button-checked-active-icon-bg-color: var(--colors-brand-4);
  --checkbox-button-checked-active-icon-size: var(--sizes-size-8);
  --checkbox-button-checked-active-icon-color: var(--colors-neutral-fill-11);
  --checkbox-button-checked-active-top-border-color: var(--colors-brand-4);
  --checkbox-button-checked-active-right-border-color: var(--colors-brand-4);
  --checkbox-button-checked-active-bottom-border-color: var(--colors-brand-4);
  --checkbox-button-checked-active-left-border-color: var(--colors-brand-4);
  --checkbox-button-disabled-text-color: var(--colors-neutral-text-6);
  --checkbox-button-disabled-bg-color: var(--colors-neutral-fill-10);
  --checkbox-button-disabled-top-border-color: var(--colors-neutral-line-8);
  --checkbox-button-disabled-right-border-color: var(--colors-neutral-line-8);
  --checkbox-button-disabled-bottom-border-color: var(--colors-neutral-line-8);
  --checkbox-button-disabled-left-border-color: var(--colors-neutral-line-8);
  --checkbox-button-checked-disabled-text-color: var(--colors-neutral-text-5);
  --checkbox-button-checked-disabled-bg-color: var(--colors-neutral-fill-8);
  --checkbox-button-checked-disabled-icon-bg-size: var(--sizes-size-9);
  --checkbox-button-checked-disabled-icon-bg-color: var(--colors-neutral-fill-6);
  --checkbox-button-checked-disabled-icon-size: var(--sizes-size-8);
  --checkbox-button-checked-disabled-icon-color: var(--colors-neutral-fill-11);
  --checkbox-button-checked-disabled-top-border-color: var(--colors-neutral-line-7);
  --checkbox-button-checked-disabled-right-border-color: var(--colors-neutral-line-7);
  --checkbox-button-checked-disabled-bottom-border-color: var(--colors-neutral-line-7);
  --checkbox-button-checked-disabled-left-border-color: var(--colors-neutral-line-7);
  --checkbox-default-sm-size: var(--sizes-size-8);
  --checkbox-default-sm-icon-size: var(--sizes-size-7);
  --checkbox-default-partial-default-icon: '<svg viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" ><path d="M164.571429 424.96m87.04 0l520.777142 0q87.04 0 87.04 87.04l0 0q0 87.04-87.04 87.04l-520.777142 0q-87.04 0-87.04-87.04l0 0q0-87.04 87.04-87.04Z" fill="currentColor"></path></svg>';
  --Checkbox-onHover-color: var(--checkbox-checked-default-bg-color);
  --Checkbox-onDisabled-bg: var(--colors-neutral-fill-10);
  --Checkbox-size: var(--checkbox-checkbox-default-height);
  --Checkbox-borderRadius: 0.125rem;
  --Checkbox-color: var(--borderColor);
  --Checkbox-gap: var(--gap-xs);
  --Checkbox-gb: var(--colors-neutral-fill-11);
  --Checkbox-inner-size: var(--sizes-size-5);
  --Checkbox-onDisabled-color: var(--colors-neutral-text-6);
  --Checkbox-inner-onDisabled-bg: #d4d6d9;
  --Checkbox-inner-onDisabled-color: #ffffff;
  --Checkbox-disabled-unchecked-bg: #f7f7f9;
  --Checkbox-inner-disabled-checked-bg: #e8e9eb;
  --Checkbox-border-width: var(--Form-input-borderWidth);
  --Checkbox-paddingX: 0.75rem;
  --Checkbox-button-height: 2rem;
  --Checkbox-button-line-height: 1.75rem;
  --Checkbox-button-min-width: 5rem;
  --inputTree-base-default-color: #606266;
  --inputTree-base-default-expandColor: var(--default-icon-color);
  --inputTree-base-hover-color: #606266;
  --inputTree-base-hover-expandColor: var(--default-icon-color);
  --inputTree-base-active-color: #606266;
  --inputTree-base-active-expandColor: var(--default-icon-color);
  --inputTree-base-disabled-color: var(--colors-neutral-text-6);
  --inputTree-base-disabled-expandColor: var(--default-icon-color);
  --inputTree-base-size-expandMarginRight: var(--sizes-size-5);
  --inputTree-base-size-nodeMarginRight: var(--sizes-size-5);
  --inputTree-checkboxes-size-marginRight: var(--sizes-size-5);
  --inputTree-border-color: var(--colors-neutral-line-8);
  --inputTree-border-radius: var(--borders-radius-3);
  --inputTree-input-lineHeight: var(--fonts-lineHeight-2);
  --inputTree-item-disabled-color: var(--colors-neutral-text-6);
  --inputTree-fontSize: var(--fontSizeBase);
  --inputTree-placeholder-color: var(--colors-neutral-text-6);
  --listSelect-base-default-top-border-color: var(--colors-neutral-line-8);
  --listSelect-base-default-top-border-width: var(--borders-width-2);
  --listSelect-base-default-top-border-style: var(--borders-style-2);
  --listSelect-base-default-right-border-color: var(--colors-neutral-line-8);
  --listSelect-base-default-right-border-width: var(--borders-width-2);
  --listSelect-base-default-right-border-style: var(--borders-style-2);
  --listSelect-base-default-bottom-border-color: var(--colors-neutral-line-8);
  --listSelect-base-default-bottom-border-width: var(--borders-width-2);
  --listSelect-base-default-bottom-border-style: var(--borders-style-2);
  --listSelect-base-default-left-border-color: var(--colors-neutral-line-8);
  --listSelect-base-default-left-border-width: var(--borders-width-2);
  --listSelect-base-default-left-border-style: var(--borders-style-2);
  --listSelect-base-default-top-right-border-radius: var(--borders-radius-3);
  --listSelect-base-default-top-left-border-radius: var(--borders-radius-3);
  --listSelect-base-default-bottom-right-border-radius: var(--borders-radius-3);
  --listSelect-base-default-bottom-left-border-radius: var(--borders-radius-3);
  --listSelect-base-default-paddingTop: var(--sizes-size-4);
  --listSelect-base-default-paddingBottom: var(--sizes-size-4);
  --listSelect-base-default-paddingLeft: var(--sizes-size-6);
  --listSelect-base-default-paddingRight: var(--sizes-size-6);
  --listSelect-base-default-color: var(--colors-neutral-text-2);
  --listSelect-base-default-bg-color: var(--colors-neutral-fill-11);
  --listSelect-base-hover-top-border-color: var(--colors-brand-5);
  --listSelect-base-hover-top-border-width: var(--borders-width-2);
  --listSelect-base-hover-top-border-style: var(--borders-style-2);
  --listSelect-base-hover-right-border-color: var(--colors-brand-5);
  --listSelect-base-hover-right-border-width: var(--borders-width-2);
  --listSelect-base-hover-right-border-style: var(--borders-style-2);
  --listSelect-base-hover-bottom-border-color: var(--colors-brand-5);
  --listSelect-base-hover-bottom-border-width: var(--borders-width-2);
  --listSelect-base-hover-bottom-border-style: var(--borders-style-2);
  --listSelect-base-hover-left-border-color: var(--colors-brand-5);
  --listSelect-base-hover-left-border-width: var(--borders-width-2);
  --listSelect-base-hover-left-border-style: var(--borders-style-2);
  --listSelect-base-hover-top-right-border-radius: var(--borders-radius-3);
  --listSelect-base-hover-top-left-border-radius: var(--borders-radius-3);
  --listSelect-base-hover-bottom-right-border-radius: var(--borders-radius-3);
  --listSelect-base-hover-bottom-left-border-radius: var(--borders-radius-3);
  --listSelect-base-hover-paddingTop: var(--sizes-size-3);
  --listSelect-base-hover-paddingBottom: var(--sizes-size-3);
  --listSelect-base-hover-paddingLeft: var(--sizes-size-7);
  --listSelect-base-hover-paddingRight: var(--sizes-size-7);
  --listSelect-base-hover-color: var(--colors-brand-5);
  --listSelect-base-hover-bg-color: var(--colors-neutral-fill-11);
  --listSelect-base-active-top-border-color: var(--colors-brand-5);
  --listSelect-base-active-top-border-width: var(--borders-width-2);
  --listSelect-base-active-top-border-style: var(--borders-style-2);
  --listSelect-base-active-right-border-color: var(--colors-brand-5);
  --listSelect-base-active-right-border-width: var(--borders-width-2);
  --listSelect-base-active-right-border-style: var(--borders-style-2);
  --listSelect-base-active-bottom-border-color: var(--colors-brand-5);
  --listSelect-base-active-bottom-border-width: var(--borders-width-2);
  --listSelect-base-active-bottom-border-style: var(--borders-style-2);
  --listSelect-base-active-left-border-color: var(--colors-brand-5);
  --listSelect-base-active-left-border-width: var(--borders-width-2);
  --listSelect-base-active-left-border-style: var(--borders-style-2);
  --listSelect-base-active-top-right-border-radius: var(--borders-radius-3);
  --listSelect-base-active-top-left-border-radius: var(--borders-radius-3);
  --listSelect-base-active-bottom-right-border-radius: var(--borders-radius-3);
  --listSelect-base-active-bottom-left-border-radius: var(--borders-radius-3);
  --listSelect-base-active-paddingTop: var(--sizes-size-3);
  --listSelect-base-active-paddingBottom: var(--sizes-size-3);
  --listSelect-base-active-paddingLeft: var(--sizes-size-7);
  --listSelect-base-active-paddingRight: var(--sizes-size-7);
  --listSelect-base-active-shadow: var(--Form-input-boxShadow);
  --listSelect-base-active-color: var(--colors-brand-5);
  --listSelect-base-active-bg-color: var(--colors-neutral-fill-11);
  --listSelect-base-disabled-top-border-color: var(--colors-neutral-line-8);
  --listSelect-base-disabled-top-border-width: var(--borders-width-2);
  --listSelect-base-disabled-top-border-style: var(--borders-style-2);
  --listSelect-base-disabled-right-border-color: var(--colors-neutral-line-8);
  --listSelect-base-disabled-right-border-width: var(--borders-width-2);
  --listSelect-base-disabled-right-border-style: var(--borders-style-2);
  --listSelect-base-disabled-bottom-border-color: var(--colors-neutral-line-8);
  --listSelect-base-disabled-bottom-border-width: var(--borders-width-2);
  --listSelect-base-disabled-bottom-border-style: var(--borders-style-2);
  --listSelect-base-disabled-left-border-color: var(--colors-neutral-line-8);
  --listSelect-base-disabled-left-border-width: var(--borders-width-2);
  --listSelect-base-disabled-left-border-style: var(--borders-style-2);
  --listSelect-base-disabled-top-right-border-radius: var(--borders-radius-3);
  --listSelect-base-disabled-top-left-border-radius: var(--borders-radius-3);
  --listSelect-base-disabled-bottom-right-border-radius: var(--borders-radius-3);
  --listSelect-base-disabled-bottom-left-border-radius: var(--borders-radius-3);
  --listSelect-base-disabled-paddingTop: var(--sizes-size-3);
  --listSelect-base-disabled-paddingBottom: var(--sizes-size-3);
  --listSelect-base-disabled-paddingLeft: var(--sizes-size-7);
  --listSelect-base-disabled-paddingRight: var(--sizes-size-7);
  --listSelect-base-disabled-color: var(--colors-neutral-text-6);
  --listSelect-base-disabled-bg-color: var(--colors-neutral-fill-10);
  --listSelect-base-image-width: var(--sizes-size-1);
  --ListControl-item-onHover-color: var(--listSelect-base-hover-color);
  --ListControl-item-onHover-borderColor: var(--listSelect-base-hover-top-border-color);
  --ListControl-item-onActive-color: var(--listSelect-base-active-color);
  --ListControl-item-onActive-onHover-bg: var(--colors-neutral-fill-11);
  --ListControl-item-onActive-color: var(--listSelect-base-active-top-border-color);
  --ListControl-item-onActive-before-bg: var(--colors-brand-5);
  --ListControl-item-color: var(--listSelect-base-default-color);
  --ListControl-item-onDisabled-color: var(--listSelect-base-disabled-color);
  --ListControl-item-paddingX: var(--listSelect-base-default-paddingLeft);
  --ListControl-item-paddingY: var(--listSelect-base-default-paddingTop);
  --link-onClick-color: var(--colors-link-4);
  --link-onClick-fontSize: var(--fonts-size-7);
  --link-onClick-font-style: none;
  --link-onClick-fontWeight: var(--fonts-weight-6);
  --link-onClick-text-decoration: none;
  --link-onClick-bg-color: transparent;
  --link-onHover-color: var(--colors-link-6);
  --link-onHover-fontSize: var(--fonts-size-7);
  --link-onHover-font-style: none;
  --link-onHover-fontWeight: var(--fonts-weight-6);
  --link-onHover-text-decoration: none;
  --link-onHover-bg-color: transparent;
  --link-color: var(--colors-link-5);
  --link-fontSize: var(--fonts-size-7);
  --link-font-style: none;
  --link-fontWeight: var(--fonts-weight-6);
  --link-text-decoration: none;
  --link-bg-color: transparent;
  --link-disabled-color: var(--colors-neutral-text-6);
  --link-disabled-fontSize: var(--fonts-size-7);
  --link-disabled-font-style: none;
  --link-disabled-fontWeight: var(--fonts-weight-6);
  --link-disabled-text-decoration: none;
  --link-disabled-bg-color: transparent;
  --link-icon-size: var(--sizes-size-8);
  --link-icon-margin: var(--sizes-size-3);
  --link-decoration: var(--link-text-decoration);
  --link-onHover-decoration: var(--link-onClick-text-decoration);
  --Form-item-gap: var(--sizes-base-12);
  --Form-item-mobile-gap: var(--sizes-base-4);
  --Form-item-color: #606266;
  --Form-item-fontSize: var(--fonts-size-7);
  --Form-item-fontWeight: var(--fonts-weight-6);
  --Form-item-lineHeight: var(--fonts-lineHeight-1);
  --Form-item-star-color: var(--colors-error-5);
  --Form-item-star-size: var(--sizes-size-7);
  --Form-description-color: var(--colors-neutral-text-5);
  --Form-description-fontSize: var(--fonts-size-8);
  --Form-description-fontWeight: var(--fonts-weight-6);
  --Form-description-lineHeight: var(--fonts-lineHeight-2);
  --Form-description-gap: var(--sizes-size-3);
  --Form-item-onError-color: var(--colors-neutral-text-4);
  --Form-item-onError-borderColor: var(--colors-error-5);
  --Form-item-onError-bg: var(--colors-neutral-fill-11);
  --Form-feedBack-color: var(--colors-error-5);
  --Form-feedBack-fontSize: var(--fonts-size-8);
  --Form-feedBack-fontWeight: var(--fonts-weight-6);
  --Form-feedBack-lineHeight: var(--fonts-lineHeight-2);
  --Form-feedBack-gap: var(--sizes-size-3);
  --Form-mode-default-labelGap: var(--sizes-size-5);
  --Form-mode-default-width: 100%;
  --Form--horizontal-label-gap: var(--sizes-base-8);
  --Form--horizontal-label-widthBase: var(--sizes-base-49);
  --Form--horizontal-label-widthXs: var(--sizes-base-25);
  --Form--horizontal-label-widthSm: var(--sizes-base-35);
  --Form--horizontal-label-widthMd: 8.5rem;
  --Form--horizontal-label-widthLg: 12.5rem;
  --Form--horizontal-value-marginTop: var(--sizes-size-0);
  --Form--horizontal-value-marginBottom: var(--sizes-size-0);
  --Form--horizontal-value-marginLeft: var(--sizes-size-5);
  --Form--horizontal-value-marginRight: var(--sizes-size-0);
  --Form--horizontal-value-maxWidth: 100%;
  --Form--horizontal-value-minWidth: var(--sizes-size-0);
  --Form--horizontal-value-minWidth: var(--sizes-size-0);
  --Form-mode-inline-item-gap: var(--sizes-base-8);
  --Form-mode-inline-label-gap: var(--sizes-base-8);
  --InputRange-track-bg: var(--colors-neutral-fill-8);
  --InputRange-track-height: var(--sizes-size-4);
  --InputRange-track-border-radius: var(--sizes-size-3);
  --InputRange-track-onActive-bg: var(--colors-brand-5);
  --InputRange-handle-height: var(--sizes-size-9);
  --InputRange-handle-width: var(--sizes-size-9);
  --InputRange-handle-mobile-height: var(--sizes-base-10);
  --InputRange-handle-mobile-width: var(--sizes-base-10);
  --InputRange-handle-bg: var(--colors-neutral-fill-11);
  --InputRange-handle-top-border-color: var(--colors-brand-5);
  --InputRange-handle-top-border-width: 0.0625rem;
  --InputRange-handle-top-border-style: var(--borders-style-2);
  --InputRange-handle-top-right-border-radius: var(--borders-radius-7);
  --InputRange-handle-border: var(--InputRange-handle-top-border-width)
    var(--InputRange-handle-top-border-style) var(--InputRange-handle-top-border-color);
  --InputRange-handle-border-radius: var(--InputRange-handle-top-right-border-radius);
  --InputRange-handle-onActive-transform: scale(1.3);
  --InputRange-handle-onDrage-border-width: 0.125rem;
  --InputRange-handle-onFocus-borderRadius: var(--InputRange-handle-border-radius);
  --InputRange-padding: 1rem;
  --InputRange-handle-icon-width: var(--sizes-size-5);
  --InputRange-handle-icon-height: var(--sizes-size-5);
  --InputRange-handle-icon-color: var(--colors-brand-9);
  --InputRange-track-onActive-onDisabled-bg: var(--colors-neutral-fill-6);
  --InputRange-handle-onDisabled-border-color: var(--colors-neutral-fill-7);
  --InputRange-handle-onDisabled-bg: var(--colors-neutral-fill-11);
  --InputRange-handle-icon-onDisabled-color: var(--colors-neutral-fill-7);
  --InputRange-track-transition:
    left var(--animation-duration) ease-out, width var(--animation-duration) ease-out;
  --InputRange-handle-transition: transform var(--animation-duration) ease-out;
  --InputRange-track-dot-height: var(--sizes-size-4);
  --InputRange-track-dot-width: var(--sizes-size-4);
  --InputRange-track-dot-bg: var(--colors-neutral-fill-11);
  --InputRange-marks-color: var(--colors-neutral-text-2);
  --InputRange-marks-fontSize: var(--fonts-size-7);
  --InputRange-marks-fontWeight: var(--fonts-weight-6);
  --InputRange-marks-lineHeight: var(--fonts-lineHeight-2);
  --InputRange-marks-marginTop: var(--sizes-size-0);
  --InputRange-label-color: var(--colors-neutral-fill-11);
  --InputRange-label-fontSize: var(--fonts-size-7);
  --InputRange-label-font-size: var(--InputRange-label-fontSize);
  --InputRange-label-fontWeight: var(--fonts-weight-6);
  --InputRange-label-lineHeight: var(--fonts-lineHeight-2);
  --InputRange-label-paddingTop: var(--sizes-size-5);
  --InputRange-label-paddingBottom: var(--sizes-size-5);
  --InputRange-label-paddingLeft: var(--sizes-size-5);
  --InputRange-label-paddingRight: var(--sizes-size-5);
  --InputRange-label-bg: var(--colors-neutral-fill-1);
  --InputRange-label-top-right-border-radius: var(--borders-radius-3);
  --InputRange-label-padding: var(--InputRange-label-paddingTop)
    var(--InputRange-label-paddingRight) var(--InputRange-label-paddingBottom)
    var(--InputRange-label-paddingLeft);
  --InputRange-label-border-radius: var(--InputRange-label-top-right-border-radius);
  --InputRange-label-position-bottom: calc(100% + 8px);
  --InputRange-input-width: var(--sizes-base-40);
  --InputRange-input-mobile-width: var(--sizes-base-20);
  --InputRange-input-marginTop: var(--sizes-size-0);
  --InputRange-input-marginBottom: var(--sizes-size-0);
  --InputRange-input-marginLeft: var(--sizes-size-5);
  --InputRange-input-marginRight: var(--sizes-size-5);
  --InputRange-clearIcon-height: var(--sizes-size-7);
  --InputRange-clearIcon-width: var(--sizes-size-7);
  --InputRange-clearIcon-color: var(--colors-neutral-text-4);
  --InputRange-clearIcon-hoverColor: var(--colors-brand-5);
  --Progress-line-bg: var(--colors-neutral-fill-8);
  --Progress-line-theme-color: var(--colors-brand-5);
  --Progress-line-color: var(--colors-neutral-text-2);
  --Progress-line-fontSize: var(--fonts-size-8);
  --Progress-circle-bg: var(--colors-neutral-fill-8);
  --Progress-circle-theme-color: red;
  --Progress-circle-color: var(--colors-neutral-text-2);
  --Progress-circle-fontSize: var(--fonts-size-8);
  --Progress-borderRadius: var(--borderRadius);
  --Progress-animate-backgroundColor: #fff;
  --Progress-bar-backgroundColor: var(--colors-neutral-fill-8);
  --dialog-default-border-width: var(--sizes-size-0);
  --dialog-default-border-radius: var(--sizes-size-4);
  --dialog-default-padding-y: var(--sizes-base-12);
  --dialog-header-height: var(--sizes-size-1);
  --dialog-icon-icon: '<svg viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" class="icon icon-close"><g id="close" transform="translate(1.439340, 1.439340)" fill="currentColor" fill-rule="nonzero"><polygon points="12.0606602 5.80646642e-14 13.1213203 1.06066017 7.62066017 6.56066017 13.1213203 12.0606602 12.0606602 13.1213203 6.56066017 7.62066017 1.06066017 13.1213203 0 12.0606602 5.49966017 6.56066017 1.72084569e-13 1.06066017 1.06066017 0 6.56066017 5.49966017"></polygon></g></svg>';
  --dialog-icon-size: var(--sizes-size-9);
  --dialog-icon-color: var(--colors-neutral-text-6);
  --dialog-header-color: var(--colors-neutral-text-2);
  --dialog-header-fontSize: var(--fonts-size-7);
  --dialog-header-fontWeight: var(--fonts-weight-5);
  --dialog-content-fontSize: var(--sizes-size-8);
  --dialog-content-color: var(--colors-neutral-text-2);
  --dialog-footer-height: var(--sizes-size-1);
  --dialog-footer-margin-left: var(--sizes-size-5);
  --dialog-size-sm-width: 21.875rem;
  --dialog-size-normal-width: 31.25rem;
  --dialog-size-lg-width: 68.75rem;
  --dialog-size-xl-width: 90%;
  --Modal-bg: var(--background);
  --Modal-body--noHeader-paddingTop: var(--gap-base);
  --Modal-body-borderBottom: var(--borders-style-1) solid var(--colors-neutral-line-10);
  --Modal-body-borderTop: var(--borders-style-1) solid var(--colors-neutral-line-10);
  --Modal-body-paddingX: var(--sizes-size-0);
  --Modal-body-paddingY: var(--sizes-base-12);
  --Modal-close-color: var(--text--muted-color);
  --Modal-close-width: 1rem;
  --Modal-content-borderColor: var(--colors-neutral-fill-9);
  --Modal-content-borderRadius: var(--borders-radius-4);
  --Modal-content-borderWidth: var(--borders-width-2);
  --Modal-content-minHeight: 12.0625rem;
  --Modal-content-startMarginTop: 3.75rem;
  --Modal-content-stepMarginTop: 1.875rem;
  --Modal-footer-button-width: 4.5rem;
  --Modal-footer-marginX: var(--sizes-size-0);
  --Modal-footer-marginY: var(--sizes-size-0);
  --Modal-footer-padding: var(--sizes-size-0);
  --Modal-header-bg: var(--colors-neutral-fill-11);
  --Modal-header-height: 2.5rem;
  --Modal-header-paddingX: var(--sizes-size-0);
  --Modal-header-paddingY: var(--sizes-size-0);
  --Modal-overlay-bg: rgba(0, 0, 0, 0.7);
  --Modal-title-color: var(--colors-neutral-text-2);
  --Modal-title-fontSize: var(--fonts-size-7);
  --Modal-title-fontWeight: var(--fonts-weight-5);
  --Modal-title-lineHeight: var(--lineHeightBase);
  --Modal-widthBase: 31.25rem;
  --Modal-widthLg: 68.75rem;
  --Modal-widthMd: 50rem;
  --Modal-widthSm: 21.875rem;
  --Modal-widthXl: 90%;
  --Model-close-onHover-color: var(--text-color);
  --drawer-header-height: var(--sizes-size-1);
  --drawer-header-color: var(--colors-neutral-text-2);
  --drawer-header-fontSize: var(--fonts-size-7);
  --drawer-header-fontWeight: var(--fonts-weight-5);
  --drawer-header-icon-icon: '<svg viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" class=" icon-close"><g id="close" transform="translate(1.439340, 1.439340)" fill="currentColor" fill-rule="nonzero"><polygon points="12.0606602 5.80646642e-14 13.1213203 1.06066017 7.62066017 6.56066017 13.1213203 12.0606602 12.0606602 13.1213203 6.56066017 7.62066017 1.06066017 13.1213203 0 12.0606602 5.49966017 6.56066017 1.72084569e-13 1.06066017 1.06066017 0 6.56066017 5.49966017"></polygon></g></svg>';
  --drawer-header-icon-size: var(--sizes-size-7);
  --drawer-header-icon-color: var(--colors-neutral-text-2);
  --drawer-content-paddingTop: var(--sizes-base-12);
  --drawer-content-paddingBottom: var(--sizes-base-12);
  --drawer-content-paddingLeft: var(--sizes-base-12);
  --drawer-content-paddingRight: var(--sizes-base-12);
  --drawer-footer-height: var(--sizes-size-1);
  --drawer-footer-margin-left: var(--sizes-size-9);
  --drawer-size-xs-width: 12.5rem;
  --drawer-size-sm-width: 18.75rem;
  --drawer-size-md-width: 31.25rem;
  --drawer-size-lg-width: 50rem;
  --drawer-size-xl-width: 90%;
  --Drawer-bg: var(--background);
  --Drawer-body-padding: var(--sizes-base-12);
  --Drawer-footer-margin: var(--sizes-size-9);
  --Drawer-close-color: var(--colors-neutral-text-2);
  --Drawer-close-onHover-color: var(--text-color);
  --Drawer-close-size: 0.75rem;
  --Drawer-content-borderColor: var(--borderColor);
  --Drawer-content-borderRadius: 0;
  --Drawer-content-borderWidth: var(--borderWidth);
  --Drawer-footer-borderColor: var(--colors-neutral-line-10);
  --Drawer-footer-padding: var(--gap-base);
  --Drawer-header-bg: var(--colors-neutral-fill-11);
  --Drawer-header-borderColor: var(--colors-neutral-line-10);
  --Drawer-header-padding: var(--sizes-size-9) var(--sizes-base-12);
  --Drawer-overlay-bg: rgba(0, 0, 0, 0.6);
  --Drawer-title-fontColor: var(--colors-neutral-text-2);
  --Drawer-title-fontSize: var(--fontSizeMd);
  --Drawer-widthBase: 25rem;
  --Drawer-widthLg: 50rem;
  --Drawer-widthMd: 31.25rem;
  --Drawer-widthSm: 18.75rem;
  --Drawer-widthXl: 90%;
  --Drawer-widthXs: 12.5rem;
  --Rating-star-margin: var(--sizes-size-5);
  --Rating-star-size: var(--sizes-base-12);
  --Rating-star-icon-size: var(--sizes-base-9);
  --Rating-star-icon: '<svg class="icon" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><path fill="currentColor" d="M4.99672493,4.37617062 L1.28100904,4.93702533 C0.463702827,5.06039054 -0.102812689,5.85030962 0.0156612291,6.70135899 C0.0628630935,7.04043042 0.216147743,7.35382434 0.451761131,7.59297508 L3.14101949,10.3226082 L3.14101949,10.3226082 L2.50531937,14.1787855 C2.3655991,15.0263332 2.91216489,15.8313483 3.72610824,15.9768371 C4.05030943,16.0347866 4.38381497,15.9798039 4.67496871,15.8204054 L7.99934906,14.0003997 L7.99934906,14.0003997 L11.3254267,15.8208814 C12.0564401,16.2209912 12.9605363,15.9282748 13.3447823,15.167081 C13.4978067,14.8639388 13.5505833,14.5167196 13.4949403,14.1791872 L12.859174,10.3226082 L12.859174,10.3226082 L15.5482634,7.59400813 C16.1397301,6.99385103 16.1519752,6.00805341 15.5756136,5.39216751 C15.3460036,5.14681192 15.0450939,4.98715562 14.7195072,4.93793566 L11.0034685,4.37617062 L11.0034685,4.37617062 L9.34151455,0.868493275 C8.97611971,0.0973002947 8.07952072,-0.219434766 7.33890469,0.161045701 C7.04395753,0.312570398 6.80521255,0.561123051 6.65963735,0.868217393 L4.99672493,4.37617062 L4.99672493,4.37617062 Z"></path></svg>';
  --Rating-colors: '[{"value":"#abadb1","id":2},{"value":"#787b81","id":3},{"value":"#ffa900","id":5}]';
  --Rating-text-color: var(--colors-neutral-text-2);
  --Rating-text-fontSize: var(--fonts-size-7);
  --Rating-text-fontWeight: var(--fonts-weight-6);
  --Rating-text-lineHeight: var(--fonts-lineHeight-2);
  --Rating-inactive-color: var(--colors-neutral-text-9);
  --radio-default-default-border-color: var(--colors-neutral-text-8);
  --radio-default-default-text-color: var(--colors-neutral-text-2);
  --radio-default-default-bg-color: var(--colors-neutral-fill-11);
  --radio-default-default-fontSize: var(--fonts-size-7);
  --radio-default-default-fontWeight: var(--fonts-weight-6);
  --radio-default-default-lineHeight: var(--fonts-lineHeight-2);
  --radio-default-default-point-size: var(--sizes-size-9);
  --radio-default-default-distance: var(--sizes-size-5);
  --radio-default-default-marginTop: var(--sizes-size-4);
  --radio-default-default-marginRight: var(--sizes-size-9);
  --radio-default-default-marginBottom: var(--sizes-size-0);
  --radio-default-default-marginLeft: var(--sizes-size-0);
  --radio-default-hover-text-color: var(--colors-neutral-text-2);
  --radio-default-hover-bg-color: var(--colors-neutral-fill-11);
  --radio-default-hover-border-color: var(--colors-brand-5);
  --radio-default-checked-icon-size: var(--sizes-size-5);
  --radio-default-checked-icon-color: var(--colors-brand-5);
  --radio-default-checked-icon: '<svg viewBox="0 0 1024 1024"><path d="M512 512m-512 0a512 512 0 1 0 1024 0 512 512 0 1 0-1024 0Z" fill="currentColor"></path></svg>';
  --radio-default-checked-text-color: var(--colors-neutral-text-2);
  --radio-default-checked-bg-color: var(--colors-neutral-fill-11);
  --radio-default-checked-border-color: var(--colors-brand-5);
  --radio-default-disabled-border-color: var(--colors-neutral-line-8);
  --radio-default-disabled-icon-color: var(--colors-neutral-fill-7);
  --radio-default-disabled-text-color: var(--colors-neutral-text-6);
  --radio-default-disabled-bg-color: var(--colors-neutral-fill-10);
  --radio-default-vertical-marginTop: var(--sizes-size-4);
  --radio-default-vertical-marginRight: var(--sizes-size-5);
  --radio-default-vertical-marginBottom: var(--sizes-size-0);
  --radio-default-vertical-marginLeft: var(--sizes-size-0);
  --radio-button-default-height: var(--sizes-base-16);
  --radio-button-default-fontSize: var(--fonts-size-7);
  --radio-button-default-text-color: var(--colors-neutral-text-2);
  --radio-button-default-lineHeight: var(--fonts-lineHeight-2);
  --radio-button-default-paddingTop: var(--sizes-size-3);
  --radio-button-default-paddingRight: var(--sizes-size-7);
  --radio-button-default-paddingBottom: var(--sizes-size-3);
  --radio-button-default-paddingLeft: var(--sizes-size-7);
  --radio-button-default-top-left-border-radius: var(--borders-radius-3);
  --radio-button-default-top-right-border-radius: var(--borders-radius-3);
  --radio-button-default-bottom-right-border-radius: var(--borders-radius-3);
  --radio-button-default-bottom-left-border-radius: var(--borders-radius-3);
  --radio-button-default-top-border-color: var(--colors-neutral-line-8);
  --radio-button-default-right-border-color: var(--colors-neutral-line-8);
  --radio-button-default-bottom-border-color: var(--colors-neutral-line-8);
  --radio-button-default-left-border-color: var(--colors-neutral-line-8);
  --radio-button-default-top-border-width: var(--borders-width-2);
  --radio-button-default-right-border-width: var(--borders-width-2);
  --radio-button-default-bottom-border-width: var(--borders-width-2);
  --radio-button-default-left-border-width: var(--borders-width-2);
  --radio-button-default-top-border-style: var(--borders-style-2);
  --radio-button-default-right-border-style: var(--borders-style-2);
  --radio-button-default-bottom-border-style: var(--borders-style-2);
  --radio-button-default-left-border-style: var(--borders-style-2);
  --radio-button-hover-text-color: var(--colors-brand-5);
  --radio-button-hover-top-border-color: var(--colors-brand-5);
  --radio-button-hover-right-border-color: var(--colors-brand-5);
  --radio-button-hover-bottom-border-color: var(--colors-brand-5);
  --radio-button-hover-left-border-color: var(--colors-brand-5);
  --radio-button-checked-text-color: var(--colors-neutral-text-11);
  --radio-button-checked-bg-color: var(--colors-brand-5);
  --radio-button-checked-top-border-color: var(--colors-brand-5);
  --radio-button-checked-right-border-color: var(--colors-brand-5);
  --radio-button-checked-bottom-border-color: var(--colors-brand-5);
  --radio-button-checked-left-border-color: var(--colors-brand-5);
  --radio-button-disabled-unchecked-text-color: var(--colors-neutral-text-6);
  --radio-button-disabled-unchecked-bg-color: var(--colors-neutral-fill-10);
  --radio-button-disabled-unchecked-top-border-color: var(--colors-neutral-line-8);
  --radio-button-disabled-unchecked-right-border-color: var(--colors-neutral-line-8);
  --radio-button-disabled-unchecked-bottom-border-color: var(--colors-neutral-line-8);
  --radio-button-disabled-unchecked-left-border-color: var(--colors-neutral-line-8);
  --radio-button-disabled-checked-text-color: var(--colors-neutral-text-5);
  --radio-button-disabled-checked-bg-color: var(--colors-neutral-fill-8);
  --radio-button-disabled-checked-top-border-color: var(--colors-neutral-line-7);
  --radio-button-disabled-checked-right-border-color: var(--colors-neutral-line-7);
  --radio-button-disabled-checked-bottom-border-color: var(--colors-neutral-line-7);
  --radio-button-disabled-checked-left-border-color: var(--colors-neutral-line-7);
  --switch-default-off-bg-color: var(--colors-neutral-fill-7);
  --switch-default-off-hover-bg-color: var(--colors-neutral-fill-6);
  --switch-default-off-slider-color: var(--colors-neutral-fill-11);
  --switch-default-on-bg-color: var(--colors-brand-5);
  --switch-default-on-hover-bg-color: var(--colors-brand-4);
  --switch-default-on-slider-color: var(--colors-neutral-fill-11);
  --switch-option-fontSize: var(--fonts-size-7);
  --switch-option-fontWeight: var(--fonts-weight-6);
  --switch-option-lineHeight: var(--fonts-lineHeight-2);
  --switch-option-color: var(--colors-neutral-text-2);
  --switch-option-marginTop: var(--sizes-size-0);
  --switch-option-marginBottom: var(--sizes-size-0);
  --switch-option-marginLeft: var(--sizes-size-5);
  --switch-option-marginRight: var(--sizes-size-0);
  --switch-text-off-fontSize: var(--fonts-size-8);
  --switch-text-off-fontWeight: var(--fonts-weight-3);
  --switch-text-off-color: var(--colors-neutral-text-11);
  --switch-text-off-marginTop: var(--sizes-size-0);
  --switch-text-off-marginBottom: var(--sizes-size-0);
  --switch-text-off-marginLeft: var(--sizes-base-12);
  --switch-text-off-marginRight: var(--sizes-size-5);
  --switch-text-on-fontSize: var(--fonts-size-8);
  --switch-text-on-fontWeight: var(--fonts-weight-3);
  --switch-text-on-color: var(--colors-neutral-text-11);
  --switch-text-on-marginTop: var(--sizes-size-0);
  --switch-text-on-marginBottom: var(--sizes-size-0);
  --switch-text-on-marginLeft: var(--sizes-base-4);
  --switch-text-on-marginRight: var(--sizes-base-12);
  --switch-size-default-height: var(--sizes-base-10);
  --switch-size-default-minWidth: var(--sizes-base-22);
  --switch-size-default-slider-width: var(--sizes-size-9);
  --switch-size-default-slider-margin: var(--sizes-size-2);
  --switch-size-default-top-right-border-radius: var(--sizes-base-15);
  --switch-size-default-top-left-border-radius: var(--sizes-base-15);
  --switch-size-default-bottom-right-border-radius: var(--sizes-base-15);
  --switch-size-default-bottom-left-border-radius: var(--sizes-base-15);
  --switch-size-sm-height: var(--sizes-size-9);
  --switch-size-sm-minWidth: var(--sizes-base-14);
  --switch-size-sm-slider-width: var(--sizes-size-7);
  --switch-size-sm-slider-margin: var(--sizes-size-2);
  --switch-size-sm-top-right-border-radius: var(--sizes-base-15);
  --switch-size-sm-top-left-border-radius: var(--sizes-base-15);
  --switch-size-sm-bottom-right-border-radius: var(--sizes-base-15);
  --switch-size-sm-bottom-left-border-radius: var(--sizes-base-15);
  --Switch-bgColor: var(--switch-default-off-bg-color);
  --Switch-borderColor: var(--colors-neutral-line-6);
  --Switch-gap: var(--switch-option-marginLeft);
  --Switch-height: var(--switch-size-default-height);
  --Switch-onActive-bgColor: var(--switch-default-on-bg-color);
  --Switch-onDisabled-bgColor: var(--colors-brand-9);
  --Switch-onDisabled-circle-BackgroundColor: var(--colors-neutral-fill-11);
  --Switch-onDisabled-color: var(--colors-neutral-text-11);
  --Switch-onHover-bgColor: var(--switch-default-off-hover-bg-color);
  --Switch-valueColor: var(--switch-text-off-color);
  --Switch-width: var(--switch-size-default-minWidth);
  --Switch-slider-margin: var(--switch-size-default-slider-margin);
  --Switch-slider-width: var(--switch-size-default-slider-width);
  --Switch-slider-transition: all 0.5s ease;
  --Switch-text-marginRight: var(--switch-text-off-marginRight);
  --Switch-text-marginLeft: var(--switch-text-off-marginLeft);
  --Switch-width--sm: var(--switch-size-sm-minWidth);
  --Switch-height--sm: var(--switch-size-sm-height);
  --Switch-slider-width--sm: var(--switch-size-sm-slider-width);
  --Switch-text-marginRight--sm: var(--switch-text-off-marginRight);
  --Switch-text-marginLeft--sm: var(--switch-text-off-marginLeft);
  --Switch-checked-bgColor: var(--switch-default-on-bg-color);
  --Switch-checked-onHover-bgColor: var(--switch-default-on-hover-bg-color);
  --Switch-checked-onActive-bgColor: var(--colors-brand-4);
  --Switch-spinner-icon-width: var(--sizes-base-7);
  --Switch-spinner-icon-width--sm: var(--sizes-base-5);
  --switch-spinner-left--sm: var(--sizes-size-0);
  --collapse-default-top-border-color: var(--colors-neutral-line-8);
  --collapse-default-top-border-width: var(--borders-width-2);
  --collapse-default-top-border-style: var(--borders-style-2);
  --collapse-default-right-border-color: var(--colors-neutral-line-8);
  --collapse-default-right-border-width: var(--borders-width-2);
  --collapse-default-right-border-style: var(--borders-style-2);
  --collapse-default-bottom-border-color: var(--colors-neutral-line-8);
  --collapse-default-bottom-border-width: var(--borders-width-2);
  --collapse-default-bottom-border-style: var(--borders-style-2);
  --collapse-default-left-border-color: var(--colors-neutral-line-8);
  --collapse-default-left-border-width: var(--borders-width-2);
  --collapse-default-left-border-style: var(--borders-style-2);
  --collapse-default-top-right-border-radius: var(--borders-radius-3);
  --collapse-default-top-left-border-radius: var(--borders-radius-3);
  --collapse-default-bottom-right-border-radius: var(--borders-radius-3);
  --collapse-default-bottom-left-border-radius: var(--borders-radius-3);
  --collapse-default-header-paddingTop: var(--sizes-size-9);
  --collapse-default-header-paddingBottom: var(--sizes-size-9);
  --collapse-default-header-paddingLeft: var(--sizes-size-9);
  --collapse-default-header-paddingRight: var(--sizes-size-9);
  --collapse-default-header-color: var(--colors-neutral-text-2);
  --collapse-default-header-fontSize: var(--fonts-size-7);
  --collapse-default-header-fontWeight: var(--fonts-weight-6);
  --collapse-default-header-lineHeight: var(--fonts-lineHeight-2);
  --collapse-default-header-bg-color: var(--colors-neutral-fill-10);
  --collapse-default-header-hover-bg-color: var(--colors-neutral-fill-9);
  --collapse-default-header-hover-color: var(--colors-neutral-text-2);
  --collapse-default-disabled-header-bg-color: var(--colors-neutral-fill-10);
  --collapse-default-disabled-color: var(--colors-neutral-text-6);
  --collapse-default-content-paddingTop: var(--sizes-size-9);
  --collapse-default-content-paddingBottom: var(--sizes-size-9);
  --collapse-default-content-paddingLeft: var(--sizes-size-9);
  --collapse-default-content-paddingRight: var(--sizes-size-9);
  --collapse-default-content-color: var(--colors-neutral-text-2);
  --collapse-default-content-fontSize: var(--fonts-size-8);
  --collapse-default-content-fontWeight: var(--fonts-weight-6);
  --collapse-default-content-lineHeight: var(--fonts-lineHeight-2);
  --collapse-default-bg-color: var(--colors-neutral-fill-11);
  --collapse-icon-icon: '<svg viewBox="0 0 99 176" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><g transform="translate(-0.132812, 0.304688)" fill="currentColor" fill-rule="nonzero"><path d="M95.6353697,79.4031346 C95.3433678,79.1111326 95.037575,78.8417152 94.7219891,78.5906854 L19.819373,3.29993262 C15.6492004,-0.870040234 8.88818437,-0.870040234 4.71821172,3.29993262 C0.548238867,7.47010527 0.548238867,14.2309215 4.71821172,18.4010939 L72.9329621,87.0041811 L3.25320488,156.617783 C-0.916767969,160.787956 -0.916767969,167.548772 3.25320488,171.719144 C7.42317773,175.889117 14.1841937,175.889117 18.3543662,171.719144 L94.7211895,95.4180764 C95.0369754,95.1668467 95.342968,94.8976293 95.6351697,94.6054273 C97.7329475,92.5076496 98.7744412,89.7539166 98.7615739,87.0043809 C98.7746414,84.2544453 97.7331475,81.5009123 95.6353697,79.4031346 Z" id="路径"></path></g></svg>';
  --collapse-icon-size: var(--sizes-size-6);
  --collapse-icon-color: var(--colors-neutral-text-5);
  --collapse-icon-margin: var(--sizes-size-5);
  --collapse-icon-rotate: 90deg;
  --Collapse-header-fontSize: var(--collapse-default-header-fontSize);
  --Collapse-header-fontWeight: var(--collapse-default-header-fontWeight);
  --Collapse-header-padding: var(--collapse-default-header-paddingTop)
    var(--collapse-default-header-paddingRight) var(--collapse-default-header-paddingBottom)
    var(--collapse-default-header-paddingLeft);
  --Collapse-header-bg: var(--collapse-default-header-bg-color);
  --Collapse-header-onHover-bg: var(--collapse-default-header-hover-bg-color);
  --Collapse-header-collapsed-borderTop: none;
  --Collapse-header-collapsed-borderBottom: none;
  --Collapse-header-wrapper-direction: row-reverse;
  --Collapse-header-bg-disabled-color: var(--collapse-disabled-header-hover-bg-color);
  --Collapse-content-padding: var(--collapse-default-content-paddingTop)
    var(--collapse-default-content-paddingRight) var(--collapse-default-content-paddingBottom)
    var(--collapse-default-content-paddingLeft);
  --Collapse-content-color: var(--collapse-default-content-color);
  --Collapse-content-fontSize: var(--collapse-default-content-fontSize);
  --Collapse-content-fontWeight: var(--collapse-default-content-fontWeight);
  --Collapse-mobile-marginBottom: var(--sizes-size-9);
  --Tabs-borderColor: var(--colors-neutral-line-8);
  --Tabs-link-disabled-color: var(--colors-neutral-text-6);
  --Tabs--sidebar-iconColor: var(--colors-brand-5);
  --Tabs-borderRadius: var(--borderRadius);
  --Tabs-borderWidth: var(--borderWidth);
  --Tabs-borderColor: var(--colors-neutral-line-8);
  --Tabs-color: var(--text-color);
  --Tabs-content-bg: var(--colors-neutral-fill-11);
  --Tabs-linkFontSize: var(--fonts-size-7);
  --Tabs-linkMargin: 0 0.1875rem 0 0;
  --Tabs-linkPadding: var(--gap-sm) var(--gap-base);
  --Tabs-onActive-bg: var(--background);
  --Tabs-onActive-borderColor: var(--borderColor);
  --Tabs-onActive-color: var(--colors-neutral-text-2);
  --Tabs-onError-color: var(--colors-error-5);
  --Tabs-onDisabled-color: var(--colors-neutral-text-7);
  --Tabs-onHover-borderColor: var(--colors-neutral-line-8);
  --Tabs-add-icon-size: 0.9375rem;
  --Tabs-add-icon-padding: 0.0625rem;
  --Tabs-add-icon-margin: var(--gap-xs);
  --Tabs-add-margin: var(--gap-lg);
  --Tabs-gray-color: #83868c;
  --Tabs-close-margin: var(--gap-xs);
  --Tabs-close-marginTop: 0.0625rem;
  --Tabs-close-size: 0.75rem;
  --Tabs-link-maxWidth: 10rem;
  --Tabs-icon-gap: var(--gap-sm);
  --Tabs-animation-duration: var(--animation-duration);
  --Tabs--simple-paddingTop: var(--sizes-size-3);
  --Tabs--simple-paddingBottom: var(--sizes-size-3);
  --Tabs--simple-paddingLeft: var(--sizes-size-9);
  --Tabs--simple-paddingRight: var(--sizes-size-9);
  --Tabs--simple-split-size: var(--Tabs--simple-paddingTop) var(--Tabs--simple-paddingRight)
    var(--Tabs--simple-paddingBottom) var(--Tabs--simple-paddingLeft);
  --Tabs--simple-split-width: var(--borders-width-2);
  --Tabs--simple-split-style: var(--borders-style-2);
  --Tabs--simple-split-color: var(--colors-neutral-line-8);
  --Tabs--simple-color: var(--colors-neutral-text-2);
  --Tabs--simple-fontSize: var(--fonts-size-7);
  --Tabs--simple-fontWeight: var(--fonts-weight-6);
  --Tabs--simple-lineHeight: var(--fonts-lineHeight-2);
  --Tabs--simple-active-color: var(--colors-brand-5);
  --Tabs--simple-active-fontSize: var(--fonts-size-7);
  --Tabs--simple-active-fontWeight: var(--fonts-weight-6);
  --Tabs--simple-active-lineHeight: var(--fonts-lineHeight-2);
  --Tabs--simple-hover-color: var(--colors-brand-5);
  --Tabs--simple-hover-fontSize: var(--fonts-size-7);
  --Tabs--simple-hover-fontWeight: var(--fonts-weight-6);
  --Tabs--simple-hover-lineHeight: var(--fonts-lineHeight-2);
  --Tabs--simple-disabled-color: var(--colors-neutral-text-6);
  --Tabs--simple-disabled-fontSize: var(--fonts-size-7);
  --Tabs--simple-disabled-fontWeight: var(--fonts-weight-6);
  --Tabs--simple-disabled-lineHeight: var(--fonts-lineHeight-2);
  --Tabs--strong-paddingTop: var(--sizes-size-5);
  --Tabs--strong-paddingBottom: var(--sizes-size-5);
  --Tabs--strong-paddingLeft: var(--sizes-size-9);
  --Tabs--strong-paddingRight: var(--sizes-size-9);
  --Tabs--strong-marginTop: var(--sizes-size-0);
  --Tabs--strong-marginBottom: var(--sizes-size-0);
  --Tabs--strong-marginLeft: var(--sizes-size-0);
  --Tabs--strong-marginRight: var(--sizes-size-5);
  --Tabs--strong-bg: var(--colors-neutral-fill-11);
  --Tabs--strong-top-border-color: var(--colors-neutral-text-8);
  --Tabs--strong-top-border-width: var(--borders-width-2);
  --Tabs--strong-top-border-style: var(--borders-style-2);
  --Tabs--strong-right-border-color: var(--colors-neutral-text-8);
  --Tabs--strong-right-border-width: var(--borders-width-2);
  --Tabs--strong-right-border-style: var(--borders-style-2);
  --Tabs--strong-bottom-border-color: var(--colors-neutral-text-8);
  --Tabs--strong-bottom-border-width: var(--borders-width-2);
  --Tabs--strong-bottom-border-style: var(--borders-style-2);
  --Tabs--strong-left-border-color: var(--colors-neutral-text-8);
  --Tabs--strong-left-border-width: var(--borders-width-2);
  --Tabs--strong-left-border-style: var(--borders-style-2);
  --Tabs--strong-top-right-border-radius: var(--borders-radius-3);
  --Tabs--strong-top-left-border-radius: var(--borders-radius-3);
  --Tabs--strong-bottom-right-border-radius: var(--borders-radius-1);
  --Tabs--strong-bottom-left-border-radius: var(--borders-radius-1);
  --Tabs--strong-color: var(--colors-neutral-text-2);
  --Tabs--strong-fontSize: var(--fonts-size-7);
  --Tabs--strong-fontWeight: var(--fonts-weight-6);
  --Tabs--strong-lineHeight: var(--fonts-lineHeight-2);
  --Tabs--strong-active-bg: var(--colors-neutral-fill-11);
  --Tabs--strong-active-top-border-color: var(--colors-neutral-text-8);
  --Tabs--strong-active-top-border-width: var(--borders-width-2);
  --Tabs--strong-active-top-border-style: var(--borders-style-2);
  --Tabs--strong-active-right-border-color: var(--colors-neutral-text-8);
  --Tabs--strong-active-right-border-width: var(--borders-width-2);
  --Tabs--strong-active-right-border-style: var(--borders-style-2);
  --Tabs--strong-active-bottom-border-color: transparent;
  --Tabs--strong-active-bottom-border-width: var(--borders-width-2);
  --Tabs--strong-active-bottom-border-style: var(--borders-style-2);
  --Tabs--strong-active-left-border-color: var(--colors-neutral-text-8);
  --Tabs--strong-active-left-border-width: var(--borders-width-2);
  --Tabs--strong-active-left-border-style: var(--borders-style-2);
  --Tabs--strong-active-color: var(--colors-brand-5);
  --Tabs--strong-active-fontSize: var(--fonts-size-7);
  --Tabs--strong-active-fontWeight: var(--fonts-weight-6);
  --Tabs--strong-active-lineHeight: var(--fonts-lineHeight-2);
  --Tabs--strong-hover-bg: var(--colors-neutral-fill-11);
  --Tabs--strong-hover-top-border-color: var(--colors-neutral-text-8);
  --Tabs--strong-hover-top-border-width: var(--borders-width-2);
  --Tabs--strong-hover-top-border-style: var(--borders-style-2);
  --Tabs--strong-hover-right-border-color: var(--colors-neutral-text-8);
  --Tabs--strong-hover-right-border-width: var(--borders-width-2);
  --Tabs--strong-hover-right-border-style: var(--borders-style-2);
  --Tabs--strong-hover-bottom-border-color: var(--colors-neutral-text-8);
  --Tabs--strong-hover-bottom-border-width: var(--borders-width-2);
  --Tabs--strong-hover-bottom-border-style: var(--borders-style-2);
  --Tabs--strong-hover-left-border-color: var(--colors-neutral-text-8);
  --Tabs--strong-hover-left-border-width: var(--borders-width-2);
  --Tabs--strong-hover-left-border-style: var(--borders-style-2);
  --Tabs--strong-hover-color: var(--colors-brand-5);
  --Tabs--strong-hover-fontSize: var(--fonts-size-7);
  --Tabs--strong-hover-fontWeight: var(--fonts-weight-6);
  --Tabs--strong-hover-lineHeight: var(--fonts-lineHeight-2);
  --Tabs--strong-disabled-bg: var(--colors-neutral-fill-11);
  --Tabs--strong-disabled-top-border-color: var(--colors-neutral-text-8);
  --Tabs--strong-disabled-top-border-width: var(--borders-width-2);
  --Tabs--strong-disabled-top-border-style: var(--borders-style-2);
  --Tabs--strong-disabled-right-border-color: var(--colors-neutral-text-8);
  --Tabs--strong-disabled-right-border-width: var(--borders-width-2);
  --Tabs--strong-disabled-right-border-style: var(--borders-style-2);
  --Tabs--strong-disabled-bottom-border-color: var(--colors-neutral-text-8);
  --Tabs--strong-disabled-bottom-border-width: var(--borders-width-2);
  --Tabs--strong-disabled-bottom-border-style: var(--borders-style-2);
  --Tabs--strong-disabled-left-border-color: var(--colors-neutral-text-8);
  --Tabs--strong-disabled-left-border-width: var(--borders-width-2);
  --Tabs--strong-disabled-left-border-style: var(--borders-style-2);
  --Tabs--strong-disabled-color: var(--colors-neutral-text-6);
  --Tabs--strong-disabled-fontSize: var(--fonts-size-7);
  --Tabs--strong-disabled-fontWeight: var(--fonts-weight-6);
  --Tabs--strong-disabled-lineHeight: var(--fonts-lineHeight-2);
  --Tabs--strong-add-size: 2rem;
  --Tabs--strong-arrow-size: 1.5rem;
  --Tabs--line-padding: var(--sizes-base-16);
  --Tabs--line-border-color: var(--colors-neutral-text-8);
  --Tabs--line-border-width: var(--borders-width-2);
  --Tabs--line-border-style: var(--borders-style-2);
  --Tabs--line-color: var(--colors-neutral-text-2);
  --Tabs--line-fontSize: var(--fonts-size-7);
  --Tabs--line-fontWeight: var(--fonts-weight-6);
  --Tabs--line-lineHeight: var(--fonts-lineHeight-2);
  --Tabs--line-active-color: var(--colors-brand-5);
  --Tabs--line-active-fontSize: var(--fonts-size-7);
  --Tabs--line-active-fontWeight: var(--fonts-weight-6);
  --Tabs--line-active-lineHeight: var(--fonts-lineHeight-2);
  --Tabs--line-active-border-color: var(--colors-brand-5);
  --Tabs--line-active-border-width: var(--borders-width-3);
  --Tabs--line-active-border-style: var(--borders-style-2);
  --Tabs--line-onHover-borderColor: var(--Tabs--line-active-border-color);
  --Tabs--line-hover-color: var(--colors-brand-5);
  --Tabs--line-hover-fontSize: var(--fonts-size-7);
  --Tabs--line-hover-fontWeight: var(--fonts-weight-6);
  --Tabs--line-hover-lineHeight: var(--fonts-lineHeight-2);
  --Tabs--line-disabled-color: var(--colors-neutral-text-6);
  --Tabs--line-disabled-fontSize: var(--fonts-size-7);
  --Tabs--line-disabled-fontWeight: var(--fonts-weight-6);
  --Tabs--line-disabled-lineHeight: var(--fonts-lineHeight-2);
  --Tabs--card-paddingTop: var(--sizes-size-4);
  --Tabs--card-paddingBottom: var(--sizes-size-0);
  --Tabs--card-paddingLeft: var(--sizes-size-6);
  --Tabs--card-paddingRight: var(--sizes-size-6);
  --Tabs--card-padding: var(--Tabs--card-paddingTop) var(--Tabs--card-paddingRight)
    var(--Tabs--card-paddingBottom) var(--Tabs--card-paddingLeft);
  --Tabs--card-border-color: var(--colors-neutral-line-8);
  --Tabs--card-border-width: var(--borders-width-2);
  --Tabs--card-border-style: var(--borders-style-2);
  --Tabs--card-borderTopColor: var(--Tabs--card-border-color);
  --Tabs--card-bg: var(--colors-neutral-fill-10);
  --Tabs--card-linkBg: transparent;
  --Tabs--card-color: var(--colors-neutral-text-2);
  --Tabs--card-fontSize: var(--fonts-size-7);
  --Tabs--card-fontWeight: var(--fonts-weight-6);
  --Tabs--card-lineHeight: var(--fonts-lineHeight-2);
  --Tabs--card-linkMargin: var(--sizes-size-6);
  --Tabs--card-linkPadding: var(--sizes-size-6);
  --Tabs--card-borderRadius: var(--borders-radius-3);
  --Tabs--card-active-color: var(--colors-neutral-text-2);
  --Tabs--card-active-fontSize: var(--fonts-size-7);
  --Tabs--card-active-fontWeight: var(--fonts-weight-6);
  --Tabs--card-active-lineHeight: var(--fonts-lineHeight-2);
  --Tabs--card-active-linkBg: var(--colors-neutral-fill-11);
  --Tabs--card-onActive-bg: var(--Tabs--card-active-linkBg);
  --Tabs--card-hover-color: var(--colors-neutral-text-2);
  --Tabs--card-hover-fontSize: var(--fonts-size-7);
  --Tabs--card-hover-fontWeight: var(--fonts-weight-6);
  --Tabs--card-hover-lineHeight: var(--fonts-lineHeight-2);
  --Tabs--card-hover-linkBg: var(--colors-neutral-fill-11);
  --Tabs--card-disabled-color: var(--colors-neutral-text-6);
  --Tabs--card-disabled-fontSize: var(--fonts-size-7);
  --Tabs--card-disabled-fontWeight: var(--fonts-weight-6);
  --Tabs--card-disabled-lineHeight: var(--fonts-lineHeight-2);
  --Tabs--card-disabled-linkBg: transparent;
  --Tabs--card-add-gap: var(--gap-md);
  --Tabs--card-add-gap-top: 0.4375rem;
  --Tabs--card-arrow-gap: var(--gap-sm);
  --Tabs--tiled-top-border-color: var(--colors-neutral-line-8);
  --Tabs--tiled-top-border-width: var(--borders-width-2);
  --Tabs--tiled-top-border-style: var(--borders-style-2);
  --Tabs--tiled-right-border-color: var(--colors-neutral-line-8);
  --Tabs--tiled-right-border-width: var(--borders-width-2);
  --Tabs--tiled-right-border-style: var(--borders-style-2);
  --Tabs--tiled-bottom-border-color: var(--colors-neutral-line-8);
  --Tabs--tiled-bottom-border-width: var(--borders-width-2);
  --Tabs--tiled-bottom-border-style: var(--borders-style-2);
  --Tabs--tiled-left-border-color: var(--colors-neutral-line-8);
  --Tabs--tiled-left-border-width: var(--borders-width-2);
  --Tabs--tiled-left-border-style: var(--borders-style-2);
  --Tabs--tiled-color: var(--colors-neutral-text-2);
  --Tabs--tiled-fontSize: var(--fonts-size-7);
  --Tabs--tiled-fontWeight: var(--fonts-weight-6);
  --Tabs--tiled-lineHeight: var(--fonts-lineHeight-2);
  --Tabs--tiled-paddingTop: var(--sizes-size-5);
  --Tabs--tiled-paddingBottom: var(--sizes-size-5);
  --Tabs--tiled-paddingLeft: var(--sizes-size-7);
  --Tabs--tiled-paddingRight: var(--sizes-size-7);
  --Tabs--tiled-active-top-border-color: var(--colors-brand-5);
  --Tabs--tiled-active-top-border-width: var(--borders-width-2);
  --Tabs--tiled-active-top-border-style: var(--borders-style-2);
  --Tabs--tiled-active-right-border-color: var(--colors-neutral-line-8);
  --Tabs--tiled-active-right-border-width: var(--borders-width-2);
  --Tabs--tiled-active-right-border-style: var(--borders-style-2);
  --Tabs--tiled-active-bottom-border-color: transparent;
  --Tabs--tiled-active-bottom-border-width: var(--borders-width-2);
  --Tabs--tiled-active-bottom-border-style: var(--borders-style-2);
  --Tabs--tiled-active-left-border-color: var(--colors-neutral-line-8);
  --Tabs--tiled-active-left-border-width: var(--borders-width-2);
  --Tabs--tiled-active-left-border-style: var(--borders-style-2);
  --Tabs--tiled-active-color: var(--colors-neutral-text-2);
  --Tabs--tiled-active-fontSize: var(--fonts-size-7);
  --Tabs--tiled-active-fontWeight: var(--fonts-weight-6);
  --Tabs--tiled-active-lineHeight: var(--fonts-lineHeight-2);
  --Tabs--tiled-hover-top-border-color: var(--colors-neutral-line-8);
  --Tabs--tiled-hover-top-border-width: var(--borders-width-2);
  --Tabs--tiled-hover-top-border-style: var(--borders-style-2);
  --Tabs--tiled-hover-right-border-color: var(--colors-neutral-line-8);
  --Tabs--tiled-hover-right-border-width: var(--borders-width-2);
  --Tabs--tiled-hover-right-border-style: var(--borders-style-2);
  --Tabs--tiled-hover-bottom-border-color: var(--colors-neutral-line-8);
  --Tabs--tiled-hover-bottom-border-width: var(--borders-width-2);
  --Tabs--tiled-hover-bottom-border-style: var(--borders-style-2);
  --Tabs--tiled-hover-left-border-color: var(--colors-neutral-line-8);
  --Tabs--tiled-hover-left-border-width: var(--borders-width-2);
  --Tabs--tiled-hover-left-border-style: var(--borders-style-2);
  --Tabs--tiled-hover-color: var(--colors-neutral-text-2);
  --Tabs--tiled-hover-fontSize: var(--fonts-size-7);
  --Tabs--tiled-hover-fontWeight: var(--fonts-weight-6);
  --Tabs--tiled-hover-lineHeight: var(--fonts-lineHeight-2);
  --Tabs--tiled-disabled-top-border-color: var(--colors-neutral-line-8);
  --Tabs--tiled-disabled-top-border-width: var(--borders-width-2);
  --Tabs--tiled-disabled-top-border-style: var(--borders-style-2);
  --Tabs--tiled-disabled-right-border-color: var(--colors-neutral-line-8);
  --Tabs--tiled-disabled-right-border-width: var(--borders-width-2);
  --Tabs--tiled-disabled-right-border-style: var(--borders-style-2);
  --Tabs--tiled-disabled-bottom-border-color: var(--colors-neutral-line-8);
  --Tabs--tiled-disabled-bottom-border-width: var(--borders-width-2);
  --Tabs--tiled-disabled-bottom-border-style: var(--borders-style-2);
  --Tabs--tiled-disabled-left-border-color: var(--colors-neutral-line-8);
  --Tabs--tiled-disabled-left-border-width: var(--borders-width-2);
  --Tabs--tiled-disabled-left-border-style: var(--borders-style-2);
  --Tabs--tiled-disabled-color: var(--colors-neutral-text-6);
  --Tabs--tiled-disabled-fontSize: var(--fonts-size-7);
  --Tabs--tiled-disabled-fontWeight: var(--fonts-weight-6);
  --Tabs--tiled-disabled-lineHeight: var(--fonts-lineHeight-2);
  --Tabs--tiled-add-gap: var(--gap-base);
  --Tabs--radio-top-border-color: var(--colors-neutral-line-8);
  --Tabs--radio-top-border-width: var(--borders-width-2);
  --Tabs--radio-top-border-style: var(--borders-style-2);
  --Tabs--radio-right-border-color: var(--colors-neutral-line-8);
  --Tabs--radio-right-border-width: var(--borders-width-2);
  --Tabs--radio-right-border-style: var(--borders-style-2);
  --Tabs--radio-bottom-border-color: var(--colors-neutral-line-8);
  --Tabs--radio-bottom-border-width: var(--borders-width-2);
  --Tabs--radio-bottom-border-style: var(--borders-style-2);
  --Tabs--radio-left-border-color: var(--colors-neutral-line-8);
  --Tabs--radio-left-border-width: var(--borders-width-2);
  --Tabs--radio-left-border-style: var(--borders-style-2);
  --Tabs--radio-color: var(--colors-neutral-text-2);
  --Tabs--radio-fontSize: var(--fonts-size-7);
  --Tabs--radio-fontWeight: var(--fonts-weight-6);
  --Tabs--radio-lineHeight: var(--fonts-lineHeight-2);
  --Tabs--radio-paddingTop: var(--sizes-size-5);
  --Tabs--radio-paddingBottom: var(--sizes-size-5);
  --Tabs--radio-paddingLeft: var(--sizes-size-7);
  --Tabs--radio-paddingRight: var(--sizes-size-7);
  --Tabs--radio-bg: var(--colors-neutral-fill-11);
  --Tabs--radio-height: var(--sizes-base-15);
  --Tabs--radio-active-top-border-color: var(--colors-brand-5);
  --Tabs--radio-active-top-border-width: var(--borders-width-2);
  --Tabs--radio-active-top-border-style: var(--borders-style-2);
  --Tabs--radio-active-right-border-color: var(--colors-brand-5);
  --Tabs--radio-active-right-border-width: var(--borders-width-2);
  --Tabs--radio-active-right-border-style: var(--borders-style-2);
  --Tabs--radio-active-bottom-border-color: var(--colors-brand-5);
  --Tabs--radio-active-bottom-border-width: var(--borders-width-2);
  --Tabs--radio-active-bottom-border-style: var(--borders-style-2);
  --Tabs--radio-active-left-border-color: var(--colors-brand-5);
  --Tabs--radio-active-left-border-width: var(--borders-width-2);
  --Tabs--radio-active-left-border-style: var(--borders-style-2);
  --Tabs--radio-active-color: var(--colors-neutral-text-11);
  --Tabs--radio-active-fontSize: var(--fonts-size-7);
  --Tabs--radio-active-fontWeight: var(--fonts-weight-6);
  --Tabs--radio-active-lineHeight: var(--fonts-lineHeight-2);
  --Tabs--radio-active-bg: var(--colors-brand-5);
  --Tabs--radio-hover-top-border-color: var(--colors-neutral-line-8);
  --Tabs--radio-hover-top-border-width: var(--borders-width-2);
  --Tabs--radio-hover-top-border-style: var(--borders-style-2);
  --Tabs--radio-hover-right-border-color: var(--colors-neutral-line-8);
  --Tabs--radio-hover-right-border-width: var(--borders-width-2);
  --Tabs--radio-hover-right-border-style: var(--borders-style-2);
  --Tabs--radio-hover-bottom-border-color: var(--colors-neutral-line-8);
  --Tabs--radio-hover-bottom-border-width: var(--borders-width-2);
  --Tabs--radio-hover-bottom-border-style: var(--borders-style-2);
  --Tabs--radio-hover-left-border-color: var(--colors-neutral-line-8);
  --Tabs--radio-hover-left-border-width: var(--borders-width-2);
  --Tabs--radio-hover-left-border-style: var(--borders-style-2);
  --Tabs--radio-hover-color: var(--colors-neutral-text-2);
  --Tabs--radio-hover-fontSize: var(--fonts-size-7);
  --Tabs--radio-hover-fontWeight: var(--fonts-weight-6);
  --Tabs--radio-hover-lineHeight: var(--fonts-lineHeight-2);
  --Tabs--radio-hover-bg: var(--colors-neutral-fill-11);
  --Tabs--radio-disabled-top-border-color: var(--colors-neutral-line-8);
  --Tabs--radio-disabled-top-border-width: var(--borders-width-2);
  --Tabs--radio-disabled-top-border-style: var(--borders-style-2);
  --Tabs--radio-disabled-right-border-color: var(--colors-neutral-line-8);
  --Tabs--radio-disabled-right-border-width: var(--borders-width-2);
  --Tabs--radio-disabled-right-border-style: var(--borders-style-2);
  --Tabs--radio-disabled-bottom-border-color: var(--colors-neutral-line-8);
  --Tabs--radio-disabled-bottom-border-width: var(--borders-width-2);
  --Tabs--radio-disabled-bottom-border-style: var(--borders-style-2);
  --Tabs--radio-disabled-left-border-color: var(--colors-neutral-line-8);
  --Tabs--radio-disabled-left-border-width: var(--borders-width-2);
  --Tabs--radio-disabled-left-border-style: var(--borders-style-2);
  --Tabs--radio-disabled-color: var(--colors-neutral-text-6);
  --Tabs--radio-disabled-fontSize: var(--fonts-size-7);
  --Tabs--radio-disabled-fontWeight: var(--fonts-weight-6);
  --Tabs--radio-disabled-lineHeight: var(--fonts-lineHeight-2);
  --Tabs--radio-disabled-bg: var(--colors-neutral-fill-11);
  --Tabs--vertical-color: var(--colors-neutral-text-2);
  --Tabs--vertical-fontSize: var(--fonts-size-7);
  --Tabs--vertical-fontWeight: var(--fonts-weight-6);
  --Tabs--vertical-lineHeight: var(--fonts-lineHeight-2);
  --Tabs--vertical-paddingTop: var(--sizes-size-5);
  --Tabs--vertical-paddingBottom: var(--sizes-size-5);
  --Tabs--vertical-paddingLeft: var(--sizes-size-7);
  --Tabs--vertical-paddingRight: var(--sizes-size-7);
  --Tabs--vertical-bg: var(--colors-neutral-fill-10);
  --Tabs--vertical-width: 8.75rem;
  --Tabs--vertical-active-color: var(--colors-brand-5);
  --Tabs--vertical-active-fontSize: var(--fonts-size-7);
  --Tabs--vertical-active-fontWeight: var(--fonts-weight-6);
  --Tabs--vertical-active-lineHeight: var(--fonts-lineHeight-2);
  --Tabs--vertical-active-border-color: var(--colors-brand-5);
  --Tabs--vertical-active-border-width: var(--borders-width-4);
  --Tabs--vertical-active-border-style: var(--borders-style-2);
  --Tabs--vertical-onActive-borderWidth: var(--Tabs--vertical-active-border-width);
  --Tabs--vertical-onActive-border: var(--Tabs--vertical-active-border-color);
  --Tabs--vertical-onActive-color: var(--Tabs--vertical-active-color);
  --Tabs--vertical-hover-color: var(--colors-brand-6);
  --Tabs--vertical-hover-fontSize: var(--fonts-size-7);
  --Tabs--vertical-hover-fontWeight: var(--fonts-weight-6);
  --Tabs--vertical-hover-lineHeight: var(--fonts-lineHeight-2);
  --Tabs--vertical-disabled-color: var(--colors-neutral-text-6);
  --Tabs--vertical-disabled-fontSize: var(--fonts-size-7);
  --Tabs--vertical-disabled-fontWeight: var(--fonts-weight-6);
  --Tabs--vertical-disabled-lineHeight: var(--fonts-lineHeight-2);
  --Tabs--sidebar-color: var(--colors-neutral-text-5);
  --Tabs--sidebar-fontSize: var(--fonts-size-7);
  --Tabs--sidebar-fontWeight: var(--fonts-weight-6);
  --Tabs--sidebar-lineHeight: var(--fonts-lineHeight-2);
  --Tabs--sidebar-sideWidth: var(--sizes-base-31);
  --Tabs--sidebar-sideMargin: var(--sizes-base-11);
  --Tabs--sidebar-iconSize: 1.5rem;
  --Tabs--sidebar-iconMargin: 0.3125rem;
  --Tabs--sidebar-active-color: var(--colors-brand-5);
  --Tabs--sidebar-active-fontSize: var(--fonts-size-7);
  --Tabs--sidebar-active-fontWeight: var(--fonts-weight-6);
  --Tabs--sidebar-active-lineHeight: var(--fonts-lineHeight-2);
  --Tabs--sidebar-hover-color: var(--colors-brand-6);
  --Tabs--sidebar-hover-fontSize: var(--fonts-size-7);
  --Tabs--sidebar-hover-fontWeight: var(--fonts-weight-6);
  --Tabs--sidebar-hover-lineHeight: var(--fonts-lineHeight-2);
  --Tabs--sidebar-disabled-color: var(--colors-neutral-text-6);
  --Tabs--sidebar-disabled-fontSize: var(--fonts-size-7);
  --Tabs--sidebar-disabled-fontWeight: var(--fonts-weight-6);
  --Tabs--sidebar-disabled-lineHeight: var(--fonts-lineHeight-2);
  --Tabs--chrome-onHover-bg: var(--colors-neutral-fill-10);
  --Tabs--chrome-bg: var(--colors-neutral-fill-10);
  --Tabs--chrome-radius-size: 0.5rem;
  --Tabs--chrome-right-border-color: var(--colors-neutral-line-5);
  --Panel-bg-color: var(--colors-neutral-fill-11);
  --Panel-marginTop: var(--sizes-size-0);
  --Panel-marginBottom: var(--sizes-base-10);
  --Panel-marginLeft: var(--sizes-size-0);
  --Panel-marginRight: var(--sizes-size-0);
  --Panel-top-border-color: var(--colors-neutral-line-8);
  --Panel-top-border-width: var(--borders-width-2);
  --Panel-top-border-style: var(--borders-style-2);
  --Panel-right-border-color: var(--colors-neutral-line-8);
  --Panel-right-border-width: var(--borders-width-2);
  --Panel-right-border-style: var(--borders-style-2);
  --Panel-bottom-border-color: var(--colors-neutral-line-8);
  --Panel-bottom-border-width: var(--borders-width-2);
  --Panel-bottom-border-style: var(--borders-style-2);
  --Panel-left-border-color: var(--colors-neutral-line-8);
  --Panel-left-border-width: var(--borders-width-2);
  --Panel-left-border-style: var(--borders-style-2);
  --Panel-top-right-border-radius: var(--borders-radius-3);
  --Panel-top-left-border-radius: var(--borders-radius-3);
  --Panel-bottom-right-border-radius: var(--borders-radius-3);
  --Panel-bottom-left-border-radius: var(--borders-radius-3);
  --Panel-borderRadius: var(--Panel-top-left-border-radius) var(--Panel-top-right-border-radius)
    var(--Panel-bottom-right-border-radius) var(--Panel-bottom-left-border-radius);
  --Panel-borderWidth: var(--Panel-top-border-width) var(--Panel-right-border-width)
    var(--Panel-bottom-border-width) var(--Panel-left-border-width);
  --Panel-shadow: var(--shadows-shadow-sm);
  --Panel-heading-paddingTop: var(--sizes-size-5);
  --Panel-heading-paddingBottom: var(--sizes-size-5);
  --Panel-heading-paddingLeft: var(--sizes-size-7);
  --Panel-heading-paddingRight: var(--sizes-size-7);
  --Panel-heading-bg-color: var(--colors-neutral-fill-10);
  --Panel-heading-color: var(--colors-neutral-text-3);
  --Panel-heading-fontSize: var(--fonts-size-8);
  --Panel-heading-fontWeight: var(--fonts-weight-6);
  --Panel-heading-lineHeight: var(--fonts-lineHeight-2);
  --Panel-heading-top-border-color: transparent;
  --Panel-heading-top-border-width: var(--borders-width-1);
  --Panel-heading-top-border-style: var(--borders-style-2);
  --Panel-heading-right-border-color: transparent;
  --Panel-heading-right-border-width: var(--borders-width-1);
  --Panel-heading-right-border-style: var(--borders-style-2);
  --Panel-heading-bottom-border-color: var(--colors-neutral-line-8);
  --Panel-heading-bottom-border-width: var(--borders-width-2);
  --Panel-heading-bottom-border-style: var(--borders-style-2);
  --Panel-heading-left-border-color: transparent;
  --Panel-heading-left-border-width: var(--borders-width-1);
  --Panel-heading-left-border-style: var(--borders-style-2);
  --Panel-headingPadding: var(--Panel-heading-paddingTop) var(--Panel-heading-paddingRight)
    var(--Panel-heading-paddingBottom) var(--Panel-heading-paddingLeft);
  --Panel-headingBorderRadius: var(--Panel-top-left-border-radius)
    var(--Panel-top-right-border-radius) 0 0;
  --Panel-body-paddingTop: var(--sizes-size-7);
  --Panel-body-paddingBottom: var(--sizes-size-7);
  --Panel-body-paddingLeft: var(--sizes-size-7);
  --Panel-body-paddingRight: var(--sizes-size-7);
  --Panel-bodyPadding: var(--Panel-body-paddingTop) var(--Panel-body-paddingRight)
    var(--Panel-body-paddingBottom) var(--Panel-body-paddingLeft);
  --Panel-footer-paddingTop: var(--sizes-size-5);
  --Panel-footer-paddingBottom: var(--sizes-size-5);
  --Panel-footer-paddingLeft: var(--sizes-size-7);
  --Panel-footer-paddingRight: var(--sizes-size-7);
  --Panel-footer-bg-color: var(--colors-neutral-fill-none);
  --Panel-footer-top-border-color: var(--colors-neutral-line-8);
  --Panel-footer-top-border-width: var(--borders-width-2);
  --Panel-footer-top-border-style: var(--borders-style-2);
  --Panel-footer-right-border-color: transparent;
  --Panel-footer-right-border-width: var(--borders-width-1);
  --Panel-footer-right-border-style: var(--borders-style-2);
  --Panel-footer-bottom-border-color: transparent;
  --Panel-footer-bottom-border-width: var(--borders-width-1);
  --Panel-footer-bottom-border-style: var(--borders-style-2);
  --Panel-footer-left-border-color: transparent;
  --Panel-footer-left-border-width: var(--borders-width-1);
  --Panel-footer-left-border-style: var(--borders-style-2);
  --Panel-footerBorderRadius: 0 0 var(--Panel-bottom-right-border-radius)
    var(--Panel-bottom-left-border-radius);
  --Panel-footerBg: var(--Panel-footer-bg-color);
  --Panel-footerPadding: var(--Panel-footer-paddingTop) var(--Panel-footer-paddingRight)
    var(--Panel-footer-paddingBottom) var(--Panel-footer-paddingLeft);
  --Panel-footerBorderColor: var(--Panel-footer-top-border-color)
    var(--Panel-footer-right-border-color) var(--Panel-footer-bottom-border-color)
    var(--Panel-footer-left-border-color);
  --Panel-footer-buttonSpace: var(--sizes-size-5);
  --Panel-footerButtonMarginLeft: var(--Panel-footer-buttonSpace);
  --Panel-fixedBottom-borderTop: none;
  --Panel-fixedBottom-boxShadow: var(--shadows-shadow-normal);
  --Panel-btnToolbarTextAlign: right;
  --Divider-style: var(--borders-style-2);
  --Divider-color: var(--colors-neutral-line-8);
  --Divider-width: var(--borders-width-2);
  --Divider-marginTop: var(--sizes-size-7);
  --Divider-marginLeft: var(--sizes-size-0);
  --Divider-marginRight: var(--sizes-size-0);
  --Divider-marginBottom: var(--sizes-size-7);
  --Divider-text-width: 5%;
  --Divider-text-fontSize: var(--fonts-size-7);
  --Divider-text-fontWeight: var(--fonts-weight-6);
  --Divider-text-color: var(--colors-neutral-text-2);
  --Divider-text-marginTop: var(--sizes-size-0);
  --Divider-text-marginLeft: var(--sizes-size-9);
  --Divider-text-marginRight: var(--sizes-size-9);
  --Divider-text-marginBottom: var(--sizes-size-0);
  --inputFile-base-des-color: var(--Form-description-color);
  --inputFile-base-des-fontSize: var(--fonts-size-7);
  --inputFile-base-des-fontWeight: var(--fonts-weight-6);
  --inputFile-base-des-margin: var(--sizes-base-5);
  --inputFile-list-marginTop: var(--sizes-base-4);
  --inputFile-list-marginBottom: var(--sizes-base-4);
  --inputFile-list-marginLeft: var(--sizes-size-0);
  --inputFile-list-marginRight: var(--sizes-size-0);
  --inputFile-list-paddingTop: var(--sizes-size-2);
  --inputFile-list-paddingBottom: var(--sizes-size-2);
  --inputFile-list-paddingLeft: var(--sizes-size-3);
  --inputFile-list-paddingRight: var(--sizes-size-3);
  --inputFile-list-color: var(--colors-brand-5);
  --inputFile-list-fontSize: var(--fonts-size-8);
  --inputFile-list-fontWeight: var(--fonts-weight-6);
  --inputFile-list-bg-color: transparent;
  --inputFile-list-bg-color-hover: var(--colors-neutral-fill-9);
  --inputFile-list-icon-size: var(--sizes-base-6);
  --inputFile-list-icon-color: var(--colors-neutral-text-2);
  --inputFile-list-icon-margin: var(--sizes-size-3);
  --inputFile-list-delete-icon-size: var(--sizes-base-6);
  --inputFile-list-delete-icon-color: var(--colors-neutral-text-5);
  --inputFile-list-delete-icon-color-hover: var(--colors-neutral-text-4);
  --inputFile-drag-top-border-color: var(--colors-neutral-line-8);
  --inputFile-drag-top-border-width: var(--borders-width-2);
  --inputFile-drag-top-border-style: var(--borders-style-3);
  --inputFile-drag-right-border-color: var(--colors-neutral-line-8);
  --inputFile-drag-right-border-width: var(--borders-width-2);
  --inputFile-drag-right-border-style: var(--borders-style-3);
  --inputFile-drag-bottom-border-color: var(--colors-neutral-line-8);
  --inputFile-drag-bottom-border-width: var(--borders-width-2);
  --inputFile-drag-bottom-border-style: var(--borders-style-3);
  --inputFile-drag-left-border-color: var(--colors-neutral-line-8);
  --inputFile-drag-left-border-width: var(--borders-width-2);
  --inputFile-drag-left-border-style: var(--borders-style-3);
  --inputFile-drag-top-right-border-radius: var(--borders-radius-3);
  --inputFile-drag-top-left-border-radius: var(--borders-radius-3);
  --inputFile-drag-bottom-right-border-radius: var(--borders-radius-3);
  --inputFile-drag-bottom-left-border-radius: var(--borders-radius-3);
  --inputFile-drag-hover-top-border-color: var(--colors-neutral-line-8);
  --inputFile-drag-hover-top-border-width: var(--borders-width-2);
  --inputFile-drag-hover-top-border-style: var(--borders-style-3);
  --inputFile-drag-hover-right-border-color: var(--colors-neutral-line-8);
  --inputFile-drag-hover-right-border-width: var(--borders-width-2);
  --inputFile-drag-hover-right-border-style: var(--borders-style-3);
  --inputFile-drag-hover-bottom-border-color: var(--colors-neutral-line-8);
  --inputFile-drag-hover-bottom-border-width: var(--borders-width-2);
  --inputFile-drag-hover-bottom-border-style: var(--borders-style-3);
  --inputFile-drag-hover-left-border-color: var(--colors-neutral-line-8);
  --inputFile-drag-hover-left-border-width: var(--borders-width-2);
  --inputFile-drag-hover-left-border-style: var(--borders-style-3);
  --inputFile-drag-color: var(--colors-neutral-text-3);
  --inputFile-drag-fontSize: var(--fonts-size-8);
  --inputFile-drag-fontWeight: var(--fonts-weight-6);
  --inputFile-drag-icon-size: var(--sizes-base-24);
  --inputFile-drag-icon-color: var(--colors-neutral-fill-8);
  --inputFile-drag-icon-margin: var(--sizes-size-5);
  --inputFile-drag-bg-color: var(--colors-neutral-fill-11);
  --inputFile-drag-bg-color-hover: var(--colors-neutral-text-11);
  --FileControl-danger-color: var(--colors-error-5);
  --FileControl-drag-color: var(--inputFile-drag-color);
  --FileControl-border-color: var(--inputFile-drag-top-border-color)
    var(--inputFile-drag-right-border-color) var(--inputFile-drag-bottom-border-color)
    var(--inputFile-drag-left-border-color);
  --FileControl-onDisabled-color: var(--colors-neutral-text-6);
  --FileControl-onDisabled-bg: var(--colors-neutral-fill-10);
  --FileControl-onHover-bg: var(--inputFile-list-bg-color-hover);
  --FileControl-icon-color: var(--inputFile-list-delete-icon-color);
  --FileControl-icon-onHover-color: var(--inputFile-list-delete-icon-color-hover);
  --FileControl-progress-borderRadius: var(--borders-radius-2);
  --inputImage-base-default-top-border-color: var(--colors-neutral-line-8);
  --inputImage-base-default-top-border-width: var(--borders-width-2);
  --inputImage-base-default-top-border-style: var(--borders-style-2);
  --inputImage-base-default-right-border-color: var(--colors-neutral-line-8);
  --inputImage-base-default-right-border-width: var(--borders-width-2);
  --inputImage-base-default-right-border-style: var(--borders-style-2);
  --inputImage-base-default-bottom-border-color: var(--colors-neutral-line-8);
  --inputImage-base-default-bottom-border-width: var(--borders-width-2);
  --inputImage-base-default-bottom-border-style: var(--borders-style-2);
  --inputImage-base-default-left-border-color: var(--colors-neutral-line-8);
  --inputImage-base-default-left-border-width: var(--borders-width-2);
  --inputImage-base-default-left-border-style: var(--borders-style-2);
  --inputImage-base-default-top-right-border-radius: var(--borders-radius-3);
  --inputImage-base-default-top-left-border-radius: var(--borders-radius-3);
  --inputImage-base-default-bottom-right-border-radius: var(--borders-radius-3);
  --inputImage-base-default-bottom-left-border-radius: var(--borders-radius-3);
  --inputImage-base-default-fontSize: var(--fonts-size-7);
  --inputImage-base-default-fontWeight: var(--fonts-weight-6);
  --inputImage-base-default-color: var(--colors-neutral-text-5);
  --inputImage-base-default-icon: '<svg viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"><g><rect fill="currentColor" opacity="0" x="0" y="0" width="16" height="16"></rect><path d="M8.5,2 L8.5,7.5 L14,7.5 L14,8.5 L8.5,8.5 L8.5,14 L7.5,14 L7.5,8.5 L2,8.5 L2,7.5 L7.5,7.5 L7.5,2 L8.5,2 Z"  fill="currentColor" fill-rule="nonzero"></path></g></g></svg>';
  --inputImage-base-default-icon-size: var(--sizes-base-12);
  --inputImage-base-default-icon-color: var(--colors-neutral-text-5);
  --inputImage-base-default-icon-margin: var(--sizes-size-5);
  --inputImage-base-default-bg-color: var(--colors-neutral-fill-11);
  --inputImage-base-hover-top-border-color: var(--colors-brand-5);
  --inputImage-base-hover-top-border-width: var(--borders-width-2);
  --inputImage-base-hover-top-border-style: var(--borders-style-2);
  --inputImage-base-hover-right-border-color: var(--colors-brand-5);
  --inputImage-base-hover-right-border-width: var(--borders-width-2);
  --inputImage-base-hover-right-border-style: var(--borders-style-2);
  --inputImage-base-hover-bottom-border-color: var(--colors-brand-5);
  --inputImage-base-hover-bottom-border-width: var(--borders-width-2);
  --inputImage-base-hover-bottom-border-style: var(--borders-style-2);
  --inputImage-base-hover-left-border-color: var(--colors-brand-5);
  --inputImage-base-hover-left-border-width: var(--borders-width-2);
  --inputImage-base-hover-left-border-style: var(--borders-style-2);
  --inputImage-base-hover-color: var(--colors-neutral-text-5);
  --inputImage-base-hover-icon-color: var(--colors-neutral-text-5);
  --inputImage-base-hover-bg-color: var(--colors-neutral-fill-11);
  --inputImage-base-active-top-border-color: var(--colors-brand-5);
  --inputImage-base-active-top-border-width: var(--borders-width-2);
  --inputImage-base-active-top-border-style: var(--borders-style-2);
  --inputImage-base-active-right-border-color: var(--colors-brand-5);
  --inputImage-base-active-right-border-width: var(--borders-width-2);
  --inputImage-base-active-right-border-style: var(--borders-style-2);
  --inputImage-base-active-bottom-border-color: var(--colors-brand-5);
  --inputImage-base-active-bottom-border-width: var(--borders-width-2);
  --inputImage-base-active-bottom-border-style: var(--borders-style-2);
  --inputImage-base-active-left-border-color: var(--colors-brand-5);
  --inputImage-base-active-left-border-width: var(--borders-width-2);
  --inputImage-base-active-left-border-style: var(--borders-style-2);
  --inputImage-base-active-color: var(--colors-neutral-text-5);
  --inputImage-base-active-icon-color: var(--colors-neutral-text-5);
  --inputImage-base-active-bg-color: var(--colors-neutral-fill-11);
  --inputImage-base-disabled-top-border-color: var(--colors-neutral-line-8);
  --inputImage-base-disabled-top-border-width: var(--borders-width-2);
  --inputImage-base-disabled-top-border-style: var(--borders-style-2);
  --inputImage-base-disabled-right-border-color: var(--colors-neutral-line-8);
  --inputImage-base-disabled-right-border-width: var(--borders-width-2);
  --inputImage-base-disabled-right-border-style: var(--borders-style-2);
  --inputImage-base-disabled-bottom-border-color: var(--colors-neutral-line-8);
  --inputImage-base-disabled-bottom-border-width: var(--borders-width-2);
  --inputImage-base-disabled-bottom-border-style: var(--borders-style-2);
  --inputImage-base-disabled-left-border-color: var(--colors-neutral-line-8);
  --inputImage-base-disabled-left-border-width: var(--borders-width-2);
  --inputImage-base-disabled-left-border-style: var(--borders-style-2);
  --inputImage-base-disabled-color: var(--colors-neutral-text-6);
  --inputImage-base-disabled-icon-color: var(--colors-neutral-text-6);
  --inputImage-base-disabled-bg-color: var(--colors-neutral-fill-10);
  --ImageControl-addBtn-bg: var(--inputImage-base-default-bg-color);
  --ImageControl-addBtn-border: var(--colors-neutral-line-7);
  --ImageControl-addBtn-borderRadius: var(--borders-radius-3);
  --ImageControl-addBtn-color: var(--inputImage-base-default-color);
  --ImageControl-addBtn-onActive-bg: var(--inputImage-base-active-bg-color);
  --ImageControl-addBtn-onActive-border: var(--colors-brand-5);
  --ImageControl-addBtn-onActive-color: var(--inputImage-base-active-color);
  --ImageControl-addBtn-onDisabled-bg: var(--inputImage-base-disabled-bg-color);
  --ImageControl-addBtn-onDisabled-border: var(--colors-neutral-line-8);
  --ImageControl-addBtn-onDisabled-color: var(--inputImage-base-disabled-color);
  --ImageControl-addBtn-onHover-bg: var(--inputImage-base-hover-bg-color);
  --ImageControl-addBtn-onHover-border: var(--colors-brand-5);
  --ImageControl-addBtn-onHover-color: var(--inputImage-base-hover-color);
  --ImageControl-addBtn-upload-color: var(--inputImage-base-default-color);
  --ImageControl-progress-borderRadius: var(--borders-radius-2);
  --select-base-default-top-border-color: var(--colors-neutral-line-8);
  --select-base-default-top-border-width: var(--borders-width-2);
  --select-base-default-top-border-style: var(--borders-style-2);
  --select-base-default-right-border-color: var(--colors-neutral-line-8);
  --select-base-default-right-border-width: var(--borders-width-2);
  --select-base-default-right-border-style: var(--borders-style-2);
  --select-base-default-bottom-border-color: var(--colors-neutral-line-8);
  --select-base-default-bottom-border-width: var(--borders-width-2);
  --select-base-default-bottom-border-style: var(--borders-style-2);
  --select-base-default-left-border-color: var(--colors-neutral-line-8);
  --select-base-default-left-border-width: var(--borders-width-2);
  --select-base-default-left-border-style: var(--borders-style-2);
  --select-base-default-top-right-border-radius: var(--borders-radius-3);
  --select-base-default-top-left-border-radius: var(--borders-radius-3);
  --select-base-default-bottom-right-border-radius: var(--borders-radius-3);
  --select-base-default-bottom-left-border-radius: var(--borders-radius-3);
  --select-base-default-paddingTop: var(--sizes-size-3);
  --select-base-default-paddingBottom: var(--sizes-size-3);
  --select-base-default-paddingLeft: var(--sizes-size-6);
  --select-base-default-paddingRight: var(--sizes-size-6);
  --select-base-default-color: var(--colors-neutral-text-2);
  --select-base-default-fontSize: var(--fonts-size-7);
  --select-base-default-fontWeight: var(--fonts-weight-6);
  --select-base-default-bg-color: var(--colors-neutral-fill-11);
  --select-base-default-option-paddingTop: var(--sizes-size-0);
  --select-base-default-option-paddingBottom: var(--sizes-size-0);
  --select-base-default-option-paddingLeft: var(--sizes-size-6);
  --select-base-default-option-paddingRight: var(--sizes-size-6);
  --select-base-default-option-color: var(--colors-neutral-text-2);
  --select-base-default-option-fontSize: var(--fonts-size-7);
  --select-base-default-option-fontWeight: var(--fonts-weight-6);
  --select-base-default-option-bg-color: transparent;
  --select-base-default-option-line-height: var(--sizes-base-16);
  --select-base-hover-top-border-color: var(--colors-brand-5);
  --select-base-hover-top-border-width: var(--borders-width-2);
  --select-base-hover-top-border-style: var(--borders-style-2);
  --select-base-hover-right-border-color: var(--colors-brand-5);
  --select-base-hover-right-border-width: var(--borders-width-2);
  --select-base-hover-right-border-style: var(--borders-style-2);
  --select-base-hover-bottom-border-color: var(--colors-brand-5);
  --select-base-hover-bottom-border-width: var(--borders-width-2);
  --select-base-hover-bottom-border-style: var(--borders-style-2);
  --select-base-hover-left-border-color: var(--colors-brand-5);
  --select-base-hover-left-border-width: var(--borders-width-2);
  --select-base-hover-left-border-style: var(--borders-style-2);
  --select-base-hover-bg-color: var(--colors-neutral-fill-11);
  --select-base-hover-option-color: var(--colors-neutral-text-2);
  --select-base-hover-option-bg-color: var(--colors-brand-10);
  --select-base-active-top-border-color: var(--colors-brand-5);
  --select-base-active-top-border-width: var(--borders-width-2);
  --select-base-active-top-border-style: var(--borders-style-2);
  --select-base-active-right-border-color: var(--colors-brand-5);
  --select-base-active-right-border-width: var(--borders-width-2);
  --select-base-active-right-border-style: var(--borders-style-2);
  --select-base-active-bottom-border-color: var(--colors-brand-5);
  --select-base-active-bottom-border-width: var(--borders-width-2);
  --select-base-active-bottom-border-style: var(--borders-style-2);
  --select-base-active-left-border-color: var(--colors-brand-5);
  --select-base-active-left-border-width: var(--borders-width-2);
  --select-base-active-left-border-style: var(--borders-style-2);
  --select-base-active-shadow: var(--shadows-shadow-none);
  --select-base-active-bg-color: var(--colors-neutral-fill-11);
  --select-base-active-option-color: var(--colors-brand-5);
  --select-base-active-option-bg-color: var(--colors-neutral-fill-11);
  --select-base-disabled-top-border-color: var(--colors-neutral-line-8);
  --select-base-disabled-top-border-width: var(--borders-width-2);
  --select-base-disabled-top-border-style: var(--borders-style-2);
  --select-base-disabled-right-border-color: var(--colors-neutral-line-8);
  --select-base-disabled-right-border-width: var(--borders-width-2);
  --select-base-disabled-right-border-style: var(--borders-style-2);
  --select-base-disabled-bottom-border-color: var(--colors-neutral-line-8);
  --select-base-disabled-bottom-border-width: var(--borders-width-2);
  --select-base-disabled-bottom-border-style: var(--borders-style-2);
  --select-base-disabled-left-border-color: var(--colors-neutral-line-8);
  --select-base-disabled-left-border-width: var(--borders-width-2);
  --select-base-disabled-left-border-style: var(--borders-style-2);
  --select-base-disabled-bg-color: var(--colors-neutral-fill-10);
  --select-base-disabled-option-color: var(--colors-neutral-text-6);
  --select-base-disabled-option-bg-color: var(--colors-neutral-fill-11);
  --select-multiple-top-right-border-radius: var(--borders-radius-2);
  --select-multiple-top-left-border-radius: var(--borders-radius-2);
  --select-multiple-bottom-right-border-radius: var(--borders-radius-2);
  --select-multiple-bottom-left-border-radius: var(--borders-radius-2);
  --select-multiple-paddingTop: var(--sizes-size-0);
  --select-multiple-paddingBottom: var(--sizes-size-0);
  --select-multiple-paddingLeft: var(--sizes-size-3);
  --select-multiple-paddingRight: var(--sizes-size-3);
  --select-multiple-marginTop: var(--sizes-size-0);
  --select-multiple-marginBottom: var(--sizes-size-0);
  --select-multiple-marginLeft: var(--sizes-size-0);
  --select-multiple-marginRight: var(--sizes-size-3);
  --select-multiple-color: var(--colors-neutral-text-2);
  --select-multiple-fontSize: var(--fonts-size-8);
  --select-multiple-fontWeight: var(--fonts-weight-6);
  --select-multiple-bg-color: var(--colors-neutral-fill-10);
  --select-multiple-hover-bg-color: var(--colors-brand-10);
  --select-multiple-icon-color: var(--colors-neutral-text-6);
  --select-multiple-icon-hover-color: var(--colors-neutral-text-2);
  --select-group-color: var(--colors-neutral-text-5);
  --select-group-fontSize: var(--fonts-size-7);
  --select-group-fontWeight: var(--fonts-weight-6);
  --select-group-lineHeight: var(--fonts-lineHeight-2);
  --select-group-paddingTop: var(--sizes-size-3);
  --select-group-paddingBottom: var(--sizes-size-3);
  --select-group-paddingLeft: var(--sizes-size-7);
  --select-group-paddingRight: var(--sizes-size-7);
  --select-table-header-paddingTop: var(--sizes-size-5);
  --select-table-header-paddingBottom: var(--sizes-size-5);
  --select-table-header-paddingLeft: var(--sizes-size-7);
  --select-table-header-paddingRight: var(--sizes-base-9);
  --select-table-option-paddingTop: var(--sizes-size-4);
  --select-table-option-paddingBottom: var(--sizes-size-5);
  --select-table-option-paddingLeft: var(--sizes-size-7);
  --select-table-option-paddingRight: var(--sizes-base-9);
  --select-table-color: var(--colors-neutral-text-2);
  --select-table-fontSize: var(--fonts-size-8);
  --select-tree-color: var(--colors-neutral-text-2);
  --select-tree-fontSize: var(--fonts-size-7);
  --select-tree-hover-bg-color: var(--colors-neutral-fill-10);
  --select-tree-active-bg-color: var(--colors-brand-10);
  --Form-select-bg: var(--select-base-default-bg-color);
  --Form-select-mobile-icon-check-color: var(--colors-brand-5);
  --Form-select-height: var(--Form-select-outer-top);
  --Form-select-borderColor: var(--select-base-default-top-border-color)
    var(--select-base-default-right-border-color) var(--select-base-default-bottom-border-color)
    var(--select-base-default-left-border-color);
  --Form-select-borderRadius: var(--select-base-default-top-left-border-radius)
    var(--select-base-default-top-right-border-radius)
    var(--select-base-default-bottom-right-border-radius)
    var(--select-base-default-bottom-left-border-radius);
  --Form-select-borderWidth: var(--select-base-default-top-border-width)
    var(--select-base-default-right-border-width) var(--select-base-default-bottom-border-width)
    var(--select-base-default-left-border-width);
  --Form-select-caret-iconColor: var(--colors-neutral-text-5);
  --Form-select-caret-onHover-iconColor: var(--colors-neutral-text-5);
  --Form-select-caret-fontSize: var(--fonts-size-8);
  --Form-select-checkall-bottomBorder: #eceff8;
  --Form-select-color: var(--select-base-default-color);
  --Form-select-input-fontSize: var(--fontSizeSm);
  --Form-select-menu-bg: var(--colors-neutral-fill-11);
  --Form-select-menu-color: var(--colors-neutral-text-2);
  --Form-select-menu-height: var(--sizes-base-12);
  --Form-select-menu-onActive-bg: var(--select-base-active-option-bg-color);
  --Form-select-menu-onActive-color: var(--select-base-active-option-color);
  --Form-select-menu-onDisabled-bg: var(--select-base-disabled-option-bg-color);
  --Form-select-menu-onDisabled-color: var(--select-base-disabled-option-color);
  --Form-select-menu-onHover-bg: var(--select-base-hover-option-bg-color);
  --Form-select-menu-onHover-color: var(--select-base-hover-option-color);
  --Form-select-group-color: var(--Form-select-caret-iconColor);
  --Form-select-onError-borderColor: var(--Form-input-onError-borderColor);
  --Form-select-onFocused-borderColor: var(--Form-input-onFocused-borderColor);
  --Form-select-onFocused-color: var(--Form-select-color);
  --Form-select-onHover-bg: var(--select-base-hover-bg-color);
  --Form-select-onHover-borderColor: var(--colors-brand-5);
  --Form-select-outer-borderWidth: var(--borders-width-2);
  --Form-select-outer-borderColor: var(--colors-neutral-fill-9);
  --Form-select-outer-top: var(--sizes-base-16);
  --Form-select-outer-boxShadow: var(--shadows-shadow-normal);
  --Form-select-paddingX: var(--Form-input-paddingX);
  --Form-select-placeholderColor: var(--Form-input-placeholderColor);
  --Form-select-popoverGap: var(--borders-radius-3);
  --Form-select-icon-rigin: var(--sizes-size-9);
  --Form-select-search-height: var(--sizes-base-15);
  --Form-select-value-bgColor: var(--select-multiple-bg-color);
  --Form-select-value-bgColor--dark: var(--colors-neutral-fill-4);
  --Form-select-value-borderColor: var(--colors-neutral-line-9);
  --Form-select-valueIcon-color: var(--select-multiple-icon-color);
  --Form-select-valueIcon-color--dark: var(--colors-neutral-text-8);
  --Form-select-valueIcon-onHover-color: var(--select-multiple-icon-hover-color);
  --Form-select-multiple-bgColor: var(--colors-neutral-fill-10);
  --Form-selectOption-height: var(--Form-select-height);
  --Form-selectValue-bg: hsl(211.0588235294, 102.5%, 90%);
  --Form-selectValue-onHover-bgColor: var(--select-multiple-hover-bg-color);
  --Form-selectValue-borderColor: var(--colors-brand-7);
  --Form-selectValue-color: var(--colors-brand-5);
  --Form-selectValue-fontSize: var(--select-multiple-fontSize);
  --Form-selectValue-onDisable-bg: hsl(211.0588235294, 102.5%, 95%);
  --Form-selectValue-onHover-bg: hsl(211.0588235294, 102.5%, 85%);
  --Form-selectValue-onDisabled-color: var(--Form-select-caret-iconColor);
  --Form-selectValue-onInvalid-color: var(--danger);
  --Form-valueLabel-maxWidth: 7.5rem;
  --Form-select-onFocus-boxShadow: none;
  --ResultBox-tag-height: 1.375rem;
  --ResultBox-tag-marginBottom: var(--select-multiple-marginBottom);
  --ResultBox-icon--onDisabled-color: #ebebeb;
  --ResultBox-icon--onHover-color: var(--select-multiple-icon-hover-color);
  --ResultBox-icon-color: var(--select-multiple-icon-color);
  --ResultBox-value--onDisabled-color: #cccccc;
  --ResultBox-value--onHover-bg: var(--select-multiple-hover-bg-color);
  --ResultBox-value--onHover-bg--dark: #b8babf;
  --ResultBox-value-bg: var(--select-multiple-bg-color);
  --ResultBox-value-color: var(--select-multiple-color);
  --ResultBox-value-clear-bg: var(--colors-neutral-fill-8);
  --ResultBox-value-clear-hover-bg: var(--colors-neutral-fill-9);
  --Tree-max-height: 300px;
  --Tree-indent: var(--gap-md);
  --Tree-icon-gap: var(--sizes-size-5);
  --Tree-icon-margin-right: 0.5rem;
  --Tree-inputHeight: calc(var(--Form-input-height) * 0.85);
  --Tree-item-onHover-bg: var(--colors-neutral-fill-10);
  --Tree-item-onHover-bg-pure: var(--select-tree-hover-bg-color);
  --Tree-itemArrowWidth: 1rem;
  --Tree-itemHeight: var(--sizes-base-12);
  --Tree-itemLabel--onChecked-color: var(--Form-selectValue-color);
  --TreeSelect-popover-bg: var(--colors-neutral-fill-11);
  --Tree-item-text-max-height: 15.625rem;
  --Tree-item-text-top: 0.25rem;
  --Tree-item-arrow-padding-left: 0.25rem;
  --Tree-item-arrow-color: #84868c;
  --Tree-item-onChekced-bg: var(--select-tree-active-bg-color);
  --Tree-item-onChekced-bg-borderRadius: var(--borders-radius-2);
  --inputDate-default-top-border-color: var(--colors-neutral-line-8);
  --inputDate-default-top-border-width: var(--borders-width-2);
  --inputDate-default-top-border-style: var(--borders-style-2);
  --inputDate-default-right-border-color: var(--colors-neutral-line-8);
  --inputDate-default-right-border-width: var(--borders-width-2);
  --inputDate-default-right-border-style: var(--borders-style-2);
  --inputDate-default-bottom-border-color: var(--colors-neutral-line-8);
  --inputDate-default-bottom-border-width: var(--borders-width-2);
  --inputDate-default-bottom-border-style: var(--borders-style-2);
  --inputDate-default-left-border-color: var(--colors-neutral-line-8);
  --inputDate-default-left-border-width: var(--borders-width-2);
  --inputDate-default-left-border-style: var(--borders-style-2);
  --inputDate-default-top-right-border-radius: var(--borders-radius-3);
  --inputDate-default-top-left-border-radius: var(--borders-radius-3);
  --inputDate-default-bottom-right-border-radius: var(--borders-radius-3);
  --inputDate-default-bottom-left-border-radius: var(--borders-radius-3);
  --inputDate-default-paddingTop: var(--sizes-size-3);
  --inputDate-default-paddingBottom: var(--sizes-size-3);
  --inputDate-default-paddingLeft: var(--sizes-size-6);
  --inputDate-default-paddingRight: var(--sizes-size-6);
  --inputDate-default-fontSize: var(--fonts-size-7);
  --inputDate-default-fontWeight: var(--fonts-weight-6);
  --inputDate-default-height: var(--sizes-base-16);
  --inputDate-default-color: var(--colors-neutral-text-2);
  --inputDate-default-bg-color: var(--colors-neutral-fill-11);
  --inputDate-default-icon: '<svg viewBox="0 0 13 12" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"><g transform="translate(-1.338385, -2.000000)"><rect x="0" y="0" width="16" height="16"></rect><path d="M6,2 L6,3 L10,3 L10,2 L11,2 L11,3 L14,3 L14,14 L2,14 L2,3 L5,3 L5,2 L6,2 Z M13,7 L3,7 L3,13 L13,13 L13,7 Z M5,4 L3,4 L3,6 L13,6 L13,4 L11,4 L11,5 L10,5 L10,4 L6,4 L6,5 L5,5 L5,4 Z" fill="currentColor"></path></g></g></svg>';
  --inputDate-default-icon-color: var(--colors-neutral-text-5);
  --inputDate-default-icon-size: var(--sizes-base-7);
  --inputDate-default-title-color: var(--colors-neutral-text-2);
  --inputDate-default-title-arrow-color: var(--colors-neutral-text-5);
  --inputDate-default-option-color: var(--colors-neutral-text-2);
  --inputDate-default-option-bg-color: var(--colors-neutral-fill-11);
  --inputDate-default-option-today-border-color: var(--colors-brand-5);
  --inputDate-default-option-top-right-border-radius: var(--borders-radius-2);
  --inputDate-default-option-top-left-border-radius: var(--borders-radius-2);
  --inputDate-default-option-bottom-right-border-radius: var(--borders-radius-2);
  --inputDate-default-option-bottom-left-border-radius: var(--borders-radius-2);
  --inputDate-hover-top-border-color: var(--colors-brand-5);
  --inputDate-hover-top-border-width: var(--borders-width-2);
  --inputDate-hover-top-border-style: var(--borders-style-2);
  --inputDate-hover-right-border-color: var(--colors-brand-5);
  --inputDate-hover-right-border-width: var(--borders-width-2);
  --inputDate-hover-right-border-style: var(--borders-style-2);
  --inputDate-hover-bottom-border-color: var(--colors-brand-5);
  --inputDate-hover-bottom-border-width: var(--borders-width-2);
  --inputDate-hover-bottom-border-style: var(--borders-style-2);
  --inputDate-hover-left-border-color: var(--colors-brand-5);
  --inputDate-hover-left-border-width: var(--borders-width-2);
  --inputDate-hover-left-border-style: var(--borders-style-2);
  --inputDate-hover-color: var(--colors-neutral-text-2);
  --inputDate-hover-bg-color: var(--colors-neutral-fill-11);
  --inputDate-hover-title-color: var(--colors-brand-6);
  --inputDate-hover-title-arrow-color: var(--colors-neutral-text-2);
  --inputDate-hover-option-color: var(--colors-neutral-text-2);
  --inputDate-hover-option-bg-color: var(--colors-neutral-fill-10);
  --inputDate-active-top-border-color: var(--colors-brand-5);
  --inputDate-active-top-border-width: var(--borders-width-2);
  --inputDate-active-top-border-style: var(--borders-style-2);
  --inputDate-active-right-border-color: var(--colors-brand-5);
  --inputDate-active-right-border-width: var(--borders-width-2);
  --inputDate-active-right-border-style: var(--borders-style-2);
  --inputDate-active-bottom-border-color: var(--colors-brand-5);
  --inputDate-active-bottom-border-width: var(--borders-width-2);
  --inputDate-active-bottom-border-style: var(--borders-style-2);
  --inputDate-active-left-border-color: var(--colors-brand-5);
  --inputDate-active-left-border-width: var(--borders-width-2);
  --inputDate-active-left-border-style: var(--borders-style-2);
  --inputDate-active-shadow: var(--shadows-shadow-none);
  --inputDate-active-color: var(--colors-neutral-text-2);
  --inputDate-active-bg-color: var(--colors-neutral-fill-11);
  --inputDate-active-option-color: var(--colors-neutral-text-11);
  --inputDate-active-option-bg-color: var(--colors-brand-5);
  --inputDate-disabled-top-border-color: var(--colors-neutral-line-8);
  --inputDate-disabled-top-border-width: var(--borders-width-2);
  --inputDate-disabled-top-border-style: var(--borders-style-2);
  --inputDate-disabled-right-border-color: var(--colors-neutral-line-8);
  --inputDate-disabled-right-border-width: var(--borders-width-2);
  --inputDate-disabled-right-border-style: var(--borders-style-2);
  --inputDate-disabled-bottom-border-color: var(--colors-neutral-line-8);
  --inputDate-disabled-bottom-border-width: var(--borders-width-2);
  --inputDate-disabled-bottom-border-style: var(--borders-style-2);
  --inputDate-disabled-left-border-color: var(--colors-neutral-line-8);
  --inputDate-disabled-left-border-width: var(--borders-width-2);
  --inputDate-disabled-left-border-style: var(--borders-style-2);
  --inputDate-disabled-color: var(--colors-neutral-text-2);
  --inputDate-disabled-bg-color: var(--colors-neutral-fill-10);
  --inputDate-disabled-option-color: var(--colors-neutral-text-6);
  --inputDate-disabled-option-bg-color: var(--colors-neutral-fill-11);
  --inputDate-other-color: var(--colors-neutral-text-2);
  --inputDate-other-bg-color: var(--colors-neutral-fill-11);
  --inputDate-other-top-right-border-radius: var(--borders-radius-2);
  --inputDate-other-top-left-border-radius: var(--borders-radius-2);
  --inputDate-other-bottom-right-border-radius: var(--borders-radius-2);
  --inputDate-other-bottom-left-border-radius: var(--borders-radius-2);
  --inputDate-other-hover-color: var(--colors-neutral-text-2);
  --inputDate-other-hover-bg-color: var(--colors-neutral-fill-10);
  --inputDate-other-active-color: var(--colors-neutral-text-11);
  --inputDate-other-active-bg-color: var(--colors-brand-5);
  --inputDate-other-disabled-color: var(--colors-neutral-text-6);
  --inputDate-other-disabled-bg-color: var(--colors-neutral-fill-10);
  --inputDate-range-line-height: var(--borders-width-3);
  --inputDate-range-line-color: var(--colors-brand-4);
  --inputDate-range-separator-width: var(--sizes-size-5);
  --inputDate-range-separator-margin: var(--sizes-size-5);
  --inputDate-range-separator-color: var(--colors-neutral-fill-6);
  --inputDate-range-between-color: var(--colors-brand-10);
  --DatePicker-bg: var(--inputDate-default-bg-color);
  --DatePicker-borderColor: var(--inputDate-default-top-border-color)
    var(--inputDate-default-right-border-color) var(--inputDate-default-bottom-border-color)
    var(--inputDate-default-left-border-color);
  --DatePicker-borderStyle: var(--inputDate-default-top-border-style)
    var(--inputDate-default-right-border-style) var(--inputDate-default-bottom-border-style)
    var(--inputDate-default-left-border-style);
  --DatePicker-borderWidth: var(--inputDate-default-top-border-width)
    var(--inputDate-default-right-border-width) var(--inputDate-default-bottom-border-width)
    var(--inputDate-default-left-border-width);
  --DatePicker-borderRadius: var(--inputDate-default-top-left-border-radius)
    var(--inputDate-default-top-right-border-radius)
    var(--inputDate-default-bottom-right-border-radius)
    var(--inputDate-default-bottom-left-border-radius);
  --DatePicker-color: var(--inputDate-default-color);
  --DatePicker-header-onHover-color: var(--inputDate-hover-title-color);
  --DatePicker-arrow-color: var(--inputDate-default-title-arrow-color);
  --DatePicker-fontSize: var(--inputDate-default-fontSize);
  --DatePicker-header-select-borderColor: #fff;
  --DatePicker-height: var(--inputDate-default-height);
  --DatePicker-iconColor: var(--icon-color);
  --DatePicker-lineHeight: var(--Form-input-lineHeight);
  --DatePicker-onFocused-borderColor: var(--inputDate-active-top-border-color)
    var(--inputDate-active-right-border-color) var(--inputDate-active-bottom-border-color)
    var(--inputDate-active-left-border-color);
  --DatePicker-onHover-bg: var(--inputDate-hover-bg-color);
  --DatePicker-onHover-borderColor: var(--inputDate-hover-top-border-color)
    var(--inputDate-hover-right-border-color) var(--inputDate-hover-bottom-border-color)
    var(--inputDate-hover-left-border-color);
  --DatePicker-onDisabled-bg: var(--colors-neutral-text-9);
  --DatePicker-onDisabled-color: var(--colors-neutral-text-6);
  --DatePicker-onHover-iconColor: var(--colors-brand-5);
  --DatePicker-paddingX: 0.75rem;
  --DatePicker-paddingY: var(--sizes-size-3);
  --DatePicker-placeholderColor: var(--colors-neutral-text-6);
  --DatePicker-minWidth: calc(
    var(--fontSizeLg) * 5 + var(--DatePicker-paddingX) * 2 + var(--Form-input-clearBtn-size) * 2
  );
  --DateRangePicker-minWidth: calc(
    var(--fontSizeLg) * 8 + var(--DatePicker-paddingX) * 2 + var(--Form-input-clearBtn-size) * 2
  );
  --DateRangePicker-activeCursor-color: var(--inputDate-range-line-color);
  --DateRangePicker-activeCursor-height: var(--inputDate-range-line-height);
  --Calendar-btn-bg: var(--info);
  --Calendar-btn-border: var(--Calendar-btn-bg);
  --Calendar-btn-borderRadius: var(--Button-borderRadius);
  --Calendar-btn-color: var(--colors-neutral-fill-11);
  --Calendar-btn-fontSize: var(--fontSizeSm);
  --Calendar-btn-height: 1.875rem;
  --Calendar-btn-lineHeight: var(--lineHeightBase);
  --Calendar-btn-onActive-bg: var(--colors-brand-4);
  --Calendar-btn-onActive-border: var(--colors-brand-3);
  --Calendar-btn-onActive-color: var(--Calendar-btn-color);
  --Calendar-btn-onHover-bg: var(--colors-brand-4);
  --Calendar-btn-onHover-border: var(--colors-brand-3);
  --Calendar-btn-onHover-color: var(--Calendar-btn-color);
  --Calendar-btn-paddingX: 0.625rem;
  --Calendar-btn-paddingY: calc(
    (var(--Calendar-btn-height) - var(--Calendar-btn-lineHeight) * var(--Calendar-btn-fontSize)) / 2
  );
  --Calendar-btnCancel-bg: var(--light);
  --Calendar-btnCancel-border: var(--colors-neutral-line-7);
  --Calendar-btnCancel-borderRadius: var(--borders-radius-3);
  --Calendar-btnCancel-color: var(--text-color);
  --Calendar-btnCancel-onActive-bg: var(--colors-neutral-fill-11);
  --Calendar-btnCancel-onActive-border: var(--colors-brand-4);
  --Calendar-btnCancel-onActive-color: var(--colors-brand-4);
  --Calendar-btnCancel-onHover-bg: var(--colors-neutral-fill-11);
  --Calendar-btnCancel-onHover-border: var(--colors-brand-6);
  --Calendar-btnCancel-onHover-color: var(--colors-brand-6);
  --Calendar-cell-bg: var(--inputDate-default-option-bg-color);
  --Calendar-cell-onActive-bg: var(--inputDate-active-option-bg-color);
  --Calendar-cell-onBetween-bg: var(--inputDate-range-between-color);
  --Calendar-cell-onDisabled-bg: var(--inputDate-other-disabled-bg-color);
  --Calendar-cell-onHover-bg: var(--inputDate-hover-option-bg-color);
  --Calendar-color: var(--inputDate-default-option-color);
  --Calendar-fontSize: var(--fontSizeSm);
  --Calendar-input-borderColor: var(--borderColor);
  --Calendar-input-borderRadius: var(--borders-radius-3);
  --Calendar-input-color: var(--info);
  --Calendar-input-fontSize: var(--fontSizeBase);
  --Calendar-input-height: 2.5rem;
  --Calendar-input-lineHeight: var(--lineHeightBase);
  --Calendar-input-onFocused-borderColor: var(--info);
  --Calendar-input-paddingX: 0.625rem;
  --Calendar-input-paddingY: calc(
    (
        var(--Calendar-input-height) - var(--Calendar-input-lineHeight) *
          var(--Calendar-input-fontSize)
      ) /
      2
  );
  --Calendar-shortcut-color: #151b26;
  --Calendar-shortcut-decoration: none;
  --Calendar-shortcut-onHover-color: var(--colors-brand-6);
  --Calendar-shortcut-onHover-decoration: none;
  --Calendar-shortcuts-bg: var(--colors-neutral-text-9);
  --Calendar-shortcuts-height: var(--sizes-size-9);
  --Calendar-wLabel-color: var(--colors-neutral-text-6);
  --Calendar-icon-bottom: -0.25rem;
  --Calendar-icon-width: var(--sizes-size-6);
  --Calendar-icon-height: var(--sizes-size-6);
  --Calendar-borderWidth: var(--borders-width-2);
  --Calendar-borderColor: var(--inputDate-default-option-today-border-color);
  --Calendar-rdt-day: 6.875rem;
  --Calendar-schedule-content-padding: 0 var(--sizes-size-3);
  --Calendar-schedule-content-height: var(--sizes-base-10);
  --Calendar-schedule-content-color: var(--colors-neutral-text-11);
  --inputTime-default-icon: '<svg viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><title>ic_时间</title><g id="ic_时间" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"><circle id="1-FL" stroke="currentColor" cx="8" cy="8" r="6.5" stroke-width="1px" fill="none" stroke-linecap="butt" stroke-linejoin="round"/><polyline id="2-FLW" stroke="currentColor" points="7.87443646 5.5 7.87443646 8.53778873 5.5 9.28133409" stroke-width="1px" fill="none" stroke-linecap="butt" stroke-linejoin="round"/></g></svg>';
  --inputTime-default-fontSize: var(--fonts-size-8);
  --inputTime-default-fontWeight: var(--fonts-weight-6);
  --inputTime-default-color: var(--colors-neutral-text-2);
  --inputTime-default-bg-color: var(--colors-neutral-fill-11);
  --inputTime-hover-color: var(--colors-neutral-text-2);
  --inputTime-hover-bg-color: var(--colors-neutral-fill-10);
  --inputTime-active-color: var(--colors-neutral-text-2);
  --inputTime-active-bg-color: var(--colors-brand-10);
  --signature-tool-height: 2.5rem;
  --signature-tool-margin-top: 0.5rem;
  --signature-placeholder-gap: 0.625rem;
  --signature-placeholder-color: var(--colors-neutral-line-6);
  --steps-base-color: var(--colors-neutral-text-2);
  --steps-base-fontSize: var(--fonts-size-7);
  --steps-base-fontWeight: var(--fonts-weight-6);
  --steps-base-subTitle-color: var(--colors-neutral-text-2);
  --steps-base-subTitle-fontSize: var(--fonts-size-7);
  --steps-base-subTitle-fontWeight: var(--fonts-weight-6);
  --steps-base-des-color: var(--colors-neutral-text-5);
  --steps-base-des-fontSize: var(--fonts-size-8);
  --steps-base-des-fontWeight: var(--fonts-weight-6);
  --steps-base-title-paddingRight: var(--sizes-size-5);
  --steps-base-subTitle-paddingLeft: var(--sizes-size-6);
  --steps-base-icon-size: var(--sizes-base-12);
  --steps-base-icon-paddingRight: var(--sizes-size-5);
  --steps-base-icon-fontSize: var(--fonts-size-8);
  --steps-base-line-color: var(--colors-neutral-line-8);
  --steps-base-line-active-color: var(--colors-brand-5);
  --steps-status-wait-bg-color: var(--colors-neutral-fill-11);
  --steps-status-wait-color: var(--colors-neutral-text-8);
  --steps-status-process-bg-color: var(--colors-brand-5);
  --steps-status-process-color: var(--colors-neutral-text-11);
  --steps-status-finish-bg-color: var(--colors-brand-5);
  --steps-status-finish-color: var(--colors-neutral-text-11);
  --steps-status-error-bg-color: var(--colors-error-5);
  --steps-status-error-color: var(--colors-neutral-text-11);
  --steps-dot-icon-size: var(--sizes-size-5);
  --steps-dot-wait-bg-color: var(--colors-neutral-fill-8);
  --steps-dot-process-bg-color: var(--colors-brand-5);
  --steps-dot-finish-bg-color: var(--colors-neutral-fill-11);
  --steps-dot-error-bg-color: var(--colors-error-5);
  --steps-simple-icon: '<svg viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><g stroke="none" stroke-width="1" fill="currentColor" fill-rule="evenodd"><polyline  stroke="currentColor" transform="translate(5.496854, 8.006854) scale(-1, 1) rotate(-135.000000) translate(-5.496854, -8.006854) " points="1.49685425 4.00685425 9.49685425 4.00685425 9.49685425 12.0068542" stroke-width="1" fill="none" stroke-linecap="butt" stroke-linejoin="round"/></g></svg>';
  --steps-simple-icon-size: var(--sizes-size-8);
  --Steps-bg: var(--steps-status-wait-color);
  --Steps-status-success: var(--steps-status-finish-bg-color);
  --Steps-status-error: var(--steps-status-error-bg-color);
  --Steps-status-wait: var(--colors-neutral-text-5);
  --Steps-icon-fontsize: var(--steps-base-icon-fontSize);
  --Steps-title-fontsize: var(--steps-base-fontSize);
  --Steps-title-color: var(--steps-base-color);
  --Steps-sub-title-fontsize: var(--steps-base-subTitle-fontSize);
  --Steps-sub-title-color: var(--steps-base-subTitle-color);
  --Steps-description-title-fontsize: var(--steps-base-des-fontSize);
  --Steps-description-title-color: var(--steps-base-des-color);
  --Steps-line-bg: var(--steps-base-line-color);
  --Steps-line-success-bg: var(--steps-base-line-active-color);
  --alert-base-marginTop: var(--sizes-size-0);
  --alert-base-marginBottom: var(--sizes-size-9);
  --alert-base-marginLeft: var(--sizes-size-0);
  --alert-base-marginRight: var(--sizes-size-0);
  --alert-base-paddingTop: var(--sizes-size-3);
  --alert-base-paddingBottom: var(--sizes-size-3);
  --alert-base-paddingLeft: var(--sizes-size-9);
  --alert-base-paddingRight: var(--sizes-size-9);
  --alert-base-top-right-border-radius: var(--borders-radius-3);
  --alert-base-top-left-border-radius: var(--borders-radius-3);
  --alert-base-bottom-right-border-radius: var(--borders-radius-3);
  --alert-base-bottom-left-border-radius: var(--borders-radius-3);
  --alert-base-fontSize: var(--fonts-size-8);
  --alert-base-fontWeight: var(--fonts-weight-6);
  --alert-base-shadow: var(--shadows-shadow-none);
  --alert-base-title-paddingTop: var(--sizes-size-9);
  --alert-base-title-paddingBottom: var(--sizes-size-9);
  --alert-base-title-paddingLeft: var(--sizes-size-9);
  --alert-base-title-paddingRight: var(--sizes-size-9);
  --alert-base-title-color: var(--colors-neutral-text-2);
  --alert-base-title-fontSize: var(--fonts-size-7);
  --alert-base-title-fontWeight: var(--fonts-weight-5);
  --alert-base-title-margin-bottom: var(--sizes-size-3);
  --alert-icon-size: var(--sizes-base-8);
  --alert-icon-margin-right: var(--sizes-size-5);
  --alert-level-info-color: var(--colors-neutral-text-2);
  --alert-level-info-bg-color: var(--colors-brand-10);
  --alert-level-info-icon-color: var(--colors-brand-5);
  --alert-level-success-color: var(--colors-neutral-text-2);
  --alert-level-success-bg-color: var(--colors-success-10);
  --alert-level-success-icon-color: var(--colors-success-5);
  --alert-level-warning-color: var(--colors-neutral-text-2);
  --alert-level-warning-bg-color: var(--colors-warning-10);
  --alert-level-warning-icon-color: var(--colors-warning-5);
  --alert-level-danger-color: var(--colors-neutral-text-2);
  --alert-level-danger-bg-color: var(--colors-error-10);
  --alert-level-danger-icon-color: var(--colors-error-5);
  --Alert--danger-bg: var(--alert-level-danger-bg-color);
  --Alert--danger-borderColor: transparent;
  --Alert--danger-color: var(--alert-level-danger-color);
  --Alert--info-bg: var(--alert-level-info-bg-color);
  --Alert--info-borderColor: transparent;
  --Alert--info-color: var(--alert-level-info-color);
  --Alert--success-bg: var(--alert-level-success-bg-color);
  --Alert--success-borderColor: transparent;
  --Alert--success-color: var(--alert-level-success-color);
  --Alert--warning-bg: var(--alert-level-warning-bg-color);
  --Alert--warning-borderColor: transparent;
  --Alert--warning-color: var(--alert-level-warning-color);
  --Alert-borderColor: transparent;
  --Alert-borderRadius: var(--alert-base-top-left-border-radius)
    var(--alert-base-top-right-border-radius) var(--alert-base-bottom-right-border-radius)
    var(--alert-base-bottom-left-border-radius);
  --Alert-borderWidth: var(--borderWidth);
  --Alert-boxShadow: var(--alert-base-shadow);
  --Alert-fontSize: var(--alert-base-fontSize);
  --Alert-marginBottom: var(--alert-base-marginBottom);
  --Alert-paddingX: var(--sizes-size-9);
  --Alert-paddingY: var(--gap-xs);
  --Alert-fontColor: var(--colors-neutral-text-4);
  --Alert-title-fontColor: var(--colors-neutral-text-2);
  --Alert-height: var(--sizes-base-20);
  --Alert-actions-marginRight: var(--sizes-base-4);
  --spinner-base-color: var(--colors-link-5);
  --spinner-base-fontSize: var(--fonts-size-8);
  --spinner-base-fontWeight: var(--fonts-weight-6);
  --spinner-base-tip-size: var(--sizes-base-6);
  --spinner-sm-size: var(--sizes-base-8);
  --spinner-size-size: var(--sizes-base-16);
  --spinner-lg-size: var(--sizes-base-24);
  --spinner-overlay-bg: var(--colors-neutral-fill-11);
  --Spinner--lg-height: var(--spinner-lg-size);
  --Spinner--lg-width: var(--spinner-lg-size);
  --Spinner--sm-height: var(--spinner-sm-size);
  --Spinner--sm-width: var(--spinner-sm-size);
  --Spinner-bg: url('data:image/svg+xml;base64,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');
  --Spinner-height: var(--spinner-size-size);
  --Spinner-overlay-bg: rgba(255, 255, 255, 0.1);
  --Spinner-width: var(--spinner-size-size);
  --Spinner-color: var(--spinner-base-color);
  --Spinner-color--disabled: rgba(0, 0, 0, 0.65);
  --loading-dark-bg-color: var(--colors-neutral-text-11);
  --image-image-normal-paddingTop: var(--sizes-size-3);
  --image-image-normal-paddingBottom: var(--sizes-size-3);
  --image-image-normal-paddingLeft: var(--sizes-size-3);
  --image-image-normal-paddingRight: var(--sizes-size-3);
  --image-image-normal-color: var(--colors-neutral-text-2);
  --image-image-normal-fontSize: var(--fonts-size-7);
  --image-image-normal-title-marginTop: var(--sizes-size-0);
  --image-image-description-color: var(--colors-neutral-text-2);
  --image-image-description-fontSize: var(--fonts-size-8);
  --image-image-description-marginTop: var(--sizes-size-0);
  --image-image-normal-icon: '<svg viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><g  stroke="none" stroke-width="1" fill="currentColor" fill-rule="evenodd"><g><rect  opacity="0" x="0.5" y="0.5" width="15" height="15"></rect><path d="M7.9999,3.0001 C11.9889,3.0001 14.9999,6.8731 14.9999,8.0001 C14.9999,8.8831 11.9889,13.0001 7.9999,13.0001 C3.9609,13.0001 0.9999,8.8831 0.9999,8.0001 C0.9999,6.8731 3.9609,3.0001 7.9999,3.0001 Z M7.9999,4.0001 C4.7329,4.0001 2.2179,7.0861 2.0089,7.9731 C2.2749,8.7711 4.7189,12.0001 7.9999,12.0001 C11.2099,12.0001 13.7339,8.7311 13.9929,7.9631 C13.8069,7.1261 11.2709,4.0001 7.9999,4.0001 Z M7.975,5.879 C9.08,5.879 9.975,6.775 9.975,7.879 C9.975,8.983 9.08,9.879 7.975,9.879 C6.871,9.879 5.975,8.983 5.975,7.879 C5.975,6.775 6.871,5.879 7.975,5.879 Z M7.975,6.879 C7.424,6.879 6.975,7.327 6.975,7.879 C6.975,8.43 7.424,8.879 7.975,8.879 C8.527,8.879 8.975,8.43 8.975,7.879 C8.975,7.327 8.527,6.879 7.975,6.879 Z" ></path></g></g></svg>';
  --image-images-item-marginTop: var(--sizes-size-3);
  --image-images-item-marginBottom: var(--sizes-size-3);
  --image-images-item-marginLeft: var(--sizes-size-3);
  --image-images-item-marginRight: var(--sizes-size-3);
  --image-images-item-size: 3rem;
  --image-images-item-color: var(--colors-neutral-text-5);
  --image-images-preview-radius: var(--sizes-size-3);
  --image-images-preview-bgColor: var(--colors-neutral-text-11);
  --image-images-preview-paddingTop: var(--sizes-size-3);
  --image-images-preview-paddingBottom: var(--sizes-size-3);
  --image-images-preview-paddingLeft: var(--sizes-size-9);
  --image-images-preview-paddingRight: var(--sizes-size-9);
  --image-images-prev-icon: '<svg viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><g stroke="none" stroke-width="1" fill="currentColor" fill-rule="evenodd"><polyline stroke="currentColor" transform="translate(10.496854, 8.006854) rotate(-135.000000) translate(-10.496854, -8.006854) " points="6.49685425 4.00685425 14.4968542 4.00685425 14.4968542 12.0068542" stroke-width="1" fill="none" stroke-linecap="butt" stroke-linejoin="round"/></g></svg>';
  --image-images-next-icon: '<svg viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><g stroke="none" stroke-width="1" fill="currentColor" fill-rule="evenodd"><polyline stroke="currentColor" transform="translate(5.496854, 8.006854) scale(-1, 1) rotate(-135.000000) translate(-5.496854, -8.006854) " points="1.49685425 4.00685425 9.49685425 4.00685425 9.49685425 12.0068542" stroke-width="1" fill="none" stroke-linecap="butt" stroke-linejoin="round"/></g></svg>';
  --Tag-base-fontSize: var(--fonts-size-8);
  --Tag-base-fontWeight: var(--fonts-weight-6);
  --Tag-base-height: var(--sizes-base-12);
  --Tag-base-paddingTop: var(--sizes-size-0);
  --Tag-base-paddingBottom: var(--sizes-size-0);
  --Tag-base-paddingLeft: var(--sizes-size-5);
  --Tag-base-paddingRight: var(--sizes-size-5);
  --Tag-base-padding: var(--Tag-base-paddingTop) var(--Tag-base-paddingRight)
    var(--Tag-base-paddingBottom) var(--Tag-base-paddingLeft);
  --Tag-model-normal-top-border-color: var(--colors-neutral-line-6);
  --Tag-model-normal-top-border-width: var(--borders-width-2);
  --Tag-model-normal-top-border-style: var(--borders-style-1);
  --Tag-model-normal-right-border-color: var(--colors-neutral-line-6);
  --Tag-model-normal-right-border-width: var(--borders-width-2);
  --Tag-model-normal-right-border-style: var(--borders-style-1);
  --Tag-model-normal-bottom-border-color: var(--colors-neutral-line-6);
  --Tag-model-normal-bottom-border-width: var(--borders-width-2);
  --Tag-model-normal-bottom-border-style: var(--borders-style-1);
  --Tag-model-normal-left-border-color: var(--colors-neutral-line-6);
  --Tag-model-normal-left-border-width: var(--borders-width-2);
  --Tag-model-normal-left-border-style: var(--borders-style-1);
  --Tag-model-normal-top-right-border-radius: var(--borders-radius-1);
  --Tag-model-normal-top-left-border-radius: var(--borders-radius-1);
  --Tag-model-normal-bottom-right-border-radius: var(--borders-radius-1);
  --Tag-model-normal-bottom-left-border-radius: var(--borders-radius-1);
  --Tag-model-normal-status-size: var(--sizes-size-0);
  --Tag-model-normal-status-margin: var(--sizes-size-0);
  --Tag-model-normal-close-size: var(--sizes-size-0);
  --Tag-model-normal-close-margin: var(--sizes-size-0);
  --Tag-model-rounded-top-border-color: var(--colors-neutral-line-6);
  --Tag-model-rounded-top-border-width: var(--borders-width-2);
  --Tag-model-rounded-top-border-style: var(--borders-style-2);
  --Tag-model-rounded-right-border-color: var(--colors-neutral-line-6);
  --Tag-model-rounded-right-border-width: var(--borders-width-2);
  --Tag-model-rounded-right-border-style: var(--borders-style-2);
  --Tag-model-rounded-bottom-border-color: var(--colors-neutral-line-6);
  --Tag-model-rounded-bottom-border-width: var(--borders-width-2);
  --Tag-model-rounded-bottom-border-style: var(--borders-style-2);
  --Tag-model-rounded-left-border-color: var(--colors-neutral-line-6);
  --Tag-model-rounded-left-border-width: var(--borders-width-2);
  --Tag-model-rounded-left-border-style: var(--borders-style-2);
  --Tag-model-rounded-top-right-border-radius: 12px;
  --Tag-model-rounded-top-left-border-radius: 12px;
  --Tag-model-rounded-bottom-right-border-radius: 12px;
  --Tag-model-rounded-bottom-left-border-radius: 12px;
  --Tag-model-rounded-status-size: var(--sizes-size-0);
  --Tag-model-rounded-status-margin: var(--sizes-size-0);
  --Tag-model-rounded-close-size: var(--sizes-size-0);
  --Tag-model-rounded-close-margin: var(--sizes-size-0);
  --Tag-model-status-top-border-color: var(--colors-neutral-line-6);
  --Tag-model-status-top-border-width: var(--borders-width-2);
  --Tag-model-status-top-border-style: var(--borders-style-1);
  --Tag-model-status-right-border-color: var(--colors-neutral-line-6);
  --Tag-model-status-right-border-width: var(--borders-width-2);
  --Tag-model-status-right-border-style: var(--borders-style-1);
  --Tag-model-status-bottom-border-color: var(--colors-neutral-line-6);
  --Tag-model-status-bottom-border-width: var(--borders-width-2);
  --Tag-model-status-bottom-border-style: var(--borders-style-1);
  --Tag-model-status-left-border-color: var(--colors-neutral-line-6);
  --Tag-model-status-left-border-width: var(--borders-width-2);
  --Tag-model-status-left-border-style: var(--borders-style-1);
  --Tag-model-status-top-right-border-radius: var(--borders-radius-1);
  --Tag-model-status-top-left-border-radius: var(--borders-radius-1);
  --Tag-model-status-bottom-right-border-radius: var(--borders-radius-1);
  --Tag-model-status-bottom-left-border-radius: var(--borders-radius-1);
  --Tag-model-status-status-size: var(--sizes-size-5);
  --Tag-model-status-status-margin: var(--sizes-size-5);
  --Tag-model-status-close-size: var(--sizes-size-5);
  --Tag-model-status-close-margin: var(--sizes-size-5);
  --Tag-color-active-color: var(--colors-neutral-fill-11);
  --Tag-color-active-bg-color: var(--colors-brand-5);
  --Tag-color-inactive-color: var(--colors-neutral-fill-11);
  --Tag-color-inactive-bg-color: var(--colors-neutral-fill-6);
  --Tag-color-error-color: var(--colors-neutral-fill-11);
  --Tag-color-error-bg-color: var(--colors-error-5);
  --Tag-color-success-color: var(--colors-neutral-fill-11);
  --Tag-color-success-bg-color: var(--colors-success-5);
  --Tag-color-processing-color: var(--colors-neutral-fill-11);
  --Tag-color-processing-bg-color: var(--colors-brand-6);
  --Tag-color-warning-color: var(--colors-neutral-fill-11);
  --Tag-color-warning-bg-color: var(--colors-warning-5);
  --Tag-content-fontSize: var(--Tag-base-fontSize);
  --Tag-height: var(--Tag-base-height);
  --Tag-borderRadius: var(--Tag-model-normal-top-left-border-radius)
    var(--Tag-model-normal-top-right-border-radius)
    var(--Tag-model-normal-bottom-right-border-radius)
    var(--Tag-model-normal-bottom-left-border-radius);
  --Tag-fontColor: var(--colors-neutral-text-2);
  --Tag-rounded-borderRadius: var(--Tag-model-rounded-top-left-border-radius)
    var(--Tag-model-rounded-top-right-border-radius)
    var(--Tag-model-rounded-bottom-right-border-radius)
    var(--Tag-model-rounded-bottom-left-border-radius);
  --Tag-status-margin: var(--Tag-model-status-status-margin);
  --Tag-status-size: var(--Tag-model-status-status-size);
  --Tag-close-margin: var(--Tag-model-status-close-margin);
  --Tag-close-size: var(--Tag-model-status-close-size);
  --Tag-default-color: var(--colors-neutral-fill-9);
  --Tag-inactive-color: var(--Tag-color-inactive-color);
  --Tag-active-color: var(--Tag-color-active-color);
  --Tag-processing-color: var(--Tag-color-processing-color);
  --Tag-success-color: var(--Tag-color-success-color);
  --Tag-error-color: var(--Tag-color-error-color);
  --Tag-warning-color: var(--Tag-color-warning-color);
  --Tag-inactive-bg-color: var(--Tag-color-inactive-bg-color);
  --Tag-active-bg-color: var(--Tag-color-active-bg-color);
  --Tag-processing-bg-color: var(--Tag-color-processing-bg-color);
  --Tag-success-bg-color: var(--Tag-color-success-bg-color);
  --Tag-error-bg-color: var(--Tag-color-error-bg-color);
  --Tag-warning-bg-color: var(--Tag-color-warning-bg-color);
  --Tag-normal-borderWidth: var(--Tag-model-normal-top-border-width)
    var(--Tag-model-normal-right-border-width) var(--Tag-model-normal-bottom-border-width)
    var(--Tag-model-normal-left-border-width);
  --Tag-normal-borderRadius: var(--Tag-model-normal-top-left-border-radius)
    var(--Tag-model-normal-top-right-border-radius)
    var(--Tag-model-normal-bottom-right-border-radius)
    var(--Tag-model-normal-bottom-left-border-radius);
  --Tag-normal-borderColor: var(--Tag-model-normal-top-border-color)
    var(--Tag-model-normal-right-border-color) var(--Tag-model-normal-bottom-border-color)
    var(--Tag-model-normal-left-border-color);
  --Tag-normal-status-size: var(--Tag-model-normal-status-size);
  --Tag-normal-status-margin: var(--Tag-model-normal-status-margin);
  --Tag-rounded-borderWidth: var(--Tag-model-rounded-top-border-width)
    var(--Tag-model-rounded-right-border-width) var(--Tag-model-rounded-bottom-border-width)
    var(--Tag-model-rounded-left-border-width);
  --Tag-rounded-borderRadius: var(--Tag-model-rounded-top-left-border-radius)
    var(--Tag-model-rounded-top-right-border-radius)
    var(--Tag-model-rounded-bottom-right-border-radius)
    var(--Tag-model-rounded-bottom-left-border-radius);
  --Tag-rounded-borderColor: var(--Tag-model-rounded-top-border-color)
    var(--Tag-model-rounded-right-border-color) var(--Tag-model-rounded-bottom-border-color)
    var(--Tag-model-rounded-left-border-color);
  --Tag-rounded-borderStyle: var(--Tag-model-rounded-top-border-style)
    var(--Tag-model-rounded-right-border-style) var(--Tag-model-rounded-bottom-border-style)
    var(--Tag-model-rounded-left-border-style);
  --Toast-color: var(--colors-neutral-text-11);
  --Toast-paddingTop: var(--sizes-size-3);
  --Toast-paddingBottom: var(--sizes-size-3);
  --Toast-paddingLeft: var(--sizes-size-9);
  --Toast-paddingRight: var(--sizes-size-9);
  --Toast-top-right-border-radius: var(--borders-radius-3);
  --Toast-top-left-border-radius: var(--borders-radius-3);
  --Toast-bottom-right-border-radius: var(--borders-radius-3);
  --Toast-bottom-left-border-radius: var(--borders-radius-3);
  --Toast-icon-size: var(--sizes-size-9);
  --Toast--danger-color: var(--colors-neutral-text-2);
  --Toast--danger-fontSize: var(--fonts-size-8);
  --Toast--danger-fontWeight: var(--fonts-weight-6);
  --Toast--danger-top-border-color: var(--colors-neutral-line-11);
  --Toast--danger-top-border-width: var(--borders-width-2);
  --Toast--danger-top-border-style: var(--borders-style-2);
  --Toast--danger-right-border-color: var(--colors-neutral-line-11);
  --Toast--danger-right-border-width: var(--borders-width-2);
  --Toast--danger-right-border-style: var(--borders-style-2);
  --Toast--danger-bottom-border-color: var(--colors-neutral-line-11);
  --Toast--danger-bottom-border-width: var(--borders-width-2);
  --Toast--danger-bottom-border-style: var(--borders-style-2);
  --Toast--danger-left-border-color: var(--colors-neutral-line-11);
  --Toast--danger-left-border-width: var(--borders-width-2);
  --Toast--danger-left-border-style: var(--borders-style-2);
  --Toast--danger-top-right-border-radius: var(--borders-radius-3);
  --Toast--danger-top-left-border-radius: var(--borders-radius-3);
  --Toast--danger-bottom-right-border-radius: var(--borders-radius-3);
  --Toast--danger-bottom-left-border-radius: var(--borders-radius-3);
  --Toast--danger-bgColor: var(--colors-neutral-fill-11);
  --Toast--info-color: var(--colors-neutral-text-2);
  --Toast--info-fontSize: var(--fonts-size-8);
  --Toast--info-fontWeight: var(--fonts-weight-6);
  --Toast--info-top-border-color: var(--colors-neutral-line-11);
  --Toast--info-top-border-width: var(--borders-width-2);
  --Toast--info-top-border-style: var(--borders-style-2);
  --Toast--info-right-border-color: var(--colors-neutral-line-11);
  --Toast--info-right-border-width: var(--borders-width-2);
  --Toast--info-right-border-style: var(--borders-style-2);
  --Toast--info-bottom-border-color: var(--colors-neutral-line-11);
  --Toast--info-bottom-border-width: var(--borders-width-2);
  --Toast--info-bottom-border-style: var(--borders-style-2);
  --Toast--info-left-border-color: var(--colors-neutral-line-11);
  --Toast--info-left-border-width: var(--borders-width-2);
  --Toast--info-left-border-style: var(--borders-style-2);
  --Toast--info-bgColor: var(--colors-neutral-fill-11);
  --Toast--success-color: var(--colors-neutral-text-2);
  --Toast--success-fontWeight: var(--fonts-weight-6);
  --Toast--success-fontSize: var(--fonts-size-8);
  --Toast--success-fontWeight: var(--fonts-weight-6);
  --Toast--success-top-border-color: var(--colors-neutral-line-11);
  --Toast--success-top-border-width: var(--borders-width-2);
  --Toast--success-top-border-style: var(--borders-style-2);
  --Toast--success-right-border-color: var(--colors-neutral-line-11);
  --Toast--success-right-border-width: var(--borders-width-2);
  --Toast--success-right-border-style: var(--borders-style-2);
  --Toast--success-bottom-border-color: var(--colors-neutral-line-11);
  --Toast--success-bottom-border-width: var(--borders-width-2);
  --Toast--success-bottom-border-style: var(--borders-style-2);
  --Toast--success-left-border-color: var(--colors-neutral-line-11);
  --Toast--success-left-border-width: var(--borders-width-2);
  --Toast--success-left-border-style: var(--borders-style-2);
  --Toast--success-top-right-border-radius: var(--borders-radius-3);
  --Toast--success-top-left-border-radius: var(--borders-radius-3);
  --Toast--success-bottom-right-border-radius: var(--borders-radius-3);
  --Toast--success-bottom-left-border-radius: var(--borders-radius-3);
  --Toast--success-bgColor: var(--colors-neutral-fill-11);
  --Toast--warning-color: var(--colors-neutral-text-2);
  --Toast--warning-fontSize: var(--fonts-size-8);
  --Toast--warning-fontWeight: var(--fonts-weight-6);
  --Toast--warning-top-border-color: var(--colors-neutral-line-11);
  --Toast--warning-top-border-width: var(--borders-width-2);
  --Toast--warning-top-border-style: var(--borders-style-2);
  --Toast--warning-right-border-color: var(--colors-neutral-line-11);
  --Toast--warning-right-border-width: var(--borders-width-2);
  --Toast--warning-right-border-style: var(--borders-style-2);
  --Toast--warning-bottom-border-color: var(--colors-neutral-line-11);
  --Toast--warning-bottom-border-width: var(--borders-width-2);
  --Toast--warning-bottom-border-style: var(--borders-style-2);
  --Toast--warning-left-border-color: var(--colors-neutral-line-11);
  --Toast--warning-left-border-width: var(--borders-width-2);
  --Toast--warning-left-border-style: var(--borders-style-2);
  --Toast--warning-top-right-border-radius: var(--borders-radius-3);
  --Toast--warning-top-left-border-radius: var(--borders-radius-3);
  --Toast--warning-bottom-right-border-radius: var(--borders-radius-3);
  --Toast--warning-bottom-left-border-radius: var(--borders-radius-3);
  --Toast--warning-bgColor: var(--colors-neutral-fill-11);
  --Toast-border-width: var(--borders-width-2);
  --Toast-borderRadius: var(--borders-radius-3);
  --Toast-box-shadow:
    0px 4px 6px 0px rgba(8, 14, 26, 0.06), 0px 1px 10px 0px rgba(8, 14, 26, 0.05),
    0px 2px 4px -1px rgba(8, 14, 26, 0.04);
  --Toast-close-color: var(--colors-neutral-text-5);
  --Toast-close--onHover-color: var(--colors-brand-5);
  --Toast-icon-width: var(--Toast-icon-size);
  --Toast-icon-height: var(--Toast-icon-size);
  --Toast-opacity: 1;
  --Toast-title-display: inline;
  --Toast-width: 25rem;
  --Pick-base-top-border-color: var(--colors-neutral-line-8);
  --Pick-base-top-border-width: var(--borders-width-2);
  --Pick-base-top-border-style: var(--borders-style-2);
  --Pick-base-right-border-color: var(--colors-neutral-line-8);
  --Pick-base-right-border-width: var(--borders-width-2);
  --Pick-base-right-border-style: var(--borders-style-2);
  --Pick-base-bottom-border-color: var(--colors-neutral-line-8);
  --Pick-base-bottom-border-width: var(--borders-width-2);
  --Pick-base-bottom-border-style: var(--borders-style-2);
  --Pick-base-left-border-color: var(--colors-neutral-line-8);
  --Pick-base-left-border-width: var(--borders-width-2);
  --Pick-base-left-border-style: var(--borders-style-2);
  --Pick-base-top-right-border-radius: var(--borders-radius-3);
  --Pick-base-top-left-border-radius: var(--borders-radius-3);
  --Pick-base-bottom-right-border-radius: var(--borders-radius-3);
  --Pick-base-bottom-left-border-radius: var(--borders-radius-3);
  --Pick-base-placeholder-color: var(--colors-neutral-line-6);
  --Pick-base-placeholder-fontSize: var(--fonts-size-7);
  --Pick-base-placeholder-fontWeight: var(--fonts-weight-6);
  --Pick-base-paddingTop: var(--sizes-size-3);
  --Pick-base-paddingBottom: var(--sizes-size-3);
  --Pick-base-paddingLeft: var(--sizes-size-6);
  --Pick-base-paddingRight: var(--sizes-size-6);
  --Pick-base-bgColor: var(--colors-neutral-fill-11);
  --Pick-base-value-color: var(--colors-other-5);
  --Pick-base-value-fontSize: var(--fonts-size-8);
  --Pick-base-value-fontWeight: var(--fonts-weight-6);
  --Pick-base-value-top-border-color: var(--colors-other-7);
  --Pick-base-value-top-border-width: var(--borders-width-2);
  --Pick-base-value-top-border-style: var(--borders-style-2);
  --Pick-base-value-right-border-color: var(--colors-other-7);
  --Pick-base-value-right-border-width: var(--borders-width-2);
  --Pick-base-value-right-border-style: var(--borders-style-2);
  --Pick-base-value-bottom-border-color: var(--colors-other-7);
  --Pick-base-value-bottom-border-width: var(--borders-width-2);
  --Pick-base-value-bottom-border-style: var(--borders-style-2);
  --Pick-base-value-left-border-color: var(--colors-other-7);
  --Pick-base-value-left-border-width: var(--borders-width-2);
  --Pick-base-value-left-border-style: var(--borders-style-2);
  --Pick-base-value-bgColor: #cce5ff;
  --Pick-base-top-right-border-radius: var(--borders-radius-3);
  --Pick-base-top-left-border-radius: var(--borders-radius-3);
  --Pick-base-bottom-right-border-radius: var(--borders-radius-3);
  --Pick-base-bottom-left-border-radius: var(--borders-radius-3);
  --Pick-base-icon: '<svg viewBox="0 0 200 200" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><g  fill="currentColor" fill-rule="nonzero"><path d="M181.25,0 L56.25,0 C45.8984375,0 37.5,8.3984375 37.5,18.75 L37.5,37.5 L18.75,37.5 C8.3984375,37.5 0,45.8984375 0,56.25 L0,181.25 C0,191.601562 8.3984375,200 18.75,200 L143.75,200 C154.101562,200 162.5,191.601562 162.5,181.25 L162.5,162.5 L181.25,162.5 C191.601562,162.5 200,154.101562 200,143.75 L200,18.75 C200,8.3984375 191.601562,0 181.25,0 Z M143.75,181.25 L18.75,181.25 L18.75,100 L143.75,100 L143.75,181.25 Z M181.25,143.75 L162.5,143.75 L162.5,56.25 C162.5,45.8984375 154.101562,37.5 143.75,37.5 L56.25,37.5 L56.25,18.75 L181.25,18.75 L181.25,143.75 Z" ></path></g></svg>';
  --Pick-base-icon-size: var(--sizes-size-9);
  --Pick-base-icon-color: #84878c;
  --Pick-base-value-hover-icon-color: #b3d7ff;
  --Pick-base-value-icon-color: var(--colors-other-5);
  --Picker-onHover-iconColor: var(--icon-onHover-color);
  --Picker-tag-height: 1.5rem;
  --Picker-tag-marginBottom: var(--select-multiple-marginBottom);
  --Pick-status-hover-top-border-color: var(--colors-other-5);
  --Pick-status-hover-top-border-width: var(--borders-width-2);
  --Pick-status-hover-top-border-style: var(--borders-style-2);
  --Pick-status-hover-right-border-color: var(--colors-other-5);
  --Pick-status-hover-right-border-width: var(--borders-width-2);
  --Pick-status-hover-right-border-style: var(--borders-style-2);
  --Pick-status-hover-bottom-border-color: var(--colors-other-5);
  --Pick-status-hover-bottom-border-width: var(--borders-width-2);
  --Pick-status-hover-bottom-border-style: var(--borders-style-2);
  --Pick-status-hover-left-border-color: var(--colors-other-5);
  --Pick-status-hover-left-border-width: var(--borders-width-2);
  --Pick-status-hover-left-border-style: var(--borders-style-2);
  --Pick-status-hover-bgColor: var(--colors-neutral-fill-11);
  --Pick-status-focus-top-border-color: var(--colors-other-7);
  --Pick-status-focus-top-border-width: var(--borders-width-2);
  --Pick-status-focus-top-border-style: var(--borders-style-2);
  --Pick-status-focus-right-border-color: var(--colors-other-7);
  --Pick-status-focus-right-border-width: var(--borders-width-2);
  --Pick-status-focus-right-border-style: var(--borders-style-2);
  --Pick-status-focus-bottom-border-color: var(--colors-other-7);
  --Pick-status-focus-bottom-border-width: var(--borders-width-2);
  --Pick-status-focus-bottom-border-style: var(--borders-style-2);
  --Pick-status-focus-left-border-color: var(--colors-other-7);
  --Pick-status-focus-left-border-width: var(--borders-width-2);
  --Pick-status-focus-left-border-style: var(--borders-style-2);
  --Pick-status-focus-shadow: var(--shadows-shadow-none);
  --Pick-status-focus-bgColor: var(--colors-neutral-fill-11);
  --Pick-status-disabled-color: var(--colors-neutral-text-2);
  --Pick-status-disabled-fontSize: var(--fonts-size-7);
  --Pick-status-disabled-fontWeight: var(--fonts-weight-6);
  --Pick-status-disabled-top-border-color: var(--colors-neutral-line-8);
  --Pick-status-disabled-top-border-width: var(--borders-width-2);
  --Pick-status-disabled-top-border-style: var(--borders-style-2);
  --Pick-status-disabled-right-border-color: var(--colors-neutral-line-8);
  --Pick-status-disabled-right-border-width: var(--borders-width-2);
  --Pick-status-disabled-right-border-style: var(--borders-style-2);
  --Pick-status-disabled-bottom-border-color: var(--colors-neutral-line-8);
  --Pick-status-disabled-bottom-border-width: var(--borders-width-2);
  --Pick-status-disabled-bottom-border-style: var(--borders-style-2);
  --Pick-status-disabled-left-border-color: var(--colors-neutral-line-8);
  --Pick-status-disabled-left-border-width: var(--borders-width-2);
  --Pick-status-disabled-left-border-style: var(--borders-style-2);
  --Pick-status-disabled-bgColor: var(--colors-neutral-fill-10);
  --Pick-status-disabled-color: var(--colors-neutral-line-6);
  --Pick-status-disabled-fontSize: var(--fonts-size-7);
  --Pick-status-disabled-fontWeight: var(--fonts-weight-6);
  --Status-base-fontSize: var(--fonts-size-9);
  --Status-base-icon-size: var(--sizes-size-8);
  --Status-base-text-margin: var(--sizes-size-3);
  --Status-fail-color: var(--colors-error-5);
  --Status-success-color: var(--colors-success-5);
  --Status-warning-color: var(--colors-warning-5);
  --Status-schedule-color: var(--colors-neutral-fill-2);
  --Status-pending-before-color: var(--colors-error-5);
  --Status-pending-after-color: var(--colors-brand-5);
  --Status-font-size: var(--Status-base-fontSize);
  --Status-font-margin: var(--Status-base-text-margin);
  --Status-icon-width: var(--Status-base-icon-size);
  --Status-icon-height: var(--Status-base-icon-size);
  --Status-pending-beforeColor: var(--Status-pending-before-color);
  --Status-pending-afterColor: var(--Status-pending-after-color);
  --Timeline-time-color: var(--colors-neutral-text-5);
  --Timeline-time-fontSize: var(--fonts-size-7);
  --Timeline-time-fontWeight: var(--fonts-weight-6);
  --Timeline-title-color: var(--colors-neutral-text-2);
  --Timeline-title-fontSize: var(--fonts-size-7);
  --Timeline-title-fontWeight: var(--fonts-weight-6);
  --Timeline-type-info-color: var(--colors-info-5);
  --Timeline-type-success-color: var(--colors-success-5);
  --Timeline-type-warning-color: var(--colors-warning-5);
  --Timeline-type-danger-color: var(--colors-error-5);
  --Timeline-detail-label-color: var(--colors-brand-5);
  --Timeline-detail-label-fontSize: var(--fonts-size-7);
  --Timeline-detail-label-fontWeight: var(--fonts-weight-6);
  --Timeline-detail-content-color: var(--colors-neutral-text-2);
  --Timeline-detail-content-fontSize: var(--fonts-size-7);
  --Timeline-detail-content-fontWeight: var(--fonts-weight-6);
  --Timeline-shadow: var(--shadows-shadow-normal);
  --Timeline-detail-icon-size: var(--sizes-base-8);
  --Timeline-detail-icon-color: var(--colors-brand-5);
  --Timeline-visible-paddingTop: var(--sizes-size-6);
  --Timeline-visible-paddingBottom: var(--sizes-size-6);
  --Timeline-visible-paddingLeft: var(--sizes-size-6);
  --Timeline-visible-paddingRight: var(--sizes-size-6);
  --Timeline-visible-top-right-border-radius: var(--borders-radius-3);
  --Timeline-visible-top-left-border-radius: var(--borders-radius-3);
  --Timeline-visible-bottom-right-border-radius: var(--borders-radius-3);
  --Timeline-visible-bottom-left-border-radius: var(--borders-radius-3);
  --Timeline-left-size: var(--sizes-size-2);
  --Timeline-horizontal-top-size: var(--sizes-size-0);
  --Timeline-line-bg: #e6e6e8;
  --Timeline-round-bg: #dadbdd;
  --TimelineItem--axle-flex: 0 0 1.5rem;
  --TimelineItem--left-line-width: 0.125rem;
  --TimelineItem--left-line-left: 0.8125rem;
  --TimelineItem--left-line-top: 1.25rem;
  --TimelineItem--horizontal-left-line-top: 1.125rem;
  --TimelineItem--icon-left-line-left: 0.75rem;
  --TimelineItem--round-width: 0.5rem;
  --TimelineItem--round-height: 0.5rem;
  --TimelineItem--round-left: 0.625rem;
  --TimelineItem--round-top: 0.5rem;
  --TimelineItem--icon-width: 1rem;
  --TimelineItem--icon-height: 1rem;
  --TimelineItem--icon-left: 0.375rem;
  --TimelineItem--content-padding-bottom: var(--gap-md);
  --TimelineItem--content-margin-left: var(--Timeline-left-size);
  --TimelineItem--content-time-margin-bottom: var(--gap-xs);
  --TimelineItem--content-title-margin-bottom: var(--gap-xs);
  --TimelineItem--detail-button-margin-bottom: var(--gap-base);
  --TimelineItem-detail-arrow-width: var(--Timeline-detail-icon-size);
  --TimelineItem-detail-visible-padding: var(--Timeline-visible-paddingTop)
    var(--Timeline-visible-paddingRight) var(--Timeline-visible-paddingBottom)
    var(--Timeline-visible-paddingLeft);
  --TimelineItem-detail-visible-max-width: 18.75rem;
  --Timeline-alternate-margin-left: var(--gap-xl);
  --Timeline-visible-border-radius: var(--Timeline-visible-top-left-border-radius)
    var(--Timeline-visible-top-right-border-radius)
    var(--Timeline-visible-bottom-right-border-radius)
    var(--Timeline-visible-bottom-left-border-radius);
  --Timeline-horizontal-content-margin-top: var(--Timeline-horizontal-top-size);
  --TimelineItem--icon-radius: 50%;
  --TimelineItem--round-radius: 50%;
  --TimelineItem--content-radius: px2rem(2px);
  --TimelineItem-detail-visible-shadow: var(--Timeline-shadow);
  --TimelineItem--font-size: var(--fontSizeSm);
  --TimelineItem--text-primary-color: var(--Timeline-title-color);
  --TimelineItem--text-secondary-color: var(--Timeline-time-color);
  --TimelineItem--detail-button-color: var(--Timeline-detail-label-color);
  --TimelineItem--line-bg: var(--Timeline-line-bg);
  --TimelineItem--content-bg: #f2f2f4;
  --TimelineItem-custem-button-margin-left: var(--fontSizeSm);
  --TimelineItem-custem-time-padding-right: var(--fontSizeSm);
  --TimelineItem-round-bg: var(--Timeline-round-bg);
  --Timeline--success-bg: var(--Timeline-type-success-color);
  --Timeline--info-bg: var(--Timeline-type-info-color);
  --Timeline--warning-bg: var(--Timeline-type-warning-color);
  --Timeline--danger-bg: var(--Timeline-type-danger-color);
  --inputTag-option-height: var(--sizes-base-16);
  --inputTag-option-color: var(--colors-neutral-text-2);
  --inputTag-option-fontSize: var(--fonts-size-7);
  --inputTag-option-fontWeight: var(--fonts-weight-6);
  --inputTag-option-lineHeight: var(--fonts-lineHeight-2);
  --inputTag-option-paddingTop: var(--sizes-size-3);
  --inputTag-option-paddingBottom: var(--sizes-size-3);
  --inputTag-option-paddingLeft: var(--sizes-size-6);
  --inputTag-option-paddingRight: var(--sizes-size-6);
  --inputTag-option-bg-color: var(--colors-neutral-fill-11);
  --inputTag-option-hover-color: var(--colors-neutral-text-2);
  --inputTag-option-hover-bg-color: var(--colors-brand-10);
  --inputTag-popover-maxHeight: 300px;
  --fieldSet-legend-height: var(--sizes-size-9);
  --fieldSet-legend-color: var(--colors-neutral-text-2);
  --fieldSet-legend-fontSize: var(--fonts-size-7);
  --fieldSet-legend-fontWeight: var(--fonts-weight-6);
  --fieldSet-legend-paddingTop: var(--sizes-size-0);
  --fieldSet-legend-paddingBottom: var(--sizes-size-0);
  --fieldSet-legend-paddingLeft: var(--sizes-size-8);
  --fieldSet-legend-paddingRight: var(--sizes-size-0);
  --fieldSet-legend-marginTop: var(--sizes-size-7);
  --fieldSet-legend-marginBottom: var(--sizes-size-7);
  --fieldSet-legend-marginLeft: var(--sizes-size-0);
  --fieldSet-legend-marginRight: var(--sizes-size-0);
  --fieldSet-legend-border-color: var(--colors-brand-5);
  --fieldSet-legend-border-width: var(--sizes-size-3);
  --fieldSet-size-xs-paddingTop: var(--sizes-base-10);
  --fieldSet-size-xs-paddingRight: var(--sizes-size-3);
  --fieldSet-size-xs-paddingBottom: var(--sizes-size-3);
  --fieldSet-size-xs-paddingLeft: var(--sizes-size-3);
  --fieldSet-size-xs-fontSize: var(--fonts-size-8);
  --fieldSet-size-sm-paddingTop: var(--sizes-base-12);
  --fieldSet-size-sm-paddingRight: var(--sizes-size-6);
  --fieldSet-size-sm-paddingBottom: var(--sizes-size-6);
  --fieldSet-size-sm-paddingLeft: var(--sizes-size-6);
  --fieldSet-size-sm-fontSize: var(--fonts-size-8);
  --fieldSet-size-base-paddingTop: var(--sizes-base-15);
  --fieldSet-size-base-paddingRight: var(--sizes-size-9);
  --fieldSet-size-base-paddingBottom: var(--sizes-size-9);
  --fieldSet-size-base-paddingLeft: var(--sizes-size-9);
  --fieldSet-size-base-fontSize: var(--fonts-size-7);
  --fieldSet-size-md-paddingTop: var(--sizes-base-15);
  --fieldSet-size-md-paddingRight: var(--sizes-base-10);
  --fieldSet-size-md-paddingBottom: var(--sizes-base-10);
  --fieldSet-size-md-paddingLeft: var(--sizes-base-10);
  --fieldSet-size-md-fontSize: var(--fonts-size-7);
  --fieldSet-size-lg-paddingTop: var(--sizes-base-20);
  --fieldSet-size-lg-paddingRight: var(--sizes-base-15);
  --fieldSet-size-lg-paddingBottom: var(--sizes-base-15);
  --fieldSet-size-lg-paddingLeft: var(--sizes-base-15);
  --fieldSet-size-lg-fontSize: var(--fonts-size-6);
  --inputRichText-default-top-border-color: var(--colors-neutral-line-8);
  --inputRichText-default-top-border-width: var(--borders-width-2);
  --inputRichText-default-top-border-style: var(--borders-style-2);
  --inputRichText-default-right-border-color: var(--colors-neutral-line-8);
  --inputRichText-default-right-border-width: var(--borders-width-2);
  --inputRichText-default-right-border-style: var(--borders-style-2);
  --inputRichText-default-bottom-border-color: var(--colors-neutral-line-8);
  --inputRichText-default-bottom-border-width: var(--borders-width-2);
  --inputRichText-default-bottom-border-style: var(--borders-style-2);
  --inputRichText-default-left-border-color: var(--colors-neutral-line-8);
  --inputRichText-default-left-border-width: var(--borders-width-2);
  --inputRichText-default-left-border-style: var(--borders-style-2);
  --inputRichText-default-top-left-border-radius: var(--borders-radius-1);
  --inputRichText-default-top-right-border-radius: var(--borders-radius-1);
  --inputRichText-default-bottom-right-border-radius: var(--borders-radius-1);
  --inputRichText-default-bottom-left-border-radius: var(--borders-radius-1);
  --inputRichText-hover-top-border-color: var(--colors-neutral-line-8);
  --inputRichText-hover-top-border-width: var(--borders-width-2);
  --inputRichText-hover-top-border-style: var(--borders-style-2);
  --inputRichText-hover-right-border-color: var(--colors-neutral-line-8);
  --inputRichText-hover-right-border-width: var(--borders-width-2);
  --inputRichText-hover-right-border-style: var(--borders-style-2);
  --inputRichText-hover-bottom-border-color: var(--colors-neutral-line-8);
  --inputRichText-hover-bottom-border-width: var(--borders-width-2);
  --inputRichText-hover-bottom-border-style: var(--borders-style-2);
  --inputRichText-hover-left-border-color: var(--colors-neutral-line-8);
  --inputRichText-hover-left-border-width: var(--borders-width-2);
  --inputRichText-hover-left-border-style: var(--borders-style-2);
  --inputRichText-hover-top-left-border-radius: var(--borders-radius-1);
  --inputRichText-hover-top-right-border-radius: var(--borders-radius-1);
  --inputRichText-hover-bottom-right-border-radius: var(--borders-radius-1);
  --inputRichText-hover-bottom-left-border-radius: var(--borders-radius-1);
  --inputRichText-active-top-border-color: var(--colors-brand-5);
  --inputRichText-active-top-border-width: var(--borders-width-2);
  --inputRichText-active-top-border-style: var(--borders-style-2);
  --inputRichText-active-right-border-color: var(--colors-brand-5);
  --inputRichText-active-right-border-width: var(--borders-width-2);
  --inputRichText-active-right-border-style: var(--borders-style-2);
  --inputRichText-active-bottom-border-color: var(--colors-brand-5);
  --inputRichText-active-bottom-border-width: var(--borders-width-2);
  --inputRichText-active-bottom-border-style: var(--borders-style-2);
  --inputRichText-active-left-border-color: var(--colors-brand-5);
  --inputRichText-active-left-border-width: var(--borders-width-2);
  --inputRichText-active-left-border-style: var(--borders-style-2);
  --inputRichText-active-top-left-border-radius: var(--borders-radius-1);
  --inputRichText-active-top-right-border-radius: var(--borders-radius-1);
  --inputRichText-active-bottom-right-border-radius: var(--borders-radius-1);
  --inputRichText-active-bottom-left-border-radius: var(--borders-radius-1);
  --inputRichText-disabled-top-border-color: var(--colors-neutral-line-8);
  --inputRichText-disabled-top-border-width: var(--borders-width-2);
  --inputRichText-disabled-top-border-style: var(--borders-style-2);
  --inputRichText-disabled-right-border-color: var(--colors-neutral-line-8);
  --inputRichText-disabled-right-border-width: var(--borders-width-2);
  --inputRichText-disabled-right-border-style: var(--borders-style-2);
  --inputRichText-disabled-bottom-border-color: var(--colors-neutral-line-8);
  --inputRichText-disabled-bottom-border-width: var(--borders-width-2);
  --inputRichText-disabled-bottom-border-style: var(--borders-style-2);
  --inputRichText-disabled-left-border-color: var(--colors-neutral-line-8);
  --inputRichText-disabled-left-border-width: var(--borders-width-2);
  --inputRichText-disabled-left-border-style: var(--borders-style-2);
  --inputRichText-disabled-top-left-border-radius: var(--borders-radius-1);
  --inputRichText-disabled-top-right-border-radius: var(--borders-radius-1);
  --inputRichText-disabled-bottom-right-border-radius: var(--borders-radius-1);
  --inputRichText-disabled-bottom-left-border-radius: var(--borders-radius-1);
  --conditionBuilder-toolbar-width: var(--sizes-base-14);
  --conditionBuilder-toolbar-height: var(--sizes-base-14);
  --conditionBuilder-toolbar-color: var(--colors-brand-3);
  --conditionBuilder-toolbar-fontSize: var(--fonts-size-8);
  --conditionBuilder-toolbar-fontWeight: var(--fonts-weight-5);
  --conditionBuilder-toolbar-bg-color: var(--colors-brand-9);
  --conditionBuilder-toolbar-hover-color: var(--colors-neutral-text-11);
  --conditionBuilder-toolbar-hover-fontSize: var(--fonts-size-8);
  --conditionBuilder-toolbar-hover-fontWeight: var(--fonts-weight-5);
  --conditionBuilder-toolbar-hover-bg-color: var(--colors-brand-5);
  --conditionBuilder-line-width: var(--sizes-size-2);
  --conditionBuilder-line-bg-color: var(--colors-brand-9);
  --conditionBuilder-body-bg-color: var(--colors-neutral-line-10);
  --conditionBuilder-body-paddingTop: var(--sizes-size-3);
  --conditionBuilder-body-paddingRight: var(--sizes-size-7);
  --conditionBuilder-body-paddingBottom: var(--sizes-size-3);
  --conditionBuilder-body-paddingLeft: var(--sizes-base-14);
  --table-border-width: var(--borders-width-1);
  --table-border-color: var(--colors-neutral-line-8);
  --table-paddingTop: var(--sizes-size-6);
  --table-paddingRight: var(--sizes-size-6);
  --table-paddingBottom: var(--sizes-size-6);
  --table-paddingLeft: var(--sizes-size-6);
  --table-paddingX: var(--sizes-size-7);
  --table-header-color: #909399;
  --table-header-fontSize: var(--fonts-size-7);
  --table-header-fontWeight: var(--fonts-weight-3);
  --table-header-lineHeight: var(--fonts-lineHeight-2);
  --table-header-bg-color: #f3f4f5;
  --table-header-separate-line-color: var(--colors-neutral-fill-11);
  --table-header-separate-line-width: var(--borders-width-1);
  --table-body-color: #606266;
  --table-body-fontSize: var(--fonts-size-7);
  --table-body-fontWeight: var(--fonts-weight-6);
  --table-body-line-height: var(--sizes-base-20);
  --table-body-lineHeight: var(--fonts-lineHeight-2);
  --table-body-bg-color: var(--colors-neutral-fill-11);
  --table-body-default-bg-color: var(--table-body-bg-color);
  --table-body-hover-color: var(--colors-neutral-text-2);
  --table-body-hover-bg-color: var(--colors-brand-10);
  --table-body-hover-border-color: var(--colors-neutral-line-8);
  --table-body-disabled-color: var(--colors-neutral-text-6);
  --table-body-disabled-bg-color: var(--colors-neutral-fill-10);
  --table-title-color: var(--colors-neutral-text-2);
  --table-title-fontSize: var(--fonts-size-7);
  --table-title-fontWeight: var(--fonts-weight-6);
  --table-title-lineHeight: var(--fonts-lineHeight-2);
  --table-title-bg-color: var(--colors-neutral-fill-11);
  --table-title-paddingTop: var(--sizes-size-6);
  --table-title-paddingRight: var(--sizes-size-5);
  --table-title-paddingBottom: var(--sizes-size-6);
  --table-title-paddingLeft: var(--sizes-size-5);
  --table-icon-color: var(--colors-neutral-text-6);
  --table-icon-hover-color: var(--colors-neutral-text-2);
  --table-icon-active-color: var(--colors-brand-5);
  --table-icon-marginLeft: var(--sizes-size-5);
  --table-sort-icon: '<svg viewBox="0 0 7 12" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><g fill="currentColor"><polygon points="3.5354 0.0004 2.8284 0.7064 0.0004 3.5344 0.7074 4.2424 3.0354 1.9134 3.0354 5.0354 4.0354 5.0354 4.0354 1.9134 6.3634 4.2424 7.0704 3.5344 4.2424 0.7064"></polygon><polygon points="3.035 7.0348 3.035 10.1558 0.708 7.8278 0 8.5348 2.828 11.3628 3.535 12.0708 4.243 11.3628 7.071 8.5348 6.363 7.8278 4.036 10.1558 4.036 7.0348"></polygon></g></svg>';
  --table-sort-up-icon: '<svg viewBox="0 0 7 10" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><g fill="currentColor"><polygon points="3.5 0 2.8 0.703 0 3.514 0.7 4.217 3.005 1.903 3.005 10 3.995 10 3.995 1.903 6.3 4.217 7 3.514 4.2 0.703"></polygon></g></svg>';
  --table-sort-down-icon: '<svg viewBox="0 0 7 10" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><g fill="currentColor"><polygon  transform="translate(3.500000, 5.000000) scale(1, -1) translate(-3.500000, -5.000000) " points="3.5 0 2.8 0.703 0 3.514 0.7 4.217 3.005 1.903 3.005 10 3.995 10 3.995 1.903 6.3 4.217 7 3.514 4.2 0.703"></polygon></g></svg>';
  --table-filter-icon: '<svg viewBox="0 0 12 12" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><g  stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"><g fill="currentColor"><path d="M0,0 L4,4.999 L4.082,10 L8,12 L8,4.999 L12,0 L0,0 Z M5.0528196,9.05445227 L4.97933146,4.82735426 L4.97320745,4.49967969 L4.75580505,4.24311339 L2,1 L10,1 L7.24419495,4.24311339 L7.02066854,4.50640615 L7.02066854,4.84272902 L7.02066854,10 L5.0528196,9.05445227 Z" ></path></g></g></svg>';
  --table-search-icon: '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 18 18" version="1.1"><path d="M2,8 C2,4.691 4.691,2 8,2 C11.309,2 14,4.691 14,8 C14,11.309 11.309,14 8,14 C4.691,14 2,11.309 2,8 L2,8 Z M18,16.586 L14.314,12.9 C15.367,11.545 16,9.849 16,8 C16,3.582 12.418,0 8,0 C3.582,0 0,3.582 0,8 C0,12.418 3.582,16 8,16 C9.849,16 11.545,15.367 12.9,14.314 L16.586,18 L18,16.586 Z" fill="currentColor"></path></svg>';
  --table-togglable-paddingTop: var(--sizes-size-4);
  --table-togglable-paddingRight: var(--sizes-size-5);
  --table-togglable-paddingBottom: var(--sizes-size-4);
  --table-togglable-paddingLeft: var(--sizes-size-5);
  --table-togglable-bg-color: var(--colors-neutral-fill-11);
  --table-togglable-hover-bg-color: var(--colors-neutral-fill-8);
  --table-size-large-paddingTop: var(--sizes-base-10);
  --table-size-large-paddingRight: var(--sizes-size-7);
  --table-size-large-paddingBottom: var(--sizes-base-10);
  --table-size-large-paddingLeft: var(--sizes-size-7);
  --table-size-small-paddingTop: var(--sizes-size-4);
  --table-size-small-paddingRight: var(--sizes-size-3);
  --table-size-small-paddingBottom: var(--sizes-size-4);
  --table-size-small-paddingLeft: var(--sizes-size-3);
  --Table--unsaved-heading-bg: var(--colors-neutral-fill-9);
  --Table--unsaved-heading-color: var(--colors-brand-5);
  --Table-bg: var(--colors-neutral-fill-11);
  --Table-borderColor: var(--table-border-color);
  --Table-borderRadius: var(--borderRadius);
  --Table-borderWidth: var(--table-border-width);
  --Table-color: var(--table-body-color);
  --Table-expandBtn-color: var(--colors-neutral-text-5);
  --Table-fixed-zIndex: 5;
  --Table-fixedLeft-boxShadow: inset 10px 0 8px -8px rgba(5, 5, 5, 0.06);
  --Table-fixedRight-boxShadow: inset -10px 0 8px -8px rgba(5, 5, 5, 0.06);
  --Table-fixedTop-boxShadow: inset 0 10px 8px -8px rgba(5, 5, 5, 0.06);
  --Table-fontSize: var(--table-body-fontSize);
  --Table-heading-bg: var(--table-title-bg-color);
  --Table-heading-height: 2.5rem;
  --Table-lineHeight: var(--fonts-lineHeight-2);
  --Table-onChecked-bg: var(--colors-neutral-fill-11);
  --Table-onChecked-borderColor: var(--colors-neutral-line-8);
  --Table-onChecked-color: var(--colors-neutral-text-2);
  --Table-onChecked-onHover-bg: var(--colors-neutral-fill-10);
  --Table-onChecked-onHover-borderColor: var(--colors-neutral-line-8);
  --Table-onChecked-onHover-color: var(--colors-neutral-line-4);
  --Table-onDragging-opacity: 1;
  --Table-onDragging-bg: var(--table-body-hover-bg-color);
  --Table-onHover-bg-rgb: 245, 251, 255;
  --Table-onHover-bg: var(--table-body-hover-bg-color);
  --Table-onHover-borderColor: var(--table-border-color);
  --Table-onHover-boxShadow: var(--shadows-shadow-normal);
  --Table-onHover-color: var(--table-body-hover-color);
  --Table-onModified-bg: #e8f0fe;
  --Table-onModified-borderColor: rgb(207.5625, 224.0625, 252.9375);
  --Table-onModified-color: #4285f4;
  --Table-placeholder-height: 12.5rem;
  --Table-strip-bg: transparent;
  --Table-tbody-borderTopColor: var(--colors-neutral-line-9);
  --Table-thead-bg: var(--table-header-bg-color);
  --Table-thead-borderColor: var(--table-header-separate-line-color);
  --Table-thead-borderWidth: var(--table-header-separate-line-width);
  --Table-thead-color: var(--table-header-color);
  --Table-thead-fontSize: var(--table-header-fontSize);
  --Table-thead-iconColor: var(--colors-neutral-text-5);
  --Table-toolbar-marginX: 0.25rem;
  --Table-toolbar-marginY: var(--gap-base);
  --Table-tree-borderColor: var(--colors-neutral-line-8);
  --Table-tree-indent: var(--gap-lg);
  --Table-searchableForm-backgroundColor: var(--colors-neutral-fill-10);
  --Table-searchableForm-borderRadius: 0.25rem;
  --Table-empty-icon-size: 4.625rem;
  --TableRow-onDisabled-bg: var(--table-body-disabled-bg-color);
  --TableRow-onDisabled-color: var(--table-body-disabled-color);
  --TableCell--edge-paddingX: var(--gap-md);
  --TableCell--edge-paddingX-default: var(--table-paddingX);
  --TableCell-filterBtn--onActive-color: var(--colors-brand-5);
  --TableCell-filterBtn-width: 1rem;
  --TableCell-filterPopOver-dropDownItem-height: var(--sizes-base-15);
  --TableCell-filterPopOver-dropDownItem-padding: var(--sizes-size-0) var(--sizes-size-6);
  --TableCell-line-height-large: 2.5rem;
  --TableCell-line-height-middle: 1.875rem;
  --TableCell-height: 2.5rem;
  --TableCell-height-default: 2.5625rem;
  --TableCell-height-large: 3.8125rem;
  --TableCell-height-small: 2.0625rem;
  --TableCell-paddingX: var(--sizes-size-6);
  --TableCell-paddingX-large: var(--gap-base);
  --TableCell-paddingX-small: var(--gap-xs);
  --TableCell-paddingY: calc(
    (var(--TableCell-height) - var(--Table-fontSize) * var(--Table-lineHeight)) / 2
  );
  --TableCell-paddingY-default: calc(
    (var(--TableCell-height-default) - var(--Table-fontSize) * var(--Table-lineHeight)) / 2
  );
  --TableCell-paddingY-large: calc(
    (var(--TableCell-height-large) - var(--Table-fontSize) * var(--Table-lineHeight)) / 2
  );
  --TableCell-paddingY-small: calc(
    (var(--TableCell-height-small) - var(--Table-fontSize) * var(--Table-lineHeight)) / 2
  );
  --TableCell-searchBtn--onActive-color: var(--table-icon-active-color);
  --TableCell-searchBtn-width: 1rem;
  --TableCell-sortBtn--default-onActive-opacity: 1;
  --TableCell-sortBtn--default-opacity: 0;
  --TableCell-sortBtn--onActive-color: var(--table-icon-active-color);
  --TableCell-sortBtn-width: var(--sizes-size-8);
  --TableCell-icon-gap: var(--table-icon-marginLeft);
  --combo-bg-color: var(--colors-neutral-fill-11);
  --combo-vertical-top-border-color: var(--colors-neutral-line-8);
  --combo-vertical-top-border-width: var(--borders-width-2);
  --combo-vertical-top-border-style: var(--borders-style-3);
  --combo-vertical-right-border-color: var(--colors-neutral-line-8);
  --combo-vertical-right-border-width: var(--borders-width-2);
  --combo-vertical-right-border-style: var(--borders-style-3);
  --combo-vertical-bottom-border-color: var(--colors-neutral-line-8);
  --combo-vertical-bottom-border-width: var(--borders-width-2);
  --combo-vertical-bottom-border-style: var(--borders-style-3);
  --combo-vertical-left-border-color: var(--colors-neutral-line-8);
  --combo-vertical-left-border-width: var(--borders-width-2);
  --combo-vertical-left-border-style: var(--borders-style-3);
  --combo-vertical-top-left-border-radius: var(--borders-radius-1);
  --combo-vertical-top-right-border-radius: var(--borders-radius-1);
  --combo-vertical-bottom-right-border-radius: var(--borders-radius-1);
  --combo-vertical-bottom-left-border-radius: var(--borders-radius-1);
  --combo-vertical-hover-top-border-color: var(--colors-brand-5);
  --combo-vertical-hover-top-border-width: var(--borders-width-2);
  --combo-vertical-hover-top-border-style: var(--borders-style-3);
  --combo-vertical-hover-right-border-color: var(--colors-brand-5);
  --combo-vertical-hover-right-border-width: var(--borders-width-2);
  --combo-vertical-hover-right-border-style: var(--borders-style-3);
  --combo-vertical-hover-bottom-border-color: var(--colors-brand-5);
  --combo-vertical-hover-bottom-border-width: var(--borders-width-2);
  --combo-vertical-hover-bottom-border-style: var(--borders-style-3);
  --combo-vertical-hover-left-border-color: var(--colors-brand-5);
  --combo-vertical-hover-left-border-width: var(--borders-width-2);
  --combo-vertical-hover-left-border-style: var(--borders-style-3);
  --combo-vertical-paddingTop: var(--sizes-size-6);
  --combo-vertical-paddingRight: var(--sizes-size-6);
  --combo-vertical-paddingBottom: var(--sizes-size-6);
  --combo-vertical-paddingLeft: var(--sizes-size-6);
  --combo-multi-addBtn-color: var(--button-primary-default-font-color);
  --combo-multi-addBtn-fontSize: var(--fonts-size-8);
  --combo-multi-addBtn-fontWeight: var(--fonts-weight-6);
  --combo-multi-addBtn-lineHeight: var(--fonts-lineHeight-2);
  --combo-multi-addBtn-top-left-border-radius: var(--borders-radius-3);
  --combo-multi-addBtn-top-right-border-radius: var(--borders-radius-3);
  --combo-multi-addBtn-bottom-right-border-radius: var(--borders-radius-3);
  --combo-multi-addBtn-bottom-left-border-radius: var(--borders-radius-3);
  --combo-multi-addBtn-bg-color: var(--colors-brand-5);
  --combo-multi-addBtn-height: var(--sizes-base-13);
  --combo-multi-addBtn-paddingTop: var(--sizes-size-3);
  --combo-multi-addBtn-paddingRight: var(--sizes-size-5);
  --combo-multi-addBtn-paddingBottom: var(--sizes-size-3);
  --combo-multi-addBtn-paddingLeft: var(--sizes-size-5);
  --combo-multi-addBtn-hover-color: var(--button-primary-hover-font-color);
  --combo-multi-addBtn-hover-bg-color: var(--colors-brand-6);
  --combo-multi-addBtn-active-color: var(--button-primary-active-font-color);
  --combo-multi-addBtn-active-bg-color: var(--colors-brand-4);
  --combo-multi-delBtn-color: var(--colors-neutral-text-5);
  --combo-multi-delBtn-hover-color: var(--colors-neutral-text-2);
  --Combo--horizontal-dragger-top: var(--sizes-size-3);
  --Combo--horizontal-item-gap: var(--gap-xs);
  --Combo--vertical-item-borderColor: var(--combo-vertical-top-border-color)
    var(--combo-vertical-right-border-color) var(--combo-vertical-bottom-border-color)
    var(--combo-vertical-left-border-color);
  --Combo--vertical-item--onError-borderColor: var(--colors-error-5);
  --Combo--vertical-item-borderRadius: var(--combo-vertical-top-left-border-radius)
    var(--combo-vertical-top-right-border-radius) var(--combo-vertical-bottom-right-border-radius)
    var(--combo-vertical-bottom-left-border-radius);
  --Combo--vertical-item-borderStyle: var(--combo-vertical-top-border-style)
    var(--combo-vertical-right-border-style) var(--combo-vertical-bottom-border-style)
    var(--combo-vertical-left-border-style);
  --Combo--vertical-item-borderWidth: var(--combo-vertical-top-border-width)
    var(--combo-vertical-right-border-width) var(--combo-vertical-bottom-border-width)
    var(--combo-vertical-left-border-width);
  --Combo--vertical-item-gap: var(--gap-xs);
  --Combo--vertical-item-onHover-borderColor: var(--combo-vertical-hover-top-border-color)
    var(--combo-vertical-hover-right-border-color) var(--combo-vertical-hover-bottom-border-color)
    var(--combo-vertical-hover-left-border-color);
  --Combo--vertical-item-paddingX: 0.625rem;
  --Combo--vertical-item-paddingY: 0.625rem;
  --Combo-addBtn-bg: var(--combo-multi-addBtn-bg-color);
  --Combo-addBtn-border: var(--combo-multi-addBtn-bg-color);
  --Combo-addBtn-borderRadius: var(--borders-radius-3);
  --Combo-addBtn-color: var(--combo-multi-addBtn-color);
  --Combo-addBtn-fontSize: var(--combo-multi-addBtn-fontSize);
  --Combo-addBtn-fontWeight: var(--combo-multi-addBtn-fontWeight);
  --Combo-addBtn-height: var(--combo-multi-addBtn-height);
  --Combo-addBtn-lineHeight: var(--combo-multi-addBtn-lineHeight);
  --Combo-addBtn-onActive-bg: var(--combo-multi-addBtn-active-bg-color);
  --Combo-addBtn-onActive-border: var(--combo-multi-addBtn-active-bg-color);
  --Combo-addBtn-onActive-color: var(--combo-multi-addBtn-active-color);
  --Combo-addBtn-onHover-bg: var(--combo-multi-addBtn-hover-bg-color);
  --Combo-addBtn-onHover-border: var(--combo-multi-addBtn-hover-bg-color);
  --Combo-addBtn-onHover-color: var(--combo-multi-addBtn-hover-color);
  --Combo-addBtn-paddingX: var(--sizes-size-5);
  --Combo-addBtn-paddingY: calc(
    (
        var(--Combo-addBtn-height) - var(--borders-width-2) * 2 - var(--Combo-addBtn-lineHeight) *
          var(--Combo-addBtn-fontSize)
      ) /
      2
  );
  --Combo-items-marginBottom: var(--gap-sm);
  --Combo-toolbarBtn-color: var(--icon-color);
  --Combo-toolbarBtn-height: var(--gap-md);
  --Combo-toolbarBtn-lineHeight: 1;
  --Combo-toolbarBtn-onHover-color: var(--colors-neutral-text-4);
  --Combo-toolbarBtn-paddingX: var(--gap-xs);
  --Combo-toolbarBtn-paddingY: 0.125rem;
  --Wizard-badge-size: var(--sizes-base-13);
  --Wizard-badge-fontSize: var(--fonts-size-7);
  --Wizard-badge-color: var(--colors-neutral-text-6);
  --Wizard-badge-border-width: var(--borders-width-2);
  --Wizard-badge-border-color: var(--colors-neutral-line-6);
  --Wizard-badge-bg-color: var(--colors-neutral-fill-11);
  --Wizard-badge-onActive-color: var(--colors-neutral-text-11);
  --Wizard-badge-onActive-bg-color: var(--colors-neutral-fill-3);
  --Wizard-badge-text-margin: var(--sizes-size-4);
  --Wizard-step-fontSize: var(--fonts-size-7);
  --Wizard-step-color: var(--colors-neutral-text-6);
  --Wizard-step-paddingTop: var(--sizes-size-6);
  --Wizard-step-paddingRight: var(--sizes-size-0);
  --Wizard-step-paddingBottom: var(--sizes-size-6);
  --Wizard-step-paddingLeft: var(--sizes-size-0);
  --Wizard-step-bg-color: var(--colors-neutral-fill-11);
  --Wizard-step-li-onActive-color: var(--colors-neutral-text-3);
  --Wizard-step-li-onActive-bg-color: var(--colors-neutral-fill-11);
  --Wizard-after-color: var(--colors-neutral-text-11);
  --Wizard-after-onActive-color: var(--colors-neutral-fill-11);
  --Wizard-stepContent-paddingTop: var(--sizes-size-7);
  --Wizard-stepContent-paddingRight: var(--sizes-size-7);
  --Wizard-stepContent-paddingBottom: var(--sizes-size-7);
  --Wizard-stepContent-paddingLeft: var(--sizes-size-7);
  --Wizard-badge-bg: var(--Wizard-badge-bg-color);
  --Wizard-badge-border: var(--Wizard-badge-border-width) var(--borders-style-2)
    var(--Wizard-badge-border-color);
  --Wizard-badge-borderRadius: var(--borders-radius-7);
  --Wizard-badge-marginRight: var(--Wizard-badge-text-margin);
  --Wizard-badge-onActive-backgroundColor: var(--Wizard-badge-onActive-bg-color);
  --Wizard-badge-onComplete-backgroundColor: var(--Wizard-badge-onActive-bg-color);
  --Wizard-badge-onComplete-color: var(--Wizard-badge-onActive-color);
  --Wizard-steps-bg--isComplete: var(--colors-neutral-fill-11);
  --Wizard-steps-bg: var(--Wizard-step-bg-color);
  --Wizard-steps-borderWidth: var(--borders-width-1);
  --Wizard-steps-height: var(--sizes-base-30);
  --Wizard-steps-li-onActive-arrow-bg: var(--Wizard-after-onActive-color);
  --Wizard-steps-li-onActive-bg: var(--Wizard-step-li-onActive-bg-color);
  --Wizard-steps-li-onActive-color: var(--Wizard-step-li-onActive-color);
  --Wizard-steps-liAfterContent: '';
  --Wizard-steps-liVender: 'iconfont';
  --Wizard-steps-padding: var(--Wizard-step-paddingTop) var(--Wizard-step-paddingRight)
    var(--Wizard-step-paddingBottom) var(--Wizard-step-paddingLeft);
  --Wizard-steps-textAlign: center;
  --Wizard-steps-ulDisplay: inline-block;
  --Wizard-stepsContent-padding: var(--Wizard-stepContent-paddingTop)
    var(--Wizard-stepContent-paddingRight) var(--Wizard-stepContent-paddingBottom)
    var(--Wizard-stepContent-paddingLeft);
  --common-popover-border: var(--borders-width-2) solid var(--colors-neutral-fill-9);
  --Form-static-fontSize: var(--fonts-size-7);
  --Form-static-color: var(--colors-neutral-text-2);
  --Form-static-lineHeight: var(--fonts-lineHeight-2);
  --Form-static-fontWeight: var(--fonts-weight-6);
}
:root {
  --colors-neutral-fill-none: translate;
  --colors-error-1: #4c0008;
  --colors-error-2: #750d12;
  --colors-error-3: #9e1d1e;
  --colors-error-4: #c63d3d;
  --colors-error-5: #f56c6c; /* 替换后的基准色 */
  --colors-error-6: #ff8989;
  --colors-error-7: #ffa8a6;
  --colors-error-8: #ffc7c4;
  --colors-error-9: #ffe3e1;
  --colors-error-10: #fff0ed;
  --colors-warning-1: #582100; /* 降低明度保持深色区厚重感 */
  --colors-warning-2: #7f3200; /* 增加黄色相偏移 */
  --colors-warning-3: #a64d04; /* 衔接新基准色的中间过渡 */
  --colors-warning-4: #cc6a1a; /* 前移黄色相为基准色铺垫 */
  --colors-warning-5: #e6a23c; /* 新基准色(更柔和的黄金色) */
  --colors-warning-6: #f7b862; /* 降低饱和度避免荧光感 */
  --colors-warning-7: #ffd08a; /* 加入奶油色过渡 */
  --colors-warning-8: #ffe4b3; /* 匹配现代设计趋势的浅米黄 */
  --colors-warning-9: #fff2d9; /* 增加暖灰平衡亮度 */
  --colors-warning-10: #fff8ec; /* 最高阶加入珍珠白质感 */
  --colors-success-1: #001d03; /* 增加深度提升对比度 */
  --colors-success-2: #034a0a; /* 加强黄绿倾向 */
  --colors-success-3: #0d6e18; /* 衔接新基准色的中间调 */
  --colors-success-4: #2d8d23; /* 预热新基准色的黄绿色相 */
  --colors-success-5: #67c23a; /* 新基准色(更具生命力的青柠绿) */
  --colors-success-6: #87ce5c; /* 降低饱和度避免刺眼 */
  --colors-success-7: #a8db83; /* 加入灰绿提升高级感 */
  --colors-success-8: #c9e8ad; /* 匹配现代UI趋势的浅灰绿 */
  --colors-success-9: #e5f5d8; /* 增加冷调平衡暖色 */
  --colors-success-10: #f3fcec; /* 最高阶微调为中性白绿 */
  --colors-link-1: #000d3d; /* 强化深蓝基调提升专业感 */
  --colors-link-2: #001b66; /* 增加青色倾向 */
  --colors-link-3: #062d8f; /* 衔接新基准色的中间过渡 */
  --colors-link-4: #1d4db3; /* 预演新基准色的青蓝特性 */
  --colors-link-5: #409eff; /* 新基准色(现代科技蓝) */
  --colors-link-6: #66b3ff; /* 降低饱和度提升可用性 */
  --colors-link-7: #8cc5ff; /* 加入冷灰平衡亮度 */
  --colors-link-8: #b3d8ff; /* 匹配Figma设计趋势的浅天蓝 */
  --colors-link-9: #d9ebff; /* 增加冷白偏移 */
  --colors-link-10: #edf6ff; /* 最高阶微调为珍珠白 */
  --colors-info-1: #000d3d; /* 强化深蓝基调提升专业感 */
  --colors-info-2: #001b66; /* 增加青色倾向 */
  --colors-info-3: #062d8f; /* 衔接新基准色的中间过渡 */
  --colors-info-4: #1d4db3; /* 预演新基准色的青蓝特性 */
  --colors-info-5: #409eff; /* 新基准色(现代科技蓝) */
  --colors-info-6: #66b3ff; /* 降低饱和度提升可用性 */
  --colors-info-7: #8cc5ff; /* 加入冷灰平衡亮度 */
  --colors-info-8: #b3d8ff; /* 匹配Figma设计趋势的浅天蓝 */
  --colors-info-9: #d9ebff; /* 增加冷白偏移 */
  --colors-info-10: #edf6ff; /* 最高阶微调为珍珠白 */
  --colors-other-1: #000d3d; /* 强化深蓝基调提升专业感 */
  --colors-other-2: #001b66; /* 增加青色倾向 */
  --colors-other-3: #062d8f; /* 衔接新基准色的中间过渡 */
  --colors-other-4: #1d4db3; /* 预演新基准色的青蓝特性 */
  --colors-other-5: #409eff; /* 新基准色(现代科技蓝) */
  --colors-other-6: #66b3ff; /* 降低饱和度提升可用性 */
  --colors-other-7: #8cc5ff; /* 加入冷灰平衡亮度 */
  --colors-other-8: #b3d8ff; /* 匹配Figma设计趋势的浅天蓝 */
  --colors-other-9: #d9ebff; /* 增加冷白偏移 */
  --colors-other-10: #edf6ff; /* 最高阶微调为珍珠白 */
  --colors-brand-1: #000d3d; /* 强化深蓝基调提升专业感 */
  --colors-brand-2: #001b66; /* 增加青色倾向 */
  --colors-brand-3: #062d8f; /* 衔接新基准色的中间过渡 */
  --colors-brand-4: #1d4db3; /* 预演新基准色的青蓝特性 */
  --colors-brand-5: #409eff; /* 新基准色(现代科技蓝) */
  --colors-brand-6: #66b3ff; /* 降低饱和度提升可用性 */
  --colors-brand-7: #8cc5ff; /* 加入冷灰平衡亮度 */
  --colors-brand-8: #b3d8ff; /* 匹配Figma设计趋势的浅天蓝 */
  --colors-brand-9: #d9ebff; /* 增加冷白偏移 */
  --colors-brand-10: #edf6ff; /* 最高阶微调为珍珠白 */
  --colors-neutral-text-1: #070c14;
  --colors-neutral-text-2: #151b26;
  --colors-neutral-text-3: #303540;
  --colors-neutral-text-4: #5c5f66;
  --colors-neutral-text-5: #84878c;
  --colors-neutral-text-6: #b8babf;
  --colors-neutral-text-7: #d4d6d9;
  --colors-neutral-text-8: #e8e9eb;
  --colors-neutral-text-9: #f2f3f5;
  --colors-neutral-text-10: #f7f8fa;
  --colors-neutral-text-11: #ffffff;
  --colors-neutral-fill-none: transparent;
  --colors-neutral-fill-1: #070c14;
  --colors-neutral-fill-2: #151b26;
  --colors-neutral-fill-3: #303540;
  --colors-neutral-fill-4: #5c5f66;
  --colors-neutral-fill-5: #84878c;
  --colors-neutral-fill-6: #b8babf;
  --colors-neutral-fill-7: #d4d6d9;
  --colors-neutral-fill-8: #e8e9eb;
  --colors-neutral-fill-9: #f2f3f5;
  --colors-neutral-fill-10: #f7f8fa;
  --colors-neutral-fill-11: #ffffff;
  --colors-neutral-fill-12: #eef3fe;
  --colors-neutral-line-1: #070c14;
  --colors-neutral-line-2: #151b26;
  --colors-neutral-line-3: #303540;
  --colors-neutral-line-4: #5c5f66;
  --colors-neutral-line-5: #84878c;
  --colors-neutral-line-6: #b8babf;
  --colors-neutral-line-7: #d4d6d9;
  --colors-neutral-line-8: #e8e9eb;
  --colors-neutral-line-9: #f2f3f5;
  --colors-neutral-line-10: #f7f8fa;
  --colors-neutral-line-11: #ffffff;
  --fonts-base-family:
    -apple-system, 'Noto Sans', 'Helvetica Neue', Helvetica, 'Nimbus Sans L', Arial,
    'Liberation Sans', 'PingFang SC', 'Hiragino Sans GB', 'Noto Sans CJK SC', 'Source Han Sans SC',
    'Source Han Sans CN', 'Microsoft YaHei', 'Wenquanyi Micro Hei', 'WenQuanYi Zen Hei', 'ST Heiti',
    SimHei, 'WenQuanYi Zen Hei Sharp', sans-serif;
  --fonts-size-1: 48px;
  --fonts-size-2: 40px;
  --fonts-size-3: 32px;
  --fonts-size-4: 24px;
  --fonts-size-5: 18px;
  --fonts-size-6: 16px;
  --fonts-size-7: 14px;
  --fonts-size-8: 12px;
  --fonts-size-9: 12px;
  --fonts-weight-1: 900;
  --fonts-weight-2: 800;
  --fonts-weight-3: 700;
  --fonts-weight-4: 600;
  --fonts-weight-5: 500;
  --fonts-weight-6: 400;
  --fonts-weight-7: 300;
  --fonts-weight-8: 200;
  --fonts-weight-9: 100;
  --fonts-lineHeight-1: 1.3;
  --fonts-lineHeight-2: 1.5;
  --fonts-lineHeight-3: 1.7;
  --borders-style-1: none;
  --borders-style-2: solid;
  --borders-style-3: dashed;
  --borders-style-4: dotted;
  --borders-width-1: 0px;
  --borders-width-2: 1px;
  --borders-width-3: 2px;
  --borders-width-4: 4px;
  --borders-radius-1: 0px;
  --borders-radius-2: 2px;
  --borders-radius-3: 4px;
  --borders-radius-4: 6px;
  --borders-radius-5: 8px;
  --borders-radius-6: 10px;
  --borders-radius-7: 50%;
  --sizes-size-0: 0rem;
  --sizes-size-1: auto;
  --sizes-size-2: 0.125rem;
  --sizes-size-3: 0.25rem;
  --sizes-size-4: 0.375rem;
  --sizes-size-5: 0.5rem;
  --sizes-size-6: 0.625rem;
  --sizes-size-7: 0.75rem;
  --sizes-size-8: 0.875rem;
  --sizes-size-9: 1rem;
  --sizes-base-1: 0.125rem;
  --sizes-base-2: 0.25rem;
  --sizes-base-3: 0.375rem;
  --sizes-base-4: 0.5rem;
  --sizes-base-5: 0.625rem;
  --sizes-base-6: 0.75rem;
  --sizes-base-7: 0.875rem;
  --sizes-base-8: 1rem;
  --sizes-base-9: 1.125rem;
  --sizes-base-10: 1.25rem;
  --sizes-base-11: 1.375rem;
  --sizes-base-12: 1.5rem;
  --sizes-base-13: 1.625rem;
  --sizes-base-14: 1.75rem;
  --sizes-base-15: 1.875rem;
  --sizes-base-16: 2rem;
  --sizes-base-17: 2.125rem;
  --sizes-base-18: 2.25rem;
  --sizes-base-19: 2.375rem;
  --sizes-base-20: 2.5rem;
  --sizes-base-21: 2.625rem;
  --sizes-base-22: 2.75rem;
  --sizes-base-23: 2.875rem;
  --sizes-base-24: 3rem;
  --sizes-base-25: 3.125rem;
  --sizes-base-26: 3.25rem;
  --sizes-base-27: 3.375rem;
  --sizes-base-28: 3.5rem;
  --sizes-base-29: 3.625rem;
  --sizes-base-30: 3.75rem;
  --sizes-base-31: 3.875rem;
  --sizes-base-32: 4rem;
  --sizes-base-33: 4.125rem;
  --sizes-base-34: 4.25rem;
  --sizes-base-35: 4.375rem;
  --sizes-base-36: 4.5rem;
  --sizes-base-37: 4.625rem;
  --sizes-base-38: 4.75rem;
  --sizes-base-39: 4.875rem;
  --sizes-base-40: 5rem;
  --sizes-base-41: 5.125rem;
  --sizes-base-42: 5.25rem;
  --sizes-base-43: 5.375rem;
  --sizes-base-44: 5.5rem;
  --sizes-base-45: 5.625rem;
  --sizes-base-46: 5.75rem;
  --sizes-base-47: 5.875rem;
  --sizes-base-48: 6rem;
  --sizes-base-49: 6.125rem;
  --sizes-base-50: 6.25rem;
  --shadows-shadow-none: 0px 0px 0px 0px transparent;
  --shadows-shadow-sm: 0px 1px 2px 0px rgba(0, 0, 0, 0.05);
  --shadows-shadow-normal: 0px 1px 3px 0px rgba(0, 0, 0, 0.1), 0px 1px 2px 0px rgba(0, 0, 0, 0.06);
  --shadows-shadow-md: 0px 4px -1px 0px rgba(0, 0, 0, 0.1), 0px 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadows-shadow-lg: 0px 10px 15px -3px rgba(0, 0, 0, 0.1), 0px 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadows-shadow-xl:
    0px 20px 25px -5px rgba(0, 0, 0, 0.1), 0px 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadows-shadow-2xl: 0px 25px 50px -12px rgba(0, 0, 0, 0.25);
}
/* 默认 CSS 样式，目前主要是 Excel 使用 */
.ov-excel {
  --bar-bg-color: #f5f5f5;
  --bar-input-bg-color: #ffffff;
  --active-sheet-color: #217346;
}
/*!
* animate.css - https://animate.style/
* Version - 4.1.1
* Licensed under the MIT license - http://opensource.org/licenses/MIT
*
* Copyright (c) 2020 Animate.css
*/
:root {
  --animate-duration: 1s;
  --animate-delay: 1s;
  --animate-repeat: 1;
}
.cxd-Layout--headerFixed {
  --affix-offset-top: var(--Layout-header-height);
}
.cxd-Crud2.is-mobile.is-mobile-cards .cxd-Card {
  --Card-borderRadius: var(--sizes-size-5);
  --gap-base: var(--sizes-size-9);
  --fontSizeBase: var(--fonts-size-7);
  --body-lineHeight: var(--sizes-base-11);
  --Card-actions-borderColor: #f2f2f4;
  --Card-actions-fontSize: var(--fontSizeBase);
}
.cxd-Crud2.is-mobile.is-mobile-cards .cxd-Panel {
  --Panel-bodyPadding: var(--gap-md);
  --Panel-headingPadding: var(--gap-sm) var(--gap-md);
  --Panel-body-paddingTop: var(--gap-md);
  --Panel-body-paddingBottom: var(--gap-md);
  --Panel-body-paddingLeft: var(--gap-md);
  --Panel-body-paddingRight: var(--gap-md);
}
.cxd-TextareaControl {
  --Form-input-clearBtn-padding: 0.125rem;
}
.cxd-Transfer-footer-pagination > ul > li {
  --Pagination-minWidth: 1.375rem;
  --Pagination-height: 1.375rem;
  --Pagination-padding: 0 0.375rem;
}
.cxd-Transfer-footer-pagination .cxd-Pagination-perpage {
  --select-base-default-paddingTop: 0;
  --select-base-default-paddingBottom: 0;
  --select-base-default-paddingLeft: 0.375rem;
  --select-base-default-paddingRight: 0.375rem;
}
.cxd-FormulaEditor-VariableList-base {
  --Form-input-fontSize: var(--fontSizeSm);
  --gap-sm: 0.625rem;
}
.cxd-FormulaEditor-VariableList-tabs {
  --Tabs--line-fontSize: var(--fontSizeSm);
  --Tabs--line-active-fontSize: var(--fontSizeSm);
  --Tabs--line-hover-fontSize: var(--fontSizeSm);
  --Tabs--line-lineHeight: 30px;
  --Tabs--line-active-lineHeight: 30px;
  --Tabs--line-hover-lineHeight: 30px;
  --Tabs--line-fontWeight: 500;
  --Tabs--line-active-fontWeight: 500;
  --Tabs--line-hover-fontWeight: 500;
  --Tabs--card-linkPadding: 0.3125rem;
}
.cxd-Nav-Menu {
  --Menu-width: var(--Layout-aside-width);
  --Menu-width--collapsed: var(--Layout-aside-width-collapsed);
  --Menu-fontSize--collapsed: var(--Nav-item-collapsed-fontSize);
  --Menu-fontColor-onDisabled: var(--Nav-item-fontColor-onDisabled);
  --Menu-Submenu-item-paddingX: var(--Nav-Item-Badge-paddingRight);
  --Menu-light-backgroundColor: var(--Layout-light-backgroundColor);
  --Menu-light-active-backgroundColor: var(--colors-neutral-fill-8);
  --Menu-light-backgroundColor-onHover: var(--Layout-light-bgColor-onHover);
  --Menu-light-fontColor: var(--Layout-light-fontColor);
  --Menu-light-fontColor-onHover: var(--Layout-fontColor--onHover);
  --Menu-light-ancestor-fontColor-onActive: var(--Layout-light-fontColor);
  --Menu-light-fontColor-onActive: var(--Layout-fontColor--onActive);
  --Menu-light-groupTitle-fontColor: #84868c;
  --Menu-light-selectedIndicator-color: var(--Layout-fontColor--info);
  --Menu-dark-backgroundColor: var(--Layout-dark-backgroundColor);
  --Menu-dark-backgroundColor-onHover: var(--Layout-fontColor--info);
  --Menu-dark-fontColor: var(--Layout-dark-fontColor);
  --Menu-dark-fontColor-onHover: var(--Layout-fontColor--onHover);
  --Menu-dark-fontColor-onActive: var(--Layout-dark-fontColor);
  --Menu-dark-groupTitle-fontColor: #84868c;
  --Menu-dark-selectedIndicator-color: var(--Layout-dark-selected-color);
}

// ============== 自定义表格筛选表单的样式 ============== //
.cxd-Crud2-filter .cxd-Panel--form {
  background-color: #f3f4f5;
  border: none;
  border-radius: var(--borders-radius-3);
  margin-bottom: 10px;
}
.cxd-Crud2-filter .cxd-Panel--form .cxd-Form {
  margin: 0;
}

.cxd-Crud2-filter .cxd-Panel--form .cxd-Form .cxd-Form-item {
  margin-bottom: 10px;
}

.cxd-Crud2-filter .cxd-Panel--form .cxd-Form .cxd-Form-item .cxd-Form-label {
  display: flex;
  align-items: center;
  text-align: left;
  margin: 0;
  padding: 0 10px;
  background-color: #fff;
  border: 1px solid #e4e7ed;
  border-top-left-radius: var(--borders-radius-3);
  border-bottom-left-radius: var(--borders-radius-3);
}

.cxd-Crud2-filter .cxd-Panel--form .cxd-Form .cxd-Form-item .cxd-Form-value {
  background-color: #fff;
  border: 1px solid #e4e7ed;
  border-left:none;
  border-top-right-radius: var(--borders-radius-3);
  border-bottom-right-radius: var(--borders-radius-3);
  .cxd-TextControl{
    .cxd-TextControl-input{
      .cxd-TextControl-clear{
        .icon{
          //解决input筛选框,清空按钮的样式问题
          width: 1em;
          height: 1em;
          vertical-align:top;
          overflow: hidden;
        }
      }
    }
  }
}

.cxd-Crud2-filter .cxd-Panel--form .cxd-Form .cxd-Form-item .cxd-Form-value .cxd-Form-control {
  width: 100%;
  > div, &.is-focused > div {
    width: 100%;
    border: none;
  } 
  &.cxd-RadiosControl, &.cxd-CheckboxesControl, &.cxd-CheckboxControl {
    padding-left: 10px;
  }
  &.cxd-TreeControl,.cxd-TextareaControl-input, .cxd-ImageControl-addBtn {
    border: none;
  }
  &.cxd-TagControl, &.cxd-FileControl {
    height: 100%;
    display: flex;
    align-items: center;
  }
  &.cxd-NumberControl {
    height: 100%;
    > .cxd-Number {
      height: 100%;
      &:hover .cxd-Number-handler-wrap {
        display: flex;
        flex-direction: column;
        justify-content: center;
      }
    }
  }
}

//表格列中的提示信息样式
.cxd-Table-cell{
  .cxd-Table-head-cell-wrapper{
    .cxd-Remark{
      .cxd-Remark-icon{
        background: transparent;
        border: none;
        vertical-align: text-top;
      }
      .cxd-Remark-icon:hover{
       color: var(--warning);
      }
    }
  }
  //将所有的列表操作列，设置鼠标样式
  .cxd-OperationField{
    span{
      cursor: pointer;
    }
  }
}
//容器列-当做操作列固定右侧时的样式
.cxd-Table-cell-fix-right{
  .cxd-Container{
    span{
      cursor: pointer;
    }
  }
}

//弹框、抽屉关闭icon样式
.cxd-Modal{
  .cxd-Modal-content{
    .cxd-Modal-header{
      .cxd-Modal-close{
        .icon{
          width: 1em;
          height: auto;
          vertical-align: top;
          overflow: hidden;
        }
      }
    }
  }
}

// ============== 自定义表格的样式 ============== //

//覆盖表格中内容过长鼠标hover的提示样式
.cxd-Table-render-wrapper .cxd-PopOver{
  .cxd-Panel--default{
    // background: #303133 !important;
    .cxd-Panel-body{
      padding: 5px !important;
      .cxd-PlainField{
        // color: #fff !important;
        cursor: pointer;
      }
    }
  }
  
}
//表格列编辑时，清除按钮icon样式
.cxd-TextControl-clear{
  .icon{
    width: 1em;
    height: 1em;
    vertical-align: .15em !important;
    overflow: hidden;
  }
}
//表格列编辑时，文字显示省略号时，编辑icon不在一行问题
.cxd-Field--quickEditable{
  display: flex;
}

//树状组件-添加行为按钮时样式覆盖
.cxd-Tree-itemLabel .cxd-Tree-item-icons {
  // visibility: hidden;
  display: none;
}
.cxd-Tree-itemLabel:hover .cxd-Tree-item-icons {
  display: inline-block;
  visibility: visible;
}
.cxd-Tree-itemLabel{
  .cxd-Tree-itemText{
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
    display: inline-block;
  }
}           
//解决插入图片默认边框问题
.cxd-Image{
  border:none !important;
}
//flex下图片不居中问题
.cxd-Image--thumb{
  display: block;
}

//表格右侧固定的容器列的下拉按钮样式覆盖
.cxd-Table-cell-wrapper{
  .cxd-DropDown{
    .cxd-Button{
      border: none !important;
    }
    
  }
  .cxd-Button--default:not(:disabled):not(.is-disabled):hover, .cxd-Button--default:not(:disabled):not(.is-disabled).hover{
    border:none !important;
    background: transparent !important;
  }
}