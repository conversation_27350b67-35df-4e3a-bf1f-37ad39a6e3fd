import React from 'react';
import PropTypes from 'prop-types';
import './style.less';

/**
 * Context用于在Popover组件和其子组件间共享状态
 */
const PopoverContext = React.createContext({
  isOpen: false,
  toggle: () => {},
  close: () => {},
  open: () => {}
});

/**
 * PopoverTrigger组件 - 作为触发弹出层的元素
 */
class PopoverTrigger extends React.Component {
  static contextType = PopoverContext;

  render() {
    const { children } = this.props;
    const { toggle, triggerRef } = this.context;

    return (
      <div className="qz-popover-trigger" onClick={toggle} ref={triggerRef}>
        {children}
      </div>
    );
  }
}

PopoverTrigger.propTypes = {
  children: PropTypes.node.isRequired
};

/**
 * PopoverContent组件 - 弹出层的内容
 */
class PopoverContent extends React.Component {
  static contextType = PopoverContext;

  render() {
    const { children, className } = this.props;
    const { contentRef, placement } = this.context;

    return (
      <div
        className={`qz-popover-content qz-popover-content--${placement} ${className}`}
        ref={contentRef}
      >
        <div className="qz-popover-arrow"></div>
        <div className="qz-popover-inner">{children}</div>
      </div>
    );
  }
}

PopoverContent.propTypes = {
  children: PropTypes.node.isRequired,
  placement: PropTypes.oneOf(['top', 'right', 'bottom', 'left'])
};

/**
 * Popover组件 - 提供弹出层功能
 */
class Popover extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      isOpen: false,
      triggerRef: React.createRef(),
      contentRef: React.createRef(),
      adjustedPlacement: props.placement || 'bottom'
    };

    this.togglePopover = this.togglePopover.bind(this);
    this.openPopover = this.openPopover.bind(this);
    this.closePopover = this.closePopover.bind(this);
    this.handleClickOutside = this.handleClickOutside.bind(this);
    this.adjustPopoverPosition = this.adjustPopoverPosition.bind(this);
  }

  componentDidMount() {
    document.addEventListener('mousedown', this.handleClickOutside);
    window.addEventListener('resize', this.adjustPopoverPosition);
  }

  componentDidUpdate(prevProps, prevState) {
    // 当弹出层打开时，检查并调整位置
    if (!prevState.isOpen && this.state.isOpen) {
      this.adjustPopoverPosition();
    }
  }

  componentWillUnmount() {
    document.removeEventListener('mousedown', this.handleClickOutside);
    window.removeEventListener('resize', this.adjustPopoverPosition);
  }

  /**
   * 处理点击外部事件，当点击位置不在弹出层内部时关闭弹出层
   */
  handleClickOutside(event) {
    const { contentRef, triggerRef, isOpen } = this.state;
    if (isOpen) {
      if (
        contentRef.current &&
        !contentRef.current.contains(event.target) &&
        triggerRef.current &&
        !triggerRef.current.contains(event.target)
      ) {
        this.closePopover();
      }
    }
  }

  /**
   * 切换弹出层的开关状态
   */
  togglePopover() {
    this.setState((prevState) => ({
      isOpen: !prevState.isOpen
    }));
  }

  /**
   * 打开弹出层
   */
  openPopover() {
    this.setState({ isOpen: true });
  }

  /**
   * 关闭弹出层
   */
  closePopover() {
    this.setState({ isOpen: false });
  }

  /**
   * 调整弹出层位置，确保不超出屏幕范围
   */
  adjustPopoverPosition() {
    const { contentRef, triggerRef } = this.state;
    const { placement } = this.props;

    if (!contentRef.current || !triggerRef.current) return;

    // const triggerRect = triggerRef.current.getBoundingClientRect();
    const contentRect = contentRef.current.getBoundingClientRect();
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    // 计算是否超出屏幕边界
    const overflowTop = contentRect.top < 0;
    const overflowBottom = contentRect.bottom > viewportHeight;
    const overflowLeft = contentRect.left < 0;
    const overflowRight = contentRect.right > viewportWidth;

    let newPlacement = placement || 'bottom';

    // 根据溢出情况调整位置
    if (newPlacement === 'top' && overflowTop) {
      newPlacement = 'bottom';
    } else if (newPlacement === 'bottom' && overflowBottom) {
      newPlacement = 'top';
    } else if (newPlacement === 'left' && overflowLeft) {
      newPlacement = 'right';
    } else if (newPlacement === 'right' && overflowRight) {
      newPlacement = 'left';
    }

    // 处理水平方向溢出
    if (
      (newPlacement === 'top' || newPlacement === 'bottom') &&
      (overflowLeft || overflowRight)
    ) {
      contentRef.current.style.left = '50%';
      contentRef.current.style.transform = 'translateX(-50%)';

      // 检查修正后是否仍然溢出
      const updatedRect = contentRef.current.getBoundingClientRect();
      if (updatedRect.left < 0) {
        contentRef.current.style.left = '0';
        contentRef.current.style.transform = 'none';
      } else if (updatedRect.right > viewportWidth) {
        contentRef.current.style.left = 'auto';
        contentRef.current.style.right = '0';
        contentRef.current.style.transform = 'none';
      }
    }

    // 处理垂直方向溢出
    if (
      (newPlacement === 'left' || newPlacement === 'right') &&
      (overflowTop || overflowBottom)
    ) {
      contentRef.current.style.top = '50%';
      contentRef.current.style.transform = 'translateY(-50%)';

      // 检查修正后是否仍然溢出
      const updatedRect = contentRef.current.getBoundingClientRect();
      if (updatedRect.top < 0) {
        contentRef.current.style.top = '0';
        contentRef.current.style.transform = 'none';
      } else if (updatedRect.bottom > viewportHeight) {
        contentRef.current.style.top = 'auto';
        contentRef.current.style.bottom = '0';
        contentRef.current.style.transform = 'none';
      }
    }

    if (newPlacement !== this.state.adjustedPlacement) {
      this.setState({ adjustedPlacement: newPlacement });
    }
  }

  render() {
    const { isOpen, triggerRef, contentRef, adjustedPlacement } = this.state;
    const { children, className = '' } = this.props;

    // 准备传递给Context的值
    const contextValue = {
      isOpen,
      toggle: this.togglePopover,
      open: this.openPopover,
      close: this.closePopover,
      triggerRef,
      contentRef,
      placement: adjustedPlacement
    };

    // 查找子组件
    let triggerElement = null;
    let contentElement = null;

    React.Children.forEach(children, (child) => {
      if (child.type === PopoverTrigger) {
        triggerElement = child;
      } else if (child.type === PopoverContent) {
        contentElement = child;
      }
    });

    return (
      <PopoverContext.Provider value={contextValue}>
        <div className={`qz-popover ${className}`}>
          {triggerElement}
          {isOpen &&
            React.cloneElement(contentElement, {
              placement: adjustedPlacement
            })}
        </div>
      </PopoverContext.Provider>
    );
  }
}

Popover.propTypes = {
  children: PropTypes.node.isRequired,
  placement: PropTypes.oneOf(['top', 'right', 'bottom', 'left']),
  className: PropTypes.string
};

export { Popover, PopoverTrigger, PopoverContent };
