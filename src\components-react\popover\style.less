@import '../../assets/css/mixins.less';

.qz-popover {
  position: relative;
  display: inline-block;
}

.qz-popover-trigger {
  cursor: pointer;
  display: inline-block;
}

.qz-popover-content {
  position: absolute;
  z-index: 1000;
  min-width: 150px;
  max-width: 300px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 12px;
  font-size: 14px;
  color: #303133;
  line-height: 1.4;
  max-height: 80vh; // 最大高度不超过视口高度的80%
  overflow: auto;

  // 箭头基本样式
  .qz-popover-arrow {
    position: absolute;
    width: 0;
    height: 0;
    border: 6px solid transparent;
  }

  // 根据不同方向定位弹出层和箭头
  &.qz-popover-content--top {
    bottom: calc(100% + 10px);
    left: 50%;
    transform: translateX(-50%);

    .qz-popover-arrow {
      bottom: -12px;
      left: 50%;
      transform: translateX(-50%);
      border-top-color: #fff;
    }
  }

  &.qz-popover-content--bottom {
    top: calc(100% + 10px);
    left: 50%;
    transform: translateX(-50%);

    .qz-popover-arrow {
      top: -12px;
      left: 50%;
      transform: translateX(-50%);
      border-bottom-color: #fff;
    }
  }

  &.qz-popover-content--left {
    right: calc(100% + 10px);
    top: 50%;
    transform: translateY(-50%);

    .qz-popover-arrow {
      right: -12px;
      top: 50%;
      transform: translateY(-50%);
      border-left-color: #fff;
    }
  }

  &.qz-popover-content--right {
    left: calc(100% + 10px);
    top: 50%;
    transform: translateY(-50%);

    .qz-popover-arrow {
      left: -12px;
      top: 50%;
      transform: translateY(-50%);
      border-right-color: #fff;
    }
  }
}

.qz-popover-inner {
  overflow-y: auto;
}

// 当弹出层被手动调整位置时
.qz-popover-content[style] {
  // 当使用JS调整位置时，覆盖原有的定位规则
  &.qz-popover-content--top,
  &.qz-popover-content--bottom,
  &.qz-popover-content--left,
  &.qz-popover-content--right {
    // 当有内联样式时，允许其覆盖预设的定位
    &[style*="left"],
    &[style*="right"],
    &[style*="top"],
    &[style*="bottom"] {
      transform: none;
    }
  }
}

// 分组下拉菜单样式
.ApiTable__group-more-option {
  padding: 8px 12px;
  cursor: pointer;
  .ellipsis();
  
  &:hover {
    background-color: #f5f7fa;
  }
  
  &.selected {
    color: #4a97eb;
    background-color: #ecf5ff;
  }
}
