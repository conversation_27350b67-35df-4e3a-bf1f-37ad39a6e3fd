<template>
  <div class="ai-robot-icon">
    <qz-tips :tips-content="tips" placement="top">
      <div class="default-icon">
        <img :src="defaultIcon" />
      </div>
    </qz-tips>
  </div>
</template>

<script>
const defaultIcon = require('@/assets/imgs/ai-robot.png');
export default {
  props: ['props'],
  data() {
    return {
      defaultIcon,
      tips: '提示内容'
    };
  },
  mounted() {
    const { tipsContent, tips } = this.props;
    if (tipsContent) {
      this.tips = tipsContent;
    } else {
      this.tips = tips || '提示内容';
    }
  }
};
</script>

<style lang="less" scoped>
.ai-robot-icon {
  .default-icon {
    width: 20px;
    height: 20px;
    border-radius: 50%;
  }

  .default-icon img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
}
</style>
