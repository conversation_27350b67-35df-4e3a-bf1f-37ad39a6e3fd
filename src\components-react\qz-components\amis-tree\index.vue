<template>
  <div class="tree-container">
    <qz-pro-virtual-tree
      :data="treeData"
      nodeKey="id"
      :keeps="40"
      :props="defaultProps"
      :height="500"
      lazy
      :load="loadNode"
    />
  </div>
</template>

<script>
import { postTreeData } from '@/service/common-service';
export default {
  props: ['props'],
  data() {
    return {
      treeData: [],
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      sid: '',
      sType: ''
    };
  },

  components: {},

  mounted() {
    if (this.props?.data?.id) {
      this.sid = this.props.data.id;
    }
    if (!this.sid) {
      this.$message.warning('参数id不存在');
      return;
    }
    //用于区分是哪个的页面的，对应不同的参数
    this.sType = this.props?.sType || '';
    if (this.sType) {
      let p = {};
      if (this.sType == 'schema') {
        p = {
          tableName: 'dc_schema',
          searchConditionList: [
            { fieldName: 'id', columnExp: '=', value: this.sid }
          ],
          columnList: ['name', 'id'],
          needChildCount: 'false',
          childTableName: '',
          childRelationColumn: ''
        };
      } else if (this.sType == 'database') {
        p = {
          tableName: 'dc_database',
          searchConditionList: [
            { fieldName: 'id', columnExp: '=', value: this.sid }
          ],
          columnList: ['name', 'schema_count', 'id'],
          needChildCount: 'false',
          childTableName: '',
          childRelationColumn: ''
        };
      } else if (this.sType == 'serve') {
        p = {
          tableName: 'dc_data_source',
          searchConditionList: [
            { fieldName: 'id', columnExp: '=', value: this.sid }
          ],
          columnList: ['name', 'id'],
          needChildCount: 'false',
          childTableName: '',
          childRelationColumn: ''
        };
      }
      postTreeData(p)
        .then((res) => {
          this.treeData = res?.data || [];
        })
        .catch((err) => {
          this.$message.error(err.msg || '请求失败');
        });
    } else {
      this.$message.warning('请输入配置页面type');
    }
  },

  methods: {
    loadNode(node, resolve) {
      if (node.data.length == 0) return;
      // console.log(node.data,'node.data');
      const { id } = node.data;
      let p = {};
      if (this.sType == 'schema') {
        p = {
          tableName: 'dc_table',
          searchConditionList: [
            { fieldName: 'schema_id', columnExp: '=', value: id }
          ],
          columnList: ['name', 'id']
        };
      } else if (this.sType == 'database') {
        p = {
          tableName: 'dc_table',
          searchConditionList: [
            { fieldName: 'database_id', columnExp: '=', value: id }
          ],
          columnList: ['name', 'id']
        };
      } else if (this.sType == 'serve') {
        p = {
          tableName: 'dc_database',
          searchConditionList: [
            { fieldName: 'source_id', columnExp: '=', value: id }
          ],
          columnList: ['name', 'schema_count', 'id']
        };
      }
      postTreeData(p)
        .then((res) => {
          resolve(res.data);
        })
        .catch((err) => {
          this.$message.error(err.msg || '请求失败');
        });
    }
  }
};
</script>
<style lang="less" scoped>
.tree-container {
  padding: 5px;
  border: 1px solid #cccccc38;
  border-radius: 5px;
}
</style>
