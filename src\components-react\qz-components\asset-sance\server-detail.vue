<template>
  <div class="server-detail">
    <el-row :gutter="8">
      <el-col :span="12">
        <span class="filed">服务名称:</span>
        {{ serverDetail.name || '--' }}
      </el-col>
      <el-col :span="12">
        <span class="filed">所属探测任务:</span>
        {{ serverDetail.server_task_id || '--' }}
      </el-col>
    </el-row>
    <el-row :gutter="8" class="mt10">
      <el-col :span="12">
        <span class="filed">服务地址:</span>
        {{ serverDetail.url || '--' }}
      </el-col>
      <el-col :span="12">
        <span class="filed">数据库数:</span>
        {{ serverDetail.database_count || '--' }}
      </el-col>
    </el-row>
    <el-row :gutter="8" class="mt10">
      <el-col :span="12">
        <span class="filed">连接状态:</span>
        {{ getConnectType(serverDetail.connect_type) }}
      </el-col>
      <el-col :span="12">
        <span class="filed">schema数:</span>
        {{ serverDetail.schema_count || '--' }}
      </el-col>
    </el-row>
    <el-row :gutter="8" class="mt10">
      <el-col :span="12">
        <span class="filed">扫描状态:</span>
        {{ getScanType(serverDetail.scan_type) }}
      </el-col>
      <el-col :span="12">
        <span class="filed">数据表数:</span>
        {{ serverDetail.table_count || '--' }}
      </el-col>
    </el-row>
    <el-row :gutter="8" class="mt10">
      <el-col :span="12">
        <span class="filed">服务类型:</span>
        {{ serverDetail.dc_data_source_type.name || '--' }}
      </el-col>
      <el-col :span="12">
        <span class="filed">数据字段数:</span>
        {{ serverDetail.column_count || '--' }}
      </el-col>
    </el-row>
    <!-- <el-row :gutter="8" class="mt10">
        <el-col :span="12">
            <span class="filed">业务系统:</span>
            {{serverDetail.name||'--'}}
        </el-col>
        <el-col :span="12">
             <span class="filed">数据行数:</span>
            {{serverDetail.name||'--'}}
        </el-col>
    </el-row> -->
    <el-row :gutter="8" class="mt10">
      <el-col :span="12">
        <span class="filed">首次发现时间:</span>
        {{ handerTime(serverDetail.created_at) }}
      </el-col>
      <el-col :span="12">
        <span class="filed">最近更新时间:</span>
        {{ handerTime(serverDetail.updated_at) }}
      </el-col>
    </el-row>
    <el-tabs v-model="activeName">
      <el-tab-pane label="表格展示" name="table">
        <qz-pro-table ref="serverTable" border :data-source="serverTableList">
          <qz-table-column
            prop="name"
            label="库名称"
            min-width="90"
            show-overflow-tooltip
          />
          <!-- <qz-table-column
                    prop="dataBase"
                    label="数据行数"
                    min-width="140"
                    show-overflow-tooltip>
                </qz-table-column> -->
          <qz-table-column
            prop="schema_count"
            label="schema数据量"
            min-width="140"
            show-overflow-tooltip
          >
          </qz-table-column>
          <qz-table-column
            prop="table_count"
            label="表数量"
            min-width="140"
            show-overflow-tooltip
          >
          </qz-table-column>
          <qz-table-column
            prop="column_count"
            label="字段数量"
            min-width="140"
            show-overflow-tooltip
          >
          </qz-table-column>
        </qz-pro-table>
      </el-tab-pane>
      <el-tab-pane label="树状展示" name="tree">
        <qz-pro-virtual-tree
          :data="treeData"
          nodeKey="id"
          :keeps="40"
          :props="defaultProps"
          :height="500"
          lazy
          :load="loadNode"
        />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import {
  postServerDetail,
  postDataBaseList,
  postTreeList
} from '@/service/data-sance-service';
import { handleUtcTime } from '@/utils/string-utils.js';
export default {
  props: ['params'],
  data() {
    return {
      serverDetail: {
        dc_data_source_type: {
          name: ''
        }
      },
      activeName: 'table',
      treeData: [],
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      conType: {
        0: '成功',
        2: '未连接',
        1: '失败'
      },
      scanType: {
        0: '已扫描',
        1: '未扫描'
      }
    };
  },

  components: {},

  mounted() {
    const params = {
      searchConditionList: [
        { fieldName: 'id', columnExp: '=', value: this.params.info.id }
      ],
      columnList: [
        'created_at',
        'name',
        'id',
        'updated_at',
        'url',
        'name',
        'business_system',
        'dc_server_find_task.name',
        'database_count',
        'scan_type',
        'connect_type',
        'schema_count',
        'table_count',
        'column_count',
        'dc_data_source_type.name'
      ]
    };
    postServerDetail(params).then((res) => {
      this.serverDetail = res?.data || {};
    });
    const treeParams = {
      tableName: 'dc_data_source',
      searchConditionList: [
        { fieldName: 'id', columnExp: '=', value: this.params.info.id }
      ],
      columnList: ['name', 'id'],
      needChildCount: 'false',
      childTableName: '',
      childRelationColumn: ''
    };
    postTreeList(treeParams).then((res) => {
      this.treeData = res?.data || [];
    });
  },

  methods: {
    getConnectType(val) {
      return this.conType[val];
    },
    getScanType(val) {
      return this.scanType[val];
    },
    handerTime(time) {
      if (!time) return '--';
      return handleUtcTime(time);
    },
    loadNode(node, resolve) {
      const p = {
        tableName: 'dc_database',
        searchConditionList: [
          { fieldName: 'source_id', columnExp: '=', value: node.data.id }
        ],
        columnList: ['name', 'schema_count', 'id']
      };
      postTreeList(p).then((res) => {
        resolve(res?.data || []);
      });
    },
    serverTableList(pageInfo) {
      const p = {
        ...pageInfo,
        isPageQuery: true,
        searchConditionList: [
          { fieldName: 'source_id', columnExp: '=', value: this.params.info.id }
        ],
        sortList: [],
        columnList: [
          'created_at',
          'name',
          'column_count',
          'table_count',
          'schema_count',
          'id',
          'updated_at',
          'dc_data_source.url',
          'dc_data_source.name',
          'dc_data_source.business_system',
          'dc_data_source.dc_data_source_type.name'
        ]
      };
      return postDataBaseList(p).then((res) => {
        return new Promise((resolve, reject) => {
          resolve({
            data: {
              rows: res?.data?.rows || [],
              totalCount: res?.data?.count || 0
            }
          });
        });
      });
    }
  }
};
</script>
<style lang="less" scoped>
.server-detail {
  padding: 20px;
  .filed {
    color: #606266;
    margin-right: 10px;
  }
}
</style>
