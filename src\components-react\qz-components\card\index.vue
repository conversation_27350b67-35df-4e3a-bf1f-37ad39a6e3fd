<template>
  <div style="width: 100%; height: 100%; padding: 20px">
    <div
      :style="
        cardType == 'normal'
          ? {
              fontSize: '14px',
              color: '#000000',
              fontWeight: 'bold',
              display: 'block',
              marginBottom: '10px'
            }
          : {
              fontSize: '14px',
              color: '#154be5',
              fontWeight: 'bold',
              display: 'block',
              marginBottom: '10px'
            }
      "
    >
      {{ comData || '--' }}
    </div>
    <div class="desc" style="fontsize: 14px; color: #666; display: block">
      {{ desc || '--' }}
    </div>
  </div>
</template>

<script>
export default {
  props: ['props'],
  data() {
    return {
      comData: '',
      desc: '',
      cardType: ''
    };
  },

  components: {},

  mounted() {
    const { sql, comdata, desc, cardType } = this.props;
    this.cardType = cardType || 'normal';
    this.comData = comdata || '';
    this.desc = desc || '';
  },

  methods: {}
};
</script>
<style lang="less" scoped>
// .box-card {
//   width: 100%;
//   height: 100%;
//   min-width: 100px;
//   min-height: 100px;
//   padding: 20px;
//   box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
//   .normal-card {
//     font-size: 14px;
//     color: #000000;
//     font-weight: bold;
//     display: block;
//     margin-bottom: 10px;
//   }
//   .data-card {
//     font-size: 20px;
//     color: #154be5;
//     font-weight: bold;
//     display: block;
//     margin-bottom: 10px;
//   }
//   .desc {
//     font-size: 14px;
//     color: #666;
//     display: block;
//   }
// }
</style>
