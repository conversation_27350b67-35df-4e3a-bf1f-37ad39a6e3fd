import { BasePlugin, getEventControlConfig, getSchemaTpl } from 'amis-editor';
const rendererName = 'qz-data-label-render';
export class DataLabelPlugin extends BasePlugin {
  // 这里要跟对应的渲染器名字对应上
  // 注册渲染器的时候会要求指定渲染器名字
  rendererName = rendererName;
  // 暂时只支持这个，配置后会开启代码编辑器
  $schema = '/schemas/UnknownSchema.json';
  //组件关键字，用来辅助组件列表搜索
  searchKeywords = '';
  // 用来配置名称和描述
  name = '数据标签新增/详情组件';
  title = '';
  description = '数据标签新增/详情组件';

  // tag，决定会在哪个 tab 下面显示的
  tags = ['全知组件'];

  // 图标
  icon = 'fa fa-user';

  // 用来生成预览图的
  previewSchema = {
    type: rendererName
  };

  // 拖入组件里面时的初始数据
  scaffold = {
    type: rendererName,
    jsonschema: ''
  };

  // 事件定义
  events = [
    {
      eventName: 'save',
      eventLabel: '保存成功',
      description: '保存成功时触发'
    },
    {
      eventName: 'cancel',
      eventLabel: '取消',
      description: '取消时触发'
    }
  ];

  // 右侧面板相关
  panelBodyCreator = (context) => {
    return getSchemaTpl('tabs', [
      //   {
      //     title: '属性',
      //     body: [
      //       getSchemaTpl('layout:originPosition', { value: 'left-top' }),
      //       getSchemaTpl('description', {
      //         name: 'jsonschema',
      //         label: 'jsonschema内容',
      //         maxRows: 10,
      //         rows: 10,
      //         placeholder: '请输入jsonschema内容',
      //       }),
      //     ],
      //   },
      {
        title: '事件',
        className: 'p-none',
        body: [
          getSchemaTpl('eventControl', {
            name: 'onEvent',
            ...getEventControlConfig(this.manager, context)
          })
        ]
      }
    ]);
  };
}
