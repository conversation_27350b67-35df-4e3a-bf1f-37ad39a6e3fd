<template>
  <div class="bar-charts">
    <qz-horizontal-bar v-if="type == 'horizontal'" :chartInfo="comdata" />
    <qz-vertical-bar v-else :chartInfo="comdata" />
  </div>
</template>

<script>
export default {
  props: ['props'],
  data() {
    return {
      comdata: {},
      type: 'horizontal'
    };
  },

  components: {},

  mounted() {
    const { chartType, sql, comdata } = this.props;
    this.type = chartType || 'horizontal';
    if (!comdata) {
      const xData = [29, 30, 36, 58, 198];
      const yData = [
        'http://nfc.cmpay.com',
        'http://************',
        'http://ipos.10086.cn',
        'http://nfc.cmpay.com',
        'http://www.dengluruomima003.com/login'
      ];

      if (this.type === 'horizontal') {
        this.comdata = {
          xData: xData,
          yData: yData
        };
      } else {
        this.comdata = {
          xData: yData,
          yData: xData
        };
      }
    } else {
      this.comdata = comdata || {};
    }
  },

  methods: {}
};
</script>
<style lang="less" scoped></style>
