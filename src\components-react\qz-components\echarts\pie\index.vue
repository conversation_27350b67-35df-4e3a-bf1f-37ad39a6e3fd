<template>
  <div class="pie-chart" :id="id"></div>
</template>

<script>
import * as echarts from 'echarts';
import { getUuid } from '@/utils/uuid';
export default {
  props: ['props'],
  data() {
    return {
      pieData: {},
      id: getUuid()
    };
  },

  components: {},

  mounted() {
    const { sql, comdata, title } = this.props;
    this.$nextTick(() => {
      const chartDom = document.getElementById(this.id);
      const myChart = echarts.init(chartDom);
      let dataList = [];
      if (!comdata) {
        dataList = [
          {
            name: '互联网',
            value: 11078
          },
          {
            name: '境外互联网',
            value: 5604
          },
          {
            name: '境内互联网',
            value: 1049
          },
          {
            name: '互联网-境内',
            value: 523
          },
          {
            name: '局域网-生产网',
            value: 1049
          },
          {
            name: '局域网-测试网',
            value: 523
          }
        ];
      } else {
        dataList = comdata;
      }
      const option = {
        legend: {
          show: false
        },
        toolbox: {
          show: false
        },
        series: [
          {
            type: 'pie',
            radius: [50, 100],
            center: ['50%', '50%'],
            itemStyle: {
              borderRadius: 8
            },
            data: dataList
          }
        ]
      };
      option && myChart.setOption(option);
    });
  },

  methods: {}
};
</script>
<style lang="less" scoped>
.pie-chart {
  width: 100%;
  height: 100%;
  min-width: 300px;
  min-height: 300px;
}
</style>
