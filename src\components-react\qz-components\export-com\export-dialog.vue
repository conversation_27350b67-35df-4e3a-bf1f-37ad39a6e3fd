<template>
  <div class="export-dialog">
    <el-dialog title="提示" :visible.sync="isShowError" width="600px">
      <i class="el-icon-warning-outline mr5 warn-icon"></i>
      <span
        >部分数据导入失败，请点击前往<a
          href="/system/config?code=config_export"
          target="_blank"
          >数据导出管理</a
        >下载查看导出失败内容</span
      >
    </el-dialog>
  </div>
</template>

<script>
import {
  uploadFileTask,
  postTaskStatus
} from '@/service/upload-update-service';
import { mapState } from 'vuex';
export default {
  data() {
    return {
      exportTimer: null,
      isShowError: false
    };
  },

  components: {},
  computed: {
    ...mapState(['fileInfo'])
  },
  watch: {
    fileInfo: {
      handler(val) {
        if (!val) return;
        this.handleSuccess();
      },
      deep: true
    }
  },

  mounted() {},

  methods: {
    handleSuccess() {
      const params = {
        parameter: {},
        executionType: 'IMMEDIATE',
        executorType: this.fileInfo.executorType,
        name: this.fileInfo.exportName
      };
      params.parameter.importType = this.fileInfo.importType;
      params.parameter.file = {
        path: this.fileInfo.path,
        bucket: this.fileInfo.bucket
      };
      uploadFileTask(params).then((res) => {
        const id = res.data.id;
        const columnList = ['id', 'state', 'data'];
        const searchConditionList = [
          {
            fieldName: 'id',
            columnExp: '=',
            value: id
          }
        ];
        const p = {};
        p.columnList = columnList;
        p.searchConditionList = searchConditionList;
        this.getTaskStatus(p);
        this.exportTimer = setInterval(() => {
          this.getTaskStatus(p);
        }, 1500);
      });
    },
    getTaskStatus(params) {
      postTaskStatus(params)
        .then((res) => {
          //4失败，5成功
          if (res.data.state == 4 || res.data.state == 5) {
            this.clearTimer();
            this.removeExportMessage();
            if (res.data.state == 4) {
              this.isShowError = true;
            } else {
              if (res.data.data.includes('link')) {
                this.isShowError = true;
              } else {
                this.$message.success('导入成功');
              }
            }
          }
        })
        .catch((err) => {
          this.$message.error(err.msg || '导入失败');
        });
    },
    removeExportMessage() {
      const messDom = document.querySelector('.export-message');
      if (messDom) {
        document.body.removeChild(messDom);
      }
    },
    clearTimer() {
      if (this.exportTimer) {
        clearInterval(this.exportTimer);
        this.exportTimer = null;
      }
    }
  }
};
</script>
<style lang="less" scoped>
.export-dialog {
  /deep/ .el-dialog__header {
    padding: 10px;
  }
  /deep/ .el-dialog__body {
    padding: 20px;
    background: #f0f8ff;
    i {
      color: #d11212;
    }
    a {
      cursor: pointer;
      color: #409eff;
      font-size: 12px;
    }
  }
}
</style>
