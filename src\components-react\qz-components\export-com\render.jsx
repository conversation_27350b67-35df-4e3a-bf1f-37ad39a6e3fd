import { Render<PERSON> } from 'amis';
import React from 'react';
import VueProxy from '@/components-react/vue-proxy';
import ExportCom from './index.vue';
Renderer({
  type: 'qz-export-file-render',
  autoVar: true
})((props) => {
  const { dispatchEvent } = props;
  return (
    <VueProxy
      component={ExportCom}
      props={{ props }}
      listeners={{
        save(e) {
          dispatchEvent(
            'save',
            e,
            // props一定要传，否则事件会丢失
            { props }
          );
        },
        cancel(e) {
          dispatchEvent('cancel', e, { props });
        }
      }}
    ></VueProxy>
  );
});
