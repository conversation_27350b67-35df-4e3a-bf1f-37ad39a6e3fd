<template>
  <div>
    <div>基本信息</div>
    <el-divider></el-divider>
    <el-form ref="baseForm" :model="formData" v-bind="formProps">
      <el-form-item
        :rules="[
          { required: true, message: '请输入服务名称', trigger: 'blur' },
          {
            min: 2,
            max: 100,
            message: '长度在 2 到 100 个字符之间',
            trigger: 'blur'
          }
        ]"
        prop="name"
        label="服务名称"
      >
        <el-input
          v-model="formData.name"
          placeholder="请输入服务名称"
        ></el-input>
      </el-form-item>
      <el-form-item label="业务系统" prop="businessSystem">
        <el-input
          v-model="formData.businessSystem"
          placeholder="请输入业务系统"
        ></el-input>
      </el-form-item>
      <el-form-item
        label="服务类型"
        prop="type"
        :rules="[
          { required: true, message: '请选择服务类型', trigger: 'blur' }
        ]"
      >
        <el-select
          ref="serviceTypeRef"
          v-model="formData.type"
          placeholder="请选择服务类型"
          filterable
          allow-create
          default-first-option
          class="full-width"
          @change="getDriverVersionList()"
        >
          <el-option
            v-for="type in databaseTypeList"
            :key="type.id"
            :label="type.name"
            :value="type.id"
          ></el-option>
          <div
            class="action-link pl20 option-add"
            @click="showServiceTypeDialog"
          >
            <i class="el-icon-plus mr5"></i>
            新增服务类型
          </div>
        </el-select>
      </el-form-item>
      <el-form-item
        label="驱动版本"
        prop="driver"
        :rules="[
          { required: true, message: '请选择驱动版本号', trigger: 'blur' }
        ]"
      >
        <el-select
          ref="serviceVersionRef"
          v-model="formData.driver"
          placeholder="请选择驱动版本号"
          filterable
          allow-create
          default-first-option
          class="full-width"
        >
          <el-option
            v-for="type in driverVersionList"
            :key="type.id"
            :label="type.name"
            :value="type.id"
          ></el-option>
          <div
            class="action-link pl20 option-add"
            @click="showServiceVersionDialog"
          >
            <i class="el-icon-plus mr5"></i>
            新增驱动版本
          </div>
        </el-select>
      </el-form-item>

      <div v-loading="schemaLoading">
        <div v-if="!noSchema">
          <div>连接认证</div>
          <el-divider></el-divider>
          <VueForm
            ref="formRender"
            v-model="formData.schema"
            :ui-schema="uiSchema"
            :schema="formSchema"
            :form-props="formProps"
            :form-footer="{ show: false }"
          ></VueForm>
        </div>
        <el-form-item prop="url" label="服务地址">
          <el-input
            v-model="formData.url"
            placeholder="请输入服务地址"
          ></el-input>
        </el-form-item>
        <el-empty v-if="noSchema" description="请先选择服务类型"></el-empty>
      </div>
    </el-form>
    <div class="flex-row space-between">
      <div>
        <el-button
          v-if="noSchema == false"
          :loading="testLoading"
          size="small"
          @click="handleTest"
        >
          测试
        </el-button>
      </div>
      <div>
        <el-button size="small" @click="handleCancel">取消</el-button>
        <el-button
          :loading="saveLoading"
          size="small"
          type="primary"
          @click="handleSave"
        >
          保存
        </el-button>
      </div>
    </div>
  </div>
</template>

<script>
import VueForm from '@lljj/vue-json-schema-form';
import { cloneDeep, debounce, isEqual } from 'lodash-es';
import {
  getDcDataType,
  getDcDriverList,
  getUiConfig,
  doDatasourceTest,
  saveDatasource,
  getJdbcUrlRender,
  getJdbcUrlParse,
  getDatasourceDetail
} from '@/service/datasource-service';
import { hasOwnProperty } from '@/components/sample/method';

function getDefaultFormProps() {
  return {
    labelPosition: 'left',
    labelWidth: '132px',
    labelSuffix: '：',
    size: 'small'
  };
}

export default {
  props: {
    demoSchema: {
      type: Object,
      default: () => ({})
    },
    datasourceId: {
      type: [String, Number],
      default: ''
    }
  },
  components: {
    VueForm
  },
  data() {
    return {
      databaseTypeList: [],
      driverVersionList: [],
      databaseDriverVersionList: [],
      jdbcUrlTemplateMap: {},
      formSchema: this.demoSchema,
      uiSchema: {},
      formProps: getDefaultFormProps(),
      formData: {
        name: '',
        businessSystem: '',
        type: '',
        driver: '',
        url: '',
        schema: {}
      },
      schemaLoading: false,
      testLoading: false,
      saveLoading: false
    };
  },
  watch: {
    'formData.url': {
      handler: debounce(function (newVal) {
        this.getParseConfig(newVal, '');
      }, 500)
    },
    'formData.schema': {
      handler: debounce(function (newVal, oldVal) {
        const template =
          this.jdbcUrlTemplateMap[this.formData.type]?.[0]?.value;
        let validate = true;
        if (!this.$refs?.formRender?.$$uiFormRef) return;
        this.$refs.formRender.$$uiFormRef.validate((valid) => {
          if (!valid) {
            validate = false;
          }
        });
        if (!template || !validate) return;
        getJdbcUrlRender({
          jdbcTemplate: template,
          params: newVal.datasourceConfig
        }).then((res) => {
          if (this.formData.url === res.data) return;
          this.formData.url = res.data;
        });
      }, 500),
      deep: true,
      immediate: true
    }
  },
  computed: {
    noSchema() {
      return Object.keys(this.formSchema).length === 0;
    }
  },
  async created() {
    await this.getDbTypeList();
    // 详情模式
    if (this.datasourceId) {
      getDatasourceDetail(this.datasourceId).then((res) => {
        const data = res.data || {};
        this.formData.name = data.name || '';
        this.formData.businessSystem = data.business_system || '';
        this.formData.type = data.source_type_id || '';
        this.formData.driver = data.driver_id || '';
        this.formData.url = data.url || '';
        // 后端返回的schema是字符串，前端需要转换成对象)
        if (data.config == '{}') {
          this.getParseConfig(this.formData.url, 'detail');
        } else {
          this.formData.schema.datasourceConfig = JSON.parse(
            data.config || '{}'
          );
        }
        this.getDriverVersionList({ hasDetail: true });
      });
    }
  },
  methods: {
    getParseConfig(val, type) {
      const template = this.jdbcUrlTemplateMap[this.formData.type]?.[0]?.value;
      if (!template) return;
      getJdbcUrlParse({
        jdbcTemplate: template,
        jdbcUrl: val
      }).then((res) => {
        const config = res.data || {};
        if (isEqual(this.formData.schema.datasourceConfig, config)) return;
        for (const key in this.formData.schema.datasourceConfig) {
          if (hasOwnProperty(config, key)) {
            this.formData.schema.datasourceConfig[key] = cloneDeep(config[key]);
          }
        }
        if (type === 'detail') {
          this.formData.schema.datasourceConfig = {
            ...this.formData.schema.datasourceConfig,
            ...config
          };
        }
      });
    },
    async getDbTypeList() {
      const resData = await getDcDataType();
      this.databaseTypeList = resData.data?.rows;
      this.databaseTypeList.forEach((item) => {
        this.jdbcUrlTemplateMap[item.id] = JSON.parse(item.templates);
      });
    },
    convertParamsToBe() {
      const params = {
        name: this.formData.name,
        driver: { id: this.formData.driver },
        businessSystem: this.formData.businessSystem,
        type: { id: this.formData.type },
        url: this.formData.url,
        properties: { ...this.formData.schema.datasourceConfig }
      };
      if (this.datasourceId) {
        params.id = this.datasourceId;
      }
      return params;
    },
    async getDriverVersionList(config = {}) {
      console.log('config', config);
      this.driverVersionList = [];
      if (!config.hasDetail) {
        this.formData.driver = '';
      }

      this.formSchema = {};
      this.uiSchema = {};
      this.formProps = getDefaultFormProps();

      this.schemaLoading = true;
      try {
        const driverResData = await getDcDriverList(this.formData.type);
        this.driverVersionList = driverResData.data?.rows || [];

        const uiConfigResData = await getUiConfig(this.formData.type);
        const uiConfig = JSON.parse(uiConfigResData?.data?.ui_config || '{}');
        if (uiConfig) {
          if (!config.hasDetail) {
            this.formData.schema = {};
          }
          this.uiSchema = uiConfig.uiSchema || {};
          this.formSchema = uiConfig.schema || {};
          this.formProps = uiConfig.formProps || getDefaultFormProps();
          this.formProps.size = 'small';
        }
      } catch (e) {
        console.error(e);
      } finally {
        this.schemaLoading = false;
      }
    },
    handleTest() {
      this.testLoading = true;

      // 执行测试逻辑
      const params = this.convertParamsToBe();
      doDatasourceTest(params)
        .then(
          (res) => {
            this.$message.success('连接成功');
          },
          (err) => {
            console.error(err);
            this.$message.error(err.msg || '连接失败');
          }
        )
        .finally(() => {
          this.testLoading = false;
        });
    },
    handleCancel() {
      this.$emit('cancel');
    },
    handleSave() {
      this.$refs.baseForm.validate((valid) => {
        if (!valid) return;

        this.$refs.formRender.$$uiFormRef.validate((valid) => {
          if (!valid) return;
          this.saveLoading = true;

          // 执行保存逻辑
          const params = this.convertParamsToBe();
          saveDatasource(params)
            .then(
              (res) => {
                this.$message.success('保存成功');
                this.$emit('save');
              },
              (err) => {
                console.error(err);
                this.$message.error(err.msg || '保存失败');
              }
            )
            .finally(() => {
              this.saveLoading = false;
            });
        });
      });
    },
    showServiceTypeDialog() {
      this.$refs.serviceTypeRef.blur();
      this.$dialogAlert({
        params: {
          callback: () => {
            this.getDbTypeList();
          }
        },
        component: () => import('./packages/service-type-dialog.vue'),
        alertWidth: '500px',
        alertHeight: 'auto',
        alertTitle: '新增服务类型',
        alertStyle: {
          zIndex: 9999
        }
      });
    },
    showServiceVersionDialog() {
      if (!this.formData.type) {
        this.$message.warning('请先选择服务类型');
        return;
      }

      const serviceType = this.formData.type;
      const serviceName = this.databaseTypeList.find(
        (item) => item.id === this.formData.type
      ).name;

      this.$refs.serviceVersionRef.blur();
      this.$dialogAlert({
        params: {
          serviceType,
          serviceName,
          callback: () => {
            this.getDriverVersionList({ hasDetail: true });
          }
        },
        component: () => import('./packages/service-version-dialog.vue'),
        alertWidth: '500px',
        alertHeight: 'auto',
        alertTitle: '新增驱动版本',
        alertStyle: {
          zIndex: 9999
        }
      });
    }
  }
};
</script>

<style lang="css" scoped>
.el-divider {
  margin: 10px 0;
}
.option-add {
  height: 34px;
  line-height: 34px;
}
</style>
