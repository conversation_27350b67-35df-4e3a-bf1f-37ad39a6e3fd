<template>
  <el-form
    ref="form"
    :model="customInfo"
    class="service-type-dialog"
    label-width="120px"
    size="small"
  >
    <el-form-item label="新增方式" prop="type">
      <el-radio-group v-model="customInfo.type">
        <el-radio-button label="QZ_PLUGIN">插件</el-radio-button>
        <el-radio-button label="DATABASE_DRIVER">自定义</el-radio-button>
      </el-radio-group>
    </el-form-item>
    <template v-if="customInfo.type === 'DATABASE_DRIVER'">
      <el-form-item
        label="数据类型名称"
        prop="name"
        :rules="[
          {
            required: true,
            message: '请输入数据类型名称',
            trigger: 'blur'
          }
        ]"
      >
        <el-input
          v-model="customInfo.name"
          placeholder="请输入数据类型名称"
        ></el-input>
      </el-form-item>
      <el-form-item
        label="版本名称"
        prop="driverName"
        :rules="[
          { required: true, message: '请输入版本名称', trigger: 'blur' }
        ]"
      >
        <el-input
          v-model="customInfo.driverName"
          placeholder="请输入版本名称"
        ></el-input>
      </el-form-item>
      <el-form-item
        :rules="[{ required: true, message: '请选择方言', trigger: 'blur' }]"
        label="方言"
        prop="dialect"
      >
        <el-select
          v-model="customInfo.dialect"
          placeholder="请选择"
          class="full-width"
          popper-class="top-level"
          filterable
        >
          <el-option
            v-for="item in dialectList"
            :key="item"
            :label="item"
            :value="item"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        :rules="[{ required: true, message: '请输入驱动类', trigger: 'blur' }]"
        label="驱动类"
        prop="driverClass"
      >
        <el-input
          v-model="customInfo.driverClass"
          :rules="[
            { required: true, message: '请输入驱动类', trigger: 'blur' }
          ]"
          placeholder="请输入驱动类"
        ></el-input>
      </el-form-item>
    </template>
    <el-form-item
      :rules="[
        {
          required: true,
          message:
            customInfo.type === 'QZ_PLUGIN' ? '请上传插件包' : '请上传驱动文件',
          trigger: 'blur'
        }
      ]"
      :label="customInfo.type === 'QZ_PLUGIN' ? '插件包' : '驱动文件'"
      prop="driverFileUri"
    >
      <el-upload
        :action="DATA_URL_FILE_UPLOAD"
        :on-success="handleFileUploadSuccess"
        :on-error="handleFileUploadError"
        :on-remove="handleFileRemove"
        :limit="1"
        accept=".zip,.jar"
        class="file-uploader"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip" slot="tip">只能上传zip/jar文件</div>
      </el-upload>
    </el-form-item>
    <div class="dialog-footer">
      <el-button @click="cancel" size="small">取消</el-button>
      <el-button
        :loading="saveLoading"
        type="primary"
        size="small"
        @click="confirm"
      >
        确定
      </el-button>
    </div>
  </el-form>
</template>

<script>
import { saveServiceType, getDialectList } from '@/service/datasource-service';
import { DATA_URL_FILE_UPLOAD } from '@/constant/data-url-constants';

export default {
  props: ['params'],
  data() {
    return {
      DATA_URL_FILE_UPLOAD,
      dialectList: [],
      customInfo: {
        type: 'QZ_PLUGIN',
        name: '',
        driverName: '',
        dialect: '',
        driverClass: '',
        driverFileUri: ''
      },
      saveLoading: false
    };
  },
  mounted() {
    // 获取方言列表
    getDialectList().then((res) => {
      this.dialectList = (res.data?.rows || [])
        .map((item) => item.dialect || '')
        .filter((item) => !!item);
    });
  },
  methods: {
    handleFileUploadSuccess(res) {
      const uri = res.data?.uri || '';
      this.customInfo.driverFileUri = uri;
    },
    handleFileUploadError(err) {
      this.$message.error(err.msg || '文件上传失败');
      this.customInfo.driverFileUri = '';
    },
    handleFileRemove() {
      this.customInfo.driverFileUri = '';
    },
    cancel() {
      this.params.close();
    },
    confirm() {
      this.$refs.form.validate((valid) => {
        if (!valid) return;
        this.saveLoading = true;

        let params = {};
        if (this.customInfo.type === 'QZ_PLUGIN') {
          params = {
            type: this.customInfo.type,
            driverFiles: [this.customInfo.driverFileUri]
          };
        } else {
          params = {
            type: this.customInfo.type,
            name: this.customInfo.name,
            driverName: this.customInfo.driverName,
            dialect: this.customInfo.dialect,
            driverClass: this.customInfo.driverClass,
            driverFiles: [this.customInfo.driverFileUri],
            bucket: 'plugin'
          };
        }
        saveServiceType(params)
          .then(() => {
            this.$message.success('新增成功');
            this.params.callback && this.params.callback();
            this.params.close();
          })
          .catch((err) => {
            this.$message.error(err.msg || '新增失败');
          })
          .finally(() => {
            this.saveLoading = false;
          });
      });
    }
  }
};
</script>

<style lang="less">
.service-type-dialog {
  padding: 10px 20px;

  .file-uploader .el-upload,
  .file-uploader .el-upload .el-upload-dragger {
    width: 100%;
  }

  .dialog-footer {
    margin-top: 20px;
    text-align: right;
  }
}
</style>
