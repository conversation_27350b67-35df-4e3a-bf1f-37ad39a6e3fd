<template>
  <el-form
    ref="form"
    :model="formInfo"
    class="service-version-dialog"
    label-width="120px"
    size="small"
  >
    <el-form-item label="服务类型">
      {{ params.serviceName }}
    </el-form-item>
    <el-form-item
      label="版本名称"
      prop="name"
      :rules="[{ required: true, message: '请输入版本名称', trigger: 'blur' }]"
    >
      <el-input v-model="formInfo.name" placeholder="请输入版本名称"></el-input>
    </el-form-item>
    <el-form-item
      :rules="[{ required: true, message: '请选择方言', trigger: 'blur' }]"
      label="方言"
      prop="dialect"
    >
      <el-select
        v-model="formInfo.dialect"
        placeholder="请选择"
        class="full-width"
        popper-class="top-level"
        filterable
      >
        <el-option
          v-for="item in dialectList"
          :key="item"
          :label="item"
          :value="item"
        >
        </el-option>
      </el-select>
    </el-form-item>
    <el-form-item
      :rules="[{ required: true, message: '请输入驱动类', trigger: 'blur' }]"
      label="驱动类"
      prop="driverClass"
    >
      <el-input
        v-model="formInfo.driverClass"
        :rules="[{ required: true, message: '请输入驱动类', trigger: 'blur' }]"
        placeholder="请输入驱动类"
      ></el-input>
    </el-form-item>
    <el-form-item
      :rules="[
        {
          required: true,
          message: '请上传驱动文件',
          trigger: 'blur'
        }
      ]"
      label="驱动文件"
      prop="driverFileUri"
    >
      <el-upload
        :action="DATA_URL_FILE_UPLOAD"
        :on-success="handleFileUploadSuccess"
        :on-error="handleFileUploadError"
        :on-remove="handleFileRemove"
        :limit="1"
        accept=".zip,.jar"
        class="file-uploader"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip" slot="tip">只能上传zip/jar文件</div>
      </el-upload>
    </el-form-item>
    <div class="dialog-footer">
      <el-button @click="cancel" size="small">取消</el-button>
      <el-button
        :loading="saveLoading"
        type="primary"
        size="small"
        @click="confirm"
      >
        确定
      </el-button>
    </div>
  </el-form>
</template>

<script>
import {
  saveServiceVersion,
  getDialectList
} from '@/service/datasource-service';
import { DATA_URL_FILE_UPLOAD } from '@/constant/data-url-constants';

export default {
  props: ['params'],
  data() {
    return {
      DATA_URL_FILE_UPLOAD,
      dialectList: [],
      formInfo: {
        dataSourceType: { id: this.params.serviceType },
        name: '',
        dialect: '',
        driverClass: '',
        driverFileUri: ''
      },
      saveLoading: false
    };
  },
  mounted() {
    // 获取方言列表
    getDialectList().then((res) => {
      this.dialectList = (res.data?.rows || [])
        .map((item) => item.dialect || '')
        .filter((item) => !!item);
    });
  },
  methods: {
    handleFileUploadSuccess(res) {
      const uri = res.data?.uri || '';
      this.formInfo.driverFileUri = uri;
    },
    handleFileUploadError(err) {
      this.$message.error(err.msg || '文件上传失败');
      this.formInfo.driverFileUri = '';
    },
    handleFileRemove() {
      this.formInfo.driverFileUri = '';
    },
    cancel() {
      this.params.close();
    },
    confirm() {
      this.$refs.form.validate((valid) => {
        if (!valid) return;
        this.saveLoading = true;

        const params = {
          dataSourceType: { id: this.params.serviceType },
          name: this.formInfo.name,
          dialect: this.formInfo.dialect,
          driverClass: this.formInfo.driverClass,
          driverFiles: [this.formInfo.driverFileUri],
          bucket: 'plugin'
        };
        saveServiceVersion(params)
          .then(() => {
            this.$message.success('新增成功');
            this.params.callback && this.params.callback();
            this.params.close();
          })
          .catch((err) => {
            this.$message.error(err.msg || '新增失败');
          })
          .finally(() => {
            this.saveLoading = false;
          });
      });
    }
  }
};
</script>

<style lang="less">
.service-version-dialog {
  padding: 10px 20px;

  .file-uploader .el-upload,
  .file-uploader .el-upload .el-upload-dragger {
    width: 100%;
  }

  .dialog-footer {
    margin-top: 20px;
    text-align: right;
  }
}
</style>
