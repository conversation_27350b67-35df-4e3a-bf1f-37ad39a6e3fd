import { Ren<PERSON><PERSON> } from 'amis';
import React from 'react';
import VueProxy from '@/components-react/vue-proxy';
import VueSchema from './index.vue';

Renderer({
  type: 'qz-json-schema-render',
  autoVar: true
})((props) => {
  const { dispatchEvent } = props;
  let jsonSchema = {};
  try {
    jsonSchema = JSON.parse(props.jsonschema || '{}');
  } catch (e) {
    console.error('jsonschema解析失败', e);
  }
  return (
    <VueProxy
      component={VueSchema}
      props={{ demoSchema: jsonSchema, datasourceId: props.datasourceId }}
      listeners={{
        save(e) {
          dispatchEvent(
            'save',
            e,
            // props一定要传，否则事件会丢失
            { props }
          );
        },
        cancel(e) {
          dispatchEvent('cancel', e, { props });
        }
      }}
    ></VueProxy>
  );
});
