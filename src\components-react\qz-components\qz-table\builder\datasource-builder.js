import { registerDSBuilder } from 'amis-editor';
import { ApiDSBuilder } from 'amis-editor/lib/builder/ApiDSBuilder';

class QzTableDatasourceBuilder extends ApiDSBuilder {
  async buildCRUDSchema(options) {
    const original = await super.buildCRUDSchema(options);
    original.syncLocation = false;
    return original;
  }

  async buildCRUDFilterSchema(options, componentId) {
    const original = await super.buildCRUDFilterSchema(options, componentId);
    // 表格筛选的表单默认不需要标题
    original.title = '';
    return original;
  }
}

registerDSBuilder(QzTableDatasourceBuilder);
