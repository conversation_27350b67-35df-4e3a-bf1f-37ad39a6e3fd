import {
  ColumnToggler,
  getSchemaTpl,
  registerEditorPlugin,
  unRegisterEditorPlugin
} from 'amis-editor';
import { update } from 'lodash-es';

function getClosestParentByType(node, type) {
  while ((node = node?.parent)) {
    if (node.type === type) {
      return node;
    }
    if (node.id === 'root') {
      return;
    }
  }
}

class ColumnTogglerPlugin extends ColumnToggler {
  panelBodyCreator = (context) => {
    const crud = getClosestParentByType(context?.node, 'crud2');

    if (crud) {
      this.crudInfo = {
        id: crud.id,
        columns: crud.schema.columns || [],
        schema: crud.schema
      };
    }

    const columns = (this.crudInfo?.schema?.columns ?? []).map(
      (item, index) => ({
        label: item.title,
        value: index
      })
    );

    return getSchemaTpl('tabs', [
      {
        title: '属性',
        body: getSchemaTpl('collapseGroup', [
          {
            title: '基本',
            body: [
              {
                label: '按钮文字',
                type: 'input-text',
                name: 'label'
              },
              {
                label: '按钮提示',
                type: 'input-text',
                name: 'tooltip'
              },
              getSchemaTpl('switch', {
                name: 'defaultIsOpened',
                label: '是否默认展开'
              }),
              getSchemaTpl('switch', {
                name: 'draggable',
                label: '是否可排序',
                value: true
              }),
              getSchemaTpl('icon', {
                label: '按钮图标'
              }),
              {
                type: 'button-group-select',
                label: '下拉菜单对齐',
                size: 'xs',
                name: 'align',
                value: 'right',
                options: [
                  {
                    label: '左侧',
                    value: 'left'
                  },
                  {
                    label: '右侧',
                    value: 'right'
                  }
                ]
              }
            ]
          },
          {
            title: '列默认显示',
            body: [
              {
                name: `__toggled`,
                value: '',
                type: 'checkboxes',
                // className: 'b-a p-sm',
                label: false,
                inline: false,
                joinValues: false,
                extractValue: true,
                options: columns,
                // style: {
                //   maxHeight: '200px',
                //   overflow: 'auto'
                // },
                pipeIn: () => {
                  const showColumnIndex = [];
                  this.crudInfo?.schema?.columns?.forEach((item, index) => {
                    if (item.toggled !== false) {
                      showColumnIndex.push(index);
                    }
                  });

                  return showColumnIndex;
                },
                onChange: (value) => {
                  if (!this.crudInfo) {
                    return;
                  }

                  let newColumns = this.crudInfo.schema.columns;

                  newColumns = newColumns.map((item, index) => ({
                    ...item,
                    toggled: value.includes(index) ? undefined : false
                  }));

                  const updatedSchema = update(
                    this.crudInfo.schema,
                    'columns',
                    () => {
                      return newColumns;
                    }
                  );

                  this.manager.store.changeValueById(
                    this.crudInfo.id,
                    updatedSchema
                  );
                  this.crudInfo.schema = updatedSchema;
                }
              }
            ]
          }
        ])
      },
      {
        title: '外观',
        body: getSchemaTpl('collapseGroup', [
          {
            title: '基本',
            body: [
              getSchemaTpl('size', {
                label: '按钮尺寸'
              })
            ]
          },
          {
            title: 'CSS 类名',
            body: [
              getSchemaTpl('className', {
                name: 'className',
                label: '显示列样式'
              }),

              getSchemaTpl('className', {
                name: 'btnClassName',
                label: '按钮样式'
              })
            ]
          }
        ])
      }
    ]);
  };
}

unRegisterEditorPlugin(ColumnToggler.id);
registerEditorPlugin(ColumnTogglerPlugin);
