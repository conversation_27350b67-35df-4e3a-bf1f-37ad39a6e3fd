import { BasePlugin, getSchemaTpl, registerEditorPlugin } from 'amis-editor';

class FilterFormPlugin extends BasePlugin {
  static id = 'FilterFormPlugin';
  rendererName = 'crud-filter-form';
  disabledRendererPlugin = true;
  name = '筛选表单';
  panelTitle = '筛选表单';
  $schema = '/schemas/UnknownSchema.json';
  panelJustify = true;

  regions = [
    {
      label: '操作区',
      key: 'actions',
      preferTag: '按钮',
      renderMethod: 'renderActions'
    }
  ];

  panelBodyCreator = (context) => {
    return getSchemaTpl('tabs', [
      {
        title: '属性',
        body: getSchemaTpl('collapseGroup', [
          {
            title: '基本配置',
            body: [
              {
                label: '标题宽度',
                type: 'input-text',
                name: 'labelWidth',
                value: '110px',
                suffix: 'px',
                pipeIn: (value) => {
                  return value ? parseInt(value, 10) : '';
                },
                pipeOut: (value) => {
                  return value ? `${value}px` : '';
                }
              }
            ]
          },
          {
            title: '筛选项配置',
            body: [
              {
                type: 'crud-filter-form-control',
                name: 'body',
                label: '筛选项',
                nodeInfo: context.node
              }
            ]
          }
        ])
      }
    ]);
  };
}

registerEditorPlugin(FilterFormPlugin);
