import {
  SelectControlPlugin,
  ValidatorTag,
  getEventControlConfig,
  getSchemaTpl,
  registerEditorPlugin,
  tipedLabel,
  undefinedPipeOut
} from 'amis-editor';
import { inputStateTpl } from 'amis-editor/lib/renderer/style-control/helper';

class FilterFormSelectPlugin extends SelectControlPlugin {
  static id = 'FilterFormSelectPlugin';
  rendererName = 'filter-form-select';

  panelBodyCreator = (context) => {
    return getSchemaTpl('tabs', [
      {
        title: '属性',
        body: getSchemaTpl('collapseGroup', [
          {
            title: '基本',
            id: 'properties-basic',
            body: [
              getSchemaTpl('layout:originPosition', { value: 'left-top' }),
              getSchemaTpl('formItemName', {
                required: true
              }),
              getSchemaTpl('label'),
              getSchemaTpl('clearable'),
              getSchemaTpl('searchable'),
              getSchemaTpl('multiple', {
                body: [
                  getSchemaTpl('switch', {
                    label: '单行显示选中值',
                    name: 'valuesNoWrap'
                  }),
                  {
                    type: 'input-number',
                    name: 'maxTagCount',
                    label: tipedLabel(
                      '标签展示数',
                      '标签的最大展示数量，超出数量后以收纳浮层的方式展示，默认全展示'
                    )
                  }
                ]
              }),
              getSchemaTpl('checkAll'),
              getSchemaTpl('labelRemark'),
              getSchemaTpl('remark'),
              getSchemaTpl('placeholder'),
              getSchemaTpl('description')
            ]
          },
          {
            title: '选项',
            id: 'properties-options',
            body: [
              getSchemaTpl('optionControlV2'),
              getSchemaTpl('selectFirst', {
                onChange: (value, oldValue, model, form) => {
                  if (value) {
                    form.deleteValueByName('value');
                  }
                }
              }),
              getSchemaTpl('valueFormula', {
                rendererSchema: (schema) => ({
                  ...schema,
                  type: 'input-text'
                }),
                pipeOut: undefinedPipeOut,
                // 默认值组件设计有些问题，自动发起了请求，接口数据作为了默认值选项，接口形式应该是设置静态值或者FX
                needDeleteProps: ['source'],
                // 当数据源是自定义静态选项时，不额外配置默认值，在选项上直接勾选即可，放开会有个bug：当去掉勾选时，默认值配置组件不清空，只是schema清空了value
                visibleOn: 'this.selectFirst !== true && this.source != null'
              }),
              getSchemaTpl(
                'loadingConfig',
                {
                  visibleOn: 'this.source || !this.options'
                },
                { context }
              ),
              // 模板
              getSchemaTpl('optionsMenuTpl', {
                manager: this.manager,
                onChange: (value) => {}
              }),
              /** 新增选项 */
              getSchemaTpl('optionAddControl', {
                manager: this.manager
              }),
              /** 编辑选项 */
              getSchemaTpl('optionEditControl', {
                manager: this.manager
              }),
              /** 删除选项 */
              getSchemaTpl('optionDeleteControl')
            ]
          },
          {
            title: '高级',
            body: [
              getSchemaTpl('switch', {
                label: tipedLabel(
                  '选项值检查',
                  '开启后，当选项值未匹配到当前options中的选项时，选项文本飘红'
                ),
                name: 'showInvalidMatch'
              }),
              getSchemaTpl('virtualThreshold'),
              getSchemaTpl('virtualItemHeight')
            ]
          },
          {
            title: '操作符',
            body: [
              getSchemaTpl('switch', {
                label: tipedLabel(
                  '显示操作符',
                  '只有开启可多选时，操作符才会显示'
                ),
                name: 'showOperator'
              }),
              {
                type: 'crud-operator-control',
                visibleOn: 'this.showOperator === true',
                name: 'operators'
              }
            ]
          },
          getSchemaTpl('status', { isFormItem: true }),
          getSchemaTpl('validation', { tag: ValidatorTag.MultiSelect })
        ])
      },
      {
        title: '外观',
        body: [
          getSchemaTpl('collapseGroup', [
            getSchemaTpl('theme:formItem'),
            getSchemaTpl('theme:form-label'),
            getSchemaTpl('theme:form-description'),
            {
              title: '选择框样式',
              body: [
                ...inputStateTpl(
                  'themeCss.selectControlClassName',
                  '--select-base'
                )
              ]
            },
            {
              title: '下拉框样式',
              body: [
                ...inputStateTpl(
                  'themeCss.selectPopoverClassName',
                  '--select-base-${state}-option',
                  {
                    state: [
                      { label: '常规', value: 'default' },
                      { label: '悬浮', value: 'hover' },
                      { label: '选中', value: 'focused' }
                    ]
                  }
                )
              ]
            },
            getSchemaTpl('theme:cssCode'),
            getSchemaTpl('style:classNames')
          ])
        ]
      },
      {
        title: '事件',
        className: 'p-none',
        body: [
          getSchemaTpl('eventControl', {
            name: 'onEvent',
            ...getEventControlConfig(this.manager, context)
          })
        ]
      }
    ]);
  };
}

registerEditorPlugin(FilterFormSelectPlugin);
