// Only load these plugins in development environment
if (process.env.NODE_ENV === 'development') {
  require('./table');
  require('./column-toggler');
  require('./view');
  require('./view-saver');
  require('./filter-form');
  require('./form-items/select');
  require('./form-items/radios');
  require('./form-items/checkboxes');
  require('./form-items/cascader');
  require('./form-items/input-ignore-case');
}
