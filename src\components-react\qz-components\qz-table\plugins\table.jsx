// eslint-disable-next-line no-unused-vars
import React from 'react';
import {
  addSchema2Toolbar,
  CRUDTablePlugin,
  deepRemove,
  defaultValue,
  getSchemaTpl,
  Icon,
  registerEditorPlugin,
  tipedLabel,
  unRegisterEditorPlugin
} from 'amis-editor';
import { Button } from 'amis-ui';
import { isFunction } from 'lodash-es';
import { cloneDeep } from 'lodash-es';
import { add } from 'xe-utils';

class QzTablePlugin extends CRUDTablePlugin {
  name = 'API表格';
  description = 'API表格组件，支持数据源配置、筛选、排序、分页等功能。';
  // 迁移部分table2的配置出来后，再隐藏table2的配置面板
  multifactor = false;

  async buildDataSchemas(node, region, trigger) {
    const original = await super.buildDataSchemas(node, region, trigger);
    original.properties.selectedView = {
      type: 'object',
      rawType: 'object',
      title: '选中的视图'
    };
    return original;
  }

  renderFiltersCollapse(context) {
    const original = super.renderFiltersCollapse(context);
    original.body.push(
      getSchemaTpl('switch', {
        name: 'showQuickFilter',
        label: tipedLabel('显示快速筛选', '是否显示快速筛选功能'),
        onChange: (value, oldValue, model, form) => {
          const schema = cloneDeep(form.data);
          schema.showQuickFilter = value;
          if (value) {
            addSchema2Toolbar(
              schema,
              {
                type: 'input-text',
                _id_: 'quick-filter',
                label: '',
                name: 'keywords',
                placeholder: '请输入关键词进行筛选'
              },
              'header',
              'right'
            );
          } else {
            deepRemove(
              schema.headerToolbar,
              (item) => item._id_ === 'quick-filter'
            );
          }
          form.setValues(schema);
        }
      })
    );
    return original;
  }

  renderPropsTab(context) {
    /** 动态加载的配置集合 */
    const dc = this.dynamicControls;

    return {
      title: '属性',
      className: 'p-none',
      body: [
        getSchemaTpl(
          'collapseGroup',
          [
            /** 基本配置类别 */
            this.renderBasicPropsCollapse(context),
            /** 列设置类别 */
            isFunction(dc.columns) ? dc.columns(context) : dc.columns,
            /** 行设置类别 */
            this.renderRowPropsCollapse(context),
            /** 搜索类别 */
            isFunction(dc.filters) ? dc.filters(context) : dc.filters,
            /** 视图设置 */
            this.renderViewCollapse(context),
            /** 工具栏类别 */
            isFunction(dc.toolbar) ? dc.toolbar(context) : dc.toolbar,
            /** 分页类别 */
            this.renderPaginationCollapse(context),
            /** 分组类别 */
            this.renderGroupCollapse(context),
            /** 其他类别 */
            this.renderOthersCollapse(context),
            /** 状态类别 */
            {
              title: '状态',
              body: [getSchemaTpl('hidden'), getSchemaTpl('visible')]
            },
            this.renderMockPropsCollapse(context)
          ].filter(Boolean)
        )
      ]
    };
  }

  renderGroupCollapse(context) {
    return {
      title: '分组设置',
      body: [
        getSchemaTpl('apiControl', {
          name: 'groupConfig.api',
          label: tipedLabel('接口', '用于获取分组数据的 API'),
          renderLabel: true
        }),
        {
          type: 'crud-group-control',
          name: 'groupConfig.fieldList',
          label: '可分组字段',
          nodeId: context.id
        }
      ]
    };
  }

  renderViewCollapse(context) {
    return {
      title: '视图设置',
      body: [
        getSchemaTpl('switch', {
          name: 'showView',
          label: '显示视图',
          onChange: (value, oldValue, model, form) => {
            const schema = cloneDeep(form.data);
            schema.showView = value;
            if (value && !schema.view) {
              schema.view = {
                type: 'crud-view'
              };
            }
            if (value) {
              addSchema2Toolbar(
                schema,
                {
                  type: 'view-saver',
                  label: '保存视图'
                },
                'header',
                'right'
              );
            } else {
              deepRemove(
                schema.headerToolbar,
                (item) => item.type === 'view-saver'
              );
            }
            form.setValues(schema);
          }
        }),
        {
          type: 'input-number',
          name: 'maxViewWidth',
          label: '最大视图宽度',
          min: 0,
          value: 500
        },
        {
          type: 'input-number',
          name: 'minViewWidth',
          label: '最小视图宽度',
          min: 0,
          value: 200
        }
      ]
    };
  }

  renderRowPropsCollapse(context) {
    return {
      title: '行设置',
      body: [
        {
          type: 'ae-Switch-More',
          mode: 'normal',
          name: 'rowSelection',
          label: '可选择',
          hiddenOnDefault: true,
          formType: 'extend',
          bulk: false,
          form: {
            body: [
              getSchemaTpl('switch', {
                name: 'fixed',
                label: '固定选择列'
              }),
              {
                type: 'input-number',
                name: 'columnWidth',
                label: '选择列列宽',
                min: 0,
                pipeOut: (data) => data || undefined
              },
              {
                label: '可选区域',
                name: 'rowClick',
                type: 'button-group-select',
                value: false,
                options: [
                  {
                    label: '整行',
                    value: true
                  },
                  {
                    label: '勾选框',
                    value: false
                  }
                ]
              },
              getSchemaTpl('formulaControl', {
                name: 'disableOn',
                label: '行禁用条件'
              }),
              {
                name: 'selections',
                label: '选择菜单项',
                type: 'checkboxes',
                joinValues: false,
                inline: false,
                itemClassName: 'text-sm',
                options: [
                  { label: '全选所有', value: 'all' },
                  { label: '反选当页', value: 'invert' },
                  { label: '清空所有', value: 'none' },
                  { label: '选择奇数行', value: 'odd' },
                  { label: '选择偶数行', value: 'even' }
                ],
                pipeIn(v) {
                  if (!v) {
                    return;
                  }
                  return v.map((item) => ({
                    label: item.text,
                    value: item.key
                  }));
                },
                pipeOut(v) {
                  if (!v) {
                    return;
                  }
                  return v.map((item) => ({
                    key: item.value,
                    text: item.label
                  }));
                }
              }
            ].filter(Boolean)
          }
        },
        getSchemaTpl('formulaControl', {
          label: '行可勾选条件',
          name: 'itemCheckableOn'
        }),
        {
          type: 'input-number',
          name: 'maxKeepItemSelectionLength',
          label: '最大选择条数'
        },
        {
          type: 'ae-Switch-More',
          mode: 'normal',
          name: 'expandable',
          label: '可展开',
          hiddenOnDefault: true,
          formType: 'extend',
          bulk: false,
          form: {
            body: [
              {
                type: 'input-text',
                name: 'keyField',
                label: '数据源key'
              },
              {
                type: 'select',
                label: '展开按钮位置',
                name: 'position',
                options: [
                  {
                    label: '默认',
                    value: ''
                  },
                  {
                    label: '左侧',
                    value: 'left'
                  },
                  {
                    label: '右侧',
                    value: 'right'
                  },
                  {
                    label: '隐藏',
                    value: 'none'
                  }
                ]
              },
              getSchemaTpl('formulaControl', {
                name: 'expandableOn',
                visibleOn: 'this.expandable',
                label: '可展开条件'
              }),
              {
                name: 'expandable',
                asFormItem: true,
                label: false,
                children: ({ value, onBulkChange }) => {
                  const newValue = value?.type
                    ? value
                    : {
                        type: 'container',
                        body: [
                          {
                            type: 'tpl',
                            tpl: '展开行内容',
                            inline: false
                          }
                        ]
                      };
                  return (
                    <Button
                      className="w-full flex flex-col items-center"
                      onClick={() => {
                        this.manager.openSubEditor({
                          title: '配置展开区域',
                          value: newValue,
                          onChange: (value) => {
                            onBulkChange(value);
                          },
                          data: {
                            ...this.manager.store.ctx
                          } //默认数据
                        });
                      }}
                    >
                      <span className="inline-flex items-center">
                        <Icon icon="edit" className="mr-1 w-3" />
                        配置展开区域
                      </span>
                    </Button>
                  );
                }
              }
            ]
          }
        },
        {
          type: 'input-text',
          label: tipedLabel(
            '嵌套字段',
            '声明数据结构中作为子节点的字段名称，默认是<code>children</code>'
          ),
          name: 'childrenColumnName',
          pipeIn: defaultValue('children')
        },
        getSchemaTpl('switch', {
          name: 'draggable',
          label: '可拖拽'
        }),
        getSchemaTpl('formulaControl', {
          label: '可拖拽条件',
          name: 'itemDraggableOn'
        }),
        getSchemaTpl('apiControl', {
          name: 'saveOrderApi',
          label: '保存排序',
          renderLabel: true
        }),
        {
          name: 'showBadge',
          label: '行角标',
          type: 'ae-switch-more',
          mode: 'normal',
          formType: 'extend',
          bulk: true,
          form: {
            body: [
              {
                type: 'ae-badge',
                label: false,
                name: 'itemBadge',
                node: context.node,
                contentsOnly: true,
                value: {
                  mode: 'dot',
                  offset: [0, 0]
                }
              }
            ]
          }
        }
      ]
    };
  }

  // 修改基础设置
  renderBasicPropsCollapse(context) {
    const original = super.renderBasicPropsCollapse(context);
    original.body.push(this.toggleTitle());
    original.body.push(
      getSchemaTpl('switch', {
        name: 'autoFillHeight',
        label: '高度自适应'
      })
    );
    return original;
  }

  // 修改列设置
  renderColumnsControl(context) {
    const original = super.renderColumnsControl(context);
    original.body = [
      this.columnTogglable(),
      getSchemaTpl('switch', {
        name: 'resizable',
        label: tipedLabel('可调整列宽', '用户可通过拖拽调整列宽度'),
        pipeIn: (value) => !!value,
        pipeOut: (value) => value
      }),
      ...original.body
    ];

    return original;
  }

  columnTogglable() {
    return getSchemaTpl('switch', {
      name: 'columnsTogglable',
      label: tipedLabel('自定义显示列'),
      onChange: (value, oldValue, model, form) => {
        const schema = cloneDeep(form.data);
        if (value === true) {
          addSchema2Toolbar(
            schema,
            { type: 'column-toggler', draggable: true },
            'header',
            'right'
          );
          schema.columns.forEach((item) => {
            // 开启自定义显示列时，默认先展示所有列。不然会导致切换显示列时第一次点击无效的问题
            if (item.toggled === undefined) {
              item.toggled = true;
            }
          });
        } else {
          deepRemove(
            schema.headerToolbar,
            (item) => item.type === 'column-toggler'
          );
          schema.columns.forEach((item) => {
            if (item.toggled !== undefined) {
              delete item.toggled;
            }
          });
        }
        form.setValues(schema);
        return undefined;
      }
    });
  }

  toggleTitle() {
    return getSchemaTpl('switch', {
      name: 'title',
      label: '显示标题',
      pipeIn: (value) => !!value,
      onChange: (value, oldValue, model, form) => {
        const schema = cloneDeep(form.data);
        if (value === true) {
          addSchema2Toolbar(
            schema,
            {
              type: 'container',
              _id_: 'table-title',
              body: [
                {
                  type: 'tpl',
                  wrapperComponent: '',
                  tpl: '表格标题',
                  inline: false,
                  style: {
                    fontSize: 14
                  }
                }
              ]
            },
            'header',
            'left'
          );
        } else {
          deepRemove(
            schema.headerToolbar,
            (item) => item._id_ === 'table-title'
          );
        }
        form.setValues(schema);
        return undefined;
      }
    });
  }
}

unRegisterEditorPlugin(CRUDTablePlugin.id);
registerEditorPlugin(QzTablePlugin);
