import { getEventControlConfig } from 'amis-editor';
import {
  BasePlugin,
  getSchemaTpl,
  registerEditorPlugin,
  tipedLabel
} from 'amis-editor-core';

class CrudViewPlugin extends BasePlugin {
  static id = 'CrudViewPlugin';
  rendererName = 'crud-view';
  disabledRendererPlugin = true;
  name = 'CRUD视图';
  panelTitle = 'CRUD视图';
  $schema = '/schemas/UnknownSchema.json';
  panelJustify = true;

  // 事件定义
  events = [
    {
      eventName: 'change',
      eventLabel: '值变化',
      description: '选中值变化时触发'
    }
  ];

  panelBodyCreator = (context) => {
    return getSchemaTpl('tabs', [
      {
        title: '属性',
        className: 'p-none',
        body: [
          getSchemaTpl('collapseGroup', [
            {
              title: '基本配置',
              body: [
                getSchemaTpl('apiControl', {
                  name: 'source',
                  label: tipedLabel('接口', '用于获取视图数据的 API'),
                  renderLabel: true
                }),
                {
                  label: '显示字段',
                  type: 'input-text',
                  name: 'labelField',
                  placeholder: '选项文本对应的字段'
                },
                {
                  label: '值字段',
                  type: 'input-text',
                  name: 'valueField',
                  placeholder: '值对应的字段'
                },
                {
                  label: '数量字段',
                  type: 'input-text',
                  name: 'countField',
                  placeholder: '数量对应的字段'
                }
              ]
            }
          ])
        ]
      },
      {
        title: '事件',
        className: 'p-none',
        body: [
          getSchemaTpl('eventControl', {
            name: 'onEvent',
            ...getEventControlConfig(this.manager, context)
          })
        ]
      }
    ]);
  };
}

registerEditorPlugin(CrudViewPlugin);
