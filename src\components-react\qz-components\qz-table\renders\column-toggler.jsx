import React from 'react';
import Sortable from 'sortablejs';
import cloneDeep from 'lodash/cloneDeep';
import {
  noop,
  anyChanged,
  createObject,
  Overlay,
  PopOver,
  filter,
  isVisible,
  RootClose,
  unRegisterRenderer,
  registerRenderer
} from 'amis-core';
import { Checkbox, TooltipWrapper, Icon } from 'amis-ui';
import { ColumnTogglerRenderer } from 'amis/lib/renderers/Table2/ColumnToggler';
import '../styles/column-toggler.less';

class ColumnToggler extends React.Component {
  state = {
    isOpened: false,
    tempColumns: cloneDeep(this.props.columns)
  };

  static defaultProps = {
    placement: 'top',
    tooltipTrigger: ['hover', 'focus'],
    tooltipRootClose: false,
    draggable: false
  };

  target;
  sortable;

  constructor(props) {
    super(props);

    this.open = this.open.bind(this);
    this.close = this.close.bind(this);
    this.toggle = this.toggle.bind(this);
    this.domRef = this.domRef.bind(this);
    this.dragRef = this.dragRef.bind(this);
    this.onConfirm = this.onConfirm.bind(this);
  }

  componentDidMount() {
    if (this.props.defaultIsOpened) {
      this.setState({
        isOpened: true
      });
    }
  }

  componentDidUpdate(prevProps) {
    if (anyChanged('activeToggaleColumns', prevProps, this.props)) {
      this.setState({ tempColumns: cloneDeep(this.props.columns) });
    }
  }

  componentWillUnmount() {
    this.destroyDragging();
  }

  domRef(ref) {
    this.target = ref;
  }

  toggle(e) {
    e.preventDefault();

    this.setState({
      isOpened: !this.state.isOpened
    });
  }

  open() {
    this.setState({
      isOpened: true
    });
  }

  close() {
    this.setState({
      isOpened: false,
      tempColumns: cloneDeep(this.props.columns)
    });
  }

  swapColumnPosition(oldIndex, newIndex) {
    const columns = this.state.tempColumns;

    columns[oldIndex] = columns.splice(newIndex, 1, columns[oldIndex])[0];
    this.setState({ tempColumns: columns }, () => this.onConfirm());
  }

  dragRef(el) {
    const { draggable } = this.props;

    if (draggable && el) {
      this.initDragging(el);
    }
  }

  initDragging(el) {
    const ns = this.props.classPrefix;

    this.sortable = new Sortable(el, {
      group: `ColumnToggler-modal-content`,
      animation: 150,
      handle: `.${ns}ColumnToggler-menuItem-dragBar`,
      ghostClass: `${ns}ColumnToggler-menuItem--dragging`,
      onEnd: (e) => {
        if (e.newIndex === e.oldIndex) {
          return;
        }

        const parent = e.to;
        if (e.oldIndex < parent.childNodes.length - 1) {
          parent.insertBefore(
            e.item,
            parent.childNodes[
              e.oldIndex > e.newIndex ? e.oldIndex + 1 : e.oldIndex
            ]
          );
        } else {
          parent.appendChild(e.item);
        }

        this.swapColumnPosition(e.oldIndex, e.newIndex);
      }
    });
  }

  destroyDragging() {
    this.sortable && this.sortable.destroy();
  }

  async onConfirm() {
    const { tempColumns } = this.state;
    const { store, data, dispatchEvent } = this.props;

    const rendererEvent = await dispatchEvent(
      'columnToggled',
      createObject(data, {
        columns: tempColumns
      })
    );

    if (rendererEvent?.prevented) {
      return;
    }

    store.updateColumns(tempColumns);
  }

  renderOuter() {
    const {
      popOverContainer,
      classnames: cx,
      classPrefix: ns,
      translate: __,
      closeOnClick,
      closeOnOutside,
      mobileUI,
      activeToggaleColumns,
      render,
      draggable,
      toggleToggle,
      toggleAllColumns
    } = this.props;
    const { tempColumns } = this.state;

    const body = (
      <RootClose
        disabled={!this.state.isOpened}
        onRootClose={closeOnOutside !== false ? this.close : noop}
      >
        {(ref) => {
          return (
            <div
              className={cx('ColumnToggler-wrapper', { 'is-mobile': mobileUI })}
              onClick={closeOnClick ? this.close : noop}
              ref={ref}
            >
              {tempColumns?.length ? (
                <>
                  <div
                    className={cx(
                      'ColumnToggler-menuItem',
                      'ColumnToggler-menuItem-selectAll'
                    )}
                    key={'selectAll'}
                    onClick={() => {
                      toggleAllColumns &&
                        toggleAllColumns(activeToggaleColumns?.length <= 0);
                    }}
                  >
                    <Checkbox
                      size="sm"
                      classPrefix={ns}
                      key="checkall"
                      checked={!!activeToggaleColumns?.length}
                      partial={
                        !!(
                          activeToggaleColumns?.length &&
                          activeToggaleColumns?.length !== tempColumns?.length
                        )
                      }
                    >
                      {__('Select.checkAll')}
                    </Checkbox>
                  </div>
                  {render('divider', {
                    type: 'divider',
                    style: { margin: '5px' }
                  })}
                </>
              ) : null}
              <ul
                className={cx('ColumnToggler-menuContainer', {
                  'ColumnToggler-modal-content': draggable
                })}
                ref={this.dragRef}
              >
                {tempColumns?.map((column, index) => (
                  <li
                    className={cx('ColumnToggler-menuItem')}
                    key={'item' + (column.id || index)}
                  >
                    <Checkbox
                      size="sm"
                      classPrefix={ns}
                      checked={column.toggled !== false}
                      onChange={() => {
                        toggleToggle && toggleToggle(index);
                      }}
                    ></Checkbox>
                    {draggable ? (
                      <a className={cx('ColumnToggler-menuItem-dragBar')}>
                        <Icon icon="drag" className={cx('icon')} />
                      </a>
                    ) : null}
                    <span className={cx('ColumnToggler-menuItem-title')}>
                      {column.title
                        ? render('tpl', column.title)
                        : column.label || null}
                    </span>
                  </li>
                ))}
              </ul>
            </div>
          );
        }}
      </RootClose>
    );

    if (popOverContainer) {
      return (
        <Overlay container={popOverContainer} target={() => this.target} show>
          <PopOver
            overlay
            onHide={this.close}
            classPrefix={ns}
            className={cx('ColumnToggler-popover')}
            style={{ minWidth: this.target?.offsetWidth }}
          >
            {body}
          </PopOver>
        </Overlay>
      );
    }

    return body;
  }

  render() {
    const {
      tooltip,
      placement,
      tooltipContainer,
      tooltipTrigger,
      tooltipRootClose,
      disabledTip,
      block,
      disabled,
      btnDisabled,
      btnClassName,
      size,
      label,
      level,
      primary,
      className,
      classnames: cx,
      align,
      iconOnly,
      icon,
      isActived,
      data,
      draggable,
      hideExpandIcon,
      mobileUI
    } = this.props;

    const button = (
      <button
        onClick={this.toggle}
        disabled={disabled || btnDisabled}
        className={cx(
          'Button',
          btnClassName,
          typeof level === 'undefined'
            ? 'Button--default'
            : level
              ? `Button--${level}`
              : '',
          {
            'Button--block': block,
            'Button--primary': primary,
            'Button--iconOnly': iconOnly
          },
          size ? `Button--size-${size}` : ''
        )}
      >
        <Icon
          cx={cx}
          icon={icon || 'fa fa-cog'}
          className={cx('icon', { 'm-r-xs': !!label, 'm-r-none': !!icon })}
        />
        {typeof label === 'string' ? filter(label, data) : label}
        {hideExpandIcon || draggable ? null : (
          <span className={cx('ColumnToggler-caret')}>
            <Icon icon="right-arrow-bold" className="icon" />
          </span>
        )}
      </button>
    );

    return (
      <div
        className={cx(
          'ColumnToggler',
          {
            'ColumnToggler-block': block,
            'ColumnToggler--alignRight': align === 'right',
            'is-opened': this.state.isOpened,
            'is-actived': isActived
          },
          className
        )}
        ref={this.domRef}
      >
        {draggable ? (
          button
        ) : (
          <TooltipWrapper
            placement={placement}
            tooltip={disabled || mobileUI ? disabledTip : tooltip}
            container={tooltipContainer}
            trigger={tooltipTrigger}
            rootClose={tooltipRootClose}
          >
            {button}
          </TooltipWrapper>
        )}
        {this.state.isOpened ? this.renderOuter() : null}
      </div>
    );
  }
}

class QzColumnTogglerRenderer extends ColumnTogglerRenderer {
  render() {
    const {
      render,
      classPrefix: ns,
      classnames: cx,
      tooltip,
      align,
      cols,
      data,
      size,
      popOverContainer,
      ...rest
    } = this.props;
    const __ = rest.translate;
    const env = rest.env;

    if (!cols) {
      return null;
    }

    const toggableColumns = cols.filter(
      (item) =>
        isVisible(item.pristine || item, data) && item.toggable !== false
    );

    const activeToggaleColumns = toggableColumns.filter(
      (item) => item.toggled !== false
    );

    return (
      <ColumnToggler
        {...rest}
        render={render}
        tooltip={tooltip || __('Table.columnsVisibility')}
        tooltipContainer={popOverContainer || env.getModalContainer}
        isActived={cols.findIndex((column) => column.toggled === false) !== -1}
        align={align ?? 'right'}
        size={size || 'default'}
        classnames={cx}
        classPrefix={ns}
        key="columns-toggable"
        columns={cols}
        iconOnly={true}
        activeToggaleColumns={activeToggaleColumns}
        data={data}
      ></ColumnToggler>
    );
  }
}

unRegisterRenderer('column-toggler');
registerRenderer({
  type: 'column-toggler',
  component: QzColumnTogglerRenderer
});
