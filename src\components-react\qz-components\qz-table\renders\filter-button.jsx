import { registerRender<PERSON> } from 'amis-core';
import React from 'react';

class FilterButton extends React.Component {
  constructor(props) {
    super(props);
  }

  render() {
    const { render, store } = this.props;
    const filterFormVisible = store.filterFormVisible;
    return render(
      'filter-button',
      {
        type: 'button',
        label: '',
        behavior: 'custom',
        disabledOnAction: false,
        icon: 'fa fa-filter',
        level: `${filterFormVisible ? 'primary' : ''}`
      },
      {
        onClick: () => {
          store.toggleFilterForm();
        }
      }
    );
  }
}

registerRenderer({
  type: 'crud-filter-button',
  component: FilterButton
});
