import { FormItem, unRegisterRenderer } from 'amis-core';
import { addSchema2Toolbar, deepRemove, DSFeatureEnum } from 'amis-editor';
import { CRUDFiltersControl } from 'amis-editor/lib/renderer/crud2-control/CRUDFiltersControl';
import { cloneDeep } from 'lodash-es';
import React from 'react';

class QzTableFiltersControlRender extends CRUDFiltersControl {
  buildFilterButton() {
    return {
      type: 'crud-filter-button'
    };
  }

  async updateSimpleQuery(enable) {
    const { manager, nodeId, builder } = this.props;
    const store = manager.store;
    const CRUDNode = store.getNodeById(nodeId);
    const CRUDSchema = CRUDNode?.schema;

    // 下面这行不能删除。
    // 如果搜索表单中添加了自定义的表单项，删除下面这行代码会导致第一次关闭简单查询时switch还是开启状态
    await builder.guessCRUDScaffoldConfig({
      schema: CRUDSchema
    });

    const filterSchema = cloneDeep(
      CRUDSchema?.filter
        ? Array.isArray(CRUDSchema.filter)
          ? CRUDSchema.filter.find(
              (item) =>
                item.behavior &&
                Array.isArray(item.behavior) &&
                item.type === 'form'
            )
          : CRUDSchema.filter?.type === 'form'
            ? CRUDSchema.filter
            : undefined
        : undefined
    );

    if (filterSchema) {
      if (enable) {
        // 添加筛选按钮
        addSchema2Toolbar(
          CRUDSchema,
          this.buildFilterButton(CRUDSchema),
          'header',
          'right'
        );
      } else {
        // 移除筛选按钮
        deepRemove(
          CRUDSchema.headerToolbar,
          (item) => item.type === 'crud-filter-button'
        );
      }

      CRUDNode.updateSchema({
        ...CRUDSchema
      });
    } else {
      if (enable) {
        /** 没有查询表头新建一个 */
        const simpleQuerySchema =
          (await builder.buildSimpleQueryCollectionSchema?.({
            renderer: 'crud',
            schema: CRUDSchema,
            inScaffold: false,
            buildSettings: {
              useDefaultFields: true
            }
          })) ?? [];
        const filter = await builder.buildCRUDFilterSchema({
          renderer: 'crud',
          inScaffold: false,
          schema: CRUDSchema,
          feats: [DSFeatureEnum.SimpleQuery],
          scaffoldConfig: {
            dsType: CRUDSchema.dsType,
            simpleQueryFields: simpleQuerySchema
          },
          buildSettings: {
            useDefaultFields: true
          }
        });

        const newFilterSchema = cloneDeep(CRUDNode?.schema.filter);
        const isArrayFilter = Array.isArray(newFilterSchema);

        if (isArrayFilter) {
          newFilterSchema.push(filter);
        }

        // 添加筛选按钮
        addSchema2Toolbar(
          CRUDSchema,
          this.buildFilterButton(CRUDSchema),
          'header',
          'right'
        );

        CRUDNode.updateSchema({
          ...CRUDSchema,
          filter: isArrayFilter ? newFilterSchema : filter
        });
      }
    }
  }

  render() {
    const { classnames: cx } = this.props;

    return (
      <div className={cx('ae-CRUDConfigControl')}>{this.renderHeader()}</div>
    );
  }
}

unRegisterRenderer('ae-crud-filters-control');
FormItem({
  type: 'ae-crud-filters-control',
  renderLabel: false,
  wrap: false
})(QzTableFiltersControlRender);
