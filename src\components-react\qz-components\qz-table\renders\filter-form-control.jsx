import { FormItem } from 'amis-core';
import React from 'react';
import Sortable from 'sortablejs';
import '../styles/filter-form-control.less';
import { Button, Icon, toast, Modal, Checkbox } from 'amis-ui';

const supportedTypes = [
  {
    type: 'input-text',
    label: '文本框'
  },
  {
    type: 'crud-input-ignore-case',
    label: '文本框-可忽略大小写'
  },
  {
    type: 'filter-form-select',
    label: '下拉框'
  },
  {
    type: 'filter-form-cascader',
    label: '级联选择'
  },
  {
    type: 'filter-form-radios',
    label: '单选框'
  },
  {
    type: 'filter-form-checkboxes',
    label: '复选框'
  },
  {
    type: 'input-date',
    label: '日期/时间选择'
  },
  {
    type: 'input-date-range',
    label: '日期/时间范围选择'
  },
  {
    type: 'switch',
    label: '开关'
  }
];

function formatType(type) {
  const found = supportedTypes.find((t) => t.type === type);
  return found ? found.label : type;
}

class FilterFormControl extends React.Component {
  constructor(props) {
    super(props);

    const { manager, nodeInfo } = this.props;
    const store = manager.store;
    const formNode = store.getNodeById(nodeInfo.id);
    // const formSchema = formNode?.schema || {};

    const CRUDNode = store.getNodeById(formNode.parentId);
    const CRUDSchema = CRUDNode?.schema;
    this.tableColumns = CRUDSchema?.columns || [];

    this.state = {
      options: [],
      isColumnModalOpen: false,
      isCustomModalOpen: false,
      isEditModalOpen: false,
      selectedColumns: [],
      customFilter: {
        name: '',
        label: '',
        type: 'input-text'
      },
      editingFilter: {
        index: -1,
        data: null,
        originalType: ''
      },
      columnsMap: {}
    };

    this.cardListRef = React.createRef();
    this.sortable = null;
  }

  componentDidMount() {
    this.initOptions();
    // 移除这里的 initSortable 调用，改为在 componentDidUpdate 中处理
  }

  componentDidUpdate(prevProps, prevState) {
    if (prevProps.value !== this.props.value) {
      this.initOptions();
    }

    // 当 options 从无到有或者发生变化时，重新初始化 Sortable
    if (
      (prevState.options.length === 0 && this.state.options.length > 0) ||
      prevState.options.length !== this.state.options.length
    ) {
      this.destroySortable();
      this.initSortable();
    }
  }

  componentWillUnmount() {
    this.destroySortable();
  }

  initSortable() {
    if (this.cardListRef.current) {
      this.sortable = new Sortable(this.cardListRef.current, {
        animation: 150,
        handle: '.filter-form-card-drag-handle',
        ghostClass: 'filter-form-card-ghost',
        onEnd: (evt) => {
          // 当拖拽结束时，重新排序选项
          if (evt.oldIndex !== evt.newIndex) {
            this.reorderOptions(evt.oldIndex, evt.newIndex);
          }
        }
      });
    }
  }
  destroySortable() {
    if (this.sortable) {
      this.sortable.destroy();
      this.sortable = null;
    }
  }

  reorderOptions(oldIndex, newIndex) {
    const { onBulkChange } = this.props;
    const options = [...this.state.options];

    // 获取被拖拽的项
    const draggedItem = options[oldIndex];

    // 移除旧位置的项
    options.splice(oldIndex, 1);

    // 在新位置插入
    options.splice(newIndex, 0, draggedItem);

    // 更新状态并触发变更
    this.setState({ options }, () => {
      onBulkChange?.({ body: options.map((item) => item.pristine) });
    });
  }

  initOptions() {
    const { manager, nodeInfo } = this.props;
    const store = manager.store;
    const formNode = store.getNodeById(nodeInfo.id);
    const formSchema = formNode?.schema || {};
    const options = (formSchema?.body || []).map((item) => {
      return {
        ...item,
        pristine: item
      };
    });

    // 构建列名和选项的映射，用于检查重复
    const columnsMap = {};
    options.forEach((item) => {
      columnsMap[item.name] = true;
    });

    this.setState({ options, columnsMap });
  }

  // 打开添加表格列弹窗
  openColumnModal = () => {
    this.setState({
      isColumnModalOpen: true,
      selectedColumns: []
    });
  };

  // 关闭添加表格列弹窗
  closeColumnModal = () => {
    this.setState({ isColumnModalOpen: false });
  };

  // 打开添加自定义筛选项弹窗
  openCustomModal = () => {
    this.setState({
      isCustomModalOpen: true,
      customFilter: {
        name: '',
        label: '',
        type: 'input-text'
      }
    });
  };

  // 关闭添加自定义筛选项弹窗
  closeCustomModal = () => {
    this.setState({ isCustomModalOpen: false });
  };

  // 打开编辑筛选项弹窗
  openEditModal = (column, index) => {
    this.setState({
      isEditModalOpen: true,
      editingFilter: {
        index,
        data: {
          name: column.name,
          label: column.label,
          type: column.type
        },
        originalType: column.type
      }
    });
  };

  // 关闭编辑筛选项弹窗
  closeEditModal = () => {
    this.setState({ isEditModalOpen: false });
  };

  // 处理选择列变化
  handleColumnSelectionChange = (column, checked) => {
    const { selectedColumns } = this.state;

    if (checked) {
      this.setState({
        selectedColumns: [...selectedColumns, column]
      });
    } else {
      this.setState({
        selectedColumns: selectedColumns.filter(
          (col) => col.name !== column.name
        )
      });
    }
  };

  // 处理自定义筛选项输入变化
  handleCustomFilterChange = (field, value) => {
    const { customFilter } = this.state;
    this.setState({
      customFilter: {
        ...customFilter,
        [field]: value
      }
    });
  };

  // 处理编辑筛选项变化
  handleEditFilterChange = (field, value) => {
    const { editingFilter } = this.state;
    this.setState({
      editingFilter: {
        ...editingFilter,
        data: {
          ...editingFilter.data,
          [field]: value
        }
      }
    });
  };

  // 添加选中的表格列为筛选项
  confirmAddColumns = () => {
    const { onBulkChange } = this.props;
    const { options, selectedColumns, columnsMap } = this.state;

    if (!selectedColumns.length) {
      toast.info('请选择至少一个表格列');
      return;
    }

    // 创建新的筛选项
    const newOptions = selectedColumns.map((column) => ({
      type: 'input-text',
      name: column.name,
      label: column.label || column.title,
      pristine: {
        type: 'input-text',
        name: column.name,
        label: column.label || column.title
      }
    }));

    // 更新状态并触发变更
    const updatedOptions = [...options, ...newOptions];
    const updatedColumnsMap = { ...columnsMap };
    selectedColumns.forEach((col) => {
      updatedColumnsMap[col.name] = true;
    });

    this.setState(
      {
        options: updatedOptions,
        columnsMap: updatedColumnsMap,
        isColumnModalOpen: false
      },
      () => {
        onBulkChange?.({ body: updatedOptions.map((item) => item.pristine) });
      }
    );
  };

  // 添加自定义筛选项
  confirmAddCustom = () => {
    const { onBulkChange } = this.props;
    const { options, customFilter, columnsMap } = this.state;

    // 验证输入
    if (!customFilter.name) {
      toast.error('请输入筛选项名称');
      return;
    }

    if (!customFilter.label) {
      toast.error('请输入筛选项标题');
      return;
    }

    // 检查名称是否符合规范
    if (!/^[a-zA-Z0-9_]+$/.test(customFilter.name)) {
      toast.error('名称只能包含字母、数字和下划线');
      return;
    }

    // 检查名称是否已存在
    if (columnsMap[customFilter.name]) {
      toast.error(`名称「${customFilter.name}」已存在`);
      return;
    }

    // 创建新的筛选项
    const newFilter = {
      ...customFilter,
      pristine: { ...customFilter }
    };

    // 更新状态并触发变更
    const updatedOptions = [...options, newFilter];
    const updatedColumnsMap = { ...columnsMap };
    updatedColumnsMap[customFilter.name] = true;

    this.setState(
      {
        options: updatedOptions,
        columnsMap: updatedColumnsMap,
        isCustomModalOpen: false
      },
      () => {
        onBulkChange?.({ body: updatedOptions.map((item) => item.pristine) });
      }
    );
  };

  // 保存编辑后的筛选项
  confirmEditFilter = () => {
    const { onBulkChange } = this.props;
    const { options, editingFilter, columnsMap } = this.state;
    const { index, data, originalType } = editingFilter;

    // 验证输入
    if (!data.name) {
      toast.error('请输入筛选项名称');
      return;
    }

    if (!data.label) {
      toast.error('请输入筛选项标题');
      return;
    }

    // 检查名称是否符合规范
    if (!/^[a-zA-Z0-9_]+$/.test(data.name)) {
      toast.error('名称只能包含字母、数字和下划线');
      return;
    }

    // 如果名称改变了，检查是否与其他筛选项重名
    const oldName = options[index].name;
    if (data.name !== oldName && columnsMap[data.name]) {
      toast.error(`名称「${data.name}」已存在`);
      return;
    }

    // 处理类型变更的情况
    if (data.type !== originalType) {
      // 创建新筛选项
      const newFilter = {
        ...data,
        pristine: { ...data }
      };

      // 更新columnsMap
      const updatedColumnsMap = { ...columnsMap };
      delete updatedColumnsMap[oldName];
      updatedColumnsMap[data.name] = true;

      // 复制并替换原位置的筛选项，保持顺序不变
      const updatedOptions = [...options];
      updatedOptions[index] = newFilter;

      // 更新状态
      this.setState(
        {
          options: updatedOptions,
          columnsMap: updatedColumnsMap,
          isEditModalOpen: false
        },
        () => {
          onBulkChange?.({
            body: updatedOptions.map((item) => item.pristine)
          });
        }
      );
    } else {
      // 未变更类型，直接更新
      const updatedOptions = [...options];

      // 更新columnsMap
      const updatedColumnsMap = { ...columnsMap };
      if (data.name !== oldName) {
        delete updatedColumnsMap[oldName];
        updatedColumnsMap[data.name] = true;
      }

      // 更新筛选项
      updatedOptions[index] = {
        ...updatedOptions[index],
        ...data,
        pristine: {
          ...updatedOptions[index].pristine,
          ...data
        }
      };

      this.setState(
        {
          options: updatedOptions,
          columnsMap: updatedColumnsMap,
          isEditModalOpen: false
        },
        () => {
          onBulkChange?.({ body: updatedOptions.map((item) => item.pristine) });
        }
      );
    }
  };

  // 渲染添加表格列弹窗
  renderColumnModal() {
    const { env } = this.props;
    const { isColumnModalOpen, selectedColumns, columnsMap } = this.state;

    // 过滤出未添加的列
    const availableColumns = this.tableColumns.filter(
      (col) => !columnsMap[col.name] && col.name && (col.label || col.title)
    );

    return (
      <Modal
        closeOnEsc
        onHide={this.closeColumnModal}
        show={isColumnModalOpen}
        contentClassName="filter-form-modal"
        size="md"
      >
        <Modal.Header onClose={this.closeEditModal}>
          <Modal.Title>从表格列添加筛选项</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {availableColumns.length > 0 ? (
            <div className="filter-form-columns-list">
              {availableColumns.map((column, index) => (
                <div key={column.name} className="filter-form-column-item">
                  <Checkbox
                    classPrefix={env.theme.classPrefix}
                    checked={selectedColumns.some(
                      (col) => col.name === column.name
                    )}
                    onChange={(checked) =>
                      this.handleColumnSelectionChange(column, checked)
                    }
                  >
                    {column.label || column.title} ({column.name})
                  </Checkbox>
                </div>
              ))}
            </div>
          ) : (
            <div className="filter-form-empty">
              所有可用的表格列已添加为筛选项
            </div>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button onClick={this.closeColumnModal}>取消</Button>
          <Button level="primary" onClick={this.confirmAddColumns}>
            确认添加
          </Button>
        </Modal.Footer>
      </Modal>
    );
  }

  // 渲染添加自定义筛选项弹窗
  renderCustomModal() {
    const { isCustomModalOpen, customFilter } = this.state;

    return (
      <Modal
        closeOnEsc
        onHide={this.closeCustomModal}
        show={isCustomModalOpen}
        contentClassName="filter-form-modal"
        size="md"
      >
        <Modal.Header onClose={this.closeEditModal}>
          <Modal.Title>添加自定义筛选项</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <div className="filter-form-custom-fields">
            <div className="filter-form-field">
              <label>名称</label>
              <input
                type="text"
                value={customFilter.name}
                onChange={(e) =>
                  this.handleCustomFilterChange('name', e.target.value)
                }
                placeholder="请输入名称，只能包含字母、数字和下划线"
                className="form-control"
              />
            </div>
            <div className="filter-form-field">
              <label>标题</label>
              <input
                type="text"
                value={customFilter.label}
                onChange={(e) =>
                  this.handleCustomFilterChange('label', e.target.value)
                }
                placeholder="请输入标题"
                className="form-control"
              />
            </div>
            <div className="filter-form-field">
              <label>类型</label>
              <select
                value={customFilter.type}
                onChange={(e) =>
                  this.handleCustomFilterChange('type', e.target.value)
                }
                className="form-control"
              >
                {supportedTypes.map((type) => (
                  <option key={type.type} value={type.type}>
                    {type.label}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </Modal.Body>
        <Modal.Footer>
          <Button onClick={this.closeCustomModal}>取消</Button>
          <Button level="primary" onClick={this.confirmAddCustom}>
            确认添加
          </Button>
        </Modal.Footer>
      </Modal>
    );
  }

  // 渲染编辑筛选项弹窗
  renderEditModal() {
    const { isEditModalOpen, editingFilter } = this.state;

    if (!editingFilter.data) {
      return null;
    }

    return (
      <Modal
        closeOnEsc
        onHide={this.closeEditModal}
        show={isEditModalOpen}
        contentClassName="filter-form-modal"
        size="md"
      >
        <Modal.Header onClose={this.closeEditModal}>
          <Modal.Title>编辑筛选项</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <div className="filter-form-custom-fields">
            <div className="filter-form-field">
              <label>名称</label>
              <input
                type="text"
                value={editingFilter.data.name}
                onChange={(e) =>
                  this.handleEditFilterChange('name', e.target.value)
                }
                placeholder="请输入名称，只能包含字母、数字和下划线"
                className="form-control"
              />
              <div className="filter-form-field-help">
                名称用于标识筛选项，修改可能会影响已有的查询逻辑
              </div>
            </div>
            <div className="filter-form-field">
              <label>标题</label>
              <input
                type="text"
                value={editingFilter.data.label}
                onChange={(e) =>
                  this.handleEditFilterChange('label', e.target.value)
                }
                placeholder="请输入标题"
                className="form-control"
              />
            </div>
            <div className="filter-form-field">
              <label>类型</label>
              <select
                value={editingFilter.data.type}
                onChange={(e) =>
                  this.handleEditFilterChange('type', e.target.value)
                }
                className="form-control"
              >
                {supportedTypes.map((type) => (
                  <option key={type.type} value={type.type}>
                    {type.label}
                  </option>
                ))}
              </select>
              {editingFilter.data.type !== editingFilter.originalType && (
                <div className="filter-form-field-warning">
                  <Icon icon="warning" className="warning-icon" />
                  修改类型会删除原筛选项并创建新筛选项，可能会影响已有的查询配置
                </div>
              )}
            </div>
          </div>
        </Modal.Body>
        <Modal.Footer>
          <Button onClick={this.closeEditModal}>取消</Button>
          <Button level="primary" onClick={this.confirmEditFilter}>
            保存
          </Button>
        </Modal.Footer>
      </Modal>
    );
  }

  handleEdit(item) {
    const { manager } = this.props;
    const idx = this.state.options.findIndex((c) => c.id === item.id);

    if (!~idx) {
      toast.warning(`未找到对应筛选项「${item.label}」`);
      return;
    }

    // FIXME: 理论上用item.nodeId就可以，不知道为何会重新构建一次导致store中node.id更新
    manager.setActiveId(this.state.options[idx]?.$$id);
  }

  async handleDelete(item, index) {
    const { onBulkChange, env } = this.props;
    const options = this.state.options;
    const confirmed = await env.confirm(
      `确定要删除筛选项「${item.label}」吗？`
    );

    if (~index && confirmed) {
      options.splice(index, 1);
      this.setState({ options }, () => {
        onBulkChange?.({ body: options.map((item) => item.pristine) });
      });
    }
  }

  render() {
    return (
      <div className="filter-form-card-container">
        {this.state.options.length > 0 ? (
          <div className="filter-form-card-list" ref={this.cardListRef}>
            {this.state.options.map((column, index) => (
              <div
                key={column.name}
                className="filter-form-card"
                onClick={() => this.openEditModal(column, index)}
              >
                <div className="filter-form-card-content">
                  <div
                    className="filter-form-card-drag-handle"
                    onClick={(e) => e.stopPropagation()}
                  >
                    <Icon icon="drag" className="drag-icon" />
                  </div>
                  <div className="filter-form-card-info">
                    <div className="filter-form-card-label">{column.label}</div>
                    <div className="filter-form-card-detail">
                      <span className="filter-form-card-name">
                        名称: {column.name}
                      </span>
                      <span className="filter-form-card-type">
                        类型: {formatType(column.type) || '未知类型'}
                      </span>
                    </div>
                  </div>
                  <div
                    className="filter-form-card-actions"
                    onClick={(e) => e.stopPropagation()}
                  >
                    <Button
                      level="link"
                      size="sm"
                      tooltip={{
                        content: '去编辑',
                        tooltipTheme: 'dark',
                        style: { fontSize: '12px' }
                      }}
                      onClick={(e) => {
                        e.stopPropagation();
                        this.handleEdit(column);
                      }}
                    >
                      <Icon icon="column-setting" className="icon" />
                    </Button>
                    <Button
                      level="link"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        this.handleDelete(column, index);
                      }}
                    >
                      <Icon icon="column-delete" className="icon" />
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="filter-form-empty">暂无筛选项</div>
        )}

        <div className="filter-form-card-actions-bar">
          <Button level="primary" size="sm" onClick={this.openColumnModal}>
            <Icon icon="columns" className="icon m-r-xs" />
            表格列筛选项
          </Button>
          <Button
            level="info"
            size="sm"
            className="m-l-sm"
            onClick={this.openCustomModal}
          >
            <Icon icon="plus" className="icon m-r-xs" />
            自定义筛选项
          </Button>
        </div>

        {this.renderColumnModal()}
        {this.renderCustomModal()}
        {this.renderEditModal()}
      </div>
    );
  }
}

FormItem({
  type: 'crud-filter-form-control',
  renderLabel: false,
  wrap: false
})(FilterFormControl);
