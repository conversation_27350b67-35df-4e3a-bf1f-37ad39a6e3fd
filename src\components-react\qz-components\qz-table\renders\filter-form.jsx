import { registerRenderer, ScopedContext } from 'amis-core';
import React from 'react';
import '../styles/filter-form.less';
import { cloneDeep } from 'lodash-es';

export function noUndefined(obj) {
  return Object.fromEntries(
    Object.entries(obj).filter(([_, value]) => value !== undefined)
  );
}

class FilterForm extends React.Component {
  static contextType = ScopedContext;

  constructor(props) {
    super(props);
    this.handleFormItemChange = this.handleFormItemChange.bind(this);
  }

  renderActions() {
    const { actions, render } = this.props;

    return (
      <div className="flex-row align-items-center flex-end">
        {actions.map((action, index) => {
          return render('action', action, {
            key: index,
            onAction: this.handleAction
          });
        })}
      </div>
    );
  }

  handleAction = (e, action) => {
    if (action.type === 'submit') {
      this.handleSubmit(e);
    } else if (action.type === 'reset') {
      this.handleReset(e);
    }
  };

  handleSubmit = (e) => {
    e.preventDefault();
    const { onSubmit, store } = this.props;
    if (onSubmit) {
      onSubmit(noUndefined(cloneDeep(store.filterFormData)));
    } else {
      console.warn('onSubmit function is not provided');
    }
  };

  handleReset = (e) => {
    const { onReset, store } = this.props;

    e.preventDefault();

    // 重置表单数据
    store.resetFilterFormData();

    if (onReset) {
      onReset(noUndefined(cloneDeep(store.filterFormData)));
    } else {
      console.warn('onReset function is not provided');
    }
  };

  renderBody() {
    const { body, render, store } = this.props;

    return body.map((item, index) => {
      return render(`body/${index}`, item, {
        key: index,
        // 使用data而不是value来传值，保证重置filterFormData后能够被响应式更新
        data: store.filterFormData,
        className: 'ApiTable__filter-form-item',
        popOverContainer: document.body,
        onChange: (value, name) => {
          this.handleFormItemChange({
            value,
            name
          });
        },
        onIgnoreCaseChange: (ignoreCase) => {
          this.handleFormItemChange({
            value: ignoreCase,
            name: `${item.name}_ignoreCase`
          });
        },
        onOperatorChange: (operator) => {
          this.handleFormItemChange({
            value: operator,
            name: `${item.name}_operator`
          });
        }
      });
    });
  }

  handleFormItemChange({ value, name }) {
    const { store } = this.props;
    store.changeFilterFormData(name, value);
  }

  render() {
    const { labelWidth } = this.props;

    return (
      <form
        className="ApiTable__filter-form-wrapper"
        style={{
          '--Form-label-width': labelWidth
        }}
        onSubmit={this.handleSubmit}
        onReset={this.handleReset}
      >
        <div className="ApiTable__filter-form-body">{this.renderBody()}</div>
        <div className="ApiTable__filter-form-footer">
          {this.renderActions()}
        </div>
      </form>
    );
  }
}

registerRenderer({
  type: 'crud-filter-form',
  component: FilterForm
});
