import {
  CustomStyle,
  registerOptionsControl,
  resolveVariable,
  ScopedContext,
  setThemeClassName,
  ucFirst
} from 'amis-core';
import { Cascader, PopUp, ResultBox, Select, Spinner } from 'amis-ui';
import NestedSelectControl from 'amis/lib/renderers/Form/NestedSelect';
import React from 'react';

export class FilterFormCascader extends NestedSelectControl {
  static contextType = ScopedContext;

  constructor(props, context) {
    super(props);

    const scoped = context;
    scoped.registerComponent(this);
    this.state = {
      ...this.state,
      operator: ''
    };
  }

  componentDidMount() {
    const { name, data } = this.props;
    const operatorFieldName = `${name}_operator`;
    const operator = resolveVariable(operatorFieldName, data) || '';
    this.setState({ operator });
  }

  handleOperatorChange = (value) => {
    this.setState({ operator: value });
    const { onOperatorChange } = this.props;
    if (onOperatorChange) {
      onOperatorChange(value);
    }
  };

  componentWillUnmount() {
    super.componentWillUnmount?.();
    const scoped = this.context;
    scoped.unRegisterComponent(this);
  }

  render() {
    const {
      className,
      style,
      disabled,
      classnames: cx,
      multiple,
      placeholder,
      translate: __,
      inline,
      searchable,
      selectedOptions,
      clearable,
      loading,
      borderMode,
      mobileUI,
      popOverContainer,
      env,
      testIdBuilder,
      loadingConfig,
      maxTagCount,
      overflowTagPopover,
      showOperator,
      operators
    } = this.props;

    const { classPrefix: ns, themeCss, id } = this.props;

    return (
      <div
        className={cx('NestedSelectControl', className, 'with-operator')}
        ref={this.outTarget}
        style={style}
      >
        {multiple && showOperator && operators && operators.length > 0 ? (
          <Select
            className="filter-form-item-operator"
            value={this.state.operator}
            popOverContainer={() => document.body}
            options={operators}
            onChange={this.handleOperatorChange}
            simpleValue={true}
          ></Select>
        ) : null}
        <ResultBox
          mobileUI={mobileUI}
          maxTagCount={maxTagCount}
          overflowTagPopover={overflowTagPopover}
          disabled={disabled}
          ref={this.domRef}
          placeholder={__(placeholder ?? 'placeholder.empty')}
          inputPlaceholder={''}
          className={cx(
            `NestedSelect`,
            {
              'NestedSelect--inline': inline,
              'NestedSelect--single': !multiple,
              'NestedSelect--multi': multiple,
              'NestedSelect--searchable': searchable,
              'is-opened': this.state.isOpened,
              'is-focused': this.state.isFocused,
              [`NestedSelect--border${ucFirst(borderMode)}`]: borderMode
            },
            setThemeClassName({
              ...this.props,
              name: 'nestedSelectControlClassName',
              id,
              themeCss: themeCss
            })
          )}
          result={
            multiple
              ? selectedOptions
              : selectedOptions.length
                ? selectedOptions[0]
                : ''
          }
          onResultClick={this.handleOutClick}
          value={this.state.inputValue}
          onChange={this.handleInputChange}
          onResultChange={this.handleResultChange}
          onClear={this.handleResultClear}
          itemRender={this.renderValue}
          onKeyPress={this.handleKeyPress}
          onFocus={this.onFocus}
          onBlur={this.onBlur}
          onKeyDown={this.handleInputKeyDown}
          clearable={clearable}
          hasDropDownArrow={true}
          allowInput={searchable && !mobileUI}
          testIdBuilder={testIdBuilder}
        >
          {loading ? (
            <Spinner loadingConfig={loadingConfig} size="sm" />
          ) : undefined}
        </ResultBox>
        {mobileUI ? (
          <PopUp
            className={cx(`NestedSelect-popup`)}
            container={env.getModalContainer}
            isShow={this.state.isOpened}
            onHide={this.close}
            showConfirm={false}
            showClose={false}
          >
            <Cascader
              onClose={this.close}
              {...this.props}
              onChange={this.handleResultChange}
              options={this.props.options.slice()}
              value={selectedOptions}
            />
          </PopUp>
        ) : this.state.isOpened ? (
          this.renderOuter()
        ) : null}
        <CustomStyle
          {...this.props}
          config={{
            themeCss: themeCss,
            classNames: [
              {
                key: 'nestedSelectControlClassName',
                weights: {
                  hover: {
                    suf: '.is-clickable:not(.is-disabled)'
                  },
                  focused: {
                    suf: '.is-opened:not(.is-mobile)'
                  },
                  disabled: {
                    suf: '.is-disabled'
                  }
                }
              },
              {
                key: 'nestedSelectPopoverClassName',
                weights: {
                  default: {
                    suf: ` .${ns}NestedSelect-option`
                  },
                  hover: {
                    suf: ` .${ns}NestedSelect-option.is-highlight`
                  },
                  focused: {
                    inner: `.${ns}NestedSelect-option.is-active`
                  }
                }
              }
            ],
            id: id
          }}
          env={env}
        />
      </div>
    );
  }
}

registerOptionsControl({
  type: 'filter-form-cascader',
  isolateScope: true,
  component: FilterFormCascader
});
