import {
  registerOptionsControl,
  resolveVariable,
  ScopedContext
} from 'amis-core';
import { Select } from 'amis-ui';
import CheckboxesControl from 'amis/lib/renderers/Form/Checkboxes';
import React from 'react';

export class FilterFormCheckboxes extends CheckboxesControl {
  static contextType = ScopedContext;

  constructor(props, context) {
    super(props);

    const scoped = context;
    scoped.registerComponent(this);

    this.state = {
      ...this.state,
      operator: ''
    };
  }

  componentDidMount() {
    const { name, data } = this.props;
    const operatorFieldName = `${name}_operator`;
    const operator = resolveVariable(operatorFieldName, data) || '';
    this.setState({ operator });
  }

  componentWillUnmount() {
    super.componentWillUnmount?.();
    const scoped = this.context;
    scoped.unRegisterComponent(this);
  }

  handleOperatorChange = (value) => {
    this.setState({ operator: value });
    const { onOperatorChange } = this.props;
    if (onOperatorChange) {
      onOperatorChange(value);
    }
  };

  columnsSplit(body) {
    const { showOperator, operators } = this.props;
    const original = super.columnsSplit(body);
    // 添加操作符选择
    if (showOperator && operators && operators.length > 0) {
      original.unshift(
        <Select
          className="filter-form-item-operator"
          value={this.state.operator}
          popOverContainer={() => document.body}
          options={operators}
          onChange={this.handleOperatorChange}
          simpleValue={true}
        ></Select>
      );
    }
    return original;
  }
}

registerOptionsControl({
  type: 'filter-form-checkboxes',
  isolateScope: true,
  component: FilterFormCheckboxes
});
