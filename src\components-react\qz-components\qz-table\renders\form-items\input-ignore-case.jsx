import {
  registerOptionsControl,
  resolveVariable,
  ScopedContext
} from 'amis-core';
import { Checkbox } from 'amis-ui';
import TextControl from 'amis/lib/renderers/Form/InputText';
import React from 'react';

export class CrudInputIngoreCase extends TextControl {
  static contextType = ScopedContext;

  constructor(props, context) {
    super(props);

    const scoped = context;
    scoped.registerComponent(this);

    this.state = {
      ...this.state,
      ignoreCase: false // 新增状态来控制是否忽略大小写
    };
  }

  componentDidMount() {
    const { name, data } = this.props;
    const ignoreFieldName = `${name}_ignoreCase`;
    const ignoreCase = resolveVariable(ignoreFieldName, data) || false;
    this.setState({ ignoreCase });
  }

  componentWillUnmount() {
    super.componentWillUnmount();
    const scoped = this.context;
    scoped.unRegisterComponent(this);
  }

  handleIgnoreCaseChange = (value) => {
    this.setState({ ignoreCase: value });
    const { onIgnoreCaseChange } = this.props;
    if (onIgnoreCaseChange) {
      onIgnoreCaseChange(value);
    }
  };

  renderBody(body) {
    const {
      classnames: cx,
      className,
      style,
      classPrefix: ns,
      disabled,
      readOnly,
      inputOnly,
      data,
      static: isStatic
    } = this.props;

    if (inputOnly) {
      return body;
    }

    const classNames = !isStatic
      ? cx(className, `${ns}TextControl`, {
          [`${ns}TextControl--withAddOn`]: true,
          'is-focused': this.state.isFocused,
          'is-disabled': disabled || readOnly
        })
      : cx(`${ns}TextControl`, {
          [`${ns}TextControl--withAddOn`]: true
        });

    return (
      <div className={classNames} style={style}>
        {body}
        <Checkbox
          value={this.state.ignoreCase}
          onChange={this.handleIgnoreCaseChange}
        >
          忽略大小写
        </Checkbox>
      </div>
    );
  }
}

registerOptionsControl({
  type: 'crud-input-ignore-case',
  isolateScope: true,
  component: CrudInputIngoreCase
});
