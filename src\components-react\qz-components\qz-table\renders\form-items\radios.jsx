import { registerOptionsControl, ScopedContext } from 'amis-core';
import RadiosControl from 'amis/lib/renderers/Form/Radios';

export class FilterFormRadios extends RadiosControl {
  static contextType = ScopedContext;

  constructor(props, context) {
    super(props);

    const scoped = context;
    scoped.registerComponent(this);
  }

  componentWillUnmount() {
    super.componentWillUnmount?.();
    const scoped = this.context;
    scoped.unRegisterComponent(this);
  }
}

registerOptionsControl({
  type: 'filter-form-radios',
  isolateScope: true,
  component: FilterFormRadios
});
