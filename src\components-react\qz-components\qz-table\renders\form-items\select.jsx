/* eslint-disable prefer-const */
import {
  CustomStyle,
  isEffectiveApi,
  registerOptionsControl,
  resolveVariable,
  ScopedContext,
  setThemeClassName,
  str2function
} from 'amis-core';
import SelectControl from 'amis/lib/renderers/Form/Select';
import React from 'react';
import cx from 'classnames';
import { Select } from 'amis-ui';

export class FilterFormSelect extends SelectControl {
  static contextType = ScopedContext;

  constructor(props, context) {
    super(props);

    const scoped = context;
    scoped.registerComponent(this);
    this.state = {
      ...this.state,
      operator: ''
    };
  }

  componentDidMount() {
    const { name, data } = this.props;
    const operatorFieldName = `${name}_operator`;
    const operator = resolveVariable(operatorFieldName, data) || '';
    this.setState({ operator });
  }

  handleOperatorChange = (value) => {
    this.setState({ operator: value });
    const { onOperatorChange } = this.props;
    if (onOperatorChange) {
      onOperatorChange(value);
    }
  };

  componentWillUnmount() {
    super.componentWillUnmount();
    const scoped = this.context;
    scoped.unRegisterComponent(this);
  }

  render() {
    let {
      autoComplete,
      searchable,
      showInvalidMatch,
      options,
      className,
      popoverClassName,
      style,
      loading,
      value,
      selectedOptions,
      multi,
      multiple,
      placeholder,
      id,
      classPrefix,
      classnames,
      creatable,
      inline,
      noResultsText,
      render,
      menuTpl,
      borderMode,
      selectMode,
      env,
      mobileUI,
      overlay,
      filterOption,
      showOperator,
      operators,
      ...rest
    } = this.props;
    const { classPrefix: ns, themeCss } = this.props;

    if (noResultsText) {
      noResultsText = render('noResultText', noResultsText);
    }

    return (
      <div
        className={cx(
          `${classPrefix}SelectControl`,
          className,
          multiple && showOperator && operators && operators.length > 0
            ? 'with-operator'
            : ''
        )}
        style={style}
      >
        {multiple && showOperator && operators && operators.length > 0 ? (
          <Select
            className="filter-form-item-operator"
            value={this.state.operator}
            popOverContainer={() => document.body}
            options={operators}
            onChange={this.handleOperatorChange}
            simpleValue={true}
          ></Select>
        ) : null}
        {['table', 'list', 'group', 'tree', 'chained', 'associated'].includes(
          selectMode
        ) ? (
          this.renderOtherMode()
        ) : (
          <Select
            {...rest}
            onAdd={this.handleOptionAdd}
            onEdit={this.handleOptionEdit}
            onDelete={this.handleOptionDelete}
            className={cx(
              setThemeClassName({
                ...this.props,
                name: 'selectControlClassName',
                id,
                themeCss: themeCss
              })
            )}
            popoverClassName={cx(
              popoverClassName,
              setThemeClassName({
                ...this.props,
                name: 'selectPopoverClassName',
                id,
                themeCss: themeCss
              })
            )}
            mobileUI={mobileUI}
            popOverContainer={
              mobileUI
                ? env?.getModalContainer
                : rest.popOverContainer || env.getModalContainer
            }
            borderMode={borderMode}
            placeholder={placeholder}
            multiple={multiple || multi}
            ref={this.inputRef}
            value={selectedOptions}
            options={options}
            filterOption={
              typeof filterOption === 'string'
                ? str2function(filterOption, 'options', 'inputValue', 'option')
                : filterOption
            }
            loadOptions={
              isEffectiveApi(autoComplete) ? this.lazyloadRemote : undefined
            }
            showInvalidMatch={showInvalidMatch}
            creatable={creatable}
            searchable={searchable || !!autoComplete}
            onChange={this.changeValue}
            onBlur={(e) => this.dispatchEvent('blur', e)}
            onFocus={(e) => this.dispatchEvent('focus', e)}
            loading={loading}
            noResultsText={noResultsText}
            renderMenu={menuTpl ? this.renderMenu : undefined}
            overlay={overlay}
          />
        )}
        <CustomStyle
          {...this.props}
          config={{
            themeCss: themeCss,
            classNames: [
              {
                key: 'selectControlClassName',
                weights: {
                  focused: {
                    suf: '.is-opened:not(.is-mobile)'
                  },
                  disabled: {
                    suf: '.is-disabled'
                  }
                }
              },
              {
                key: 'selectPopoverClassName',
                weights: {
                  default: {
                    suf: ` .${ns}Select-option`
                  },
                  hover: {
                    suf: ` .${ns}Select-option.is-highlight`
                  },
                  focused: {
                    inner: `.${ns}Select-option.is-active`
                  }
                }
              }
            ],
            id: id
          }}
          env={env}
        />
      </div>
    );
  }
}

registerOptionsControl({
  type: 'filter-form-select',
  isolateScope: true,
  component: FilterFormSelect
});
