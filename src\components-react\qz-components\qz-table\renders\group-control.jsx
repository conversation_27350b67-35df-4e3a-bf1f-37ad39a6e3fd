import { FormItem } from 'amis-core';
import { addSchema2Toolbar, deepRemove } from 'amis-editor';
import { Icon } from 'amis-ui';
import { cloneDeep } from 'lodash-es';
import React from 'react';
import Sortable from 'sortablejs';
import '../styles/group-control.less';

class GroupControl extends React.Component {
  constructor(props) {
    super(props);

    const { manager, nodeId } = this.props;
    const store = manager.store;
    const CRUDNode = store.getNodeById(nodeId);
    const CRUDSchema = CRUDNode?.schema;
    const columns = CRUDSchema?.columns || [];

    this.columns = columns;
    this.state = {
      fieldList: cloneDeep(props.value || [])
    };

    this.addGroupField = this.addGroupField.bind(this);
    this.deleteGroupField = this.deleteGroupField.bind(this);
    this.dragRef = this.dragRef.bind(this);
  }

  dragRef(el) {
    el && this.initDragging(el);
  }

  initDragging(el) {
    this.sortable = new Sortable(el, {
      group: `group-control-drag-bar`,
      animation: 150,
      handle: `.group-control-drag-bar`,
      ghostClass: `.group-control-drag-bar--dragging`,
      onEnd: (e) => {
        if (e.newIndex === e.oldIndex) {
          return;
        }

        const parent = e.to;
        if (e.oldIndex < parent.childNodes.length - 1) {
          parent.insertBefore(
            e.item,
            parent.childNodes[
              e.oldIndex > e.newIndex ? e.oldIndex + 1 : e.oldIndex
            ]
          );
        } else {
          parent.appendChild(e.item);
        }

        this.swapFieldPosition(e.oldIndex, e.newIndex);
      }
    });
  }

  swapFieldPosition(oldIndex, newIndex) {
    const fieldList = cloneDeep(this.state.fieldList);
    const [movedField] = fieldList.splice(oldIndex, 1);
    fieldList.splice(newIndex, 0, movedField);
    this.setState({ fieldList }, () => {
      this.handleFieldListChange();
    });
  }

  destroyDragging() {
    this.sortable && this.sortable.destroy();
  }

  handleFieldListChange() {
    const fieldList = cloneDeep(this.state.fieldList);
    const { manager, nodeId, onChange } = this.props;
    const store = manager.store;
    const CRUDNode = store.getNodeById(nodeId);
    const CRUDSchema = CRUDNode?.schema;

    if (fieldList.length) {
      const exists = CRUDSchema?.headerToolbar?.[0].items?.[1]?.body?.some(
        (item) => item.type === 'crud-group-selector'
      );
      if (!exists) {
        addSchema2Toolbar(
          CRUDSchema,
          { type: 'crud-group-selector' },
          'header',
          'right'
        );
      }
    } else {
      deepRemove(
        CRUDSchema.headerToolbar,
        (item) => item.type === 'crud-group-selector'
      );
    }

    onChange(fieldList);
  }

  addGroupField(columnName) {
    const column = this.columns.find((col) => col.name === columnName);
    if (!column) {
      console.warn(`Column with name ${columnName} not found.`);
      return;
    }
    const fieldList = cloneDeep(this.state.fieldList);
    fieldList.push({ label: column.title, name: column.name });
    this.setState({ fieldList }, () => {
      this.handleFieldListChange();
    });
  }

  deleteGroupField(index) {
    const fieldList = cloneDeep(this.state.fieldList);
    fieldList.splice(index, 1);
    this.setState({ fieldList }, () => {
      this.handleFieldListChange();
    });
  }
  render() {
    const { render } = this.props;

    return (
      <div className="group-control">
        <div className="group-control-dropdown">
          {render('dropdown', {
            type: 'dropdown-button',
            label: '添加可分组字段',
            buttons: this.columns.map((column) => ({
              label: column.title,
              disabled: this.state.fieldList.some(
                (field) => field.name === column.name
              ),
              addGroupField: this.addGroupField,
              onEvent: {
                click: {
                  actions: [
                    {
                      actionType: 'custom',
                      script: `context.props.addGroupField('${column.name}')`
                    }
                  ]
                }
              }
            }))
          })}
        </div>
        <div ref={this.dragRef} className="group-control-content">
          {this.state.fieldList.map((field, index) => (
            <div key={index} className="group-control-item">
              <div className="group-control-itemContent">
                <span className="group-control-drag-bar">
                  <Icon icon="drag" className="fa fa-arrows-alt" />
                </span>
                <span className="group-control-itemLabel">{field.label}</span>
              </div>
              <div className="group-control-itemAction">
                <span
                  className="group-control-itemDelete"
                  onClick={() => this.deleteGroupField(index)}
                >
                  &times;
                </span>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  componentWillUnmount() {
    this.destroyDragging();
  }
}

class GroupControlRenderer extends GroupControl {}

FormItem({
  type: 'crud-group-control'
})(GroupControlRenderer);
