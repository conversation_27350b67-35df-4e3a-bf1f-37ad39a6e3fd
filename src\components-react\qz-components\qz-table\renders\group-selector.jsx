import { registerRenderer, RootClose } from 'amis-core';
import { Icon } from 'amis-ui';
import React from 'react';

class GroupSelector extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      isOpen: false
    };
    this.toggle = this.toggle.bind(this);
    this.doGroup = this.doGroup.bind(this);
    this.close = this.close.bind(this);
  }

  toggle(e) {
    e.preventDefault();

    this.setState({
      isOpened: !this.state.isOpened
    });
  }

  close() {
    this.setState({
      isOpened: false
    });
  }

  async doGroup(groupField) {
    const { env, crudSchema, store } = this.props;
    const api = crudSchema.groupConfig?.api;
    if (!api) {
      env.notify('error', '分组配置未设置API');
      return;
    }

    env
      .fetcher(api, {
        group: groupField.name,
        query: store.filterData
      })
      .then((res) => {
        const groupResult = res.data || [];
        store.setGroupField(groupField);
        store.setGroupResult(groupResult);
        this.close();
      })
      .catch((err) => {
        console.error(err);
        env.notify('error', `分组请求失败: ${err.message}`);
      });
  }

  renderOuter(groupFieldName) {
    const { crudSchema } = this.props;
    const groupFieldList = crudSchema.groupConfig?.fieldList || [];

    return (
      <RootClose disabled={!this.state.isOpened} onRootClose={this.close}>
        {(ref) => (
          <div ref={ref} className="GroupSelector-outer">
            {groupFieldList.length > 0 ? (
              <ul className="GroupSelector-itemList">
                {groupFieldList.map((item, index) => (
                  <li
                    key={index}
                    className={`GroupSelector-item ${groupFieldName === item.name ? 'is-actived' : ''}`}
                    onClick={() => this.doGroup(item)}
                  >
                    <span className="GroupSelector-itemLabel">
                      按 {item.label} 分组
                    </span>
                  </li>
                ))}
              </ul>
            ) : (
              <div className="GroupSelector-empty">暂无分组配置 </div>
            )}
          </div>
        )}
      </RootClose>
    );
  }

  render() {
    const { classnames: cx, btnClassName, store } = this.props;
    const groupField = store.groupField || {};

    const button = (
      <button
        className={cx(
          'Button',
          btnClassName,
          'Button--iconOnly',
          'Button--default',
          'Button--size-default'
        )}
        onClick={this.toggle}
      >
        <Icon
          icon="fa fa-list"
          className={`icon ${groupField.name ? 'is-actived' : ''} GroupSelector-button`}
        />
      </button>
    );
    return (
      <div className="GroupSelector-wrapper">
        {button}
        {this.state.isOpened ? this.renderOuter(groupField.name) : null}
      </div>
    );
  }
}

registerRenderer({
  type: 'crud-group-selector',
  component: GroupSelector
});
