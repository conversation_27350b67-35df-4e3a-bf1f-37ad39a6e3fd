import { FormItem } from 'amis-core';
import React from 'react';
import Sortable from 'sortablejs';
import { Button, Modal } from 'amis-ui';
import '../styles/filter-form-control.less';

class CrudOperatorControlRender extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      operators: [],
      showModal: false,
      editingIndex: -1,
      editingOperator: { value: '', label: '' }
    };

    this.sortableRef = React.createRef();
    this.sortableInstance = null;
  }

  componentDidMount() {
    this.initOperators();
    this.initSortable();
  }

  componentWillUnmount() {
    this.destroySortable();
  }

  componentDidUpdate(prevProps, prevState) {
    if (prevProps.value !== this.props.value && this.props.value) {
      this.initOperators();
    }
  }

  destroySortable() {
    if (this.sortableInstance) {
      this.sortableInstance.destroy();
      this.sortableInstance = null;
    }
  }

  initOperators() {
    this.setState({ operators: this.props.value || [] });
  }

  initSortable = () => {
    if (this.sortableRef.current) {
      this.sortableInstance = Sortable.create(this.sortableRef.current, {
        animation: 150,
        handle: '.drag-handle',
        onEnd: (evt) => {
          const { oldIndex, newIndex } = evt;
          if (oldIndex !== newIndex) {
            const newOperators = [...this.state.operators];
            // 获取被拖拽的项
            const draggedItem = newOperators[oldIndex];
            newOperators.splice(oldIndex, 1);
            newOperators.splice(newIndex, 0, draggedItem);
            this.updateOperators(newOperators);
          }
        }
      });
    }
  };

  updateOperators = (operators) => {
    const { onChange } = this.props;
    this.setState({ operators }, () => {
      onChange(operators);
    });
  };

  addOperator = () => {
    this.setState({
      showModal: true,
      editingIndex: this.state.operators.length,
      editingOperator: { value: '', label: '' }
    });
  };

  editOperator = (index) => {
    this.setState({
      showModal: true,
      editingIndex: index,
      editingOperator: { ...this.state.operators[index] }
    });
  };

  deleteOperator = (index) => {
    const newOperators = this.state.operators.filter((_, i) => i !== index);
    this.updateOperators(newOperators);
  };

  saveOperator = () => {
    const { editingIndex, editingOperator, operators } = this.state;

    if (!editingOperator.value.trim() || !editingOperator.label.trim()) {
      return;
    }

    const newOperators = [...operators];
    if (editingIndex === operators.length) {
      // 添加新操作符
      newOperators.push(editingOperator);
    } else {
      // 修改现有操作符
      newOperators[editingIndex] = editingOperator;
    }

    this.updateOperators(newOperators);
    this.cancelEdit();
  };

  cancelEdit = () => {
    this.setState({
      showModal: false,
      editingIndex: -1,
      editingOperator: { value: '', label: '' }
    });
  };

  handleInputChange = (field, value) => {
    this.setState({
      editingOperator: {
        ...this.state.editingOperator,
        [field]: value
      }
    });
  };

  render() {
    const { operators, showModal, editingIndex, editingOperator } = this.state;
    const { disabled, className } = this.props;

    const styles = {
      container: {
        border: '1px solid #d9d9d9',
        borderRadius: '6px',
        padding: '12px'
      },
      operatorList: {
        minHeight: '100px',
        marginBottom: '12px'
      },
      operatorItem: {
        display: 'flex',
        alignItems: 'center',
        padding: '8px 12px',
        marginBottom: '8px',
        background: 'white',
        border: '1px solid #e8e8e8',
        borderRadius: '4px'
      },
      dragHandle: {
        cursor: 'move',
        color: '#999',
        marginRight: '8px',
        fontSize: '14px'
      },
      operatorContent: {
        flex: 1,
        display: 'flex',
        alignItems: 'center'
      },
      operatorLabel: {
        fontWeight: '500',
        color: '#333',
        marginRight: '8px'
      },
      operatorValue: {
        color: '#666',
        background: '#f5f5f5',
        padding: '2px 6px',
        borderRadius: '3px',
        fontFamily: 'monospace',
        fontSize: '12px'
      },
      operatorActions: {
        display: 'flex',
        gap: '8px'
      },
      btn: {
        padding: '4px 8px',
        border: '1px solid #d9d9d9',
        borderRadius: '3px',
        background: 'white',
        cursor: 'pointer',
        fontSize: '12px',
        lineHeight: '1'
      },
      btnPrimary: {
        background: '#1890ff',
        borderColor: '#1890ff',
        color: 'white'
      },
      addButton: {
        width: '100%',
        padding: '8px',
        border: '1px dashed #d9d9d9',
        borderRadius: '4px',
        background: 'transparent',
        color: '#666',
        cursor: 'pointer',
        fontSize: '12px'
      },
      emptyHint: {
        textAlign: 'center',
        color: '#999',
        fontSize: '12px',
        padding: '20px'
      }
    };

    const isEditing = editingIndex !== -1 && editingIndex < operators.length;
    const modalTitle = isEditing ? '编辑操作符' : '添加操作符';

    return (
      <div className={className} style={styles.container}>
        <div style={styles.operatorList} ref={this.sortableRef}>
          {operators.map((operator, index) => (
            <div key={operator.value} style={styles.operatorItem}>
              <span style={styles.dragHandle} className="drag-handle">
                ⋮⋮
              </span>
              <div style={styles.operatorContent}>
                <span style={styles.operatorLabel}>{operator.label}</span>
                <span style={styles.operatorValue}>{operator.value}</span>
              </div>
              {!disabled && (
                <div style={styles.operatorActions}>
                  <button
                    style={styles.btn}
                    onClick={() => this.editOperator(index)}
                  >
                    编辑
                  </button>
                  <button
                    style={styles.btn}
                    onClick={() => this.deleteOperator(index)}
                  >
                    删除
                  </button>
                </div>
              )}
            </div>
          ))}

          {operators.length === 0 && (
            <div style={styles.emptyHint}>暂无操作符，点击下方按钮添加</div>
          )}
        </div>

        {!disabled && (
          <button style={styles.addButton} onClick={this.addOperator}>
            + 添加操作符
          </button>
        )}

        <Modal
          show={showModal}
          onHide={this.cancelEdit}
          size="sm"
          closeOnEsc
          closeOnOutside
        >
          <Modal.Header onClose={this.cancelEdit}>
            <Modal.Title>{modalTitle}</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <div className="filter-form-custom-fields">
              <div className="filter-form-field">
                <label>操作符值</label>
                <input
                  type="text"
                  placeholder="请输入操作符值"
                  value={editingOperator.value}
                  onChange={(e) =>
                    this.handleInputChange('value', e.target.value)
                  }
                  className="form-control"
                />
              </div>
              <div className="filter-form-field">
                <label>操作符标题</label>
                <input
                  type="text"
                  placeholder="请输入操作符标题"
                  value={editingOperator.label}
                  onChange={(e) =>
                    this.handleInputChange('label', e.target.value)
                  }
                  className="form-control"
                />
              </div>
            </div>
          </Modal.Body>
          <Modal.Footer>
            <Button onClick={this.cancelEdit}>取消</Button>
            <Button level="primary" onClick={this.saveOperator}>
              保存
            </Button>
          </Modal.Footer>
        </Modal>
      </div>
    );
  }
}

FormItem({
  type: 'crud-operator-control',
  renderLabel: false,
  wrap: false
})(CrudOperatorControlRender);
