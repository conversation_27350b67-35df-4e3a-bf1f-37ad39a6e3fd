import { <PERSON>I<PERSON>, unRegisterRenderer } from 'amis-core';
import { addSchema2Toolbar, DSFeatureEnum, JSONPipeIn } from 'amis-editor';
import { CRUDToolbarControl } from 'amis-editor/lib/renderer/crud2-control/CRUDToolbarControl';
import { cloneDeep } from 'lodash-es';
import React from 'react';

class QzTableToolbarControlRender extends CRUDToolbarControl {
  async handleAddAction(type) {
    this.setState({ loading: true });
    const { onChange, data: ctx, nodeId, manager, builder } = this.props;
    const options = this.state.options.concat();
    const node = manager.store.getNodeById(nodeId);
    const CRUDSchemaID = node?.schema?.id;
    let scaffold;

    switch (type) {
      case 'Insert':
        scaffold = await builder.buildInsertSchema(
          {
            feat: DSFeatureEnum.Insert,
            renderer: 'crud',
            inScaffold: false,
            schema: ctx,
            scaffoldConfig: {
              insertFields: (ctx?.columns ?? [])
                .filter((item) => item.type !== 'operation')
                .map((item) => ({
                  inputType: item.type ?? 'input-text',
                  name: item.name,
                  label: item.title
                })),
              insertApi: ''
            }
          },
          CRUDSchemaID
        );
        break;
      case 'BulkEdit':
        scaffold = await builder.buildBulkEditSchema(
          {
            feat: DSFeatureEnum.BulkEdit,
            renderer: 'crud',
            inScaffold: false,
            schema: ctx,
            scaffoldConfig: {
              bulkEditFields: (ctx?.columns ?? [])
                .filter((item) => item.type !== 'operation')
                .map((item) => ({
                  inputType: item.type ?? 'input-text',
                  name: item.name,
                  label: item.title
                })),
              bulkEdit: ''
            }
          },
          CRUDSchemaID
        );
        break;
      case 'BulkDelete':
        scaffold = await builder.buildCRUDBulkDeleteSchema(
          {
            feat: DSFeatureEnum.BulkDelete,
            renderer: 'crud',
            inScaffold: false,
            schema: ctx,
            scaffoldConfig: {
              bulkDeleteApi: ''
            }
          },
          CRUDSchemaID
        );
        break;
      default:
        scaffold = {
          type: 'button',
          label: '按钮',
          behavior: 'custom',
          className: 'm-r-xs',
          onEvent: {
            click: {
              actions: []
            }
          }
        };
    }

    if (!scaffold) {
      this.setState({ loading: false });
      return;
    }

    const headerToolbarSchema = cloneDeep(ctx.headerToolbar);
    const actionSchema = JSONPipeIn({ ...scaffold });

    options.push({
      label: this.getOptionLabel(actionSchema, type),
      value: type,
      nodeId: actionSchema.$$id,
      pristine: actionSchema
    });

    this.setState({ options, loading: false }, () => {
      const fakeCRUD = { headerToolbar: headerToolbarSchema };
      /** 自适应添加操作按钮到顶部工具栏 */
      addSchema2Toolbar(fakeCRUD, actionSchema, 'header', 'right');
      onChange?.(fakeCRUD.headerToolbar, true, true);
    });
  }
}

unRegisterRenderer('ae-crud-toolbar-control');
FormItem({
  type: 'ae-crud-toolbar-control',
  renderLabel: false,
  wrap: false
})(QzTableToolbarControlRender);
