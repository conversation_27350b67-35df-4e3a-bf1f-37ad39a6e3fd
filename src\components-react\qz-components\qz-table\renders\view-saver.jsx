import { registerRenderer } from 'amis-core';
import { ActionRenderer } from 'amis/lib/renderers/Action';
import React from 'react';
import '../styles/view-saver.less';

class ViewSaver extends ActionRenderer {
  constructor(props) {
    super(props);
    this.state = {
      isDropdownOpen: false
    };
    this.dropdownRef = React.createRef();
  }

  componentDidMount() {
    document.addEventListener('click', this.handleClickOutside);
  }

  componentWillUnmount() {
    document.removeEventListener('click', this.handleClickOutside);
  }

  handleClickOutside = (event) => {
    if (
      this.dropdownRef.current &&
      !this.dropdownRef.current.contains(event.target)
    ) {
      this.setState({ isDropdownOpen: false });
    }
  };

  toggleDropdown = () => {
    this.setState({ isDropdownOpen: !this.state.isDropdownOpen });
  };

  handleSaveView = () => {
    // 保存当前视图的逻辑，暂时不实现
    // TODO 实现保存视图的逻辑
    this.setState({ isDropdownOpen: false });
  };

  handleSaveAsNewView = () => {
    // 另存为新视图的逻辑，暂时不实现
    // TODO 实现另存为新视图的逻辑
    this.setState({ isDropdownOpen: false });
  };

  render() {
    const { isDropdownOpen } = this.state;

    return (
      <div className="ApiTable-view-saver" ref={this.dropdownRef}>
        <button className="btn-main" onClick={this.handleSaveView}>
          保存视图
        </button>
        <div className="split-line"></div>
        <button className="btn-more" onClick={this.toggleDropdown}>
          <i className="fa fa-caret-down"></i>
        </button>

        {isDropdownOpen && (
          <div className="view-saver-dropdown">
            <div className="dropdown-item" onClick={this.handleSaveAsNewView}>
              另存为新视图
            </div>
          </div>
        )}
      </div>
    );
  }
}

registerRenderer({
  type: 'view-saver',
  component: ViewSaver
});
