import { types } from 'mobx-state-tree';
import { CRUDStore, findTreeIndex, registerStore } from 'amis-core';

// 继承CRUD的store配置，添加业务需要的属性
export const QzTableStore = CRUDStore.named('QzTableStore')
  .props({
    filterFormVisible: types.optional(types.boolean, false), // 是否显示筛选表单
    filterFormData: types.optional(types.frozen(), {}), // Using frozen to allow any JS value
    groupField: types.optional(types.frozen(), {}),
    groupResult: types.optional(types.array(types.frozen()), []),
    curGroup: types.optional(types.frozen(), ''),
    selectedView: types.optional(types.frozen(), {}) // 当前选中的视图
  })
  .views((self) => {
    return {
      get eventContext() {
        const context = {
          items: self.items.concat(),
          selectedItems: self.selectedItems.concat(),
          unSelectedItems: self.unSelectedItems.concat(),
          selectedView: self.selectedView,
          selectedIndexes: self.selectedItems.map(
            (item) =>
              findTreeIndex(
                self.items,
                (i) => (item.__pristine || item) === (i.__pristine || i)
              )?.join('.') || '-1'
          )
        };

        return context;
      }
    };
  })
  .actions((self) => ({
    toggleFilterForm() {
      self.filterFormVisible = !self.filterFormVisible;
    },
    setCurGroup(group) {
      self.curGroup = group;
    },
    setGroupField(field) {
      self.groupField = field;
    },
    setGroupResult(result) {
      self.groupResult = result;
    },
    cancelGroup() {
      self.curGroup = '';
      self.groupField = {};
      self.groupResult = [];
    },
    changeFilterFormData(name, value) {
      // Create a new copy of the object to ensure proper reactivity
      self.filterFormData = {
        ...self.filterFormData,
        [name]: value
      };
    },
    bulkChangeFilterFormData(data) {
      // Create a new merged object
      self.filterFormData = {
        ...self.filterFormData,
        ...data
      };
    },
    resetFilterFormData() {
      // 将 filterFormData 的每个key的值重置为 undefined
      self.filterFormData = {};
    },
    removeFilterTag(name) {
      // 将对应name的filterFormData删掉
      // eslint-disable-next-line no-unused-vars
      const { [name]: _, ...rest } = self.filterFormData;
      self.filterFormData = rest;
    },
    setSelectedView(view) {
      self.selectedView = view;
    }
  }));

registerStore(QzTableStore);
