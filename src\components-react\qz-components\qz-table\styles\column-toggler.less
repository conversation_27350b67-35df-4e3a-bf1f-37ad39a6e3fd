.cxd-ColumnToggler > .cxd-Button {
  min-width: var(--button-size-default-height);
}
.cxd-ColumnToggler .cxd-Checkbox {
  margin: 0;
}
.cxd-ColumnToggler-caret {
  margin-top: -3px;
}
.cxd-ColumnToggler-modal-header,
.cxd-ColumnToggler-modal-content,
.cxd-ColumnToggler-modal-footer {
  padding: 0;
}
.cxd-ColumnToggler-wrapper {
  position: absolute;
  z-index: 1000;
  top: 100%;
  left: auto;
  right: 0;
  margin: 0;
  background: var(--DropDown-menu-bg);
  padding: 5px;
  border: var(--DropDown-menu-borderWidth) solid
    var(--DropDown-menu-borderColor);
  border-radius: var(--DropDown-menu-borderRadius);
  box-shadow: var(--DropDown-menu-boxShadow);
  min-width: var(--DropDown-menu-minWidth);
  text-align: left;
  max-height: 500px;
  overflow: auto;
}
.cxd-ColumnToggler-menuContainer {
  list-style: none;
  padding: 0;
  margin: 0;
}
.cxd-ColumnToggler-modal-content .cxd-ColumnToggler-menuItem {
  float: none;
  width: 100%;
  background-color: #fff;
  margin: 0;
  padding: 5px 10px;
  height: auto;
  > label {
    width: auto;
  }
  &:hover {
    background-color: var(--ColumnToggler-item-backgroundColor-onHover);
  }
}
.cxd-ColumnToggler-menuItem {
  padding: 5px 10px;
  height: auto;
}
.cxd-ColumnToggler-menuItem:hover {
  background-color: var(--ColumnToggler-item-backgroundColor-onHover);
}
.cxd-ColumnToggler-menuItem-dragBar {
  margin-right: var(--gap-xs);
  svg.icon {
    top: 0;
  }
  &:hover {
    cursor: move;
  }
}
.cxd-ColumnToggler-menuItem-title {
  font-size: var(--checkbox-default-checked-default-fontSize);
  color: var(--checkbox-default-checked-default-text-color);
}