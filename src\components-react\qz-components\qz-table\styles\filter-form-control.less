.FilterFormControl-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 20px;
  font-size: 14px;

  th,
  td {
    padding: 8px 12px;
    border: 1px solid #e8e8e8;
    text-align: left;
  }

  th {
    background-color: #f5f7fa;
    font-weight: 500;
    color: #333;
  }

  tr:hover {
    background-color: #f9f9f9;
  }

  .text-center {
    text-align: center;
    color: #999;
    padding: 16px;
  }

  .icon {
    cursor: pointer;
  }
}

.filter-form-card-container {
  padding: 8px 0;
}

.filter-form-card-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.filter-form-card {
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  background-color: #fff;
  transition: all 0.3s;
  cursor: pointer;

  &:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
    border-color: #d0d0d0;
  }
}

.filter-form-card-ghost {
  opacity: 0.5;
  background-color: #f5f5f5;
  border: 1px dashed #ccc;
}

.filter-form-card-content {
  padding: 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.filter-form-card-drag-handle {
  display: flex;
  align-items: center;
  margin-right: 8px;
  color: #999;
  cursor: move;

  .drag-icon {
    width: 1rem;
    height: 1rem;
    font-size: 14px;

    &:active {
      cursor: grabbing;
    }
  }
}

.filter-form-card-info {
  flex: 1;
  overflow: hidden;
}

.filter-form-card-label {
  font-weight: 500;
  font-size: 14px;
  margin-bottom: 4px;
  color: #333;
}

.filter-form-card-detail {
  font-size: 12px;
  color: #666;
  display: flex;
  flex-direction: column;
}

.filter-form-card-name,
.filter-form-card-type {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.filter-form-card-actions {
  display: flex;
  align-items: center;
  button {
    padding-left: 0;
    padding-right: 0;
    > svg {
      width: 1rem;
      height: 1rem;
    }
    > svg.icon-column-delete {
      top: -2px;
    }
  }
}

.filter-form-card-actions-bar {
  margin-top: 16px;
  display: flex;
  justify-content: space-between;

  .icon {
    font-size: 12px;
    position: relative;
    top: 1px;
  }

  .m-r-xs {
    margin-right: 4px;
  }

  .m-l-sm {
    margin-left: 8px;
  }
}

.filter-form-empty {
  text-align: center;
  padding: 20px 0;
  color: #999;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.filter-form-modal {
  .modal-body {
    max-height: 400px;
    overflow-y: auto;
  }
}

.filter-form-columns-list {
  display: flex;
  flex-direction: column;
  gap: 10px;

  .filter-form-column-item {
    padding: 8px;
    border-radius: 4px;
    transition: background-color 0.2s;

    &:hover {
      background-color: #f9f9f9;
    }
  }
}

.filter-form-custom-fields {
  display: flex;
  flex-direction: column;
  gap: 15px;

  .filter-form-field {
    display: flex;
    flex-direction: column;
    gap: 5px;

    label {
      font-weight: 500;
      font-size: 14px;
    }

    .form-control {
      padding: 8px 12px;
      border: 1px solid #d9d9d9;
      border-radius: 4px;
      font-size: 14px;
      transition: all 0.3s;

      &:focus {
        border-color: #40a9ff;
        outline: none;
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
      }
    }

    .filter-form-field-help {
      font-size: 12px;
      color: #999;
      margin-top: 4px;
    }

    .filter-form-field-warning {
      font-size: 12px;
      color: #fa8c16;
      margin-top: 4px;
      display: flex;
      align-items: flex-start;

      .warning-icon {
        margin-right: 4px;
        position: relative;
        top: 2px;
        width: 1rem;
      }
    }
  }
}
