.ApiTable__filter-form-wrapper {
  display: flex;
  max-height: 330px;
  flex-direction: column;
  overflow: hidden;
  margin-bottom: 10px;
  background-color: var(--Table-thead-bg);
  padding: 0;
  border-radius: 5px;
  .ApiTable__filter-form-body {
    flex: auto;
    overflow-y: auto;
    overflow-x: hidden;
    margin: 0;
    padding: 20px;
    padding-bottom: 0;
    box-sizing: border-box;
    width: 100%;
    // 采用grid布局，分为2列，每列每行间隔10px
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-gap: 10px;
  }
  .ApiTable__filter-form-footer {
    flex: none;
    text-align: right;
    padding: 10px 20px 15px;
  }
  .cxd-ApiTable__filter-form-item,
  .ApiTable__filter-form-item {
    display: flex;
    background-color: #fff;
    border-radius: 3px;
    border: 1px solid @border-light-color;
    margin-bottom: 0;
    .cxd-Form-label {
      width: var(--Form-label-width, 100px);
      flex: none;
      border-right: 1px solid @border-light-color;
      display: flex;
      align-items: center;
      text-align: left;
      margin: 0;
      padding: 0 10px;
    }
    .cxd-Form-control {
      flex: auto;
      min-width: 0;
      .cxd-TextControl-input,
      .cxd-DatePicker,
      .cxd-DateRangePicker,
      .cxd-NestedSelect,
      .cxd-Select {
        border: none;
      }
      .cxd-TextControl-input + .cxd-Checkbox {
        margin-top: 6px;
      }
      .cxd-Checkbox {
        display: inline-block;
        margin-top: 0;
      }
      &.cxd-RadiosControl,
      &.cxd-CheckboxesControl,
      &.cxd-SwitchControl {
        padding-top: 0;
        padding-left: 10px;
        height: var(--input-size-default-height);
        display: flex;
        align-items: center;
      }
      .filter-form-item-operator {
        border-right: 1px solid @border-light-color;
        border-radius: 0;
        margin-right: 10px;
      }
    }
    .cxd-Form-control.with-operator {
      display: flex;
      align-items: center;
      .filter-form-item-operator {
        flex: none;
      }
      .filter-form-item-operator + div {
        flex: auto;
      }
    }
  }
}
