.GroupSelector-wrapper {
  position: relative;
  display: inline-block;
  .GroupSelector-button.is-actived {
    color: @theme-blue;
  }
  .GroupSelector-outer {
    position: absolute;
    z-index: 1000;
    top: 100%;
    left: auto;
    right: 0;
    margin: 0;
    background: var(--DropDown-menu-bg);
    padding: 5px;
    border: var(--DropDown-menu-borderWidth) solid
      var(--DropDown-menu-borderColor);
    border-radius: var(--DropDown-menu-borderRadius);
    box-shadow: var(--DropDown-menu-boxShadow);
    min-width: var(--DropDown-menu-minWidth);
    text-align: left;
    max-height: 500px;
    overflow: auto;
  }
  .GroupSelector-itemList {
    list-style: none;
    padding: 0;
    margin: 0;
    width: 100%;
    .GroupSelector-item {
      width: 100%;
      background-color: #fff;
      margin: 0;
      padding: 5px 10px;
      height: auto;
      color: var(--ColumnToggler-title-fontColor);
      border-radius: 0.125rem;
      font-size: var(--fontSizeSm);
      line-height: var(--ColumnToggler-lineHeight);
      cursor: pointer;
      &:hover {
        background-color: var(--ColumnToggler-item-backgroundColor-onHover);
      }
      &.is-actived {
        color: @theme-blue;
        background-color: @bg-grey-color;
      }
    }
  }
}