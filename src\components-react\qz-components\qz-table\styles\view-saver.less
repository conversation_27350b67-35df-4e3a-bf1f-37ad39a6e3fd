.ApiTable-view-saver {
  position: relative;
  display: inline-flex;
  align-items: center;
  background: #fff;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  height: 32px;

  .btn-main {
    padding: 0 12px;
    border: none;
    background: none;
    color: #333;
    font-size: 14px;
    height: 100%;
    cursor: pointer;
    outline: none;
    white-space: nowrap;

    &:hover {
      background: #f5f5f5;
    }

    &:active {
      background: #e6f7ff;
    }
  }

  .split-line {
    width: 1px;
    height: 20px;
    background: #d9d9d9;
    margin: 0;
  }

  .btn-more {
    padding: 0 8px;
    border: none;
    background: none;
    color: #666;
    height: 100%;
    cursor: pointer;
    outline: none;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      background: #f5f5f5;
    }

    &:active {
      background: #e6f7ff;
    }

    i {
      font-size: 12px;
    }
  }

  .view-saver-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    margin-top: 4px;
    background: #fff;
    border: 1px solid #d9d9d9;
    border-top: none;
    border-radius: 0 0 4px 4px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    min-width: 120px;

    .dropdown-item {
      padding: 8px 12px;
      color: #333;
      font-size: 14px;
      cursor: pointer;
      white-space: nowrap;

      &:hover {
        background: #f5f5f5;
      }

      &:active {
        background: #e6f7ff;
      }
    }
  }

  // 当下拉菜单打开时，调整主容器的边框样式
  &:has(.view-saver-dropdown) {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
  }

  // 响应式设计
  @media (max-width: 768px) {
    .btn-main {
      padding: 0 8px;
      font-size: 13px;
    }

    .btn-more {
      padding: 0 6px;
    }
  }
}
