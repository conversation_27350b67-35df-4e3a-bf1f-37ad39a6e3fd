.crud-view {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  height: 100%;
  padding: 20px;
  padding-top: 0;
  .custom-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-right: 8px;
    height: 24px;
    line-height: 24px;
    width: 0;
    &__label {
      flex: 1 1 auto;
      min-width: 50px;
    }
    &__count {
      max-width: 130px;
      display: block;
      padding-left: 10px;
    }
    &__edit {
      font-size: 16px;
      font-weight: 800;
      display: none;
    }
    &--static {
      &:hover {
        .custom-tree-node__edit {
          display: block !important;
          color: @theme-blue;
        }
        .custom-tree-node__count {
          display: none !important;
        }
      }
    }
  }
  .el-button {
    padding: 9px 0;
    width: 70px;
    margin-left: 10px;
    min-width: 0;
  }
}
.options-popover {
  &__footer {
    margin-top: 10px;
    display: flex;
    justify-content: flex-end;
  }
  &__delete {
    i {
      color: @warning-color;
      margin-right: 10px;
      font-size: 14px;
    }
  }
  &__edit {
    display: flex;
    align-items: center;
    &__label {
      width: 100px;
    }
  }
}

.crud-view__search-box {
  position: relative;
  flex: none;
  margin-bottom: 10px;
}
.crud-view__search-inner {
  display: inline-block;
  width: 100%;
  height: 32px;
  line-height: 32px;
  padding-left: 15px;
  padding-right: 30px;
  border-radius: 4px;
  background-color: #fff;
  color: @text-regular-color;
  border: 1px solid @shallow-border-color;
  box-sizing: border-box;
  transition: border-color 0.3s;
  outline: none;
  &:hover {
    border-color: @border-color;
  }
  &:focus {
    border-color: @theme-blue;
  }
}
.crud-view__search-suffix {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  color: @text-regular-color;
  font-size: 16px;
  cursor: pointer;
  transition: color 0.3s;
}
.crud-view__tree-selector {
  flex: auto;
  .cxd-Tree-item:hover,
  .cxd-Tree-item.is-checked,
  .cxd-Tree-itemLabel.is-checked {
    background: @bg-grey-color;
    border-radius: 5px;
    .cxd-Tree-itemLabel-item {
      background: inherit;
    }
    .cxd-Tree-itemText {
      color: @theme-blue;
    }
  }
  .cxd-Tree-itemLabel {
    height: 40px;
    padding: 0;
    margin: 0;
  }
  .cxd-Tree-itemArrowPlaceholder {
    width: var(--Tree-itemArrowWidth);
  }
  .cxd-Tree-itemArrow {
    font-family: element-icons !important;
    transition: transform var(--animation-duration);
    margin: 0;
    width: var(--Tree-itemArrowWidth);
    & .icon {
      display: none;
    }
    &::before {
      content: '\e790';
    }
    &.is-folded {
      transform: rotate(-90deg);
    }
  }
}
