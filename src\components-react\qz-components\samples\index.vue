<template>
  <api-table
    ref="table"
    border
    table-id="samples"
    :data-source="tableData"
    toolsLayout=""
  >
    <api-table-column
      label="字段样例"
      prop="name"
      min-width="210"
      show-overflow-tooltip
    ></api-table-column>
  </api-table>
</template>

<script>
export default {
  props: ['props'],
  data() {
    return {
      tableData: []
    };
  },

  components: {},

  mounted() {
    console.log(this.props, 'props');
    this.tableData = this.props?.data?.tableData || [];
  },

  methods: {}
};
</script>
<style lang="less" scoped></style>
