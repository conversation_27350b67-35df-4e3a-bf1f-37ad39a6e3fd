<template>
  <!-- <qz-pro-table
    :data-source="dataList"
    :pageVisible="false"
  >
    <qz-table-column
      v-for="item in cols"
      :key="item.prop"
      :label="item.label"
      :prop="item.prop"
    />
  </qz-pro-table> -->
  <el-table
    :data="dataList"
    stripe
    style="width: 100%"
    :header-cell-style="headerStyle"
    :row-style="rowStyle"
  >
    <el-table-column
      v-for="item in cols"
      :key="item.prop"
      :label="item.label"
      :prop="item.prop"
    >
    </el-table-column>
  </el-table>
</template>

<script>
export default {
  props: ['props'],
  data() {
    return {
      headerStyle: {
        height: '50px',
        backgroundColor: '#f5f7fa',
        color: '#333',
        fontWeight: 'bold',
        fontSize: '16px'
      },
      rowStyle: {
        height: '50px',
        // backgroundColor: '#f0f9eb',
        fontSize: '14px',
        borderBottom: '1px solid #ebeef5',
        textAlign: 'center'
      },
      cols: [],
      dataList: []
    };
  },

  components: {},

  mounted() {
    this.$nextTick(() => {
      const { cols, comdata } = this.props;
      if (cols && cols.length > 0) {
        this.cols = cols || [];
        this.dataList = comdata || [];
        if (typeof cols === 'string') {
          this.cols = JSON.parse(cols);
        }
        if (typeof comdata === 'string') {
          this.dataList = JSON.parse(comdata);
        }
        console.log(this.dataList, 'dataList');
      } else {
        this.cols = [
          { label: '列1', prop: 'col1' },
          { label: '列2', prop: 'col2' },
          { label: '列3', prop: 'col3' }
        ];
        this.dataList = [
          { col1: '数据1', col2: '数据2', col3: '数据3' },
          { col1: '数据4', col2: '数据5', col3: '数据6' },
          { col1: '数据7', col2: '数据8', col3: '数据9' }
        ];
      }
    });
  },

  methods: {}
};
</script>
<style lang="less" scoped></style>
