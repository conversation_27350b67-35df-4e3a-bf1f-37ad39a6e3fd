<template>
  <div>
    <div class="sub-title">表信息</div>
    <div class="ml20">组件IP：{{ chartData.instanceInformation }}</div>
    <div class="sub-title mt10">统计信息</div>
    <div class="qz-pro-table">
      <el-table :data="tableData" class="ml20">
        <el-table-column prop="upstreamTable" label="上游表层数">
        </el-table-column>
        <el-table-column prop="downstreamTable" label="下游表层数">
        </el-table-column>
        <el-table-column prop="allUpstreamTable" label="全部上游表数">
        </el-table-column>
        <el-table-column
          prop="allDownstreamTable"
          label="全部下游表数"
        ></el-table-column>
        <el-table-column prop="straightUpstreamTable" label="直接上游表数">
        </el-table-column>
        <el-table-column prop="straightDownstreamTable" label="直接下游表数">
        </el-table-column>
      </el-table>
    </div>
    <div class="sub-title mt10">可视化展示</div>
    <div id="container"></div>
  </div>
</template>

<script>
import { getTableData } from '@/service/data-kinship-service';

export default {
  props: ['props'],
  data() {
    return {
      tableData: [],
      exportLoading: false,
      chartData: {
        nodes: [
          {
            id: 'node1',
            x: 0,
            y: 0,
            label: 'node1node1node1node1node1node1',
            comboId: 'combo1'
          },
          {
            id: 'node2',
            x: 0,
            y: 22,
            label: 'node2',
            comboId: 'combo1'
          },
          {
            id: 'node3',
            x: 0,
            y: 100,
            label: 'node3',
            comboId: 'combo2'
          },
          {
            id: 'node4',
            x: 240,
            y: 0,
            label: 'node4',
            comboId: 'combo3'
          },
          {
            id: 'node5',
            x: 240,
            y: 22,
            label: 'node5',
            comboId: 'combo3'
          },
          {
            id: 'node6',
            x: 480,
            y: 0,
            label: 'node6',
            comboId: 'combo4'
          },
          {
            id: 'node7',
            x: 480,
            y: 22,
            label: 'node7',
            comboId: 'combo4'
          }
        ],
        edges: [
          {
            source: 'node1',
            target: 'node4'
          },
          {
            source: 'node1',
            target: 'node5'
          },
          {
            source: 'node3',
            target: 'node4'
          },
          {
            source: 'node4',
            target: 'node7'
          }
        ],
        combos: [
          {
            id: 'combo1',
            label: 'Combo1combo1combo1combo1combo1'
          },
          {
            id: 'combo2',
            label: 'Combo2'
          },
          {
            id: 'combo3',
            label: 'Combo3'
          },
          {
            id: 'combo4',
            label: 'Combo4'
          }
        ]
      },
      pageLoading: false
    };
  },
  methods: {
    // 导出
    // exportData() {
    //   this.exportLoading = true;
    //   exportAnalyse({tableId})
    //     .then((res) => {
    //       this.$exportMessage();
    //     })
    //     .catch((err) => {
    //       this.$message.error(err.msg || "导出失败");
    //     })
    //     .finally(() => {
    //       this.exportLoading = false;
    //     });
    // },
    // 可视化图表绘制
    drawChart() {
      /**
       * 计算字符串的长度
       * @param {string} str 指定的字符串
       */
      var calcStrLen = function calcStrLen(str) {
        var len = 0;
        for (var i = 0; i < str.length; i++) {
          if (str.charCodeAt(i) > 0 && str.charCodeAt(i) < 128) {
            len++;
          } else {
            len += 2;
          }
        }
        return len;
      };

      /**
       * 计算显示的字符串
       * @param {string} str 要裁剪的字符串
       * @param {number} maxWidth 最大宽度
       * @param {number} fontSize 字体大小
       */
      var fittingString = function fittingString(str, maxWidth, fontSize) {
        var fontWidth = fontSize * 1.3; //字号+边距
        maxWidth = maxWidth * 2; // 需要根据自己项目调整
        var width = calcStrLen(str) * fontWidth;
        var ellipsis = '…';
        if (width > maxWidth) {
          var actualLen = Math.floor((maxWidth - 10) / fontWidth);
          var result = str.substring(0, actualLen) + ellipsis;
          return result;
        }
        return str;
      };

      // eslint-disable-next-line no-undef
      const minimap = new G6.Minimap({});
      const width = document.getElementById('container').scrollWidth;
      const height = document.getElementById('container').scrollHeight || 500;
      // eslint-disable-next-line no-undef
      const graph = new G6.Graph({
        container: 'container',
        width,
        height,
        // translate the graph to align the canvas's center, support by v3.5.1
        fitCenter: true,
        // fitView:true,//是否开启画布自适应
        // fitViewPadding:[50,0,0,0],//画布自适应留白
        // minZoom:1,//最大缩小比例
        // maxZoom:1,//最大放大比例
        // Set groupByTypes to false to get rendering result with reasonable visual zIndex for combos
        groupByTypes: true,
        modes: {
          default: [
            'drag-canvas',
            'zoom-canvas',
            'scroll-canvas',
            'collapse-expand-combo',
            {
              type: 'tooltip',
              formatText: function formatText(model) {
                return model.label;
              },

              shouldUpdate: function shouldUpdate(e) {
                return true;
              }
            }
          ]
        },
        plugins: [minimap],
        defaultNode: {
          type: 'rect',
          size: [200, 22],
          style: {
            fill: '#fff',
            stroke: null,
            lineWidth: 1
          },
          labelCfg: {
            style: {
              fill: '#000',
              opacity: 0.85,
              fontSize: 14
            }
          },
          linkPoints: {
            // left: true,
            // right: true,
            // size: 10,
            // fill: '#fff',
            // lineWidth: 1,
            // stroke: '#1890FF',
          }
        },

        defaultEdge: {
          type: 'cubic-horizontal',
          color: '#ababab',
          style: {
            endArrow: {
              // eslint-disable-next-line no-undef
              path: G6.Arrow.triangle(7, 10, 5),
              d: 10,
              fill: '#ababab'
            }
          }
        },
        defaultCombo: {
          type: 'rect',
          /* The minimum size of the combo. combo 最小大小 */
          //size: [50, 50],
          /* style for the keyShape */
          style: {
            lineWidth: 1,
            fill: '#91c051',
            stroke: '#91c051',
            color: '#fff'
          },
          padding: [26, 0, 0, 0],
          labelCfg: {
            /* label's offset to the keyShape */
            refY: 8,
            /* label's position, options: center, top, bottom, left, right */
            position: 'top',
            refX: 10,
            /* label's style */
            style: {
              fill: '#fff',
              fontSize: 12
            }
          }
        },
        nodeStateStyles: {
          highlight: {
            opacity: 1,
            fill: '#faebd7',
            stroke: null
          },
          dark: {
            opacity: 1,
            fill: '#fff',
            stroke: null
          }
        },
        edgeStateStyles: {
          highlight: {
            stroke: '#000',
            color: '#000',
            style: {
              endArrow: {
                // eslint-disable-next-line no-undef
                path: G6.Arrow.triangle(7, 10, 5),
                d: 10,
                fill: '#000',
                stroke: '#000'
              }
            }
          }
        }
      });
      function clearAllStats() {
        graph.setAutoPaint(false);
        graph.getNodes().forEach(function (node) {
          graph.clearItemStates(node);
        });
        graph.getEdges().forEach(function (edge) {
          graph.clearItemStates(edge);
        });
        graph.paint();
        graph.setAutoPaint(true);
      }
      graph.on('node:mouseenter', function (e) {
        var item = e.item;
        graph.setAutoPaint(false);
        graph.getNodes().forEach(function (node) {
          graph.clearItemStates(node);
          graph.setItemState(node, 'dark', true);
        });
        graph.setItemState(item, 'dark', false);
        graph.setItemState(item, 'highlight', true);
        graph.getEdges().forEach(function (edge) {
          if (edge.getSource() === item) {
            graph.setItemState(edge.getTarget(), 'dark', false);
            graph.setItemState(edge.getTarget(), 'highlight', true);
            graph.setItemState(edge, 'highlight', true);
            edge.toFront();
          } else if (edge.getTarget() === item) {
            graph.setItemState(edge.getSource(), 'dark', false);
            graph.setItemState(edge.getSource(), 'highlight', true);
            graph.setItemState(edge, 'highlight', true);
            edge.toFront();
          } else {
            graph.setItemState(edge, 'highlight', false);
          }
        });
        graph.paint();
        graph.setAutoPaint(true);
      });
      graph.on('node:mouseleave', clearAllStats);
      // 直接修改原生数据中的label字段
      this.chartData.nodes.forEach(function (node) {
        return (node.label = fittingString(node.label, 200, 14));
      });
      this.chartData.combos.forEach(function (node) {
        return (node.label = fittingString(node.label, 200, 12));
      });
      graph.data(this.chartData);
      graph.render();
    }
  },
  mounted() {
    const { tableId } = this.props.data || {};
    if (tableId) {
      this.pageLoading = true;
      getTableData({ tableId })
        .then((res) => {
          this.chartData = res.data;
          this.tableData = [res.data];
          this.drawChart();
        })
        .catch((err) => {
          this.$message.error(err.msg || '获取数据血缘分析信息失败');
        })
        .finally(() => {
          this.pageLoading = false;
        });
    } else {
      this.drawChart();
    }
  }
};
</script>
<style lang="less" scoped>
.task-analyse {
  height: calc(100% - 20px);
  /deep/.main-content {
    height: auto;
    min-height: calc(100% - 42px);
  }
}
#container {
  position: relative;
  /deep/.g6-minimap {
    position: absolute;
    right: 0;
    top: -20px;
    background-color: #fff;
    outline: rgb(25, 128, 255) solid 2px;
  }
  /deep/.g6-tooltip {
    border: 1px solid #e2e2e2;
    border-radius: 4px;
    font-size: 12px;
    color: #545454;
    background-color: rgba(255, 255, 255, 0.9);
    padding: 10px 8px;
    box-shadow: rgb(174, 174, 174) 0px 0px 10px;
  }
}
</style>
