<template>
  <!-- 活跃时间组件(适用于账号画像&IP画像) -->
  <div class="active-time">
    <div class="active-time__head">
      <span class="active-time__head__title">活跃时间</span>
    </div>
    <qz-chart :option="chartOption" height="360px"></qz-chart>
  </div>
</template>

<script>
import { formatDate } from '@/utils/format-date';

export default {
  props: ['heatData', 'dateRange', 'type'],
  data() {
    return {
      timeData: [],
      chartOption: {}
    };
  },
  mounted() {
    this.getTimeData();
  },
  watch: {
    heatData() {
      this.getTimeData();
    },
    type() {
      this.getTimeData();
    }
  },
  methods: {
    // 格式化日期
    getDates(startDate, endDate) {
      const dayList = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
      const start = new Date(startDate);
      const end = new Date(endDate);
      const startTime = start.getTime();
      const endTime = end.getTime();
      const gap = endTime - startTime;
      const result = [];
      for (let i = 0; i <= gap; i += 86400000) {
        let tmp = '';
        tmp = new Date(startTime + i);
        const month =
          tmp.getMonth() + 1 > 9
            ? tmp.getMonth() + 1
            : '0' + (tmp.getMonth() + 1);
        const date = tmp.getDate() > 9 ? tmp.getDate() : '0' + tmp.getDate();
        const day = tmp.getDay();
        result.push(month + '/' + date + ' ' + dayList[day]);
      }
      return result;
    },
    // 获取数据
    getTimeData() {
      const option = {
        tooltip: {
          position: 'top',
          formatter: (params) => {
            return params.name + '<br />访问量：' + params.value[2] + '次';
          }
        },
        animation: false,
        grid: { height: '70%', top: '5%', left: 100 },
        xAxis: {
          type: 'category',
          data: '',
          splitArea: {
            show: true
          },
          axisTick: {
            show: false
          },
          axisLine: {
            lineStyle: {
              color: '#ECECEE'
            }
          },
          axisLabel: {
            color: '#666666',
            fontFamily: 'PingFangSC-Regular'
          }
        },
        yAxis: {
          type: 'category',
          data: '',
          splitArea: {
            show: true
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            color: '#666666',
            fontFamily: 'PingFangSC-Regular'
          },
          axisLine: {
            lineStyle: {
              color: '#ECECEE'
            }
          }
        },
        visualMap: {
          show: false,
          min: 0,
          max: 10,
          calculable: true,
          orient: 'horizontal',
          left: 'center',
          bottom: '15%',
          color: ['#1e92ff', '#ADD8E6'] //设置了颜色范围（原范围当数据差距过大时较小的数据会显示为白色）
        },
        series: [
          {
            name: 'Punch Card',
            type: 'heatmap',
            data: '',
            label: {
              show: false
            }
          }
        ],
        dataZoom: [
          {
            show: true,
            yAxisIndex: 0,
            filterMode: 'empty',
            start: 0,
            end: 100,
            width: 20,
            height: '70%',
            showDataShadow: false,
            left: '92%'
          }
        ]
      };
      const hours = [];
      for (let i = 0; i < 24; i++) {
        hours.push(i + 'h');
      }
      const days = this.getDates(this.dateRange[0], this.dateRange[1]);
      let data = [];
      let max = 0;
      let min = 0;
      for (let i = 0; i < days.length; i++) {
        this.heatData.forEach((item) => {
          if (this.type === 'login') {
            item.hourlyVisitCnt = item.loginCntHrly;
          }
          if (
            item.date ==
              formatDate(
                this.dateRange[0] + i * 3600 * 1000 * 24,
                'yyyyMMdd'
              ) &&
            item.hourlyVisitCnt
          ) {
            for (let j = 0; j < 24; j++) {
              const tmp = [];
              const num = item.hourlyVisitCnt[j];
              max = num > max ? num : max;
              min = num < min ? num : min;
              tmp.push(i);
              tmp.push(j);
              tmp.push(num);
              data.push(tmp);
            }
          } else {
            for (let j = 0; j < 24; j++) {
              const tmp = [];
              tmp.push(i);
              tmp.push(j);
              tmp.push(0);
              data.push(tmp);
            }
          }
        });
      }
      option.visualMap.min = min;
      option.visualMap.max = max;
      data = data.map(function (item) {
        return [item[1], item[0], item[2] || '-'];
      });
      option.xAxis.data = hours;
      option.yAxis.data = days;
      option.series[0].data = data;
      this.chartOption = option;
    }
  }
};
</script>

<style lang="less">
.active-time {
  height: 380px;
  &__head {
    // padding-left: 20px;
    display: flex;
    align-items: center;
    &__title {
      font:
        16px PingFangSC-Medium,
        Avenir,
        Helvetica,
        Arial,
        sans-serif;
      color: #333333;
      letter-spacing: 0;
    }
  }
}
</style>
