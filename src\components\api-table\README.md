## 整体改动

相关组件名称改为 api-table、api-table-column、api-table-search-item

## 表格组件的改动点

- tableId 属性改为必填
- 新增 title 属性，用于声明表格的标题
- 新增 showHeader 属性，用于声明表格是否需要显示顶部工具栏
- 新增 api-table-tool-register 子组件，用于注册局部工具
- 新增 registerTool 全局方法，用于注册全局工具
- 新增 tools-layout 属性，用于声明表格顶部工具的排序，默认支持的类型有 searchInput, divider, group, filter, fullscreen, colConfig, operations, refresh，默认布局为 searchInput, divider, group, filter, fullscreen, colConfig, divider, operations
- 新增 registerFormatter 全局方法，用于注册全局格式化工具，入参格式为(row, column, value)
- 将 search-keywords, search-placeholder, 和 search-keywords-default 三个属性合并为 search-input-options 对象，对象格式为{key: '', placeholder: '', default: ''}
- 删除 sort-configs, sort-placeholder 属性，排序改为表头排序
- 新增 default-sort 属性，用于设置表头排序，数据格式为{prop: '', order: 'desc|asc'}
- 新增 enableRemoteSort 属性，配置是否启用后端排序，默认开启
- 删除 refreshable 属性，改为通过 tools-layout 进行配置

## 表格列组件的改动点

- 表格列组件，去掉 qz-sortable, default-sort 属性，改为 sortable 属性
