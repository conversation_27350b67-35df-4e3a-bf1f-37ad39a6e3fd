<template>
  <div class="page">
    <api-table
      ref="table"
      :data-source="getDataList"
      :group-result-getter="groupResultGetter"
      :operations="operations"
      :searchInputOptions="searchInputOptions"
      :border="true"
      table-id="tableDemo"
      searchLabelWidth="120px"
      title="测试表格"
      toolsLayout="searchInput, divider, group, filter, fullscreen, colConfig, divider, operations, divider, add"
      show-check-all
      show-col-config
      @selection-change="handleSelectionChange"
    >
      <table-tool-register id="add">
        <el-button size="mini" @click="getAllFields">获取所有列</el-button>
        <el-button size="mini" @click="getVisibleFields">获取显示列</el-button>
      </table-tool-register>
      <template slot="customSearchItems">
        <!-- 可以有多个qz-table-search-item -->
        <table-search-item
          item-key="reqLabels"
          :tagFormatter="tagFormatter"
          :tagCleaner="tagCleaner"
          :paramsFormatter="paramsFormatter"
          label="自定义搜索项"
        >
          <div style="display: flex">
            <el-select
              v-model="logicExpr"
              size="small"
              style="flex: none; border-right: 1px solid #ddd"
            >
              <el-option value="and" label="且"></el-option>
              <el-option value="or" label="或"></el-option>
            </el-select>
            <el-select
              v-model="labels"
              multiple
              size="small"
              style="flex: auto"
            >
              <el-option value="label1" label="标签1"></el-option>
              <el-option value="label2" label="标签2"></el-option>
            </el-select>
          </div>
        </table-search-item>
      </template>
      <table-column type="selection"></table-column>
      <table-column
        prop="fixleft"
        label="左固定列"
        fixed="left"
        min-width="120px"
        sortable
      ></table-column>
      <table-column
        :search-config="{
          type: 'single-selection',
          options: [
            { value: 1, label: '选项1' },
            { value: 2, label: '选项2' },
            { value: 3, label: '选项3' }
          ]
        }"
        prop="single-selection"
        label="下拉单选"
        width="120px"
        groupable
        sortable
      ></table-column>
      <table-column
        :search-config="{
          type: 'multi-selection',
          options: [
            { value: 1, label: '选项1' },
            { value: 2, label: '选项2' },
            { value: 3, label: '选项3' }
          ]
        }"
        prop="multi-selection"
        label="下拉多选"
        groupable
      ></table-column>
      <table-column
        :search-config="{
          type: 'multi-cascader',
          options: [
            {
              value: 'zhinan',
              label: '指南',
              children: [
                {
                  value: 'shejiyuanze',
                  label: '设计原则',
                  children: [
                    { value: 'yizhi', label: '一致' },
                    { value: 'fankui', label: '反馈' },
                    { value: 'xiaolv', label: '效率' },
                    { value: 'kekong', label: '可控' }
                  ]
                }
              ]
            }
          ]
        }"
        prop="multi-cascader"
        label="级联多选"
        groupable
      ></table-column>
      <table-column
        :search-config="{
          type: 'tree-selection',
          treeData: [
            {
              value: 'zhinan',
              label: '指南',
              children: [
                {
                  value: 'shejiyuanze',
                  label: '设计原则',
                  children: [
                    { value: 'yizhi', label: '一致' },
                    { value: 'fankui', label: '反馈' },
                    { value: 'xiaolv', label: '效率' },
                    { value: 'kekong', label: '可控' }
                  ]
                }
              ]
            }
          ]
        }"
        prop="tree-selection"
        label="树形单选"
      ></table-column>
      <table-column
        :search-config="{
          type: 'daterange'
        }"
        prop="daterange"
        label="日期范围"
      ></table-column>
      <table-column
        :search-config="{
          type: 'datetimerange',
          pickerOptions: dateQuickChoose,
          defaultTime: ['00:00:00', '23:59:59']
        }"
        prop="datetimerange"
        label="日期时间范围"
        width="190px"
        formatter="datetime"
      ></table-column>
      <table-column
        :search-config="{
          type: 'date'
        }"
        prop="date"
        label="日期选择"
        width="100px"
        formatter="date"
      ></table-column>
      <table-column
        :search-config="{
          type: 'switch'
        }"
        :formatter="(row, column, value) => (value ? '是' : '否')"
        prop="switch"
        label="滑块按钮"
      ></table-column>
      <table-column
        :search-config="{
          type: 'text'
        }"
        prop="text"
        label="文本输入"
      ></table-column>
      <table-column
        :search-config="{
          type: 'radio-group',
          options: [
            { value: 1, label: '选项1' },
            { value: 2, label: '选项2' },
            { value: 3, label: '选项3' }
          ]
        }"
        prop="radio-group"
        label="单选按钮组"
        width="120px"
      ></table-column>
      <table-column
        :search-config="{
          type: 'checkbox-group',
          options: [
            { value: 1, label: '选项1' },
            { value: 2, label: '选项2' },
            { value: 3, label: '选项3' }
          ]
        }"
        prop="checkbox-group"
        label="复选按钮组"
        width="120px"
      ></table-column>
      <table-column
        :search-config="{
          type: 'text'
        }"
        prop="col12"
        label="第12列"
      ></table-column>
      <table-column
        :search-config="{
          type: 'text'
        }"
        prop="col13"
        label="第13列"
      ></table-column>
      <table-column
        :search-config="{
          type: 'text'
        }"
        prop="col14"
        label="第14列"
      ></table-column>
      <table-column
        :search-config="{
          type: 'text'
        }"
        prop="col15"
        label="第15列"
      ></table-column>
      <table-column
        :search-config="{
          type: 'text'
        }"
        prop="col16"
        label="第16列"
      ></table-column>
      <table-column
        :search-config="{
          type: 'text'
        }"
        prop="col17"
        label="第17列"
      ></table-column>
      <table-column
        :search-config="{
          type: 'text'
        }"
        prop="col18"
        label="第18列"
      ></table-column>
      <table-column
        :search-config="{
          type: 'text'
        }"
        prop="col19"
        label="第19列"
      ></table-column>
      <table-column
        :search-config="{
          type: 'text'
        }"
        prop="col20"
        label="第20列"
      ></table-column>
      <table-column
        :search-config="{
          type: 'text'
        }"
        prop="col21"
        label="第21列"
      ></table-column>
      <table-column prop="col22" label="第22列"></table-column>
      <table-column prop="col23" label="第23列"></table-column>
      <table-column prop="col24" label="第24列"></table-column>
      <table-column prop="col25" label="第25列"></table-column>
      <table-column prop="col26" label="第26列"></table-column>
      <table-column prop="col27" label="第27列"></table-column>
      <table-column prop="col28" label="第28列"></table-column>
      <table-column prop="col29" label="第29列"></table-column>
      <table-column prop="col30" label="第30列"></table-column>
      <table-column prop="col31" label="第31列"></table-column>
      <table-column prop="col32" label="第32列"></table-column>
      <table-column prop="col33" label="第33列"></table-column>
      <table-column
        prop="fixright"
        label="右固定列"
        fixed="right"
      ></table-column>
    </api-table>
  </div>
</template>

<script>
import ApiTable, {
  registerTool,
  registerFormatter
} from '@/components/api-table';
import TableColumn from '@/components/api-table/table-column';
import TableToolRegister from '@/components/api-table/table-tool-register';
import TableSearchItem from '@/components/api-table/table-search-item';
import moment from 'moment';

registerFormatter('datetime', function (row, column, value) {
  return moment(value).format('YYYY-MM-DD HH:mm:ss');
});
registerFormatter('date', function (row, column, value) {
  return moment(value).format('YYYY-MM-DD');
});

registerTool('saveView', {
  render(h) {
    return h(
      'ElButton',
      {
        props: {
          size: 'mini'
        }
      },
      '保存视图'
    );
  }
});

export default {
  components: { ApiTable, TableColumn, TableToolRegister, TableSearchItem },
  data() {
    return {
      searchInputOptions: {
        key: 'api',
        placeholder: '请输入API进行关键字筛选',
        default: 'xx'
      },
      logicExpr: 'and',
      labels: [],
      selectedRows: []
    };
  },
  mounted() {
    setTimeout(() => {
      this.searchInputOptions.default = '默认值';
      this.operations[0].disabled = true;
    }, 2000);
  },
  computed: {
    operations() {
      return [
        {
          name: '批量导出',
          disabled: this.selectedRows.length === 0,
          handler: () => {
            console.log('导出数据');
          }
        },
        {
          name: '批量删除',
          disabled: this.selectedRows.length === 0,
          handler: () => {
            console.log('删除数据');
          }
        }
      ];
    }
  },
  methods: {
    getDataList({ page, limit, ...rest }) {
      console.log(
        `request data, page: ${page}, limit: ${limit}, params: ${JSON.stringify(
          rest
        )}`
      );
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve({
            data: {
              totalCount: 100,
              rows: new Array(limit).fill(0).map((item, index) => ({
                fixleft: 'col' + (index + 1),
                'single-selection': '下拉单选',
                'multi-selection': '下拉多选',
                datetimerange: Date.now(),
                date: Date.now(),
                switch: true
              }))
            }
          });
        }, 1000);
      });
    },
    groupResultGetter(groupBy) {
      return Promise.resolve(
        new Array(100).fill(0).map((item, index) => {
          return {
            value: 'val' + index,
            label: groupBy + '分组结果' + index,
            count: '20'
          };
        })
      );
    },
    getAllFields() {
      console.log(this.$refs.table.getAllFieldList());
    },
    getVisibleFields() {
      console.log(this.$refs.table.getShownFieldList());
    },
    tagFormatter() {
      if (this.labels.length > 0) {
        return this.labels
          .map((item) => {
            if (item === 'label1') {
              return '标签1';
            } else if (item === 'label2') {
              return '标签2';
            }
          })
          .join(' ' + (this.logicExpr === 'and' ? '且' : '或') + ' ');
      }
    },
    tagCleaner() {
      this.logicExpr = 'and';
      this.labels = [];
    },
    paramsFormatter() {
      if (this.labels.length > 0) {
        return {
          logicExpr: this.logicExpr,
          labels: this.labels
        };
      }
    },
    handleSelectionChange(rows) {
      this.selectedRows = rows;
    }
  }
};
</script>

<style lang="less" scoped>
.page {
  padding: 20px;
  background-color: #fff;
  height: 100%;
}
</style>
