<template>
  <div class="api-table" ref="api-table" @resize="onResize">
    <div v-if="showHeader" class="api-table__header">
      <div class="api-table__title">
        <template v-if="!$slots.title">{{ title }}</template>
        <slot v-if="$slots.title" name="title"></slot>
      </div>
      <div class="api-table__tools">
        <template v-for="(key, index) of toolKeys">
          <div v-if="tools[key]" :key="key + index" class="api-table__tool">
            <component :is="tools[key]" :ref="key"></component>
          </div>
        </template>
      </div>
    </div>
    <div
      v-show="searchFormVisible"
      class="api-table__search-form-wrapper"
      ref="searchFormRef"
    >
      <slot v-if="$slots.customSearch" name="customSearch"></slot>
      <template v-else>
        <el-form :label-width="searchLabelWidth" class="api-table__search-form">
          <SearchItemRender
            :searchableColumns="sortedSearchableColumns"
            :formConditions="formConditions"
          />
        </el-form>
        <div class="api-table__search-form-footer">
          <slot name="searchFooter">
            <el-button size="mini" @click="hideSearchForm">取消</el-button>
            <el-button size="mini" type="primary" @click="searchConfirm">
              确定
            </el-button>
          </slot>
        </div>
      </template>
    </div>
    <div
      v-if="
        !searchFormVisible &&
        ($slots.customSearch || conditionTagList.length > 0)
      "
      class="api-table__search-tags-wrapper"
      ref="searchTagRef"
    >
      <slot v-if="$slots.customSearch" name="customTagList"></slot>
      <div
        v-if="!$slots.customSearch && conditionTagList.length > 0"
        class="api-table__search-tags"
        ref="searchTags"
      >
        <div
          class="api-table__search-tag"
          v-for="tag of conditionTagList"
          :key="tag.key"
        >
          <div class="api-table__search-tag__content-wrapper">
            <column-overflow
              :content="`${tag.label}：${tag.value}`"
            ></column-overflow>
          </div>
          <div
            class="api-table__search-tag__close"
            @click="handleTagClose(tag.key)"
          >
            <div class="api-table__search-tag__close-icon">
              <qz-icon class="icon-cross"></qz-icon>
            </div>
          </div>
        </div>
        <span
          class="api-table__clear-search-tag"
          @click="clearAllTags"
          v-if="!isTagsOverflow"
        >
          清空
        </span>
      </div>
      <div
        class="api-table__clear-search-tag--right"
        @click="clearAllTags"
        v-if="isTagsOverflow"
      >
        清空
      </div>
    </div>
    <div class="api-table__body-wrapper" v-loading="loading">
      <div v-if="groupBy" class="api-table__group-results" ref="groupRef">
        <span class="api-table__group-name" ref="group-name">
          {{ groupName }}：
        </span>
        <div class="api-table__group-list">
          <div
            v-for="(option, index) in groupOptions"
            :key="option.value"
            :class="{
              selected: groupValue === option.value,
              ['option-' + index]: true
            }"
            class="api-table__group-option"
            @click="groupValue = option.value"
          >
            {{ option.label
            }}{{ option.count !== undefined ? `（${option.count}）` : '' }}
          </div>
        </div>
        <el-popover
          v-if="groupRestOptions.length > 0"
          placement="bottom"
          trigger="click"
          class="api-table__group-more"
          popper-class="api-table__group-more-popper"
        >
          <i slot="reference" class="el-icon-caret-bottom"></i>
          <div
            v-for="option in groupRestOptions"
            :key="option.value"
            :class="{ selected: groupValue === option.value }"
            class="api-table__group-more-option"
            @click="groupValue = option.value"
          >
            {{ option.label
            }}{{ option.count !== undefined ? `（${option.count}）` : '' }}
          </div>
        </el-popover>
        <div class="api-table__cancel-group" @click="cancelGroup">取消分组</div>
      </div>
      <div
        v-if="showCheckAll && allRowsSelected"
        class="api-table__checker-wrapper"
        ref="allSelectRef"
      >
        <i class="el-icon-warning-outline"></i>
        <template v-if="!checkCrossPage">
          已勾选本页 {{ selectedRows }} 项！
          <span
            class="check-cross-page"
            @click="checkCrossPage = true"
            v-if="showCheckTip"
          >
            点击跨页勾选全部 {{ totalCount }} 项
          </span>
        </template>
        <template v-else>
          已勾选全部 {{ totalCount }} 项！
          <span class="check-cross-page" @click="checkCrossPage = false">
            取消跨页勾选
          </span>
        </template>
      </div>
      <base-table
        border
        ref="table"
        v-bind="$attrs"
        v-on="$listeners"
        :max-height="tableHeight"
        :data="internalData"
        :empty-text="statusText"
        :highlight-current-row="true"
        :default-sort="defaultSort"
        class="api-table__core"
        @selection-change="handleSelectionChange"
      >
        <template v-if="columns.length > 0">
          <!-- 通过模块联邦使用column组件会产生渲染层级的错误，这时候使用 columns 属性 -->
          <TableColumn
            v-for="column of columns"
            v-bind="column"
            :key="column.property"
          ></TableColumn>
        </template>
        <slot v-else></slot>
      </base-table>
      <div class="api-table__pagination">
        <div>
          <!-- 占位，防止分页器变到左边 -->
        </div>
        <el-pagination
          v-if="showPagination"
          :current-page.sync="pageNo"
          :page-size="pageSize"
          :total="totalCount"
          :layout="pageLayout"
          :page-sizes="pageSizes"
          background
          @size-change="handlePageSizeChange"
          @current-change="changePage"
        >
          <span class="api-table__pagination__slot">
            第
            <span class="api-table__pagination__slot__link">{{ pageNo }}</span>
            /
            <span class="api-table__pagination__slot__text">
              {{ Math.ceil(totalCount / pageSize) }}
            </span>
            页
          </span>
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import Divider from './header-tools/divider.vue';
import SearchInput from './header-tools/search-input.vue';
import Group from './header-tools/group.vue';
import Filter from './header-tools/filter.vue';
import Fullscreen from './header-tools/fullscreen.vue';
import JumpPage from './header-tools/jump-page.vue';
import ColConfig from './header-tools/col-config.vue';
import Refresh from './header-tools/refresh.vue';
import Operations from './header-tools/operations.vue';
// eslint-disable-next-line no-unused-vars
import SearchFormItem from './packages/search-form-item.vue';
import BaseTable from './table.vue';
import TableColumn from './table-column';
import { doPost, doGet } from '@quanzhiFE/qz-frontend';
import { clone, getSearchConditionDefaultValueByType, isEmpty } from './util';
import { sortBy } from 'lodash-es';
export { registerFormatter } from './config';

const tools = {};
registerTool('searchInput', SearchInput);
registerTool('divider', Divider);
registerTool('group', Group);
registerTool('filter', Filter);
registerTool('fullscreen', Fullscreen);
registerTool('jumpPage', JumpPage);
registerTool('colConfig', ColConfig);
registerTool('refresh', Refresh);
registerTool('operations', Operations);

/**
 * 注册头部控件
 * @param {String} key 控件唯一ID
 * @param {VueComponent} comp vue组件
 */
export function registerTool(key, comp) {
  tools[key] = comp;
}

function typeOf(target) {
  const result = Object.prototype.toString.call(target);
  return result.slice(8, result.length - 1).toLowerCase();
}

const SearchItemRender = {
  props: {
    searchableColumns: Array,
    formConditions: Object
  },
  computed: {
    apiTable() {
      let parent = this.$parent;
      while (parent && parent.$options.componentName !== 'ApiTable') {
        parent = parent.$parent;
      }
      return parent;
    }
  },
  render() {
    // 渲染高级筛选项
    const searchFormItems = this.searchableColumns.map(
      ({ property, label, searchConfig }) => {
        return (
          <SearchFormItem
            key={searchConfig.property}
            property={property}
            label={label}
            search-config={searchConfig}
            form-conditions={this.formConditions}
            order={searchConfig.order || 0}
          />
        );
      }
    );
    const customSearchItems = this.apiTable.$slots.customSearchItems;
    const sortedSearchItems = sortBy(
      searchFormItems.concat(customSearchItems || []),
      function (VNode) {
        if (VNode?.componentOptions) {
          return VNode.componentOptions.propsData.order || 0;
        }
      }
    );
    return <el-row gutter={20}>{sortedSearchItems}</el-row>;
  }
};

import { formatLastLabels } from '@/utils/handle-custom-search';
import moment from 'moment';
export default {
  name: 'ApiTable',
  componentName: 'ApiTable',
  components: { BaseTable, TableColumn, SearchItemRender },
  props: {
    /**
     * 是否动态计算表格高度
     */
    isDynamicCalc: {
      type: Boolean,
      default: false
    },
    /**
     * 表格标题
     */
    title: String,
    /**
     * 是否显示头部，默认展示
     */
    showHeader: {
      type: Boolean,
      default: true
    },
    /**
     * 表格的唯一ID，用于在浏览器本地存储中标识唯一的表格信息
     * 要求在同一个代码仓库中尽量不冲突
     */
    tableId: {
      type: String,
      required: true
    },
    /**
     * 数据源：支持数组、函数和字符串。<br>
     * 数组：现成的数据；<br>
     * 函数：接收一个params参数，要求返回数组或者Promise；<br>
     * 字符串：数据请求url；<br>
     * <br>
     * 注意：函数的返回数据，结构必须类似{success: true,data: {totalCount: 100, rows: [...]}}，如果不是这个格式就需要使用afterFetch进行转换格式
     */
    dataSource: {
      type: [Array, Function, String],
      default: () => []
    },

    /**
     * 工具排版顺序，支持从外面注册。内置支持的工具如下：
     * searchInput: 关键字搜索框
     * divider: 垂直分割线
     * group: 分组
     * filter: 高级筛选
     * fullscreen: 全屏展示
     * jumpPage：跳转页面
     * colConfig: 列显示
     * operations: 批量操作
     * refresh: 刷新
     */
    toolsLayout: {
      type: String,
      default:
        'searchInput, divider, group, filter, fullscreen, colConfig, divider, operations,jumpPage'
    },

    /**
     * 关键字搜索框的配置
     */
    searchInputOptions: {
      type: Object,
      default() {
        return {
          key: 'keywords',
          placeholder: '请输入关键字进行筛选',
          width: '300px',
          default: ''
        };
      }
    },

    /**
     * 高级筛选的表单标签默认宽度
     */
    searchLabelWidth: {
      type: String,
      default: '110px'
    },

    /**
     * 表单搜索
     */
    searchConfigs: {
      type: Array,
      default: () => []
    },

    /**
     * 跳转页面
     */
    jumpPageConfigs: {
      type: Array,
      default: () => []
    },

    /**
     * 分组条件
     * <pre>
     * [
     *   {value: 'group1', label: '组1'},
     *   {value: 'group2', label: '组2'},
     *   {value: 'group3', label: '组3', defaultGroup: true},
     * ]
     * </pre>
     */
    groupConfigs: {
      type: Array,
      default: () => []
    },

    /**
     * 获取分组结果。入参为分组字段和表格当前的请求参数，返回值必须是Promise，返回数据的样例如下
     * <pre>
     * [
     *   { value: 'field1', label: '取值1', count: 1 },
     *   { value: 'field2', label: '取值2', count: 2 },
     *   { value: 'field3', label: '取值3', count: 3 },
     * ]
     * </pre>
     */
    groupResultGetter: {
      type: Function,
      default: () => new Function()
    },

    /**
     * 表格列配置
     */
    columns: {
      type: Array,
      default: () => []
    },

    /**
     * 是否在组件加载好时就请求数据
     */
    requestImmediately: {
      type: Boolean,
      default: true
    },

    /**
     * 请求的方法
     */
    requestMethod: {
      type: String,
      default: 'post'
    },

    // 参数格式化工具，返回需要的参数
    requestParamsFormatter: {
      type: Function,
      default: null
    },

    /**
     * 可选值：form | json。默认值为form，表示以form表单的形式提交；json表示以application/json的方式提交
     */
    requestParamsMode: {
      type: String,
      default: 'form'
    },

    /**
     * 请求失败时的处理函数，接收error对象作为参数
     */
    requestErrorHandler: {
      type: Function,
      default: null
    },

    /**
     * 对通过url获取到的数据进行一些处理，并返回处理后的数据
     */
    afterFetch: {
      type: Function,
      default: null
    },

    /**
     * 是否显示分页
     */
    pageVisible: {
      type: Boolean,
      default: true
    },

    /**
     * 单页显示数据条数
     */
    pageDefaultSize: {
      type: Number,
      default: 10
    },

    /**
     * 分页布局，具体参考element-ui pagination的layout属性说明
     */
    pageLayout: {
      type: String,
      default: 'total, prev, pager, next, sizes, jumper'
    },

    /**
     * 单页数据量
     */
    pageSizes: {
      type: Array,
      default: () => [5, 10, 20, 50, 100]
    },

    /**
     * 是否显示特殊的全选框
     */
    showCheckAll: {
      type: Boolean,
      default: false
    },

    /**
     * 是否显示跨页勾选
     */
    showCheckTip: {
      type: Boolean,
      default: true
    },

    /**
     * 批量操作，数组中是对象，每个对象结构为：{ name: string, disabled: boolean, handler: Function }
     */
    operations: {
      type: Array,
      default: () => []
    },

    /**
     * 表格初始状态下的提示文案
     */
    emptyText: {
      type: String,
      default: '暂无数据'
    },

    /**
     * 是否显示加载图标
     */
    showLoading: {
      type: Boolean,
      default: true
    },

    /**
     * 是否将排序调整为后端排序，默认开启
     */
    enableRemoteSort: {
      type: Boolean,
      default: true
    },

    defaultSort: {
      type: Object,
      default() {
        return {
          prop: '',
          order: ''
        };
      }
    }
  },
  provide() {
    return {
      rootTable: this
    };
  },
  data() {
    return {
      tools: { ...tools },
      tableHeight: null,
      isTagsOverflow: false,
      loading: false,

      totalCount: 0,
      pageSize: this.pageDefaultSize,
      pageNo: 1,

      store: null,
      internalData: [],

      searchFormVisible: false,
      /**
       * 高级筛选用到的列，值为对象数组
       */
      searchableColumns: [],
      searchConditions: {},
      formConditions: {},
      customSearchFields: [],
      conditionTagList: [],
      // 额外的请求参数，一般由外部组件调用search方法来设置
      extraReqParams: {},

      /**
       * 排序
       * {
       *  value: '排序字段',
       *  sort: '排序方向，取值为asc, desc'
       * }
       */
      sortObj: {
        value: this.defaultSort?.prop || '',
        sort: this.defaultSort?.order || ''
      },

      /**
       * 当前选中的分组信息
       */
      internalGroupConfigs: [], // 分组选项列表
      groupBy: '',
      groupOptions: [], // 当前展示最大的分组选项
      groupRestOptions: [], // 未展现的分组选项
      originGroupResult: [], // 所有的分组选项
      groupValue: '',

      // 本页选中项
      selectedRows: 0,

      /**
       * 是否需要忽略分组结果的选择
       */
      ignoreGroupValueChange: false,
      ignoreSortValueChange: false,

      // 是否为第一次加载分组结果数据
      isFirstLoadGroupResult: true,

      // 跨页全选
      checkCrossPage: false,
      allRowsSelected: false,
      isPageClicked: false,

      statusText: this.emptyText,
      cascaderOptions: []
    };
  },
  computed: {
    toolKeys() {
      return this.toolsLayout.split(',').map((item) => item.trim());
    },
    storageKey() {
      return this.tableId;
    },
    groupName() {
      return (
        this.internalGroupConfigs.find((item) => item.value === this.groupBy)
          ?.label || ''
      );
    },
    showPagination() {
      return this.pageVisible && this.totalCount > 0;
    },
    internalOperations() {
      const operations = this.operations.map((operation, index) => {
        return {
          name: operation.name || '操作' + (index + 1),
          disabled: operation.disabled || false,
          handler: operation.handler || new Function()
        };
      });

      return operations;
    },
    sortedSearchableColumns() {
      return sortBy(this.searchableColumns, function (item) {
        return item.searchConfig?.order || 0;
      });
    }
  },
  watch: {
    dataSource() {
      if (this.requestImmediately) {
        this.reload();
      }
    },
    searchConfigs() {
      this.bundleSearchConfigs();
    },
    groupConfigs: {
      deep: true,
      handler() {
        this.bundleGroupConfigs();
      }
    },
    checkCrossPage() {
      this.$emit('check-all', this.checkCrossPage);
    },
    allRowsSelected() {
      this.changeTableHeight();
    }
  },
  created() {
    // 上次渲染的列
    this.$on('searchForm.addField', (field) => {
      if (
        this.customSearchFields.some((item) => item.itemKey === field.itemKey)
      ) {
        return;
      }
      this.customSearchFields.push(field);
    });
  },
  async mounted() {
    window.addEventListener('resize', this.onResize);
    // 生成搜索表单
    this.store = this.$refs.table.store;
    const columns = this.store?.states?._columns || [];
    const searchableColumns = [];
    const searchConditions = {};
    const formConditions = {};
    columns.forEach((column) => {
      if (column.searchConfig) {
        searchableColumns.push(column);
        searchConditions[column.property] =
          column.searchConfig.default ||
          getSearchConditionDefaultValueByType(column.searchConfig.type);
        formConditions[column.property] =
          column.searchConfig.default ||
          getSearchConditionDefaultValueByType(column.searchConfig.type);
      }

      if (column.groupable) {
        this.internalGroupConfigs.push({
          label: `按 ${column.label} 分组`,
          value: column.property
        });

        if (column.defaultGroup) {
          this.groupBy = column.property;
        }
      }
    });
    this.searchableColumns = searchableColumns;
    this.searchConditions = searchConditions;
    const departmentColumn = searchableColumns.find(
      (item) => item.searchConfig.type == 'multi-department'
    );
    this.formConditions = clone(searchConditions);
    this.bundleSearchConfigs();
    this.bundleGroupConfigs();
    this.setConditionTagList();

    // 从localStorage中加载列的展示顺序和显示/隐藏状态
    this.$nextTick(() => {
      if (this.$refs.table?.$ready) {
        this.restoreColConfig();

        // 恢复列显示后再进行默认排序
        const rememberedSort = this.getRememberedSort();
        if (rememberedSort) {
          this.ignoreSortValueChange = true;
          this.sortObj = rememberedSort;
          this.store.commit('sort', {
            prop: this.sortObj.value,
            order: this.sortObj.sort,
            apiTable: this
          });
        }
      }
    });

    // 使用watch选项监听sortObj,groupBy,groupValue，会导致数据初始过程中就触发变更，可能导致额外的请求
    // 所以，接下来sortObj,groupBy,groupValue均使用$watch方法

    // 排序字段变更后，需要重新获取数据
    this.$watch(
      'sortObj',
      () => {
        if (this.ignoreSortValueChange) {
          this.ignoreSortValueChange = !this.ignoreSortValueChange;
        } else {
          this.rememberSort();
          this.setConditionTagList();
          this.$emit('before-sort', this.getRequestParams());
          this.reload();
        }
      },
      { deep: true }
    );
    // 分组组别变化后，需要获取组别下的选项并重置分组字段
    this.$watch('groupBy', () => {
      // 如果高级筛选中正好有值，需要清理掉
      this.clearOneTag(this.groupBy);
      this.setConditionTagList();
      this.$emit('before-group', this.getRequestParams());
      this.reload({ reloadForGroupChange: true });
    });
    // 分组字段变更后，需要重新获取数据
    this.$watch('groupValue', () => {
      if (this.ignoreGroupValueChange) {
        this.ignoreGroupValueChange = false;
      } else {
        // 如果高级筛选中正好有值，需要清理掉
        if (!isEmpty(this.formConditions[this.groupBy])) {
          this.formConditions[this.groupBy] = '';
          this.searchConditions[this.groupBy] = '';
        }
        this.$emit('before-group', this.getRequestParams());
        this.reload({ reloadGroupResult: false });
      }
    });

    // 加载数据
    if (this.requestImmediately) {
      this.reload();
    }

    // 处理列属性的值变化
    this.$on('column-prop-change', (prop) => {
      if (prop === 'searchConfig') {
        this.setConditionTagList();
      }
    });

    // 处理v-if加入/移除的表格列
    this.$on('insert-column', (column) => {
      if (column.searchConfig) {
        this.searchableColumns.push(column);
        this.$set(
          this.searchConditions,
          column.property,
          column.searchConfig.default ||
            getSearchConditionDefaultValueByType(column.searchConfig.type)
        );
        this.$set(
          this.formConditions,
          column.property,
          column.searchConfig.default ||
            getSearchConditionDefaultValueByType(column.searchConfig.type)
        );
      }

      if (column.groupable) {
        this.internalGroupConfigs.push({
          label: `按 ${column.label} 分组`,
          value: column.property
        });

        if (column.defaultGroup) {
          this.groupBy = column.property;
        }
      }

      this.restoreColConfig();
    });
    this.$on('remove-column', (column) => {
      if (column.searchConfig) {
        const idx = this.searchableColumns.findIndex(
          (item) => item.property === column.property
        );
        this.searchableColumns.splice(idx, 1);
        delete this.searchConditions[column.property];
        delete this.formConditions[column.property];
      }
      if (column.groupable) {
        const idx = this.internalGroupConfigs.findIndex(
          (item) => item.property === column.property
        );
        this.internalGroupConfigs.splice(idx, 1);
        if (column.defaultGroup) {
          this.groupBy = '';
        }
      }
    });
    this.changeTableHeight();
  },
  methods: {
    formatCascaderOptions(data, fn, type) {
      const self = this;
      if (typeof fn !== 'function') {
        type = fn;
        fn = null;
      }
      data.label = data.value;
      if (!data.staffId) {
        // TODO：解决部门id和员工id冲突情况下勾选不显示问题
        if (data.type === 'dept') {
          data.staffId = `${data.type}-${data.code}`;
        } else {
          data.staffId = Number(data.code);
        }
      }
      data.value = data.code;
      data.children = [...(data.children || []), ...(data.userList || [])];
      if (fn) {
        data = fn(data);
      }
      data.disabled = self.isAuthFilter && type !== 'user' && !data.hasDataAuth;
      if (data.children.length) {
        data.children.forEach((node) => {
          node.disabled =
            self.isAuthFilter && type !== 'user' && !node.hasDataAuth;
          node = this.formatCascaderOptions(node, fn);
        });
      } else if (!fn && !data.children.length && data.type !== 'user') {
        data.disabled = true;
      } else {
        data.children = null;
      }
      return data;
    },
    getSubHeight() {
      const qzHeader = document.querySelector('.qz-header');
      let subHeight = 180 + qzHeader.clientHeight;
      const searchForm = this.$refs.searchFormRef;
      const searchTags = this.$refs.searchTags;
      const group = this.$refs.groupRef;
      const allSelect = this.$refs.allSelectRef;

      if (this.searchFormVisible && searchForm) {
        // 存在高级筛选
        subHeight += searchForm.clientHeight + 10;
      }
      if (searchTags) {
        // 存在搜索tag
        this.isTagsOverflow = searchTags.scrollHeight > searchTags.clientHeight;
        subHeight = this.isTagsOverflow
          ? subHeight + searchTags.clientHeight + 40
          : subHeight + searchTags.clientHeight;
      }
      if (group) {
        // 存在分组
        subHeight += group.clientHeight + 10;
      }
      if (this.allRowsSelected && allSelect) {
        // 存在全选
        subHeight += allSelect.clientHeight + 12;
      }
      return subHeight;
    },
    changeTableHeight() {
      if (this.isDynamicCalc) {
        this.$nextTick(() => {
          const subHeight = this.getSubHeight();
          this.tableHeight = document.documentElement.clientHeight - subHeight;
        });
      }
    },
    rememberSort() {
      // 让浏览器记忆排序
      const key = `${this.storageKey}_sort`;
      localStorage.setItem(
        // eslint-disable-next-line no-undef
        `${__webpack_public_path__}_${key}`,
        JSON.stringify(this.sortObj)
      );
    },
    getRememberedSort() {
      // 让浏览器恢复排序
      const key = `${this.storageKey}_sort`;
      // eslint-disable-next-line no-undef
      const jsonStr = localStorage.getItem(`${__webpack_public_path__}_${key}`);
      return JSON.parse(jsonStr);
    },
    rememberColConfig(config) {
      // 让浏览器记忆列顺序和显示/隐藏状态
      const key = `${this.storageKey}_col_config`;
      localStorage.setItem(
        // eslint-disable-next-line no-undef
        `${__webpack_public_path__}_${key}`,
        JSON.stringify(config)
      );
    },
    getRememberedColConfig() {
      // 恢复列顺序和显示/隐藏状态
      const key = `${this.storageKey}_col_config`;
      // eslint-disable-next-line no-undef
      const jsonStr = localStorage.getItem(`${__webpack_public_path__}_${key}`);
      return JSON.parse(jsonStr);
    },
    restoreColConfig() {
      const configedCols = [];
      const existsConfig = this.getRememberedColConfig() || [];
      const columns = this.store.states._columns || [];
      existsConfig.forEach(({ property, visible }) => {
        const column = columns.find((item) => item.property === property);
        if (column) {
          column.visible = visible;
          configedCols.push(column);
        }
      });
      const unconfigedCols = columns.filter(
        (item) => !configedCols.some((ele) => ele === item)
      );

      this.store.states._columns = []
        .concat(configedCols)
        .concat(unconfigedCols);
      this.store.scheduleLayout(true);
    },
    bundleSearchConfigs() {
      // 从pro-table的searchConfigs属性中提取搜索字段
      this.searchConfigs.forEach((config) => {
        let column = this.searchableColumns.find(
          (item) => item.property === config.property
        );
        if (!column) {
          column = {
            property: config.property,
            label: config.label,
            searchConfig: config
          };
          this.searchableColumns.push(column);
          this.$set(
            this.searchConditions,
            column.property,
            column.searchConfig.default ||
              getSearchConditionDefaultValueByType(column.searchConfig.type)
          );
          this.$set(
            this.formConditions,
            column.property,
            column.searchConfig.default ||
              getSearchConditionDefaultValueByType(column.searchConfig.type)
          );
        } else {
          column.searchConfig = config;
        }
      });
    },
    bundleGroupConfigs() {
      this.groupConfigs.forEach((config) => {
        const exists = this.internalGroupConfigs.some(
          (item) => item.value === config.value
        );
        if (!exists) {
          this.internalGroupConfigs.push({
            label: `按 ${config.label} 分组`,
            value: config.value
          });
          if (config.defaultGroup) {
            this.groupBy = config.value;
          }
        }
      });
    },
    cancelGroup() {
      this.groupBy = '';
      this.groupOptions = [];
      this.originGroupResult = [];
      this.groupRestOptions = [];
      this.ignoreGroupValueChange = true;
      this.groupValue = '';
      this.$emit('cancel-group');
      this.changeTableHeight();
    },
    onResize() {
      if (this.originGroupResult.length) {
        this.calcGroupMaxCnt(this.originGroupResult);
      }
      this.changeTableHeight();
    },
    calcGroupMaxCnt(list) {
      let maxGroupCnt = 5;
      const apiTableWidth = this.$refs['api-table'].clientWidth;
      const groupNameWidth = this.$refs['group-name']?.clientWidth;
      const groupMaxWrapperWidth = apiTableWidth - groupNameWidth - 70;
      if (list.length) {
        const widthArr = [
          46 + 13 * (list[0].label.length + list[0].count.toString().length)
        ];
        for (let i = 1; i < list.length; i++) {
          widthArr.push(
            widthArr[i - 1] +
              46 +
              13 * (list[i].label.length + list[i].count.toString().length)
          );
        }
        if (widthArr[widthArr.length - 1] < groupMaxWrapperWidth) {
          maxGroupCnt = widthArr.length;
        } else {
          const countIndex = widthArr.findIndex((item) => {
            return item > groupMaxWrapperWidth;
          });
          maxGroupCnt = countIndex;
        }
      }
      this.groupOptions = list.slice(0, maxGroupCnt);
      this.groupRestOptions = list.slice(maxGroupCnt, list.length);
    },
    getGroupResult(reloadForGroupChange) {
      const newGroupBy = this.groupBy;
      return Promise.resolve().then(() => {
        if (newGroupBy) {
          if (typeOf(this.groupResultGetter === 'function')) {
            const params = this.getRequestParams();
            if (newGroupBy === this.searchInputOptions.key) {
              params[newGroupBy] = this.$refs.searchInput[0].value;
            }
            if (
              newGroupBy !== this.searchInputOptions.key ||
              !params[newGroupBy]
            ) {
              delete params[newGroupBy];
            }
            const res = this.groupResultGetter(newGroupBy, params);
            if (typeOf(res) === 'promise') {
              return res.then(
                (list) => {
                  this.calcGroupMaxCnt(list);
                  this.originGroupResult = list;
                  // 只有在前后分组有变化或者第一次加载数据，或者当前groupValue在分组结果中不存在时，才会默认选中第一项
                  if (
                    reloadForGroupChange ||
                    this.isFirstLoadGroupResult ||
                    !list.some((item) => item.value === this.groupValue)
                  ) {
                    this.ignoreGroupValueChange = true;
                    this.groupValue = list[0]?.value;
                  }
                },
                () => {
                  this.groupOptions = [];
                  this.groupRestOptions = [];
                  this.groupValue = '';
                }
              );
            }
          }
        }
      });
    },
    showSearchForm() {
      this.searchFormVisible = true;
      this.changeTableHeight();
    },
    hideSearchForm() {
      this.searchFormVisible = false;
      this.changeTableHeight();
    },
    handlePageSizeChange(size) {
      this.pageSize = size;
      /**
       * 切换单页显示数据条数
       */
      this.$emit('page-size-change', size);
      this.load();
    },
    searchConfirm() {
      // 如果此时已经按照筛选条件中的某个字段进行分组，直接取消分组
      this.searchConditions = clone(this.formConditions);
      let groupBy = Object.keys(this.searchConditions).find(
        (key) => key === this.groupBy
      );
      let groupValue = this.searchConditions[groupBy];
      if (!groupBy) {
        const field = this.customSearchFields.find(
          (field) => field.itemKey === this.groupBy
        );
        groupBy = field?.itemKey;
        groupValue = field?.tagFormatter();
      }
      if (groupBy && !isEmpty(groupValue)) {
        this.cancelGroup();
        this.setConditionTagList();
      } else {
        this.setConditionTagList();
        this.$emit('before-search', this.getRequestParams());
        this.reload();
      }
      this.changeTableHeight();
    },
    setConditionTagList() {
      const tagList = [];
      const keys = Object.keys(this.searchConditions);
      keys.forEach((key) => {
        const keyValue = this.searchConditions[key];
        let tagValue = keyValue;
        const column = this.searchableColumns.find(
          (item) => item.property === key
        );
        if (!column) {
          return;
        }
        const { searchConfig, label } = column;
        if (
          searchConfig.type === 'single-selection' ||
          searchConfig.type === 'radio-group'
        ) {
          const values = [];
          (searchConfig.options || []).forEach((option) => {
            const optionValue =
              option[searchConfig.optionProps?.value || 'value'];
            const optionLabel =
              option[searchConfig.optionProps?.label || 'label'];
            keyValue === optionValue && values.push(optionLabel);
          });
          tagValue = values.join(',');
        } else if (
          searchConfig.type === 'multi-selection' ||
          searchConfig.type === 'checkbox-group'
        ) {
          const values = [];
          (searchConfig.options || []).forEach((option) => {
            const optionValue =
              option[searchConfig.optionProps?.value || 'value'];
            const optionLabel =
              option[searchConfig.optionProps?.label || 'label'];
            keyValue.includes(optionValue) && values.push(optionLabel);
          });
          tagValue = values.join(',');
        } else if (searchConfig.type === 'multi-cascader') {
          const values = [];
          (keyValue || []).forEach((arr) => {
            const value = [];
            let nextList = searchConfig.options;
            const valueKey = searchConfig.optionProps?.value || 'value';
            const labelKey = searchConfig.optionProps?.label || 'label';
            const childrenKey =
              searchConfig.optionProps?.children || 'children';
            for (const element of arr) {
              const foundData = nextList.find(
                (item) => item[valueKey] === element
              );
              if (foundData) {
                value.push(foundData[labelKey]);
                if (
                  foundData[childrenKey] &&
                  foundData[childrenKey].length > 0
                ) {
                  nextList = foundData[childrenKey];
                } else {
                  break;
                }
              } else {
                break;
              }
            }
            values.push(value.join('/'));
          });
          tagValue = values.join(',');
        } else if (searchConfig.type === 'multi-user') {
          const userCascaderOptions = [];
          this.cascaderOptions.forEach((item) => {
            userCascaderOptions.push(
              this.formatCascaderOptions(this.$deepCopy(item))
            );
          });

          tagValue = formatLastLabels(keyValue, userCascaderOptions);
        } else if (searchConfig.type === 'multi-department') {
          const deptCascaderOptions = [];
          this.cascaderOptions.forEach((item) => {
            deptCascaderOptions.push(
              this.formatCascaderOptions(this.$deepCopy(item), (node) => {
                node.children = node.children.filter((node) => {
                  return node.type !== 'user';
                });
                return node;
              })
            );
          });
          tagValue = formatLastLabels(keyValue, deptCascaderOptions);
        } else if (searchConfig.type === 'date') {
          tagValue = keyValue && moment(keyValue).format('YYYY-MM-DD HH:mm:ss');
        } else if (
          searchConfig.type === 'daterange' ||
          searchConfig.type === 'datetimerange'
        ) {
          let tmpValue = [];
          if (keyValue && keyValue.length > 1) {
            tmpValue = [
              moment(keyValue[0]).format('YYYY-MM-DD HH:mm:ss'),
              moment(keyValue[1]).format('YYYY-MM-DD HH:mm:ss')
            ];
          }
          tagValue = [...new Set(tmpValue)].join('~');
        } else if (searchConfig.type === 'switch') {
          tagValue = keyValue ? '是' : '';
        }
        if (tagValue) {
          tagList.push({
            key,
            label: searchConfig.label || label,
            value: tagValue
          });
        }
      });

      this.customSearchFields.forEach((field) => {
        const tagValue = field.tagFormatter();
        if (tagValue) {
          tagList.push({
            key: field.itemKey,
            label: field.label,
            value: tagValue
          });
        }
      });

      this.conditionTagList = tagList;
    },
    /**
     * 根据指定字段进行分组
     */
    group(field) {
      this.groupBy = field;
      this.changeTableHeight();
    },
    /**
     * 根据指定的条件发起检索
     */
    search(params = {}) {
      delete params.field;
      delete params.sort;
      delete params.limit;
      delete params.page;
      Object.keys(params).forEach((key) => {
        if (key === this.searchInputOptions.key) {
          this.$refs.searchInput[0].setKeywords(params[key]);
        }
        if (Object.hasOwn(this.searchConditions, key) && this.groupBy !== key) {
          this.searchConditions[key] = params[key];
          this.formConditions[key] = params[key];
        } else {
          // search的目标字段为自定义表单项时，需要调用外部函数处理用户自己维护的数据
          const field = this.customSearchFields.find(
            (item) => item.itemKey === key
          );
          if (field?.searchSetter) {
            field.searchSetter(params);
          } else {
            this.extraReqParams[key] = params[key];
          }
        }
      });
      this.setConditionTagList();
      this.$emit('before-search', this.getRequestParams());
      this.reload();
      this.changeTableHeight();
    },
    sort(field, direction) {
      this.sortObj = {
        value: field,
        sort: direction
      };
    },
    clearOneTag(key) {
      if (!key) {
        return;
      }
      const field = this.customSearchFields.find(
        (item) => item.itemKey === key
      );
      if (field) {
        field.tagCleaner();
      } else {
        if (typeOf(this.searchConditions[key]) === 'array') {
          this.searchConditions[key] = [];
        } else {
          this.searchConditions[key] = '';
        }
        this.formConditions = clone(this.searchConditions);
      }
    },
    // 关闭某个tag
    handleTagClose(key) {
      this.clearOneTag(key);
      this.setConditionTagList();
      this.$emit('tag-closed', key);
      this.$emit('before-search', this.getRequestParams());
      this.reload();
      this.changeTableHeight();
    },
    /**
     * 清除表格分组、关键字筛选、高级筛选
     */
    clearAllTableOperations() {
      this.conditionTagList.forEach((tag) => this.clearOneTag(tag.key));
      this.setConditionTagList();
      this.groupBy = '';
      this.groupOptions = [];
      this.originGroupResult = [];
      this.groupRestOptions = [];
      this.ignoreGroupValueChange = true;
      this.groupValue = '';
      this.$refs.searchInput[0].clearKeywords();
      this.clearColumnSelection();
    },
    clearAllTags() {
      this.conditionTagList.forEach((tag) => this.clearOneTag(tag.key));
      this.setConditionTagList();
      this.$emit('all-tags-closed');
      this.$emit('before-search', this.getRequestParams());
      this.reload();
      this.changeTableHeight();
    },
    getFormData() {
      const conditions = clone(this.formConditions);
      // 自定义搜索项的参数
      this.customSearchFields.forEach((field) => {
        const customParams = field.paramsFormatter();
        if (customParams) {
          Object.assign(conditions, customParams);
        }
      });
      return conditions;
    },
    resetFormData() {
      for (const key in this.formConditions) {
        const type = typeOf(typeof this.formConditions[key]);
        if (type === 'boolean') {
          this.formConditions[key] = false;
        } else if (type === 'array') {
          this.formConditions[key] = [];
        } else {
          this.formConditions[key] = '';
        }
      }
    },
    getRequestParams() {
      let params = clone(this.searchConditions);
      if (!isEmpty(this.groupValue) && !isEmpty(this.groupBy)) {
        // 如果筛选中有值，以筛选的为准，然后将选中的分组选项也置为该筛选值，并且这时不重新请求分组结果
        if (!isEmpty(params[this.groupBy])) {
          if (this.groupValue !== params[this.groupBy]) {
            this.ignoreGroupValueChange = true;
            this.groupValue = params[this.groupBy];
          }
        } else {
          params[this.groupBy] = this.groupValue;
        }
      }
      if (this.sortObj.value) {
        params.field = this.sortObj.value;
        params.sort = this.sortObj.sort;
      }
      Object.keys(this.extraReqParams).forEach((key) => {
        params[key] = this.extraReqParams[key];
      });

      params.page = this.pageNo;
      params.limit = this.pageSize;

      // 自定义搜索项的参数
      this.customSearchFields.forEach((field) => {
        const customParams = field.paramsFormatter();
        if (customParams) {
          Object.assign(params, customParams);
        }
      });

      // 请求参数的自行处理
      if (this.requestParamsFormatter) {
        params = this.requestParamsFormatter(params);
      }
      return params;
    },
    selectAllPages() {
      this.$refs.table.toggleAllSelection();
      this.checkCrossPage = true;
    },
    refreshTable(configs) {
      if (configs?.target) {
        let target = configs.target;
        if (target.nodeName == 'I') {
          target = configs.target.parentNode;
        }
        target.blur();
      }
      this.$emit('before-refresh', this.getRequestParams());
      this.reload(configs);
    },
    reload(configs) {
      this.searchFormVisible = false;
      this.clearColumnSelection();
      this.$nextTick(() => {
        this.load(1, configs);
      });
    },
    changePage(pageNo) {
      this.isPageClicked = true;
      this.$emit('before-page', this.getRequestParams());
      this.load(pageNo);
    },
    // 清空行选择
    clearColumnSelection() {
      this.selectedRows = 0;
      this.checkCrossPage = false;
      this.allRowsSelected = false;
    },
    /**
     * 指定请求第几页的数据，如果没有指定会请求第一页数据
     */
    async load(
      pageNo = 1,
      { reloadForGroupChange, reloadGroupResult = true } = {}
    ) {
      this.pageNo = pageNo;
      const sourceType = typeOf(this.dataSource);
      if (sourceType === 'array') {
        // 数组类型的数据源：直接渲染数据
        this.totalCount = this.dataSource.length;

        if (this.showPagination) {
          const start = this.pageSize * (this.pageNo - 1);
          const end = start + this.pageSize;
          this.internalData = this.dataSource.slice(start, end);
        } else {
          this.internalData = this.dataSource;
        }

        // 触发加载完成事件
        this.afterLoad();
      } else if (sourceType === 'function' || sourceType === 'string') {
        if (this.showLoading) {
          this.loading = true;
        }
        if (reloadGroupResult) {
          await this.getGroupResult(reloadForGroupChange, reloadGroupResult);
        }
        this.isFirstLoadGroupResult = false;

        const params = this.getRequestParams();
        this.$emit('before-load', params);

        let result = null;

        if (sourceType === 'string') {
          // 确定url的请求方法
          let requestFunc = null;
          if (this.requestMethod.toLowerCase() == 'get') {
            requestFunc = doGet;
          } else if (this.requestMethod.toLowerCase() == 'post') {
            requestFunc = doPost;
          }

          result = requestFunc(
            {
              url: this.dataSource,
              params: params
            },
            this.requestParamsMode?.toLowerCase() === 'json'
          );
        } else {
          // 函数类型的数据源：要求返回值是对象或者Promise，并且数据结构和url返回的数据结构要一致
          result = this.dataSource(params);
        }

        const resultType = typeOf(result);
        if (resultType === 'object') {
          this.totalCount = result?.data?.count;
          this.internalData = result?.data?.rows || [];
          this.loading = false;
          this.statusText = this.emptyText;
        } else if (resultType === 'promise') {
          result
            .then((response) => {
              // 返回数据处理
              if (this.afterFetch) {
                response = this.afterFetch(response);
              }

              this.totalCount = response?.data?.count;
              this.internalData = response?.data?.rows || [];

              this.afterLoad();
            })
            .catch((error) => {
              if (this.requestErrorHandler) {
                this.requestErrorHandler(error);
              } else if (error?.message !== 'cancel') {
                console.error(error);
                this.$message.error(error?.msg || '获取列表数据失败');
              }
              if (error?.message !== 'cancel') {
                this.loading = false;
                this.statusText = this.emptyText;
              }
            });
        }
      }
    },
    afterLoad() {
      this.$nextTick(() => {
        this.$emit('loaded', this.$refs.table);
        this.loading = false;
        this.statusText = this.emptyText;
      });
    },
    reloadCurrentPage() {
      this.load(this.pageNo);
      this.clearColumnSelection();
    },
    clear() {
      this.internalData = [];
      this.totalCount = 0;
      this.pageNo = 1;
    },
    getAllFieldList() {
      return (this.store?.states?.allColumns || [])
        .filter((item) => item.property && item.label)
        .map(({ property, label }) => ({
          property,
          label
        }));
    },
    getShownFieldList() {
      return (this.store?.states?.columns || [])
        .filter((item) => item.property && item.label)
        .map(({ property, label }) => ({
          property,
          label
        }));
    },
    handleSelectionChange(rows) {
      if (this.checkCrossPage && this.isPageClicked) {
        this.isPageClicked = false;
        this.$refs.table.toggleAllSelection();
      } else {
        this.isPageClicked = false;
        this.selectedRows = rows.length;
        if (this.$refs.table.data.length) {
          this.allRowsSelected = rows.length >= this.$refs.table.data.length;
        }
        if (this.checkCrossPage && !this.allRowsSelected) {
          this.checkCrossPage = false;
        }
      }
    },
    destroyed() {
      window.removeEventListener('resize', this.onResize);
    }
  }
};
</script>
<style lang="less">
@import './common.less';

// 以下样式修复问题：在safari中，api清单的左侧固定列和其他列会错位
.api-table .el-table table {
  width: 0px;
}

.api-table__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  .api-table__title {
    font-weight: 500;
    font-size: 16px;
    color: #3d3d3d;
  }
  .api-table__tools {
    display: flex;
    align-items: center;
  }
}
.api-table__tools .api-table__tool {
  margin: 0 6px;
  &:first-child {
    margin-left: 0;
  }
  &:last-child {
    margin-right: 0;
  }
}

.api-table__group-results {
  display: flex;
  margin-bottom: 10px;
  .api-table__group-name {
    flex: none;
    font-size: 14px;
    color: #333333;
  }
  .api-table__group-list {
    display: inline-block;
    .api-table__group-option {
      display: inline-block;
      box-sizing: border-box;
      margin: 0 10px;
      color: #3d3d3d;
      cursor: pointer;
      transition: color 0.4s;
      border-bottom: 2px solid transparent;
      &.selected {
        font-weight: 600;
        border-bottom: 2px solid @theme-blue;
      }
      &:hover {
        color: @theme-blue;
      }
    }
  }
  .api-table__cancel-group {
    flex: none;
    margin-left: 10px;
    color: @theme-blue;
    cursor: pointer;
  }
}
.api-table__group-more {
  flex: none;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  cursor: pointer;
}
.api-table__group-more-popper {
  padding: 5px;
  max-height: 200px;
  overflow-y: auto;
}
.api-table__group-more-option {
  box-sizing: border-box;
  border-radius: 3px;
  padding: 5px 10px;
  margin-bottom: 5px;
  cursor: pointer;
  &.selected,
  &:hover {
    color: @theme-blue;
  }
  &.selected {
    background-color: @bg-grey;
  }
}
.api-table__checker-wrapper {
  background: #f4f9ff;
  border: 1px solid #90c4fd;
  padding: 10px 10px;
  color: #2e3444;
  border-radius: 5px;
  margin-bottom: 10px;
  .el-icon-warning-outline {
    color: #4cafeb;
  }
  .el-checkbox__label {
    color: #4cafeb;
  }
}

.api-table__search-form-wrapper {
  display: flex;
  max-height: 330px;
  flex-direction: column;
  overflow: hidden;
  margin-bottom: 10px;
  background-color: @bg-grey;
  padding: 0;
  border-radius: 5px;
  .el-form {
    flex: auto;
    overflow-y: auto;
    overflow-x: hidden;
    margin: 0;
    padding: 30px;
    padding-bottom: 0;
    box-sizing: border-box;
    width: 100%;
    .el-input__inner {
      border: none;
    }
  }
  .api-table__search-form-footer {
    flex: none;
    text-align: right;
    padding: 10px 20px 15px;
  }
}
.api-table__search-tag {
  display: flex;
  align-items: center;
  background: @shallow-grey;
  padding: 0 10px;
  font-size: 12px;
  line-height: 24px;
  margin-right: 10px;
  margin-bottom: 10px;
  max-width: 90%;
  border-radius: 4px;
  &__content-wrapper {
    width: calc(100% - 12px);
  }
  &__close-icon {
    margin-left: 10px;
    cursor: pointer;
    user-select: none;
    i {
      font-size: 12px;
    }
  }
  .icon-cross::before {
    content: 'x';
  }
}
.api-table__clear-search-tag {
  color: @theme-blue;
  cursor: pointer;
  margin-bottom: 10px;
  &--right {
    text-align: right;
    padding: 10px 20px;
    color: @theme-blue;
    cursor: pointer;
  }
}
.api-table .sort-icon {
  margin: 0;
  font-size: 12px;
  color: #c4c6c9;
}
.api-table .el-table th.el-table__cell > .cell.sortable {
  .caret-wrapper {
    height: auto !important;
    flex-direction: row;
    margin-left: 10px;
  }
  .caret-wrapper .sort-trigger {
    position: relative;
  }
  .caret-wrapper .sort-trigger.desc {
    transform: rotate(180deg);
  }
  .caret-wrapper .sort-trigger + .sort-trigger {
    margin-left: 3px;
  }
  .caret-wrapper .sort-trigger::before {
    display: block;
    content: '';
    height: 12px;
    width: 1px;
    background-color: @inactive-grey;
  }

  .caret-wrapper .sort-trigger::after {
    position: absolute;
    top: 0;
    left: 0;
    transform-origin: top;
    transform: rotate(-45deg);
    display: block;
    content: '';
    height: 5px;
    width: 1px;
    background-color: @inactive-grey;
  }
}
.api-table
  .el-table
  th.el-table__cell.asc
  > .cell.sortable
  .caret-wrapper
  .sort-trigger.asc::before,
.api-table
  .el-table
  th.el-table__cell.asc
  > .cell.sortable
  .caret-wrapper
  .sort-trigger.asc::after {
  background-color: @theme-blue;
}
.api-table
  .el-table
  th.el-table__cell.desc
  > .cell.sortable
  .caret-wrapper
  .sort-trigger.desc::before,
.api-table
  .el-table
  th.el-table__cell.desc
  > .cell.sortable
  .caret-wrapper
  .sort-trigger.desc::after {
  background-color: @theme-blue;
}

.api-table .el-table th > .cell {
  color: @table-head-font-color;
}

.api-table {
  position: relative;
  .el-table-column--selection .cell {
    text-overflow: clip;
  }
  &__search-tags {
    max-height: 200px;
    overflow: auto;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    flex-wrap: wrap;
  }
  .el-table__fixed-right::before,
  .el-table__fixed::before {
    display: none;
  }
  .el-table th.el-table__cell {
    border-right: 3px solid transparent !important;
    &:hover {
      background: @table-hover-bg-color;
      border-right: 3px solid @table-hover-border-color !important;
    }
  }
  .el-table td.el-table__cell {
    border-bottom: none !important;
  }
  .el-table td.el-table-column--selection,
  .el-table th.el-table-column--selection {
    padding-left: 0;
  }
  .el-table th.el-table-column--selection .cell {
    padding-left: 10px;
    border-right: none !important;
  }
  .el-table--border {
    border: 0 none;
    td {
      border-right: 0 none;
    }
    th.el-table-column--selection .cell {
      padding-left: 10px;
    }
    td:first-child div.cell {
      padding-left: 10px;
    }
    .el-table__fixed-header-wrapper th:nth-last-child(1),
    .el-table__header-wrapper th:nth-last-child(1) {
      .cell {
        border-right: none;
      }
    }
    &::after {
      width: 0;
    }
  }
  .el-col {
    padding-right: 0 !important;
  }
  .action-link {
    font-weight: 500;
    font-family:
      -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue',
      'PingFang SC', 'Noto Sans', 'Noto Sans CJK SC', 'Microsoft YaHei',
      '\\5FAE软雅黑', sans-serif;
  }
  &.border-wrap {
    .el-table {
      border-left: 1px solid @deep-grey;
      border-right: 1px solid @deep-grey;
    }
  }

  .el-table {
    width: 100%;
    &::before {
      display: none;
    }
    th.is-leaf {
      font-size: 12px;
      background-color: @table-head-color;
      color: @table-head-font-color;
      border-bottom: none !important;
    }
    .cell {
      font-size: 14px;
    }
    td,
    th {
      padding: 8px 0;
    }
    td:first-of-type,
    th:first-of-type {
      padding-left: 20px;
    }
  }
  .el-table__body tr:hover > td {
    background-color: @item-hover-color !important;
    .action-link {
      color: @theme-blue;
    }
  }
  .hover-row {
    background-color: @item-hover-color !important;
    .action-link {
      color: @theme-blue;
    }
  }
  .el-table:not(.border) {
    th.is-leaf {
      border-right: none;
    }
  }
  .el-table__empty-block:not(.border) {
    border-bottom: 1px solid @shallow-border-color;
  }
  &__pagination {
    &__slot {
      display: flex;
      align-items: center;
      margin-right: 10px;
      font-weight: 400;
      &__link {
        color: @theme-blue;
        min-width: 0 !important;
      }
      &__text {
        min-width: 0 !important;
      }
    }
    display: flex;
    justify-content: flex-end;
    align-items: center;
    flex-wrap: wrap;
    margin-top: 20px;
    .demonstration,
    .el-pagination,
    .el-pagination__sizes,
    .el-pagination__jump {
      font-size: 13px;
      color: @font-grey;
    }
    .demonstration,
    .el-pagination {
      padding: 0;
      margin-bottom: 15px;
    }
    .el-pagination .btn-prev:first-child {
      margin-left: 0;
    }
    .demonstration {
      margin-right: 25px;
      .total {
        font-size: 12px;
        color: @theme-blue;
        margin: 0 3px;
      }
    }
    .el-pagination.is-background .btn-next,
    .el-pagination.is-background .btn-prev,
    .el-pagination.is-background .el-pager li {
      background-color: white !important;
      border: 1px solid #c4c6c9 !important;
      font-family: PingFangSC-Regular, sans-serif;
      font-size: 12px;
      color: #666 !important;
      text-align: center;
      border-radius: 4px;
      &.active {
        color: white !important;
      }
    }
    .el-pagination.is-background .btn-prev:not(:disabled):hover,
    .el-pagination.is-background .btn-next:not(:disabled):hover {
      color: @theme-blue;
    }
    .el-pagination.is-background .btn-prev:disabled,
    .el-pagination.is-background .btn-next:disabled {
      background-color: #f4f4f5 !important;
      color: #c0c4cc !important;
    }
    .el-pagination.is-background .el-pager li.more.btn-quicknext {
      border: none;
      color: @deep-grey !important;
      margin: 0;
    }
    .el-pagination.is-background .el-pager li:not(.disabled).active {
      background-color: @theme-blue !important;
      border-color: @theme-blue !important;
    }
    .el-pagination__editor.el-input {
      width: 34px;
    }
    .el-pagination__jump {
      margin-left: 0;
    }
  }
}
.api-table__column-toggle-trigger {
  height: 32px;
  padding: 0 16px;
  box-sizing: border-box;
  border: 1px solid #d8dbdf;
  border-radius: 4px;
  line-height: 32px;
  font-size: 14px;
  color: #2e3444;
  cursor: pointer;
  user-select: none;
  [class*='el-icon-caret'] {
    color: #979797;
  }
}
.api-table__column-toggle-popper {
  max-height: 300px;
  margin-top: 1px !important;
  overflow-y: auto;
}
.api-table__search-form {
  margin-top: 0;
  .el-input__inner {
    background-color: transparent;
  }
  .el-cascader__search-input {
    min-width: 0 !important;
  }
  .el-cascader__tags .el-tag {
    max-width: 35% !important;
  }
  .el-form-item {
    margin-bottom: 8px !important;
    .el-form-item__label {
      margin-bottom: 0;
      height: 32px;
      line-height: 32px;
      text-align: left;
      padding-left: 10px;
    }
    .el-checkbox {
      margin-right: 15px;
    }
    .el-radio {
      margin-right: 15px;
    }
    .el-form-item__content {
      height: 32px;
      line-height: 32px;
    }
  }
}
.check-cross-page {
  color: #4cafeb;
  cursor: pointer;
}
</style>
