<template>
  <div class="qz-table__cell-selection" :style="style">
    <div class="qz-table__cell-selection__layer"></div>
    <div
      class="qz-table__cell-selection__corner"
      @mousedown="handleCornerMousedown"
      @mouseup="handleCornerMouseup"
    ></div>
    <input
      ref="input"
      type="text"
      class="qz-table__cell-selection__input"
      @copy="handleCopy"
      @paste="handlePaste"
    />
  </div>
</template>

<script>
export default {
  props: {
    selectionData: Object
  },
  watch: {
    selectionData: {
      deep: true,
      immediate: true,
      handler() {
        this.locatSelectionArea();
      }
    }
  },
  data() {
    return {
      style: {
        width: '0px',
        height: '0px',
        top: '-1000px',
        left: '-1000px'
      }
    };
  },
  computed: {
    table() {
      return this.$parent;
    },
    tableRect() {
      const tableEl = this.$parent.$el;
      const {
        left: tableLeft,
        top: tableTop,
        height: tableHeight,
        width: tableWidth
      } = tableEl.getBoundingClientRect();
      return {
        tableLeft,
        tableTop,
        tableHeight,
        tableWidth
      };
    }
  },
  methods: {
    locatSelectionArea() {
      let startCellEl = this.selectionData?.startCell.ele;
      let endCellEl = this.selectionData?.endCell.ele;
      if (!startCellEl || !endCellEl) {
        return;
      }

      const startRowIndex = this.selectionData?.startCell.rowIndex;
      const endRowIndex = this.selectionData?.endCell.rowIndex;
      if (startRowIndex > endRowIndex) {
        // 如果是从下往上选，需要调转计算方向
        [startCellEl, endCellEl] = [endCellEl, startCellEl];
      }
      const {
        left: startCellLeft,
        top: startCellTop,
        width: startCellWidth
      } = startCellEl.getBoundingClientRect();
      const { top: endCellTop, height: endCellHeight } =
        endCellEl.getBoundingClientRect();

      this.style.top = `${startCellTop - this.tableRect.tableTop}px`;
      this.style.left = `${startCellLeft - this.tableRect.tableLeft}px`;
      this.style.width = `${startCellWidth}px`;
      this.style.height = `${endCellTop - startCellTop + endCellHeight}px`;
    },
    handleCornerMousedown() {
      this.$emit('corner-mousedown');
    },
    handleCornerMouseup() {
      this.$emit('corner-mouseup');
    },
    focus() {
      this.$refs.input.focus();
    },
    autofill() {
      let startRowIndex = this.selectionData?.startCell.rowIndex;
      let startColIndex = this.selectionData?.startCell.colIndex;
      let endRowIndex = this.selectionData?.endCell.rowIndex;
      let endColIndex = this.selectionData?.endCell.colIndex;
      if (startRowIndex === -1 || endRowIndex === -1) {
        return;
      }

      // 自动填充，需要获取第一个选中单元格的内容
      const dataList = this.table?.store?.states?.data || [];
      const rowData = dataList[startRowIndex];

      // 给每个选中的单元格触发粘贴事件
      if (startRowIndex > endRowIndex) {
        // 如果是从下往上选，需要调转计算方向
        [startRowIndex, endRowIndex] = [endRowIndex, startRowIndex];
      }
      if (startColIndex > endColIndex) {
        // 如果是从右往左选，需要调转计算方向
        [startColIndex, endColIndex] = [endColIndex, startColIndex];
      }
      const rows = dataList.slice(startRowIndex, endRowIndex + 1);
      let passedColIndex = -1;
      for (const comp of this.table.$children) {
        passedColIndex++;
        if (
          comp.$options.componentName === 'ApiTableColumn' &&
          passedColIndex >= startColIndex &&
          passedColIndex <= endColIndex
        ) {
          comp.$emit('cell-autofill', rows, rowData);
        }
      }
    },
    handleCopy(event) {
      event.preventDefault();
      let startRowIndex = this.selectionData?.startCell.rowIndex;
      let startColIndex = this.selectionData?.startCell.colIndex;
      let endRowIndex = this.selectionData?.endCell.rowIndex;
      let endColIndex = this.selectionData?.endCell.colIndex;
      if (startRowIndex === -1 || endRowIndex === -1) {
        return;
      }
      if (startRowIndex > endRowIndex) {
        // 如果是从下往上选，需要调转计算方向
        [startRowIndex, endRowIndex] = [endRowIndex, startRowIndex];
      }
      if (startColIndex > endColIndex) {
        // 如果是从右往左选，需要调转计算方向
        [startColIndex, endColIndex] = [endColIndex, startColIndex];
      }

      // 让第一个单元格触发拷贝事件
      const dataList = this.table?.store?.states?.data || [];
      const rowData = dataList[startRowIndex];
      let passedColIndex = -1;
      for (const comp of this.table.$children) {
        passedColIndex++;
        if (
          startColIndex === passedColIndex &&
          comp.$options.componentName === 'ApiTableColumn'
        ) {
          comp.$emit('cell-copy', event, rowData);
          break;
        }
      }
    },
    handlePaste(event) {
      event.preventDefault();
      let startRowIndex = this.selectionData?.startCell.rowIndex;
      let startColIndex = this.selectionData?.startCell.colIndex;
      let endRowIndex = this.selectionData?.endCell.rowIndex;
      let endColIndex = this.selectionData?.endCell.colIndex;
      if (startRowIndex === -1 || endRowIndex === -1) {
        return;
      }
      if (startRowIndex > endRowIndex) {
        // 如果是从下往上选，需要调转计算方向
        [startRowIndex, endRowIndex] = [endRowIndex, startRowIndex];
      }
      if (startColIndex > endColIndex) {
        // 如果是从右往左选，需要调转计算方向
        [startColIndex, endColIndex] = [endColIndex, startColIndex];
      }

      const dataTransfer = event?.clipboardData;
      const text = dataTransfer?.getData('text/plain') || '';
      // 给每个选中的单元格触发粘贴事件
      const dataList = this.table?.store?.states?.data || [];
      const rows = dataList.slice(startRowIndex, endRowIndex + 1);
      let passedColIndex = -1;
      for (const comp of this.table.$children) {
        passedColIndex++;
        if (
          comp.$options.componentName === 'ApiTableColumn' &&
          passedColIndex >= startColIndex &&
          passedColIndex <= endColIndex
        ) {
          comp.$emit('cell-paste', rows, text);
        }
      }
    }
  }
};
</script>

<style lang="less">
.qz-table__cell-selection {
  display: flex;
  position: absolute;
  width: 100px;
  height: 50px;
  z-index: 1;
  top: 0;
  left: 0;
  border: 2px solid #4a97eb;
  box-sizing: border-box;
  // 不让选区响应鼠标事件，这样就不用担心选择内重新框选的问题
  pointer-events: none;

  .qz-table__cell-selection__layer {
    flex: auto;
    background-color: #4a97eb;
    opacity: 0.2;
  }

  .qz-table__cell-selection__corner {
    position: absolute;
    bottom: 0;
    right: 0;
    height: 5px;
    width: 5px;
    background-color: #1f5ce9;
    cursor: crosshair;
    pointer-events: auto;
  }
}
.qz-table__cell-selection__input {
  position: absolute;
  top: 0;
  left: 0;
  width: 0;
  height: 0;
  border: none;
  outline: none;
  background-color: transparent;
}
</style>
