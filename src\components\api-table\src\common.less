//浅色主题蓝
@item-hover-color: #f4f9ff;
//主题蓝
@theme-deep-blue: #1f5ce9;
@theme-blue: #47a5e7;
//灰度从上往下加深
@bg-grey: #f3f5f7;
@shallow-grey: #f4f4f4;
@table-head-color: #f3f5f7;
@table-head-font-color: #909399;
@mid-grey: #e7ebf7;
@input-border-grey: #e9ebf0;
@inactive-grey: #c4c6c9;
@deep-grey: #979797;
@font-grey: #666;
//浅黑
@shallow-black: #2e3444;
//侧边导航黑
@side-color: #121930;

.api-table.fullscreen {
  background-color: #fff;
  padding: 20px;
}

.api-table {
  .border-right {
    border-right: 1px solid @input-border-grey;
  }
  .and-or-box {
    width: 20%;
    border-right: 1px solid @input-border-grey;
  }
}
