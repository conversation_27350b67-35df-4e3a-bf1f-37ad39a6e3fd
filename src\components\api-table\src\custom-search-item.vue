<template>
  <el-col :span="12">
    <el-form-item :label="label" class="api-table__search-form-item">
      <slot></slot>
    </el-form-item>
  </el-col>
</template>

<script>
import Emitter from './emitter';

export default {
  name: 'ApiTableSearchItem',
  mixins: [Emitter],
  props: {
    itemKey: {
      type: String,
      required: true
    },
    label: {
      type: String,
      required: true
    },
    /**
     * 用于格式化标签，返回值是字符串
     */
    tagFormatter: {
      type: Function,
      required: true
    },
    /**
     * 用于清除标签，无须返回值
     */
    tagCleaner: {
      type: Function,
      required: true
    },
    /**
     * 用于格式化请求，返回值格式为{key: value}。会作为请求的参数传递
     */
    paramsFormatter: {
      type: Function,
      required: true
    },
    /**
     * 用于从表格外部调用search方法时，往自定义表单项中设置值
     */
    searchSetter: {
      type: Function
    },
    order: {
      type: Number,
      default: 0
    }
  },
  mounted() {
    this.dispatch('ApiTable', 'searchForm.addField', this);
  }
};
</script>
