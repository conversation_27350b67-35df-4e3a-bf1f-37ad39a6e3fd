<template>
  <el-popover
    v-if="togglableColumns.length > 0"
    placement="bottom"
    trigger="click"
    class="api-table__col-config"
    popper-class="api-table__col-config-popper"
  >
    <el-tooltip
      slot="reference"
      :open-delay="200"
      effect="dark"
      content="列设置"
      placement="top"
    >
      <svg-icon class="btn-tool" icon="table-tool-col-config"></svg-icon>
    </el-tooltip>
    <div class="api-table__col-config-box-wrapper">
      <el-checkbox
        v-model="allSelected"
        :indeterminate="partialSelected"
        @change="handleAllSelectedChanged"
      >
        显示所有列
      </el-checkbox>
    </div>
    <div class="api-table__col-config-box-divider"></div>
    <draggable v-model="togglableColumns" :options="{ forceFallback: true }">
      <transition-group>
        <div
          v-for="column of togglableColumns"
          :key="column.property || ''"
          class="api-table__col-config-box-wrapper"
        >
          <el-checkbox
            v-model="column.visible"
            @change="handleVisibleChange"
            @pointerdown.stop.native
          ></el-checkbox>
          <svg-icon class="btn-tool" icon="table-tool-drag"></svg-icon>
          <span>{{ column.label }}</span>
        </div>
      </transition-group>
    </draggable>
  </el-popover>
  <el-tooltip
    v-else
    :open-delay="200"
    effect="dark"
    content="列设置"
    placement="top"
  >
    <svg-icon class="btn-tool" icon="table-tool-col-config"></svg-icon>
  </el-tooltip>
</template>

<script>
import draggable from 'vuedraggable';

export default {
  components: { draggable },
  inject: ['rootTable'],
  data() {
    return {
      togglableColumns: [], // 不包含固定列和操作列
      ignoreChange: false,
      allSelected: false
    };
  },
  computed: {
    // 控制全选的不确定状态
    partialSelected() {
      const selectedCount = this.togglableColumns.filter(
        (item) => item.visible
      ).length;
      return selectedCount > 0 && selectedCount < this.togglableColumns.length;
    }
  },
  watch: {
    'rootTable.store.states._columns'() {
      this.ignoreChange = true;
      this.togglableColumns = [].concat(
        this.rootTable.store.states._columns.filter(
          (item) => !item.fixed && item.type !== 'selection'
        )
      );
      // 修复有些地方可选列点击后没反应的问题
      this.togglableColumns.forEach((item) => {
        this.$set(item, 'visible', !!item.visible);
      });
      const selectedCount = this.togglableColumns.filter(
        (item) => item.visible
      ).length;
      this.allSelected = selectedCount === this.togglableColumns.length;
    },
    togglableColumns() {
      if (this.ignoreChange) {
        this.ignoreChange = !this.ignoreChange;
      } else {
        this.rootTable.store.states._columns = []
          .concat(
            this.rootTable.store.states._columns.filter(
              (item) => !!item.fixed || item.type === 'selection'
            )
          )
          .concat(this.togglableColumns);
        this.rootTable.store.scheduleLayout(true);
        this.rootTable.rememberColConfig(
          this.togglableColumns.map((item) => {
            return {
              property: item.property,
              visible: item.visible
            };
          })
        );
      }
    }
  },
  methods: {
    handleVisibleChange() {
      this.rootTable.store.scheduleLayout(true);
      this.rootTable.rememberColConfig(
        this.togglableColumns.map((item) => {
          return {
            property: item.property,
            visible: item.visible
          };
        })
      );
    },
    handleAllSelectedChanged(val) {
      if (val) {
        this.togglableColumns.forEach((item) => {
          this.$set(item, 'visible', true);
        });
      } else {
        this.togglableColumns.forEach((item) => {
          this.$set(item, 'visible', false);
        });
      }
      this.togglableColumns = [].concat(this.togglableColumns);
    }
  }
};
</script>

<style lang="less" scoped>
@import '../common.less';
.btn-tool {
  width: 20px;
  height: 20px;
  color: @font-grey;
  cursor: pointer;
  &:hover {
    color: @theme-blue;
  }
}
</style>
<style lang="less">
@import '../common.less';

.api-table__col-config-popper {
  padding: 5px;
  max-height: 300px;
  overflow-y: auto;
}
.api-table__col-config-box-wrapper {
  display: flex;
  align-items: center;
  padding: 5px 10px;
  &:hover {
    background-color: @bg-grey;
  }
  .el-checkbox {
    display: flex;
    align-items: center;
    font-weight: 400;
  }
  .el-checkbox__label {
    display: flex;
    align-items: center;
    padding-left: 5px;
    color: #3d3d3d;
  }
  .el-checkbox__input.is-checked + .el-checkbox__label {
    color: #3d3d3d;
  }
  .btn-tool {
    margin: 0 5px;
    cursor: move;
  }
}
.api-table__col-config-box-divider {
  height: 1px;
  background-color: @input-border-grey;
  margin: 5px 0;
}
</style>
