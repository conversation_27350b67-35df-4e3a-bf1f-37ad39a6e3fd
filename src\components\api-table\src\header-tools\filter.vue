<template>
  <el-tooltip :open-delay="200" effect="dark" content="筛选" placement="top">
    <div @click="toggleFilter">
      <svg-icon
        :class="{ active: searchFormVisible }"
        class="btn-tool"
        icon="table-tool-filter"
      ></svg-icon>
    </div>
  </el-tooltip>
</template>

<script>
export default {
  inject: ['rootTable'],
  computed: {
    searchFormVisible() {
      return this.rootTable.searchFormVisible;
    }
  },
  methods: {
    toggleFilter() {
      if (this.searchFormVisible) {
        this.rootTable.hideSearchForm();
      } else {
        this.rootTable.showSearchForm();
      }
    }
  }
};
</script>

<style lang="less" scoped>
@import '../common.less';
.btn-tool {
  width: 16px;
  height: 16px;
  color: @font-grey;
  cursor: pointer;
  &.active,
  &:hover {
    color: @theme-blue;
  }
}
</style>
