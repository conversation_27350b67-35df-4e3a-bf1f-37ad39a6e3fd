<template>
  <el-tooltip
    :open-delay="200"
    effect="dark"
    content="全屏展示"
    placement="top"
  >
    <div @click="handleFullscreen">
      <svg-icon
        :class="{ active: isFullscreen }"
        class="btn-tool"
        icon="table-tool-fullscreen"
      ></svg-icon>
    </div>
  </el-tooltip>
</template>

<script>
import screenfull from 'screenfull';

export default {
  inject: ['rootTable'],
  data() {
    return {
      isFullscreen: false
    };
  },
  mounted() {
    screenfull.on('change', this.handleFullscreenChange);
  },
  beforeDestroy() {
    screenfull.off('change', this.handleFullscreenChange);
  },
  methods: {
    handleFullscreenChange(event) {
      const ele = this.rootTable.$el;
      if (ele !== event.target) {
        return;
      }

      this.isFullscreen = screenfull.isFullscreen;
      if (screenfull.isFullscreen) {
        ele.classList.add('fullscreen');
      } else {
        ele.classList.remove('fullscreen');
      }
    },
    handleFullscreen() {
      const ele = this.rootTable.$el;
      if (screenfull.isEnabled && ele) {
        screenfull.toggle(ele);
      }
    }
  }
};
</script>

<style lang="less" scoped>
@import '../common.less';
.btn-tool {
  width: 16px;
  height: 16px;
  color: @font-grey;
  cursor: pointer;
  &.active,
  &:hover {
    color: @theme-blue;
  }
}
</style>
