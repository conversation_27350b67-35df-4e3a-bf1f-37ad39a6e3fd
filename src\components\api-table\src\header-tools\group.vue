<template>
  <el-dropdown
    v-if="groupConfigs.length > 0"
    trigger="click"
    @command="handleGroup"
  >
    <el-tooltip :open-delay="200" effect="dark" content="分组" placement="top">
      <svg-icon
        :class="{ active: hasGroup }"
        class="btn-tool"
        icon="table-tool-group"
      ></svg-icon>
    </el-tooltip>
    <el-dropdown-menu slot="dropdown">
      <el-dropdown-item
        v-for="config of groupConfigs"
        :key="config.value"
        :command="config.value"
      >
        {{ config.label }}
      </el-dropdown-item>
    </el-dropdown-menu>
  </el-dropdown>
  <el-tooltip
    v-else
    :open-delay="200"
    effect="dark"
    content="分组"
    placement="top"
  >
    <svg-icon class="btn-tool" icon="table-tool-group"></svg-icon>
  </el-tooltip>
</template>

<script>
export default {
  inject: ['rootTable'],
  computed: {
    groupConfigs() {
      return this.rootTable.internalGroupConfigs || [];
    },
    hasGroup() {
      return !!this.rootTable.groupBy;
    }
  },
  methods: {
    handleGroup(groupBy) {
      this.rootTable.group(groupBy);
    }
  }
};
</script>

<style lang="less" scoped>
@import '../common.less';
.btn-tool {
  width: 16px;
  height: 16px;
  color: @font-grey;
  cursor: pointer;
  &.active,
  &:hover {
    color: @theme-blue;
  }
}
</style>
