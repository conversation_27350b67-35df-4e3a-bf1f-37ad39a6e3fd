<template>
  <el-dropdown trigger="click">
    <el-tooltip
      style="transform: rotate(90deg)"
      :open-delay="200"
      effect="dark"
      content="相关配置"
      placement="top"
    >
      <div>
        <qz-icon class="icon-more"></qz-icon>
      </div>
    </el-tooltip>
    <el-dropdown-menu slot="dropdown">
      <el-dropdown-item
        v-for="page of jumpPageConfigs"
        :key="page.name"
        :command="page.name"
        @click.native="page.jumpFunc"
      >
        {{ page.name }}
      </el-dropdown-item>
    </el-dropdown-menu>
  </el-dropdown>
</template>

<script>
export default {
  inject: ['rootTable'],
  computed: {
    jumpPageConfigs() {
      return this.rootTable.jumpPageConfigs;
    }
  }
};
</script>

<style lang="less" scoped>
@import '../common.less';
.icon-more {
  color: @font-grey;
  cursor: pointer;
  // transform: rotate(90);
  &:hover {
    color: @theme-blue;
  }
}
</style>
