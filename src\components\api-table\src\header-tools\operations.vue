<template>
  <el-dropdown v-if="operations.length > 0" trigger="click">
    <el-button type="primary" size="mini">
      批量操作
      <i class="el-icon-arrow-down el-icon--right"></i>
    </el-button>
    <el-dropdown-menu slot="dropdown">
      <el-dropdown-item
        v-for="{ name, handler, disabled } of operations"
        :key="name"
        :disabled="disabled"
        @click.native="handleAction(handler)"
        >{{ name }}</el-dropdown-item
      >
    </el-dropdown-menu>
  </el-dropdown>
  <el-button v-else type="primary" size="mini">
    批量操作
    <i class="el-icon-arrow-down el-icon--right"></i>
  </el-button>
</template>

<script>
export default {
  inject: ['rootTable'],
  data() {
    return {};
  },
  computed: {
    operations() {
      return this.rootTable.internalOperations;
    }
  },
  methods: {
    handleAction(handler) {
      handler && handler();
    }
  }
};
</script>

<style lang="less"></style>
