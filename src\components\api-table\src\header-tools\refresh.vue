p
<template>
  <el-tooltip :open-delay="200" effect="dark" content="刷新" placement="top">
    <div @click="handleRefresh">
      <svg-icon class="btn-tool" icon="table-tool-refresh"></svg-icon>
    </div>
  </el-tooltip>
</template>

<script>
export default {
  inject: ['rootTable'],
  methods: {
    handleRefresh() {
      this.rootTable.reload();
      this.$message.success('刷新成功');
    }
  }
};
</script>

<style lang="less" scoped>
@import '../common.less';
.btn-tool {
  width: 17px;
  height: 17px;
  color: @font-grey;
  cursor: pointer;
  &:hover {
    color: @theme-blue;
  }
}
</style>
