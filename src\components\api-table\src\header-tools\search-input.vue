<template>
  <span
    ><template v-if="querySearchAsync">
      <el-autocomplete
        v-model="value"
        size="small"
        :style="width"
        clearable
        @clear="handleSearch"
        @keypress.native.enter="handleSearch"
        :fetch-suggestions="querySearchAsync"
        :placeholder="placeholder"
      ></el-autocomplete>
    </template>
    <template v-else>
      <el-input
        v-model.trim="value"
        :placeholder="placeholder"
        :suffix-icon="!value ? 'el-icon-search' : ''"
        size="mini"
        clearable
        @clear="handleSearch"
        @keypress.native.enter="handleSearch"
        :style="width"
      ></el-input> </template
  ></span>
</template>

<script>
export default {
  inject: ['rootTable'],
  data() {
    const defaultValue = this.rootTable.searchInputOptions?.default;
    if (defaultValue) {
      this.$set(
        this.rootTable.extraReqParams,
        this.rootTable.searchInputOptions?.key || 'keywords',
        defaultValue
      );
    }
    return {
      value: defaultValue || ''
    };
  },
  computed: {
    key() {
      return this.rootTable.searchInputOptions?.key || 'keywords';
    },
    placeholder() {
      return (
        this.rootTable.searchInputOptions?.placeholder || '请输入关键字进行筛选'
      );
    },
    querySearchAsync() {
      return this.rootTable.searchInputOptions?.querySearchAsync || null;
    },
    width() {
      return this.rootTable.searchInputOptions?.width
        ? `width: ${this.rootTable.searchInputOptions?.width}`
        : 'width: 300px';
    }
  },
  watch: {
    value() {
      if (this.value === '') {
        this.$delete(this.rootTable.extraReqParams, this.key);
      } else {
        this.$set(this.rootTable.extraReqParams, this.key, this.value);
      }
    },
    'rootTable.searchInputOptions.default'() {
      const defaultValue = this.rootTable.searchInputOptions?.default;
      if (defaultValue) {
        this.$set(
          this.rootTable.extraReqParams,
          this.rootTable.searchInputOptions?.key || 'keywords',
          defaultValue
        );
        this.value = defaultValue;
      }
    }
  },
  methods: {
    handleSearch() {
      this.rootTable.reload();
    },
    setKeywords(value) {
      this.value = value;
    },
    clearKeywords() {
      this.value = '';
    }
  }
};
</script>

<style lang="less" scoped>
.vertical-divider {
  width: 1px;
  height: 24px;
  background-color: #d8d8d8;
}
@{deep}.el-input__inner {
  padding-right: 50px !important;
}
</style>
