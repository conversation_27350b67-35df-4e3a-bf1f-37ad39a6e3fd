<!--
 * @Fileoverview: 搜索表单项类型组件
 * @Description: 表格-高级筛选-搜索表单项类型组件
-->
<template>
  <el-col :span="12">
    <el-form-item
      :label="searchConfig.label || label"
      class="api-table__search-form-item"
    >
      <template slot="label">
        <el-tooltip :content="searchConfig.label || label" placement="top">
          <div class="text-hidden">{{ searchConfig.label || label }}</div>
        </el-tooltip>
      </template>
      <el-select
        v-if="
          searchConfig.type === 'single-selection' ||
          searchConfig.type === 'multi-selection'
        "
        v-model="formConditions[property]"
        :multiple="searchConfig.type === 'multi-selection'"
        :placeholder="searchConfig.placeholder || '请选择'"
        filterable
        clearable
        collapse-tags
        size="small"
        style="width: 100%"
        @change="handleChange"
      >
        <el-option
          v-for="option of searchConfig.options || []"
          :key="`${searchConfig.type}-${option[optionValueKey]}`"
          :label="option[optionLabelKey]"
          :value="option[optionValueKey]"
        ></el-option>
      </el-select>
      <el-radio-group
        v-else-if="searchConfig.type === 'radio-group'"
        v-model="formConditions[property]"
        size="small"
        style="width: 100%"
        @change="handleChange"
      >
        <el-radio
          v-for="option of searchConfig.options || []"
          :key="`${searchConfig.type}-${option[optionValueKey]}`"
          :label="option[optionValueKey]"
          @click.native.prevent="toggleRadioSelected(option[optionValueKey])"
        >
          {{ option[optionLabelKey] }}
        </el-radio>
      </el-radio-group>
      <el-checkbox-group
        v-else-if="searchConfig.type === 'checkbox-group'"
        v-model="formConditions[property]"
        size="small"
        style="width: 100%"
        @change="handleChange"
      >
        <el-checkbox
          v-for="option of searchConfig.options || []"
          :key="`${searchConfig.type}-${option[optionValueKey]}`"
          :label="option[optionValueKey]"
        >
          {{ option[optionLabelKey] }}
        </el-checkbox>
      </el-checkbox-group>
      <el-cascader
        :ref="`${property}Cascader`"
        v-else-if="searchConfig.type === 'multi-cascader'"
        v-model="formConditions[property]"
        :options="searchConfig.options"
        :props="{ multiple: true, ...(searchConfig.optionProps || {}) }"
        :placeholder="searchConfig.placeholder || '请选择'"
        :filterable="searchConfig.filterable"
        collapse-tags
        clearable
        size="small"
        style="width: 100%"
        @change="changeCascader($event, property)"
      ></el-cascader>
      <qz-tree-select
        v-else-if="searchConfig.type === 'tree-selection'"
        v-model="formConditions[property]"
        :data="searchConfig.treeData || []"
        :props="searchConfig.treeProps"
        :placeholder="searchConfig.placeholder || '请选择'"
        clearable
        size="small"
        style="width: 100%"
        @change="handleChange"
      ></qz-tree-select>
      <el-date-picker
        v-else-if="
          searchConfig.type === 'daterange' ||
          searchConfig.type === 'datetimerange' ||
          searchConfig.type === 'date'
        "
        v-model="formConditions[property]"
        :picker-options="searchConfig.pickerOptions || DATE_PICKER_OPTIONS"
        :type="searchConfig.type"
        :start-placeholder="searchConfig.startPlaceholder || '开始日期'"
        :end-placeholder="searchConfig.endPlaceholder || '结束日期'"
        :default-time="
          searchConfig.type === 'datetimerange'
            ? searchConfig.defaultTime || ['00:00:00', '23:59:59']
            : searchConfig.defaultTime
        "
        :placeholder="searchConfig.placeholder || '请选择'"
        value-format="timestamp"
        range-separator="~"
        size="small"
        style="width: 100%"
        unlink-panels
        clearable
        @change="handleChange"
      ></el-date-picker>
      <el-switch
        v-else-if="searchConfig.type === 'switch'"
        v-model="formConditions[property]"
        @change="handleChange"
      >
      </el-switch>

      <el-autocomplete
        v-model.trim="formConditions[property]"
        size="small"
        clearable
        :fetch-suggestions="searchConfig.querySearchAsync"
        @input="handleChange"
        @select="handleChange"
        v-else-if="searchConfig.type === 'autocomplete-text'"
        :placeholder="searchConfig.placeholder || '请输入'"
      ></el-autocomplete>
      <el-input
        v-else
        v-model.trim="formConditions[property]"
        :placeholder="searchConfig.placeholder || '请输入'"
        clearable
        type="text"
        size="small"
        @input="handleChange"
      ></el-input>
    </el-form-item>
  </el-col>
</template>

<script>
import { handleCascaderChange } from '@/utils/handle-cascader-change';
import { DATE_PICKER_OPTIONS } from '@/constant/common-constants';
export default {
  props: {
    property: String,
    label: String,
    searchConfig: Object,
    formConditions: Object,
    order: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      DATE_PICKER_OPTIONS
    };
  },
  computed: {
    optionValueKey() {
      return this.searchConfig.optionProps?.value || 'value';
    },
    optionLabelKey() {
      return this.searchConfig.optionProps?.label || 'label';
    }
  },
  methods: {
    changeCascader(node, type) {
      handleCascaderChange(this, node, {}, type);
      this.handleChange(node);
    },
    handleChange(value) {
      if (this.searchConfig.onChange) {
        this.searchConfig.onChange(value, this.formConditions);
      }
    },
    toggleRadioSelected(value) {
      if (this.formConditions[this.property] === value) {
        this.$set(this.formConditions, this.property, '');
      } else {
        this.$set(this.formConditions, this.property, value);
      }
    }
  }
};
</script>

<style lang="less">
@import '../common.less';

.api-table__search-form-item {
  background-color: #fff;
  border-radius: 3px;
  border: 1px solid @input-border-grey;
  .el-form-item__label::after {
    display: none;
  }
  .el-form-item__label {
    border-right: 1px solid @input-border-grey;
  }
  .qz-tree-select__current-value,
  .el-input__inner {
    border: none;
    height: 32px !important;
  }
  .el-switch,
  .el-radio-group,
  .el-checkbox-group {
    padding-left: 10px;
  }
  .el-radio:focus:not(.is-focus):not(:active):not(.is-disabled)
    .el-radio__inner {
    box-shadow: none;
  }
}
</style>
