import Vue from 'vue';

export default {
  name: 'ApiTableToolRegister',
  componentName: 'ApiTableToolRegister',
  inject: ['rootTable'],
  props: {
    id: {
      type: String,
      required: true
    }
  },
  mounted() {
    // 创建一个Vue组件
    const comp = Vue.extend({
      data: () => ({ compKey: this.id }),
      render: (h) => h('div', this.$slots.default) // 获取传递给组件的未分发到任何具名插槽中的内容
    });
    this.registerTool(this.id, comp);
  },
  updated() {
    this.rootTable.$children
      .filter((child) => child.compKey === this.id)
      .forEach((child) => {
        child.$forceUpdate();
      });
  },
  render() {
    return null;
  },
  methods: {
    registerTool(key, comp) {
      const tools = this.rootTable.tools;
      tools[key] = comp;
    }
  }
};
