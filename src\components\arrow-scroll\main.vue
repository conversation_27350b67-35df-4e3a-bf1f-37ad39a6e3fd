<template>
  <div class="arrow-scroll">
    <i
      v-if="scrollInfo.enabled"
      :class="{ disabled: !scrollInfo.prev }"
      class="arrow-scroll__handler arrow-scroll__prev el-icon-caret-left"
      @click="scrollPrev"
    ></i>
    <div ref="wrapper" class="arrow-scroll__wrapper">
      <div ref="content" :style="contentStyle" class="arrow-scroll__content">
        <slot></slot>
      </div>
    </div>
    <i
      v-if="scrollInfo.enabled"
      :class="{ disabled: !scrollInfo.next }"
      class="arrow-scroll__handler arrow-scroll__next el-icon-caret-right"
      @click="scrollNext"
    ></i>
  </div>
</template>

<script>
export default {
  props: {
    scrollStep: {
      type: Number,
      default: 200
    }
  },
  data() {
    return {
      scrollInfo: {
        enabled: false,
        prev: false,
        next: false
      },
      contentOffset: 0
    };
  },
  computed: {
    contentStyle() {
      return {
        transform: `translateX(-${this.contentOffset}px)`
      };
    }
  },
  mounted() {
    this.calcScrollable();
  },
  updated() {
    this.calcScrollable();
  },
  methods: {
    /**
     * 计算是否可滚动
     */
    calcScrollable() {
      const wrapperSize = this.$refs.wrapper.offsetWidth;
      const contentSize = this.$refs.content.offsetWidth;
      const currentOffset = this.contentOffset;
      if (wrapperSize < contentSize) {
        this.scrollInfo.enabled = true;
        this.scrollInfo.prev = currentOffset > 0;
        this.scrollInfo.next = currentOffset + wrapperSize < contentSize;
        if (wrapperSize + currentOffset > contentSize) {
          this.contentOffset = contentSize - wrapperSize;
        }
        if (currentOffset < 0) {
          this.contentOffset = 0;
        }
      } else {
        this.scrollInfo.enabled = false;
        this.contentOffset = 0;
      }
    },
    scrollPrev() {
      if (this.scrollInfo.prev) {
        this.contentOffset -= this.scrollStep;
      }
    },
    scrollNext() {
      if (this.scrollInfo.next) {
        this.contentOffset += this.scrollStep;
      }
    }
  }
};
</script>

<style lang="less" scoped>
.arrow-scroll {
  display: flex;
  align-items: center;
  overflow: hidden;
  .arrow-scroll__handler {
    cursor: pointer;
    &.disabled {
      color: #e9e8e8;
      cursor: not-allowed;
    }
  }
  .arrow-scroll__prev {
    flex: none;
    margin-right: 10px;
  }
  .arrow-scroll__next {
    flex: none;
    margin-left: 10px;
    cursor: pointer;
  }
  .arrow-scroll__wrapper {
    flex: auto;
    overflow: hidden;
  }
}
.arrow-scroll__content {
  display: inline-block;
  transition: all 0.2s;
  white-space: nowrap;
}
</style>
