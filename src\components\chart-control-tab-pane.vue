<template>
  <div class="chart-control-tab-pane" :class="{ 'no-data': !nodes.length }">
    <div v-loading="loading" ref="chart" class="chart"></div>
    <div class="left tab-pane" v-show="isDetailShow">
      <slot name="leftPane"></slot>
    </div>
    <div class="right">
      <div class="tab-pane" v-show="isFilterShow">
        <slot name="rightPane"></slot>
      </div>
      <div class="control-zone ml10">
        <div
          class="control-button"
          :class="{ active: isFilterShow }"
          @click="isFilterShow = !isFilterShow"
        >
          <qz-svg-icon icon="icon-filter"></qz-svg-icon>
          筛选
        </div>
        <div
          class="control-button"
          :class="{ active: isShowText }"
          @click="isShowText = !isShowText"
        >
          <qz-icon class="icon-tools-set"></qz-icon>
          文字
        </div>
        <div class="zoom-control">
          <i class="el-icon-circle-plus-outline" @click="zoomIn()"></i>
          <el-slider
            v-model="zoomValue"
            :min="zoomMin"
            :max="zoomMax"
            vertical
            :show-tooltip="false"
            height="150px"
          >
          </el-slider>
          <i class="el-icon-remove-outline" @click="zoomOut()"></i>
        </div>
        <div class="control-button" @click="zoomValue = zoomDefault">
          <i class="el-icon-refresh-right"></i>
          重置
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { throttle } from 'echarts';
export default {
  props: {
    nodes: {
      type: Array,
      default: () => []
    },
    links: {
      type: Array,
      default: () => []
    },
    categories: {
      type: Array,
      default: () => []
    },
    currentId: {
      type: String,
      default: ''
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      zoomMax: 16,
      zoomMin: 6,
      zoomDefault: 8,
      zoomValue: this.zoomDefault,
      options: {},
      isShowText: false,
      isFilterShow: true,
      isDetailShow: false,
      chart: null,
      resizeFunc: throttle(() => {
        this.chart && this.chart.resize();
      }, 500)
      // currentId: ""
    };
  },
  watch: {
    currentId() {
      if (this.currentId) {
        this.isDetailShow = true;
      } else {
        this.isDetailShow = false;
      }
      this.initChart();
    },
    isShowText() {
      this.initChart();
    },
    zoomValue() {
      this.initChart();
    },
    nodes() {
      this.initChart();
    }
  },
  methods: {
    // 放大
    zoomIn() {
      if (this.zoomValue == this.zoomMax) return;
      this.zoomValue += 2;
    },
    // 缩小
    zoomOut() {
      if (this.zoomValue == this.zoomMin) return;
      this.zoomValue -= 2;
    },
    handleActive(type) {
      if (this.activeList.includes(type)) {
        const index = this.activeList.findIndex((item) => {
          return item == type;
        });
        this.activeList.splice(index, 1);
      } else {
        this.activeList.push(type);
      }
    },
    initChart() {
      const self = this,
        dom = this.$refs['chart'],
        nodes = JSON.parse(JSON.stringify(this.nodes)),
        links = JSON.parse(JSON.stringify(this.links)),
        relatedModules = [this.currentId],
        categories = this.categories;
      const linkList = links.map((link) => {
        if ([link.source, link.target].includes(this.currentId)) {
          !relatedModules.includes(link.source)
            ? relatedModules.push(link.source)
            : '';
          !relatedModules.includes(link.target)
            ? relatedModules.push(link.target)
            : '';
          const node = nodes.find((item) => {
            return item.id == link.source;
          });
          link.lineStyle = {
            opacity:
              !this.currentId || relatedModules.includes(this.currentId)
                ? 1
                : 0.4,
            color: node.color
          };
        }
        return link;
      });
      const nodeList = nodes.map((node) => {
        node.itemStyle = {
          opacity: !this.currentId || relatedModules.includes(node.id) ? 1 : 0.4
        };
        return node;
      });
      this.chart =
        this.$echarts.getInstanceByDom(dom) || this.$echarts.init(dom);
      this.options = {
        roam: 'move',
        tooltip: {
          show: true,
          formatter(params) {
            if (params.dataType == 'node') {
              return `<div style="color:#303133;">${params.data.type}</div><div style="color: #999;">${params.data.name}</div>`;
            }
            if (params.dataType == 'edge' && self.isShowText) {
              return `<div style="max-width: 350px;white-space: pre-wrap;">${params.value}</div>`;
            }
          },
          extraCssText:
            'border: none;box-shadow:  0 1px 6px 3px rgba(210,212,216,0.43);'
        },
        animationDurationUpdate: 1500,
        animationEasingUpdate: 'quinticInOut',
        label: {
          normal: {
            show: true,
            textStyle: {
              fontSize: 12
            }
          }
        },
        legend: {
          show: false
        },
        emphasis: {
          disabled: true
        },
        series: {
          roam: 'move',
          type: 'graph',
          layout: 'force',
          zoom: self.zoomValue,
          force: {
            layoutAnimation: false,
            repulsion: 10,
            gravity: 0.3,
            edgeLength: 15
          },
          edgeSymbol: ['', 'arrow'],
          edgeSymbolSize: [4, 6],
          edgeLabel: {
            normal: {
              show: true,
              textStyle: {
                fontSize: 10
              },
              formatter(params) {
                let str = '';
                if (self.isShowText) {
                  str = params.value;
                }
                const values = str.split('，');
                // 长度大于1时，取第一个，其他省略
                if (values.length > 1) {
                  str = `${values[0]}...`;
                }
                return str;
              }
            }
          },
          data: nodeList,
          links: linkList
        }
      };
      self.chart.setOption(this.options, false);
    }
  },
  mounted() {
    const self = this;
    this.zoomValue = this.zoomDefault;
    this.initChart();
    this.chart.on('click', function (params) {
      self.$emit('connection-item-click', params);
    });
    this.chart.getZr().on('click', function (event) {
      if (!event.target) {
        // 点击在了空白处，做些什么。
        self.$emit('connection-item-click', {});
      }
    });
    window.addEventListener('resize', this.resizeFunc);
  },
  beforeDestroy() {
    this.chart && this.chart.dispose();
    window.removeEventListener('resize', this.resizeFunc);
  }
};
</script>
<style lang="less">
.chart-control-tab-pane {
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  & .chart {
    width: 100%;
    height: 100%;
    flex: 1 1 0%;
  }
  & .left {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    bottom: 20px;
    left: 20px;
    height: 90%;
    max-height: 770px;
    z-index: 2;
  }
  & .tab-pane {
    width: 380px;
    border-radius: 5px;
    overflow: hidden;
    min-height: 300px;
    box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.1);
  }
  & .right {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    bottom: 20px;
    right: 10px;
    display: flex;
    align-items: center;
    height: 90%;
    max-height: 525px;
    z-index: 2005;
    & .tab-pane {
      height: 100%;
    }
  }
  & .control-zone {
    display: flex;
    flex-direction: column;
  }
  & .zoom-control {
    display: flex;
    flex-direction: column;
    text-align: center;
    background: #fff;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    margin: 10px 0;
    padding: 8px 0;
    & i {
      font-size: 16px;
      cursor: pointer;
      color: #979797;
    }
    & .el-slider {
      margin-top: 10px;
      margin-bottom: 10px;
      & .el-slider__bar {
        width: 2px;
        background-color: #d5d5d5;
      }
      & .el-slider__button {
        border: 1px solid #afafaf;
        background-color: #f3f3f3;
        width: 12px;
        height: 12px;
      }
      & .el-slider__button-wrapper {
        left: -17px;
      }
      & .el-slider__runway {
        width: 2px;
      }
    }
  }
  & .control-button {
    display: flex;
    flex-direction: column;
    align-items: center;
    border: 1px solid #e4e7ed;
    border-radius: 3px;
    background: #fff;
    color: #606266;
    font-size: 12px;
    padding: 5px;
    cursor: pointer;
    &.active {
      background: #4a97eb;
      color: #fff;
    }
    & .svg-icon,
    .qz-iconfont,
    .el-icon-refresh-right {
      font-size: 18px;
    }
    & + .control-button {
      margin-top: 10px;
    }
  }
}
</style>
