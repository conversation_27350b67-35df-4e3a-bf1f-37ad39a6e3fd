<!--
 * @Fileoverview: 列表文字过多展示组件
 * @Description: 列表文字过多展示组件
-->
<template>
  <div style="display: flex" class="column-overflow">
    <el-tooltip :content="content" placement="top" :disabled="!isShowTooltip">
      <div
        class="column-overflow__hidden"
        @mouseenter="visibilityChange($event)"
      >
        {{ content || '--' }}
      </div>
    </el-tooltip>
  </div>
</template>

<script>
export default {
  props: {
    content: {
      type: String,
      default: ''
    },
    direction: {
      type: String,
      default: 'horizontal'
    }
  },
  data() {
    return {
      isShowTooltip: false
    };
  },
  methods: {
    visibilityChange(event) {
      const ev = event.target;
      let ev_size = ev.scrollWidth;
      let content_size = ev.clientWidth;
      if (this.direction === 'vertical') {
        ev_size = ev.scrollHeight;
        content_size = ev.clientHeight;
      }
      if (ev_size > content_size) {
        this.isShowTooltip = true;
      } else {
        this.isShowTooltip = false;
      }
    }
  }
};
</script>

<style lang="less">
.column-overflow {
  width: 100%;
  &__hidden {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
</style>
