<template>
  <qz-popconfirm v-bind="$attrs" @confirm="handleConfirm" @hide="handlePopHide">
    <qz-switch
      slot="reference"
      v-model="internalValue"
      :active-value="activeValue"
      :inactive-value="inactiveValue"
      style="width: 50px"
      active-text="ON"
      inactive-text="OFF"
    ></qz-switch>
  </qz-popconfirm>
</template>

<script>
export default {
  props: {
    value: [Boolean, Number, String],
    activeValue: {
      type: [Boolean, Number, String],
      default: true
    },
    inactiveValue: {
      type: [<PERSON>olean, Number, String],
      default: false
    }
  },
  data() {
    return {
      hideAsConfirm: false,
      internalValue: this.value,

      ignoreChange: false,
      originalValue: null
    };
  },
  watch: {
    value() {
      if (this.ignoreChange) {
        this.ignoreChange = false;
      } else {
        this.internalValue = this.value;
      }
    },
    internalValue() {
      this.$emit('input', this.internalValue);
    }
  },
  methods: {
    reverseValue() {
      if (this.internalValue === this.activeValue) {
        this.internalValue = this.inactiveValue;
      } else {
        this.internalValue = this.activeValue;
      }
    },
    handleConfirm() {
      this.hideAsConfirm = true;
      this.$emit('confirm');
      this.$nextTick(() => {
        this.hideAsConfirm = false;
      });
    },
    handlePopHide() {
      // 点击取消或者空白位置，需要重置状态
      if (!this.hideAsConfirm) {
        this.reverseValue();
      }
    }
  },
  mounted() {
    this.originalValue = this.value;
  }
};
</script>
