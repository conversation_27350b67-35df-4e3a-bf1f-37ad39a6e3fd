<template>
  <div class="cron-selection">
    <el-radio-group v-model="type">
      <el-radio label="once">
        一次
        <el-date-picker
          :disabled="type !== 'once'"
          v-model="onceConfig.date"
          class="ml5 w180"
          size="small"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="选择日期"
        >
        </el-date-picker>
        <el-time-picker
          :disabled="type !== 'once'"
          v-model="onceConfig.time"
          class="ml5 w180"
          size="small"
          arrow-control
          :picker-options="{
            selectableRange: '00:00:00 - 23:59:59'
          }"
          value-format="H:m:s"
          placeholder="请选择时间"
        >
        </el-time-picker>
      </el-radio>
      <el-radio label="year">
        每年
        <el-select
          :disabled="type !== 'year'"
          v-model="yearConfig.month"
          class="ml5 w180"
          size="small"
        >
          <el-option
            v-for="m in monthList"
            :key="`month${m}`"
            :label="`${m}月`"
            :value="m"
          ></el-option>
        </el-select>
        <el-select
          :disabled="type !== 'year'"
          v-model="yearConfig.day"
          class="ml5 w180"
          size="small"
        >
          <el-option
            v-for="d in yearConfigDateList"
            :key="`date${d}`"
            :label="`${d}日`"
            :value="d"
          ></el-option>
        </el-select>
        <el-time-picker
          :disabled="type !== 'year'"
          v-model="yearConfig.time"
          class="ml5 w180"
          size="small"
          arrow-control
          :picker-options="{
            selectableRange: '00:00:00 - 23:59:59'
          }"
          value-format="H:m:s"
          placeholder="请选择时间"
        >
        </el-time-picker>
      </el-radio>
      <el-radio label="month">
        每月
        <el-select
          :disabled="type !== 'month'"
          v-model="monthConfig.day"
          class="ml5 w180"
          size="small"
        >
          <el-option
            v-for="d in dateList"
            :key="`date${d}`"
            :label="`${d}日`"
            :value="d"
          ></el-option>
          <!-- <el-option label="最后一天" value="L"></el-option>
				<el-option label="最后一个工作日" value="LW"></el-option> -->
        </el-select>
        <el-time-picker
          :disabled="type !== 'month'"
          v-model="monthConfig.time"
          class="ml5 w180"
          size="small"
          arrow-control
          :picker-options="{
            selectableRange: '00:00:00 - 23:59:59'
          }"
          value-format="H:m:s"
          placeholder="请选择时间"
        >
        </el-time-picker>
      </el-radio>
      <el-radio label="week">
        每周
        <el-select
          :disabled="type !== 'week'"
          v-model="weekConfig.week"
          class="ml5 w180"
          size="small"
        >
          <el-option
            v-for="w in weekList"
            :key="`week${w}`"
            :label="`周${cnWeek[w - 1]}`"
            :value="w"
          ></el-option>
        </el-select>
        <el-time-picker
          :disabled="type !== 'week'"
          v-model="weekConfig.time"
          class="ml5 w180"
          size="small"
          arrow-control
          :picker-options="{
            selectableRange: '00:00:00 - 23:59:59'
          }"
          value-format="H:m:s"
          placeholder="请选择时间"
        >
        </el-time-picker>
      </el-radio>
      <el-radio label="day">
        每天
        <el-time-picker
          :disabled="type !== 'day'"
          v-model="dayConfig.time"
          class="ml5 w180"
          size="small"
          arrow-control
          :picker-options="{
            selectableRange: '00:00:00 - 23:59:59'
          }"
          value-format="H:m:s"
          placeholder="请选择时间"
        >
        </el-time-picker>
      </el-radio>
      <el-radio label="free">
        自定义
        <el-input
          placeholder="请输入corn表达式，例：0 10 0 * * *，每天零点十分执行"
          class="ml5 w400"
          :disabled="type !== 'free'"
          size="small"
          v-model="freeConfig.content"
        />
      </el-radio>
    </el-radio-group>
  </div>
</template>
<script>
// const
export default {
  props: ['value'],
  data() {
    return {
      type: '',
      monthList: this.getNumList(12),
      dateList: this.getNumList(31),
      weekList: this.getNumList(7),
      cnWeek: ['一', '二', '三', '四', '五', '六', '日'],
      onceConfig: {
        date: '',
        time: ''
      },
      yearConfig: {
        month: '',
        day: '',
        time: ''
      },
      monthConfig: {
        day: '',
        time: ''
      },
      weekConfig: {
        week: '',
        time: ''
      },
      dayConfig: {
        time: ''
      },
      freeConfig: {
        content: ''
      }
    };
  },
  watch: {
    value: {
      deep: true,
      handler() {
        if (this.value) {
          this.type = this.formatTaskCycle(this.value);
        }
      }
    },
    type() {
      this.$emit('input', this.type);
    }
  },
  computed: {
    yearConfigDateList() {
      const month = this.yearConfig.month;
      // 按月份改变日期选择范围
      if (month === 2) {
        return this.getNumList(28);
      }
      if ([1, 3, 5, 7, 8, 10, 12].includes(month)) {
        return this.getNumList(31);
      }
      if ([4, 6, 9, 11].includes(month)) {
        return this.getNumList(30);
      }
    }
  },
  methods: {
    checkTime(time) {
      let isValid = null;
      const pattern = /([0-1]?[0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])/;
      isValid = pattern.test(time);
      return isValid;
    },
    formatTaskCycle(expression) {
      const arr = expression.split(' ');
      if (arr.length >= 6) {
        const second = arr[0],
          min = arr[1],
          hour = arr[2],
          day = arr[3],
          month = arr[4],
          week = arr[5],
          year = arr[6] ? arr[6] : '',
          time = `${hour.length > 1 ? hour : '0' + hour}:${min.length > 1 ? min : '0' + min}:${second.length > 1 ? second : '0' + second}`;
        if (year && year != '*') {
          this.onceConfig.date = `${year}-${month}-${day}`;
          this.onceConfig.time = time;
          return 'once';
        }
        if (month && month != '*') {
          this.yearConfig.month = Number(month);
          this.yearConfig.day = Number(day);
          this.yearConfig.time = time;
          return 'year';
        }
        if (day && !['?', '*'].includes(day)) {
          this.monthConfig.day = Number(day);
          this.monthConfig.time = time;
          return 'month';
        }
        if (week && !['?', '*'].includes(week)) {
          this.weekConfig.week = Number(week);
          this.weekConfig.time = time;
          return 'week';
        }
        if (this.checkTime(time)) {
          this.dayConfig.time = time;
          return 'day';
        }
        this.freeConfig.content = expression;
        return 'free';
      } else {
        return arr[0];
      }
    },
    getNumList(length) {
      const nums = [];
      for (let i = 1; i <= length; i++) {
        nums.push(i);
      }
      return nums;
    },
    getCron() {
      if (!this.type) {
        return false;
      }
      if (this.type === 'free') {
        return this.freeConfig.content;
      }
      let week, time, month, dayofMonth, year;
      const dates = this.onceConfig.date.split('-');
      switch (this.type) {
        case 'once':
          year = dates[0];
          month = dates[1];
          dayofMonth = dates[2];
          week = '';
          time = this.onceConfig.time;
          break;
        case 'year':
          year = '';
          month = this.yearConfig.month;
          dayofMonth = this.yearConfig.day;
          week = '';
          time = this.yearConfig.time;
          break;
        case 'month':
          year = '';
          month = '';
          dayofMonth = this.monthConfig.day;
          week = '';
          time = this.monthConfig.time;
          break;
        case 'week':
          year = '';
          month = '';
          dayofMonth = '';
          week = this.weekConfig.week;
          time = this.weekConfig.time;
          break;
        case 'day':
          year = '';
          month = '';
          dayofMonth = '';
          week = '';
          time = this.dayConfig.time;
          break;
        default:
      }
      let cron = '';
      const arr = (time && time.split(':')) || [];
      if (arr.length) {
        const hour = arr[0],
          minute = arr[1],
          second = arr[2];
        cron =
          second +
          ' ' +
          minute +
          ' ' +
          hour +
          ' ' +
          (dayofMonth ? dayofMonth : week ? '?' : '*') +
          ' ' +
          (month ? month : '*') +
          ' ' +
          (week ? week : '?') +
          ' ' +
          (year ? year : '*');
      }
      return cron;
    }
  },
  mounted() {
    if (this.value) {
      this.type = this.formatTaskCycle(this.value);
    }
  }
};
</script>
<style lang="less">
.cron-selection {
  .el-radio {
    display: flex;
    align-items: center;
    margin-bottom: 5px;
  }
  .el-radio__label {
    display: flex;
    align-items: center;
  }
  .w180 {
    width: 180px;
  }
  .w400 {
    width: 400px;
  }
}
</style>
