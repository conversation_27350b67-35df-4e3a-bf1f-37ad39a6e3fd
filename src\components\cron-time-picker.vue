<!--
 * @Fileoverview: 简化版Quartz Cron表达式生成器
 * @Description: 仅支持以下三种格式：
  1. 每日的某个时间点
  2. 每周几的某个时间点
  3. 每月几号的某个时间点
-->
<!--
  Quartz Cron表达式支持一般支持6段，分别是：秒、分、时、日、月、周几
  秒（0-59）
  分钟（0-59）
  小时（0-23）
  日期（1-31）
  月份（1-12 或 JAN-DEC）
  星期（0-7，其中 0 和 7 都表示星期日，或使用 SUN-SAT）
  在 Quartz Cron Expression 中，? 字符用于日期和星期字段中的一个，表示不指定值。

  例如：
  "0 0 12 * * ?" - 每天中午 12 点执行一次。
  "0/15 0 8-17 * * ?" - 每天上午 8 点到下午 5 点，每 15 执行一次。
  "0 0 0 1 1 ?" - 每年 1 月 1 日午夜执行一次。
  "0 0 10,14,16 * * ?" - 每天上午 10 点、下午 2 点和 4 点执行一次。
  "0 0/30 8-10 * * ?" - 每天上午 8 点到 10 点，每半小时执行一次。
 -->
<template>
  <div class="cron-selector">
    <el-cascader
      v-model="periodType"
      :options="periodOptions"
      :props="{ expandTrigger: 'hover' }"
    ></el-cascader>
    <el-time-picker
      v-if="periodType == 'day'"
      v-model="time"
      value-format="HH:mm:ss"
      placeholder="请选择时间"
    ></el-time-picker>
  </div>
</template>

<script>
const weekDays = [
  {
    value: 'MON',
    label: '一'
  },
  {
    value: 'TUE',
    label: '二'
  },
  {
    value: 'WED',
    label: '三'
  },
  {
    value: 'THU',
    label: '四'
  },
  {
    value: 'FRI',
    label: '五'
  },
  {
    value: 'SAT',
    label: '六'
  },
  {
    value: 'SUN',
    label: '日'
  }
];
const daysOfMonth = new Array(31).fill(0).map((_, i) => ({
  value: i + 1,
  label: `${i + 1}号`
}));
daysOfMonth.push({
  value: 'L',
  label: '最后一天'
});
const periodOptions = [
  {
    value: 'day',
    label: '每日'
  },
  {
    value: 'week',
    label: '每周',
    children: weekDays
  },
  {
    value: 'month',
    label: '每月',
    children: daysOfMonth
  }
];
export default {
  props: {
    value: String
  },
  data() {
    return {
      periodOptions,
      periodType: ['day'],
      time: ''
    };
  },
  watch: {
    periodType: {
      handler() {
        this.updateCron();
      },
      deep: true
    },
    time() {
      this.updateCron();
    },
    value: {
      handler() {
        if (this.value) {
          // eslint-disable-next-line no-unused-vars
          const [second, minute, hour, dayOfMonth, month, dayOfWeek] =
            this.value.split(' ');
          this.time = `${hour}:${minute}:${second}`;
          if (dayOfMonth !== '*' && dayOfMonth !== '?') {
            this.periodType = [
              'month',
              dayOfMonth === 'L' ? 'L' : Number(dayOfMonth)
            ];
          } else if (dayOfWeek !== '*' && dayOfWeek !== '?') {
            this.periodType = ['week', dayOfWeek];
          } else {
            this.periodType = ['day'];
          }
        }
      },
      immediate: true
    }
  },
  methods: {
    updateCron() {
      let cron = '';

      if (!this.time) {
        this.$emit('input', cron);
      }

      const [periodType, periodValue] = this.periodType;
      const [hour, minute, second] = this.time.split(':');
      switch (periodType) {
        case 'day':
          cron = `${second} ${minute} ${hour} * * ?`;
          break;
        case 'week':
          cron = `${second} ${minute} ${hour} ? * ${periodValue}`;
          break;
        case 'month':
          cron = `${second} ${minute} ${hour} ${periodValue} * ?`;
          break;
        default:
          cron = '';
      }
      this.$emit('input', cron);
    }
  }
};
</script>

<style lang="less" scoped>
.cron-selector .el-cascader ::v-deep .el-input__inner {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.cron-selector .el-date-editor--time ::v-deep .el-input__inner {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.cron-selector .el-cascader {
  width: 160px;
}
.cron-selector .el-date-editor--time {
  margin-left: -1px;
}
</style>
