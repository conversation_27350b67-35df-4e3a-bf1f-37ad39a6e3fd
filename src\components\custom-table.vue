<template>
  <div>
    <div v-if="this.ledgerType == 'API_ASSET_ACCESS'">
      <div class="automaticReporting">
        <span>自动上报：</span>
        <el-popover placement="left" width="300" v-model="visible">
          <div v-if="isSwitch">
            <el-form
              v-if="true"
              :model="form"
              label-position="left"
              label-width="70px"
              ref="form"
              :rules="rules"
            >
              <el-form-item label="kafka:" prop="kafkaConfigId" size="mini">
                <el-select
                  v-model="form.kafkaConfigId"
                  placeholder="请选择台账"
                >
                  <el-option
                    v-for="item in kafkaList"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-form>

            <p>开启后，将每小时自动上报一次数据</p>
          </div>
          <div v-if="!isSwitch">
            <p>关闭后，不再自动上报数据</p>
          </div>

          <div style="text-align: right; margin: 0">
            <el-button size="mini" type="text" @click="apiAutomaticCancel()"
              >取消</el-button
            >
            <el-button
              type="primary"
              size="mini"
              @click="apiAutomaticReporting()"
              >确定</el-button
            >
          </div>
          <qz-switch
            slot="reference"
            v-model="isSwitch"
            active-text="ON"
            inactive-text="OFF"
            :width="40"
            @change="getSwitch()"
          ></qz-switch>
        </el-popover>
      </div>
    </div>

    <qz-pro-table
      :data-source="getDataList"
      size="small"
      ref="table"
      @selection-change="selectHandle"
      class="mt0"
      :show-check-all="true"
      @check-all="handleCheckAll"
    >
      <div slot="tools" v-if="!isDetail">
        <el-tooltip
          class="item mr10"
          effect="dark"
          :content="markedWords"
          placement="top-start"
        >
          <span>操作提示<i class="el-icon-question"></i></span>
        </el-tooltip>
        <el-button size="small" @click="dataReport" type="primary"
          >上报</el-button
        >
        <el-button
          size="small"
          v-if="relatedList.includes(ledgerType)"
          type="primary"
          @click="related"
          :loading="relatedLoading"
          :disabled="relatedLoading"
          >关联<span v-if="relatedLoading">{{
            relatedProgress + '%'
          }}</span></el-button
        >
        <el-button
          size="small"
          v-if="
            !['SENSITIVE_DATA_SHEET', 'SENSITIVE_FIELDS'].includes(ledgerType)
          "
          @click="importRisk"
          type="primary"
          >导入</el-button
        >
        <el-button
          size="small"
          v-if="['FILE_SYSTEM_ASSET', 'FILE_MARK_GRADING'].includes(ledgerType)"
          @click="exportRisk"
          type="primary"
          >导出</el-button
        >
        <el-button size="small" @click="deleteAll" type="primary"
          >批量删除</el-button
        >
      </div>
      <qz-table-column type="selection" width="45" v-if="!isDetail" />
      <qz-table-column
        v-for="(item, i) in dropCol"
        :key="i"
        :prop="item.prop"
        :label="item.label"
        :min-width="item.minWidth"
        :togglable="item.togglable"
        :default-shown="item.defaultShown || false"
        :column-key="i.toString()"
        :fixed="item.fixed || false"
        class="ellipsis"
      >
        <template slot-scope="{ row }">
          <template v-if="item.labelType && item.labelType == 'array'">
            <template v-if="item.showOverflowTooltip">
              <el-tooltip
                class="item apostrophe"
                effect="dark"
                placement="top"
                :open-delay="500"
              >
                <div slot="content">
                  <qz-copy btn-class="copy-btn">{{
                    row[item.prop] && row[item.prop].length
                      ? row[item.prop].join(',')
                      : '--'
                  }}</qz-copy>
                </div>
                <div>
                  {{
                    row[item.prop] && row[item.prop].length
                      ? row[item.prop].join(',')
                      : '--'
                  }}
                </div>
              </el-tooltip>
            </template>
          </template>

          <template v-else-if="item.labelType == 'string'">
            <template v-if="item.showOverflowTooltip">
              <el-tooltip
                class="item apostrophe"
                effect="dark"
                placement="top"
                :open-delay="500"
              >
                <div slot="content">
                  <qz-copy btn-class="copy-btn">{{
                    row[item.prop] || '--'
                  }}</qz-copy>
                </div>
                <div>{{ row[item.prop] || '--' }}</div>
              </el-tooltip>
            </template>

            <div v-else>
              {{ row[item.prop] || '--' }}
            </div>
          </template>
          <template v-else-if="item.labelType == 'number'">
            {{ typeof row[item.prop] == 'number' ? row[item.prop] : '--' }}
          </template>
          <template v-else-if="item.labelType == 'timestamp'">
            <template v-if="item.showOverflowTooltip">
              <el-tooltip
                class="item apostrophe"
                effect="dark"
                placement="top"
                :open-delay="500"
              >
                <div slot="content">
                  <qz-copy btn-class="copy-btn">{{
                    row[item.prop] || '--' | timeFormat('YYYY-MM-DD HH:mm:ss')
                  }}</qz-copy>
                </div>
                <div>
                  {{
                    row[item.prop] || '--' | timeFormat('YYYY-MM-DD HH:mm:ss')
                  }}
                </div>
              </el-tooltip>
            </template>
          </template>
          <template
            v-else-if="item.labelType == 'numberIndex' && item.gatherMap"
          >
            <template v-if="item.showOverflowTooltip">
              <el-tooltip
                class="item apostrophe"
                effect="dark"
                placement="top"
                :open-delay="500"
              >
                <div slot="content">
                  <qz-copy btn-class="copy-btn">{{
                    item.gatherMap[row[item.prop]] || '--'
                  }}</qz-copy>
                </div>
                <div>{{ item.gatherMap[row[item.prop]] || '--' }}</div>
              </el-tooltip>
            </template>
          </template>
          <template
            v-else-if="item.labelType == 'numberIndexArr' && item.gatherMap"
          >
            <template v-if="item.showOverflowTooltip">
              <el-tooltip
                class="item apostrophe"
                effect="dark"
                placement="top"
                :open-delay="500"
              >
                <div slot="content">
                  <qz-copy btn-class="copy-btn">{{
                    row[item.prop] | formatNumberIndexArr(item.gatherMap)
                  }}</qz-copy>
                </div>
                <div>
                  {{ row[item.prop] | formatNumberIndexArr(item.gatherMap) }}
                </div>
              </el-tooltip>
            </template>
          </template>
          <template v-else-if="item.labelType == 'edit'">
            <div v-if="!row.isEdit" class="text-edit-item">
              {{ row[item.prop] || '--' }}
              <i class="el-icon-edit ml5" @click="row.isEdit = true"></i>
            </div>
            <div v-else class="text-edit-item">
              <!-- <el-input :key="row.id" v-model="row[item.prop]" size="mini" type="text"/>
              <span class="action-link" @click="row.isEdit = false">取消</span>
              <span class="action-link" @click="handleSave(row)">保存</span> -->
              <el-input
                v-model="row[item.prop]"
                size="mini"
                type="text"
                placeholder="请输入"
                @blur="changeContent(row)"
              ></el-input>
            </div>
          </template>

          <template v-else>--</template>
        </template>
      </qz-table-column>
      <qz-table-column
        label="操作"
        width="120"
        fixed="right"
        v-if="needOperate"
      >
        <template slot-scope="{ row }">
          <template v-if="row.ledgerStatus != 'DELETE'">
            <span class="action-link" @click="editDataMethod(row)">编辑</span>
            <span class="action-link" @click="del(row)">删除</span>
          </template>
          <span class="action-link" v-else @click="cancleDel(row)"
            >取消删除</span
          >
        </template>
      </qz-table-column>
    </qz-pro-table>
  </div>
</template>
<script>
import {
  dataReportDel,
  dataReportDelCancle,
  dataReportRelated,
  queryRelatedProgress,
  dataAssetReportRelates,
  exportLedger,
  dataReportEdit,
  startAutomaticReporting,
  stopAutomaticReporting,
  statusAutomaticReporting,
  dataReportDelAll
} from '@/service/data-report-services';
import { getKafkaList } from '@/service/base-config-service';
import { getLocalUserInfo } from '@/utils/storage-utils';
import Template from '@/pages/setting/subscribe-warning/template.vue';

export default {
  components: { Template },
  props: {
    // 获取列表数据接口
    dataSource: {
      type: Function,
      required: true
    },
    // 列表展示列
    dropCol: {
      type: Array,
      required: true,
      default: []
    },
    // 编辑数据方法
    editDataMethod: {
      type: Function,
      required: false
    },
    //基础参数
    baseParams: {
      type: Object,
      required: false,
      default: () => {
        return {};
      }
    },
    //哪个页面
    ledgerType: {
      type: String,
      required: true,
      default: 'BUSINESS_SYSTEM'
    },
    //是否需要操作列
    needOperate: {
      type: Boolean,
      required: false,
      default: false
    },
    //是否在查看详情
    isDetail: {
      type: Boolean,
      required: false,
      default: false
    },
    //操作提示语文案
    markedWords: {
      type: String,
      required: false,
      default: '请先导入再进行上报。'
    }
  },
  data() {
    return {
      viewSampleDrawer: false,
      fieldAndSample: {},
      activeItem: {},
      selectIdsList: [],
      form: {
        kafkaConfigId: ''
      },
      rules: {
        kafkaConfigId: [
          { required: true, message: '请输入必填项', trigger: 'blur' }
        ]
      },
      username: '',
      kafkaList: [],
      //是否开启自动上报
      isAutomaticReporting: false,
      visible: false,
      isSwitch: false,
      isAll: false,
      hasData: false,
      relatedLoading: false,
      relatedProgress: 0,
      timer: null,
      relatedList: [
        'RISK_EVENT',
        'DATA_ASSET',
        'API_ASSET',
        'API_RISK_INVESTIGATION',
        'API_ASSET_ACCESS'
      ]
    };
  },
  filters: {
    formatNumberIndexArr(prop, gatherMap) {
      const valueArr = [];
      if (!prop) {
        return '--';
      }
      const propArr = prop instanceof Array ? prop : String(prop)?.split(',');
      propArr.forEach((item) => {
        if (gatherMap[item]) {
          valueArr.push(gatherMap[item]);
        } else {
          valueArr.push(item);
        }
      });
      return valueArr.join(',') || '--';
    }
  },
  methods: {
    reloadCurrentPage() {
      this.$refs.table.reloadCurrentPage();
    },
    reload() {
      this.$refs.table.reload();
    },
    // 获取列表数据
    getDataList(params) {
      const realParams = {
        ...this.baseParams,
        page: params.page,
        limit: params.limit
      };
      return this.dataSource(realParams).then((res) => {
        this.hasData = res.data.totalCount;
        if (this.isDetail) {
          res.data?.rows?.forEach((item) => {
            item.syncStatus = item.ledgerStatus;
            item.isEdit = false;
          });
        }
        return res;
      });
    },
    // 表格跨页全选
    handleCheckAll(checkAll) {
      this.isAll = checkAll;
    },
    // 选择表格中某条数据
    selectHandle(selectRows) {
      this.selectIdsList = [];
      selectRows.forEach((item) => {
        this.selectIdsList.push(item.id);
      });
    },
    // 关联数据
    related() {
      if (this.hasData) {
        this.$confirm('是否确定关联数据？', '关联', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          let relatedFunc = '';
          if (this.ledgerType == 'DATA_ASSET') {
            relatedFunc = dataAssetReportRelates;
          } else {
            relatedFunc = dataReportRelated;
          }
          relatedFunc({
            ledgerType: this.ledgerType
          })
            .then(() => {
              this.relatedLoading = true;
              this.$message.success('正在进行关联');
              this.relatedStatus();
            })
            .catch((err) => {
              this.relatedLoading = false;
              this.$message.error(err.msg || '关联失败');
            });
        });
      } else {
        this.$message.warning('请导入台账数据！');
      }
    },
    relatedStatus(isFirst = false) {
      queryRelatedProgress({
        ledgerType: this.ledgerType,
        processType: 'RELATED'
      })
        .then((res) => {
          if (res.data?.totalCount && res.data.totalCount > 0) {
            this.relatedProgress =
              (res.data.handleCount / res.data.totalCount + '00').replace(
                /\.([\d]{2})/,
                '$1.'
              ) * 1;
          } else {
            this.relatedProgress = 0;
          }
          if (res.data?.status == 'PROCESSING') {
            this.relatedLoading = true;
            this.timer = setTimeout(() => {
              this.relatedStatus();
            }, 2000);
          } else if (res.data?.status == 'SUCCESS' && !isFirst) {
            this.relatedLoading = false;
            this.relatedProgress = 0;
            this.$message.success(
              `关联成功,共关联更新${res.data.successCount}条信息。`
            );
          } else if (res.data?.status == 'FAIL' && !isFirst) {
            this.relatedLoading = false;
            this.relatedProgress = 0;
            this.$message.success('关联失败');
          }
        })
        .catch((err) => {
          this.$message.error(err.msg || '获取关联进度失败');
          this.relatedLoading = false;
          this.relatedProgress = 0;
        })
        .finally(() => {
          this.$refs.table.reloadCurrentPage();
        });

      this.$once('hook:beforeDestroy', () => {
        clearTimeout(this.timer);
        this.timer = null;
      });
    },
    // 删除某条数据
    del(row) {
      this.$confirm('是否进行删除操作', '删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        dataReportDel({ id: row.id, ledgerType: this.ledgerType })
          .then(() => {
            this.$message.success('删除成功');
            this.$refs.table.reloadCurrentPage();
          })
          .catch((err) => {
            this.$message.error(err.msg || '删除失败');
          });
      });
    },
    // 取消删除某条数据
    cancleDel(row) {
      this.$confirm('是否进行取消删除操作', '取消删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        dataReportDelCancle({ id: row.id, ledgerType: this.ledgerType })
          .then(() => {
            this.$message.success('取消删除成功');
            this.$refs.table.reloadCurrentPage();
          })
          .catch((err) => {
            this.$message.error(err.msg || '取消删除失败');
          });
      });
    },
    dataReport() {
      if (this.selectIdsList.length === 0) {
        this.$confirm('您未选择任何数据，是否确认上报一条空数据？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.selectKafka();
        });
      } else {
        this.selectKafka();
      }
    },
    selectKafka() {
      this.$dialogAlert({
        params: {
          ledgerType: this.ledgerType,
          selectIdsList: this.selectIdsList,
          baseParams: this.baseParams,
          isAll: this.isAll,
          callBack: () => {
            this.$refs.table.reloadCurrentPage();
          }
        },
        component: () => import('@/components/data-report-template.vue'),
        alertTitle: '上报',
        alertWidth: '500px',
        alertHeight: 'auto'
      });
    },
    deleteData() {
      if (this.selectIdsList.length > 0) {
        try {
          dataReportDelAll({
            ids: this.selectIdsList,
            ledgerType: this.ledgerType
          })
            .then(() => {
              this.$message.success('删除成功');
              this.$refs.table.reloadCurrentPage();
            })
            .catch((err) => {
              this.$message.error(err.msg || '删除失败');
            });
        } catch (err) {
          this.$message.error(err.msg || '删除失败');
        }
      }
    },

    importRisk() {
      this.$dialogAlert({
        params: {
          ledgerType: this.ledgerType,
          callBack: () => {
            this.$refs.table.reloadCurrentPage();
          }
        },
        component: () => import('@/components/data-report-import.vue'),
        alertTitle: '导入',
        alertWidth: '600px',
        alertHeight: 'auto'
      });
    },
    exportRisk() {
      exportLedger(this.baseParams, { ledgerType: this.ledgerType });
    },

    deleteAll() {
      if (this.selectIdsList.length === 0) {
        this.$confirm('您未选择任何数据，请选择数据？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$message.success('删除成功');
        });
      } else {
        this.$confirm('确定删除勾选数据吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.deleteData();
        });
      }
    },

    changeContent(row) {
      const editParams = {
        id: row.id,
        classifyTagSelf: row.classifyTagSelf
      };
      dataReportEdit(editParams, { ledgerType: this.ledgerType })
        .then(() => {
          this.$refs.table.reloadCurrentPage();
          this.$message.success('保存成功');
        })
        .catch((err) => {
          this.$message.error(err.msg || '保存失败');
        });
    },
    //活得kafka数据
    getKafkaDataList() {
      getKafkaList({
        page: 1,
        limit: 100,
        ledgerType: this.ledgerType,
        status: 1
      })
        .then((res) => {
          this.kafkaList = res.data.rows || [];
        })
        .catch((err) => {
          this.$message.error(err.msg || '获取kafka列表失败');
        });
    },

    //开启或者关闭自动上报功能
    apiAutomaticReporting() {
      //开启自动上报功能
      this.handleCheckAll(true);

      const realParams = {
        isAll: false,
        idList: this.selectIdsList,
        params: this.baseParams,
        type: 'API_ASSET_ACCESS',
        kafkaConfigId: this.form.kafkaConfigId,
        username: this.username
      };
      if (this.isSwitch) {
        if (!this.form.kafkaConfigId) {
          this.$message.info('请选择kafka数据');
          return;
        }

        startAutomaticReporting(realParams)
          .then(() => {
            this.$message.success('开启自动上报功能成功');
          })
          .catch((err) => {
            console.log('开启上报', err);

            this.$message.error(err.msg || '开启自动上报功能失败');
          })
          .finally(() => {
            this.loading = false;
          });
      } else {
        stopAutomaticReporting(realParams)
          .then(() => {
            this.isShow = false;
            this.$message.success('关闭自动上报功能成功');
          })
          .catch((err) => {
            console.log('上报关闭', err);
            this.$message.error(err.msg || '关闭自动上报功能失败');
          })
          .finally(() => {
            this.loading = false;
          });
      }
      this.visible = false;
    },

    apiAutomaticCancel() {
      this.visible = false;

      if (this.isSwitch) {
        this.isSwitch = false;
      } else {
        this.isSwitch = true;
      }
    },

    getSwitch() {
      if (this.isSwitch) {
        this.getKafkaDataList();
      }
    }
  },
  mounted() {
    const { username } = getLocalUserInfo();
    this.username = username;
    if (this.relatedList.includes(this.ledgerType) && !this.isDetail) {
      this.relatedStatus('isFirst');
    }
    //获取当前上报状态
    if (this.ledgerType == 'API_ASSET_ACCESS') {
      statusAutomaticReporting()
        .then((res) => {
          this.isSwitch = res.data;
        })
        .catch(() => {
          this.isSwitch = false;
        });
    }
  }
};
</script>
<style lang="less" scoped>
/deep/table {
  width: 100% !important;
}
.ellipsis {
  display: inline-block;
  width: 200px;
  white-space: nowrap; /* 确保文本在一行内显示 */
  overflow: hidden; /* 隐藏溢出容器的文本 */
  text-overflow: ellipsis; /* 使用省略号表示溢出的文本 */
}
/deep/.el-table__empty-block {
  width: 100% !important;
}
// /deep/ .qz-pro-table .el-table .cell{
//   display:  flex;
// }
.apostrophe {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
//哪一行增加插槽，写哪个对应的下表。不然会出现内容不居中
.table /deep/ .el-table__row td:nth-child(2) .cell {
  cursor: pointer;
  color: #d10000;
  text-align: left;
}
.automaticReporting {
  display: flex;
  justify-content: flex-end;
  padding-bottom: 15px;
  padding-right: 5px;
}
.text-edit-item {
  display: flex;
  align-items: center;
  & i {
    font-size: 18px;
    cursor: pointer;
  }
  & .el-input {
    margin-right: 5px;
  }
  & .action-link {
    font-size: 12px;
    flex: 1 1 0%;
  }
}
</style>
