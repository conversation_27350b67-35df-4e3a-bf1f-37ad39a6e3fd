<template>
  <div class="data-editor">
    <template v-if="editing">
      <!-- 编辑文字 -->
      <el-input
        v-if="editorType === 'text'"
        ref="input"
        v-model.trim="newValue"
        size="small"
        clearable
        @keydown.native.enter="save"
        @keyup.native.esc="cancel"
      ></el-input>
      <!-- 编辑单选 -->
      <el-select
        v-model="newValue"
        placeholder="请选择"
        size="small"
        v-if="editorType === 'singleSelect'"
        :popper-class="`data-editor__select-dropdown ${$slots.addOption ? 'can-add' : ''}`"
        @keydown.native.enter="save"
        @keyup.native.esc="cancel"
        clearable
        filterable
      >
        <el-option
          v-for="item in selectOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
          :disabled="item.enabled === false"
        >
        </el-option>
        <slot name="addOption"></slot>
        <template slot="empty">
          <slot name="addOption"></slot>
        </template>
      </el-select>
      <!-- 编辑多选 -->
      <el-select
        v-model="newValue"
        placeholder="请选择"
        size="small"
        v-if="editorType === 'multiSelect'"
        :popper-class="`data-editor__select-dropdown ${$slots.addOption ? 'can-add' : ''}`"
        @keydown.native.enter="save"
        @keyup.native.esc="cancel"
        multiple
        clearable
        filterable
      >
        <el-option
          v-for="item in selectOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
          :disabled="item.enabled === false"
        >
        </el-option>
        <slot name="addOption"></slot>
        <template slot="empty">
          <slot name="addOption"></slot>
        </template>
      </el-select>
      <span class="editor-btn" @click="cancel">取消</span>
      <span class="editor-btn" @click="save">保存 </span>
    </template>
    <template v-else>
      <span class="text" v-if="editorType === 'text'">{{ dataValue }}</span>
      <span v-if="editorType === 'multiSelect'">
        {{ formatMultiLabels(dataValue, selectOptions) }}
      </span>
      <span v-if="editorType === 'singleSelect'">
        {{ formatSingleLabel(dataValue, selectOptions) }}
      </span>
      <span class="editor-btn edit" @click="start">
        <qz-icon class="icon-edit"></qz-icon>
      </span>
    </template>
  </div>
</template>

<script>
import { deepCopy } from '@/utils/deep-copy';
export default {
  props: {
    dataValue: [Number, Array, String],
    editorType: String,
    selectOptions: Array
  },
  data() {
    return {
      editing: false,
      newValue: ''
    };
  },
  methods: {
    formatSingleLabel(id, dataList = []) {
      const label = dataList.find((item) => item.id === id);
      if (label) {
        return label.name;
      } else {
        return '';
      }
    },
    formatMultiLabels(ids = [], dataList = []) {
      const names = [];
      ids.forEach((id) => {
        const label = dataList.find((item) => item.id == id);
        if (label) {
          names.push(label.name);
        }
      });
      return names.join('，');
    },
    addValue(data) {
      this.newValue.push(data);
    },
    start() {
      this.editing = true;
      if (this.editorType === 'text') {
        this.newValue = this.dataValue;
        this.$nextTick(() => {
          this.$refs.input.focus();
        });
      } else {
        this.newValue = deepCopy(this.dataValue);
      }
    },
    cancel() {
      this.editing = false;
      this.newValue = this.dataValue;
    },
    save() {
      this.$emit('save', this.newValue);
      this.editing = false;
    }
  }
};
</script>

<style lang="less" scoped>
.data-editor {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  min-width: 100px;
  min-height: 24px;
  & .text {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
.editor-btn {
  flex: none;
  margin-left: 10px;
  color: #4a97eb;
  cursor: pointer;
  .qz-iconfont {
    color: #aaabae;
    font-size: 12px;
  }
}
.el-select @{deep} .el-tag {
  height: auto;
  white-space: pre-wrap;
}
</style>
