<!--
 * @Fileoverview: 数据标签展示模板
 * @Description: 数据标签展示模板
-->
<template>
  <div class="data-label-template" ref="dataLabels">
    <el-tooltip
      placement="top-start"
      :disabled="dataLabelList && dataLabelList.length < labelNum"
      v-if="templateType === 'labelList' || (labelList && labelList.length > 0)"
    >
      <div slot="content" style="line-height: 20px">
        <span v-for="(item, index) in dataLabelList" :key="item.dataLabelId">
          {{
            item.name &&
            item.name + (index === dataLabelList.length - 1 ? '' : ',')
          }}
        </span>
      </div>
      <div class="data-label-template__wrapper">
        <span v-for="(item, index) in dataLabelList" :key="item.dataLabelId">
          <span
            class="data-label-template__wrapper__item"
            v-if="index < labelNum && item.name"
          >
            {{ item.name }}
          </span>
          <span
            class="data-label-template__wrapper__item"
            v-else-if="labelNum && index == labelNum && item.name"
          >
            ...
          </span>
        </span>
      </div>
    </el-tooltip>
    <div v-if="!dataLabelList || dataLabelList.length == 0">
      {{ '- -' }}
    </div>
  </div>
</template>

<script>
import elementResizeDetectorMaker from 'element-resize-detector';
export default {
  props: ['dataLabels', 'templateType', 'labelList'],
  data() {
    return {
      labelNum: 0,
      dataLabelList: []
    };
  },
  mounted() {
    this.createListener();
    this.formatLabels();
  },
  watch: {
    dataLabels() {
      this.formatLabels();
    },
    labelList() {
      this.formatLabels();
    }
  },
  methods: {
    createListener() {
      this.$nextTick(() => {
        const erd = elementResizeDetectorMaker();
        erd.listenTo(this.$refs.dataLabels, () => {
          this.$nextTick(() => {
            const labelWidth = this.$refs.dataLabels.clientWidth;
            this.labelNum = Math.floor((labelWidth / 75) * 2);
          });
        });
      });
    },
    formatLabels() {
      this.dataLabelList = JSON.parse(JSON.stringify(this.dataLabels));
      if (this.templateType !== 'labelList' && this.labelList?.length > 0) {
        this.dataLabelList = this.dataLabelList?.map((item) => {
          const label = this.labelList.find((label) => label.id === item);
          if (label) {
            return {
              dataLabelId: label.id,
              name: label.name
            };
          }
        });
      }
    }
  }
};
</script>

<style lang="less">
.data-label-template {
  &__wrapper {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    margin-bottom: -8px;
    &__item {
      display: inline-block;
      padding: 2px 5px;
      margin-right: 10px;
      margin-bottom: 8px;
      line-height: 15px;
      text-align: center;
      color: #487dce;
      background: rgba(88, 146, 235, 0.09);
      border: 1px solid rgba(88, 146, 235, 1);
      font-family: PingFangSC-Regular;
      font-weight: 400;
      font-size: 10px;
      border-radius: 15px;
    }
  }
}
</style>
