<template>
  <div class="report-import">
    <el-form>
      <el-form-item label="导入文件：" label-width="120px">
        <el-upload
          ref="upload"
          :action="DATA_REPROT_UPLOAD"
          :auto-upload="false"
          name="file"
          :file-list="fileList"
          :multiple="false"
          :show-file-list="true"
          accept=".xlsx"
          :on-change="handleChangeStatus"
          :on-success="handleSuccess"
          :limit="1"
          :data="{ ledgerType: params.ledgerType }"
        >
          <el-button size="small">
            <qz-icon class="icon-upload icon"></qz-icon>点击上传
          </el-button>
          <div slot="tip" class="el-upload__tip tips">支持扩展名：.xlsx</div>
          <el-button size="small" type="text" class="btn" @click="download"
            >模板下载</el-button
          >
        </el-upload>
      </el-form-item>
    </el-form>
    <div class="text-right mt20">
      <el-button size="small" @click="cancel">取消</el-button>
      <el-button
        size="small"
        type="primary"
        @click="save"
        :disabled="reportLoading"
        :loading="reportLoading"
        ><span v-if="reportLoading">{{ reportProgress + '%' }}</span
        >确认</el-button
      >
    </div>
  </div>
</template>

<script>
import {
  DATA_REPROT_UPLOAD_TEMPLATE,
  DATA_REPROT_UPLOAD
} from '@/constant/data-url-constants';
import { queryRelatedProgress } from '@/service/data-report-services';
export default {
  props: ['params'],
  data() {
    return {
      DATA_REPROT_UPLOAD,
      fileList: [],
      reportLoading: false,
      reportProgress: 0,
      timer: null
    };
  },
  mounted() {},
  methods: {
    cancel() {
      this.params.close();
    },

    download() {
      const a = document.createElement('a');
      a.href =
        window.location.origin +
        `${DATA_REPROT_UPLOAD_TEMPLATE}?ledgerType=${this.params.ledgerType}`;
      a.target = '_blank';
      a.click();
    },

    handleChangeStatus(file, fileList) {
      this.fileList = fileList;
    },

    handleSuccess(res) {
      if (res.success) {
        this.reportLoading = true;
        this.reportStatus();
      } else {
        this.$message.error(res.msg || '导入失败');
      }
    },

    reportStatus() {
      console.log(this.params, 'this.params.callback');
      queryRelatedProgress({
        ledgerType: this.params.ledgerType,
        processType: 'IMPORT'
      })
        .then((res) => {
          if (res.data?.totalCount && res.data.totalCount > 0) {
            this.reportProgress =
              (res.data.successCount / res.data.totalCount) * 100;
          } else {
            this.reportProgress = 0;
          }
          if (res.data?.status == 'PROCESSING') {
            this.reportLoading = true;
            this.timer = setTimeout(() => {
              this.reportStatus();
            }, 2000);
            return;
          } else if (res.data?.status == 'SUCCESS') {
            this.reportLoading = false;
            this.cancel();
            this.params.callBack && this.params.callBack();
            this.$message.success('导入成功');
            this.reportProgress = 0;
          } else if (res.data?.status == 'FAIL') {
            this.reportLoading = false;
            this.reportProgress = 0;
            this.$message.error(res.data.massage || '导入失败');
          }
        })
        .catch((err) => {
          this.$message.error(err.massage || '获取导入进度失败');
          this.reportLoading = false;
          this.reportProgress = 0;
        });
      this.$once('hook:beforeDestroy', () => {
        clearTimeout(this.timer);
        this.timer = null;
      });
    },
    save() {
      if (!this.fileList.length) {
        this.$message.error('请上传文件');
        return;
      }
      this.$refs.upload.submit();
    }
  }
};
</script>

<style lang="less" scoped>
.report-import {
  .btn {
    border: none;
  }

  .tips {
    color: #888;
  }
}
</style>
