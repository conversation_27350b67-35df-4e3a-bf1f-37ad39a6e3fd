<template>
  <div class="data-report-template">
    <el-form
      v-if="isShow"
      :model="form"
      label-width="70px"
      ref="form"
      :rules="rules"
    >
      <el-form-item label="kafka：" prop="kafkaConfigId">
        <el-select v-model="form.kafkaConfigId" placeholder="请选择台账">
          <el-option
            v-for="item in kafkaList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <div v-else>
      <p>{{ this.promptInfo }}</p>
    </div>
    <div class="text-right mt20">
      <el-button size="small" @click="cancel">取消</el-button>
      <el-button
        size="small"
        v-if="isShow"
        type="primary"
        @click="sure"
        :loading="loading"
        :disabled="loading"
        >确认</el-button
      >
      <el-button size="small" v-else type="primary" @click="detail"
        >上报详情</el-button
      >
    </div>
  </div>
</template>

<script>
import { PAGE_URL_REPORT_TASK } from '@/constant/page-url-constants';
import { creatReportTask } from '@/service/data-report-services';
import { getKafkaList } from '@/service/base-config-service';

import { getLocalUserInfo } from '@/utils/storage-utils';
export default {
  props: ['params'],
  data() {
    return {
      PAGE_URL_REPORT_TASK,
      form: {
        kafkaConfigId: ''
      },
      rules: {
        kafkaConfigId: [
          { required: true, message: '请输入必填项', trigger: 'blur' }
        ]
      },
      isShow: true,
      promptInfo: '',
      kafkaList: [],
      username: '',
      loading: false
    };
  },
  created() {
    const { username } = getLocalUserInfo();
    this.username = username;
  },
  mounted() {
    getKafkaList({
      page: 1,
      limit: 100,
      ledgerType: this.params.ledgerType,
      status: 1
    })
      .then((res) => {
        this.kafkaList = res.data.rows || [];
      })
      .catch((err) => {
        this.$message.error(err.msg || '获取kafka列表失败');
      });
  },
  methods: {
    cancel() {
      this.params.close();
    },
    sure() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          this.loading = true;
          const realParams = {
            isAll: this.params.isAll,
            idList: this.params.selectIdsList,
            params: this.params.isAll ? this.params.baseParams : null,
            type: this.params.ledgerType,
            kafkaConfigId: this.form.kafkaConfigId,
            username: this.username
          };
          creatReportTask(realParams)
            .then((res) => {
              this.isShow = false;
              if (res.data) {
                this.params.callBack && this.params.callBack();
                this.promptInfo = '上报任务创建成功,请到任务列表查看详细进度！';
              } else {
                this.promptInfo = '上报任务创建失败,请到任务列表查看失败原因！';
              }
            })
            .catch((err) => {
              this.$message.error(err.msg || '上报任务创建失败');
            })
            .finally(() => {
              this.loading = false;
            });
        }
      });
    },
    detail() {
      this.$linkTo({
        path: PAGE_URL_REPORT_TASK,
        // query: {
        //   id: "kkkkk",
        // },
        type: '_blank'
      });
      this.cancel();
    }
  }
};
</script>

<style lang="less" scoped>
.data-report-template {
  .el-form {
    margin: 0;
  }
  .action-a {
    background-color: #4a97eb;
    border-color: #4a97eb;
    min-width: 60px;
    padding: 8px 10px;
    cursor: pointer;
    text-align: center;
    height: 32px;
    color: #fff;
    text-decoration: none;
  }
}
</style>
