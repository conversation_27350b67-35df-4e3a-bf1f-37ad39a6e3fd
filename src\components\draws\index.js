import drawContainer from './main.js';
export default drawContainer;

//使用方法：和弹框相似,考虑到实际情况，目前只支持最多两层
//①：props:['params']
//②：定义方法：editRule(type,row){
// this.$DrawAlert({
//     params: {
//         callBack() {

//         }
//     },
//     customClass: '抽屉的自定义css类名',
//     title:type=='0'?'新增':(type=='1'?'编辑':'复制'),
//     componentObj: {
//         component: () => import('./sql_import_single.vue')
//     }
// })
//}
//③：关闭draw
//this.params.closeOutDrawer()
