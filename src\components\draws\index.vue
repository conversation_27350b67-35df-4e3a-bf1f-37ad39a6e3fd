<template>
  <el-drawer
    ref="defiend_draw"
    class="out_defiend_draw"
    :class="customClass"
    :with-header="withHeader"
    :title="title"
    :visible.sync="drawer"
    :size="`${width}%`"
    :before-close="handleClose"
    destroy-on-close
  >
    <component :is="componentObj.component" :params="params"></component>
  </el-drawer>
</template>

<script>
export default {
  name: 'draw-container',
  props: {},
  data() {
    return {
      drawer: false,
      innerDrawer: false,
      withHeader: true,
      customClass: ''
    };
  },

  components: {},
  mounted() {
    //dom元素渲染后在显示，否则draw会出现闪动
    this.drawer = true;
    //调用方法，启动内部的draw
    // this.params.openInnerDrawer = ()=>{
    //     this.innerDrawer = true
    // }
    this.params.closeOutDrawer = () => {
      this.drawer = false;
    };
  },
  methods: {
    handleClose(done) {
      //从内向外关闭
      this.onClose && this.onClose();
      const domLength =
        document.getElementsByClassName('out_defiend_draw').length;
      const drawContainer =
        document.getElementsByClassName('out_defiend_draw')[domLength - 1];
      document.body.removeChild(drawContainer);
      done();
    }
  }
};
</script>
<style lang="less" scoped>
/deep/ .el-drawer__header {
  margin-bottom: 0;
  color: #303133;
  font-size: 16px;
  padding: 10px;
  border-bottom: 1px solid #e4e7ed;
  font-weight: 800;
}
</style>
