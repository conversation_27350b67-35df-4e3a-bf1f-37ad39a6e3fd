import draw from './index.vue';
import store from '@/store/index.js';
let instance;
const instances = [];
let seed = 1;

const DrawAlert = function (Vue) {
  const genFunc = function (options) {
    const DrawAlertConstructor = Vue.extend(draw);
    if (Vue.prototype.$isServer) return;
    options.title = options.title || '';
    options.width = options.width || 50;
    options.componentObj = options.componentObj || {};
    options = options || {};
    options.params = options.params || {};
    const id = 'draw_' + seed++;
    instance = new DrawAlertConstructor({
      data: options
    });
    instance.id = id;
    //挂载store
    instance.$store = store;
    instance.vm = instance.$mount();
    document.body.appendChild(instance.vm.$el);
    document.body.style.overflow = 'hidden';
    instance.vm.visible = true;
    instance.dom = instance.vm.$el;
    return instance.vm;
  };
  genFunc.closeAll = function () {
    const drawContainerArr =
      document.getElementsByClassName('out_defiend_draw');
    const modalContainerArr = document.getElementsByClassName('v-modal');
    Array.prototype.forEach.call(drawContainerArr, (drawItem) => {
      document.body.removeChild(drawItem);
    });
    Array.prototype.forEach.call(modalContainerArr, (modalItem) => {
      document.body.removeChild(modalItem);
    });
  };
  return genFunc;
};

export default DrawAlert;
