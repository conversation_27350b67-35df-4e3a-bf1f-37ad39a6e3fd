<!--
 * @Fileoverview: 首次安装提示
 * @Description: 首次安装提示
-->
<template>
  <div v-if="show" class="first-setting">
    <div class="first-setting--left">
      <template v-if="warningState.tip && warningState.tip.type === 'DISK'">
        <i class="el-icon-warning-outline mr5"></i>
        <div v-if="warningState.tip.ext.stop === true">
          <span>
            系统磁盘达到存储阈值，产品已停止数据处理，若要恢复工作，请立即进行
          </span>
          <span class="item" @click="goSettingPage('dataclean')">
            数据清理
          </span>
          <span>；您还可以进行</span>
          <span
            class="item"
            @click="goSettingPage('noticesetting', { tab: 'notice' })"
          >
            通知配置
          </span>
          <span>，将此通知信息发送到常用邮箱来及时获取此类信息。</span>
        </div>
        <div v-if="warningState.tip.ext.stop === false">
          <span>系统磁盘达到存储阈值，请立即进行</span>
          <span class="item" @click="goSettingPage('dataclean')">
            数据清理
          </span>
          <span>，否则一段时间后产品将自动停止所有数据处理；您还可以进行</span>
          <span
            class="item"
            @click="goSettingPage('noticesetting', { tab: 'notice' })"
          >
            通知配置
          </span>
          <span>，将此通知信息发送到常用邮箱来及时获取此类信息。</span>
        </div>
      </template>
      <template v-else>
        <div>
          <i class="el-icon-warning-outline mr5"></i>
          建议优先进行以下配置：
        </div>
        <div
          v-if="warningState.homeWarningNetworkState"
          class="item"
          @click="goSettingPage('networks')"
        >
          自有网段
        </div>
        <div
          v-if="
            warningState.homeWarningAccountState &&
            ($hasCapability({
              oldCode: MODULE_AUDIT
            }) ||
              $hasCapability({
                oldCode: MODULE_AUDIT_RISK
              }))
          "
          class="item"
          @click="goSettingPage('account')"
        >
          账号解析配置
        </div>
        <div
          v-if="warningState.homeWarningSubscribeSyncConfig"
          class="item"
          @click="goSettingPage('syncMethod', { curb: 'syslog' })"
        >
          同步方式管理
        </div>
      </template>
    </div>
    <div v-if="!warningState.tip" class="first-setting--right">
      <div class="item" @click="notNoticeWarning('todayNotNotice')">
        今日不再提示
      </div>
      <div class="item" @click="notNoticeWarning('notNotice')">
        以后不再提示
      </div>
    </div>
  </div>
</template>

<script>
import { MODULE_AUDIT, MODULE_AUDIT_RISK } from '@/constant/common-constants';
export default {
  data() {
    return {
      MODULE_AUDIT,
      MODULE_AUDIT_RISK,
      warningState: {
        homeWarningState: false,
        homeWarningNetworkState: false,
        homeWarningAccountState: false,
        homeWarningSubscribeSyncConfig: false,
        tip: null
      }
    };
  },
  computed: {
    show() {
      return this.warningState.homeWarningState || this.warningState.tip;
    }
  },
  watch: {
    show() {
      this.$nextTick(() => {
        this.$emit('state-change', this.show);
      });
    }
  },
  mounted() {},
  methods: {
    // 跳转配置页面
    goSettingPage(moduleName, query = {}) {},
    notNoticeWarning(type) {}
  }
};
</script>

<style lang="less">
.first-setting {
  display: flex;
  height: 35px;
  line-height: 35px;
  background: @bg-red-color;
  color: @white-color;
  justify-content: space-between;
  padding: 0 10px;
  i {
    font-weight: 450;
  }
  &--left {
    display: flex;
    align-items: center;
    margin-left: 10px;
    .item {
      margin: 0 10px;
      cursor: pointer;
      text-decoration: underline;
    }
  }
  &--right {
    display: flex;
    .item {
      margin: 0 10px;
      cursor: pointer;
    }
  }
}
</style>
