<template>
  <div class="extra-param-widget">
    <el-table :data="paramsList" size="small" border style="width: 100%">
      <el-table-column label="参数名" prop="paramName">
        <template slot-scope="scope">
          <el-input
            v-model="scope.row.paramName"
            placeholder="参数名"
            size="small"
          ></el-input>
        </template>
      </el-table-column>
      <el-table-column label="参数值" prop="paramValue">
        <template slot-scope="scope">
          <el-input
            v-model="scope.row.paramValue"
            placeholder="参数值"
            size="small"
          ></el-input>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="70">
        <template slot-scope="scope">
          <span
            class="action-link el-icon-delete"
            @click="removeParam(scope.$index)"
          ></span>
        </template>
      </el-table-column>
    </el-table>
    <div class="add-param" @click="addParam">
      <i class="el-icon-plus"></i> 添加参数
    </div>
  </div>
</template>

<script>
export default {
  props: {
    value: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      paramsList: []
    };
  },
  watch: {
    value: {
      immediate: true,
      handler(newVal) {
        if (newVal) {
          if (newVal !== this.getEmitData()) {
            try {
              const paramsObj = JSON.parse(newVal);
              this.paramsList = Object.entries(paramsObj).map(
                ([paramName, paramValue]) => ({
                  paramName,
                  paramValue
                })
              );
            } catch (e) {
              console.error('Invalid JSON string:', e);
              this.paramsList = [];
            }
          }
        } else {
          this.paramsList = [];
        }
      }
    },
    paramsList: {
      deep: true,
      handler() {
        this.updateValue();
      }
    }
  },
  methods: {
    addParam() {
      this.paramsList.push({
        paramName: '',
        paramValue: ''
      });
    },
    removeParam(index) {
      this.paramsList.splice(index, 1);
    },
    getEmitData() {
      const paramsObj = this.paramsList.reduce(
        (acc, { paramName, paramValue }) => {
          if (paramName) {
            acc[paramName] = paramValue;
          }
          return acc;
        },
        {}
      );
      // 对象字符串形式的值
      return JSON.stringify(paramsObj);
    },
    updateValue() {
      this.$emit('input', this.getEmitData());
    }
  }
};
</script>

<style lang="less">
.extra-param-widget {
  .add-param {
    margin-top: 10px;
    color: #409eff;
    cursor: pointer;
    font-size: 14px;
    display: inline-block;

    &:hover {
      opacity: 0.8;
    }
  }

  .el-table {
    th,
    td {
      padding: 5px;
    }
  }
}
</style>
