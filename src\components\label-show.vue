<template>
  <div>
    <el-tooltip
      :disabled="!labeList || labeList?.length == 0"
      class="item"
      effect="dark"
      placement="top"
    >
      <div slot="content" v-html="labeList?.join(',')"></div>
      <span v-if="labeList?.length">
        <span v-if="isInline">
          <span class="ellips" v-for="(item, index) in labeList" :key="index">
            {{ item | formatterLabel
            }}<template
              v-if="index !== labeList.length - 1 && labeList.length > 1"
              >,</template
            >
          </span>
        </span>
        <span v-else>
          <div class="ellips" v-for="(item, index) in labeList" :key="index">
            {{ item | formatterLabel }}
          </div></span
        >
      </span>
      <span v-else>--</span>
    </el-tooltip>
  </div>
</template>

<script>
export default {
  props: {
    labeList: {
      type: Array,
      default: () => []
    },
    isInline: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {};
  },
  methods: {},
  mounted() {}
};
</script>

<style lang="less" scoped></style>
