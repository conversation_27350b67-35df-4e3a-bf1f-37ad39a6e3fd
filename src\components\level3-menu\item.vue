<template>
  <div
    class="menu-item"
    @click="
      goTo(
        internalMenuItem.path,
        internalMenuItem.query,
        internalMenuItem.target
      )
    "
  >
    <qz-icon
      class="giant-icon"
      :class="internalMenuItem.icon"
      :style="{ color: internalMenuItem.iconColor }"
    ></qz-icon>
    <div class="title">{{ internalMenuItem.title }}</div>
    <div class="subtitle">{{ internalMenuItem.subtitle }}</div>
  </div>
</template>

<script>
import { openPage } from '@/utils/dom-utils';
/**
 * 3级菜单列表项
 */
export default {
  props: {
    menuItem: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      internalMenuItem: this.menuItem
    };
  },
  watch: {
    menuItem() {
      this.internalMenuItem = this.menuItem;
    }
  },
  methods: {
    goTo(path, query, target) {
      const { href } = this.$router.resolve({
        path,
        query
      });
      openPage(href, target);
    }
  }
};
</script>

<style lang="less" scoped>
.menu-item {
  padding: 30px;
  box-sizing: border-box;
  height: 186px;
  border: 1px solid #ececee;
  background: #fff;
  text-align: left;
  cursor: pointer;
  transition: box-shadow 0.5s;
  margin: 0 20px 20px 0;

  &:hover {
    border-color: #adc4f1;
    box-shadow: 0 1px 6px 3px rgba(210, 212, 216, 0.43);
  }

  & .icon {
    width: 30px;
    height: 30px;
  }

  & .title {
    font-family: PingFangSC-Regular, Avenir, Helvetica, Arial, sans-serif;
    font-size: 16px;
    color: #2e3444;
    margin-top: 10px;
  }

  & .subtitle {
    font-family: PingFangSC-Regular, Avenir, Helvetica, Arial, sans-serif;
    font-size: 14px;
    color: #999;
    margin-top: 10px;
  }
}
.giant-icon.qz-iconfont {
  font-size: 29px;
  width: 30px;
  height: 40px;
  display: block;
  background-repeat: no-repeat;
  background-size: 100%;
}
</style>
