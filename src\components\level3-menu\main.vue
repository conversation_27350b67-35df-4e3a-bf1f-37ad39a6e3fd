<template>
  <div class="menu-list">
    <div
      v-for="(menu, i) in internalMenuList"
      v-permission="menu.code"
      :key="i"
      class="menu-group"
    >
      <!-- <div v-if="menu.group !== 'default'" class="group-name">
        {{ menu.group }}
      </div> -->
      <div class="list-wrapper">
        <menu-item
          v-for="item in menu.children"
          v-permission="item.code"
          :key="item.path"
          :menu-item="item"
          class="menu-item"
        ></menu-item>
      </div>
    </div>
  </div>
</template>

<script>
import MenuItem from './item.vue';

export default {
  components: { MenuItem },
  props: {
    menuList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      internalMenuList: this.formatMenuList(this.menuList)
    };
  },
  watch: {
    menuList() {
      this.internalMenuList = this.formatMenuList(this.menuList);
    }
  },
  methods: {
    formatMenuList(originalList) {
      const menuList = [];
      const mebusWithoutGroup = [];
      for (const menu of originalList) {
        if (menu.group && menu.children && menu.children.length > 0) {
          menuList.push(menu);
        } else {
          mebusWithoutGroup.push(menu);
        }
      }
      menuList.push({
        group: 'default',
        children: mebusWithoutGroup
      });
      return menuList;
    }
  }
};
</script>

<style lang="less" scoped>
.menu-group {
  .group-name {
    margin-bottom: 12px;
    font-family: PingFangSC-Medium, Avenir, Helvetica, Arial, sans-serif;
    font-size: 14px;
    color: #333333;
    &::before {
      content: ' ';
      display: inline-block;
      width: 5px;
      height: 5px;
      margin-right: 10px;
      background-color: #f7b500;
      border-radius: 50%;
      vertical-align: middle;
    }
  }
}
.list-wrapper {
  display: flex;
  flex-wrap: wrap;
}

.menu-item {
  width: calc(25% - 20px);
}
</style>
