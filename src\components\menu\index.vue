<template>
  <el-menu
    :default-active="activeIndex"
    class="el-menu-demo"
    mode="horizontal"
    @select="handleSelect"
    background-color="#545c64"
    text-color="#fff"
    active-text-color="#ffd04b"
  >
    <el-menu-item :index="PAGE_URL_APP_MANAGE">应用管理</el-menu-item>
    <!-- <el-submenu index="2">
      <el-menu-item index="2-1">选项1</el-menu-item>
      <el-menu-item index="2-2">选项2</el-menu-item>
      <el-menu-item index="2-3">选项3</el-menu-item>
    </el-submenu> -->
    <el-menu-item :index="PAGE_URL_CONFIG_MANAGE">配置管理</el-menu-item>
  </el-menu>
</template>

<script>
import {
  PAGE_URL_APP_MANAGE,
  PAGE_URL_CONFIG_MANAGE
} from '@/constant/page-url-constants';
export default {
  data() {
    return {
      PAGE_URL_APP_MANAGE,
      PAGE_URL_CONFIG_MANAGE,
      activeIndex: ''
    };
  },
  mounted() {
    if (window.location.pathname) {
      this.activeIndex = window.location.pathname;
    } else {
      this.activeIndex = PAGE_URL_APP_MANAGE;
    }
  },
  methods: {
    handleSelect(key, keyPath) {
      this.activeIndex = key;
      this.$router.push({
        path: key
      });
    }
  }
};
</script>
<style lang="less" scoped></style>
