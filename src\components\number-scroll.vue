<!--
 * @Fileoverview: 数字滚动组件
 * @Description: 首页大屏/数字滚动组件
-->
<template>
  <div class="number-scroll">
    <span ref="numberGrow" class="number-scroll__number">0</span>
  </div>
</template>

<script>
export default {
  props: ['endVal', 'time'],
  watch: {
    endVal() {
      this.numberGrow(this.$refs.numberGrow);
    }
  },
  mounted() {
    this.numberGrow(this.$refs.numberGrow);
    this.fitScreen();
    const sizeFun = () => {
      this.fitScreen();
    };
    window.addEventListener('resize', sizeFun);
    this.$once('hook:beforeDestroy', function () {
      window.removeEventListener('resize', sizeFun);
    });
  },
  methods: {
    // 屏幕自适应
    fitScreen() {
      const htmlWidth =
        document.documentElement.clientWidth || document.body.clientWidth;
      const htmlDom = document.getElementsByTagName('html')[0];
      htmlDom.style.fontSize = 16 * (htmlWidth / 1480) + 'px';
    },
    numberGrow(ele) {
      const step = this.endVal / (this.time * 100);
      let current = 0;
      let start = 0;
      let t = setInterval(() => {
        start += step;
        if (start > this.endVal) {
          clearInterval(t);
          start = this.endVal;
          t = null;
        }
        if (current === start) {
          return;
        }
        current = start;
        ele.innerHTML = current
          .toFixed(0)
          .replace(/(\d)(?=(?:\d{3}[+]?)+$)/g, '$1,');
      }, 5);
    }
  },
  destroyed() {
    const htmlDom = document.getElementsByTagName('html')[0];
    htmlDom.style.fontSize = '16px';
  }
};
</script>

<style lang="less">
.number-scroll {
  transform: translateZ(0);
  &__number {
    font-family: PangMenZhengDao, Avenir, Helvetica, Arial, sans-serif;
    font-size: 1rem;
    color: #64f9ff;
    letter-spacing: 0.05rem;
  }
}
</style>
