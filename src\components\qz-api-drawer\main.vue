<template>
  <el-drawer
    ref="drawer"
    class="look"
    v-bind="$attrs"
    v-on="$listeners"
    :with-header="false"
    :size="size"
    :visible.sync="innerVisible"
    :direction="direction"
  >
    <div class="qz-api-drawer">
      <div class="qz-api-drawer__header" v-if="isShowHeader || $slots.header">
        <slot name="header">
          <div class="qz-api-drawer__header__title">{{ title }}</div>
          <div class="qz-api-drawer__header__close" @click="close">&times;</div>
        </slot>
      </div>
      <div class="qz-api-drawer__body">
        <slot></slot>
      </div>
      <div class="qz-api-drawer__footer" v-if="isShowFooter || $slots.footer">
        <slot name="footer">
          <el-button v-if="isShowCancel" @click="handleClose" size="small">
            {{ cancelText }}
          </el-button>
          <el-button
            v-if="isShowSave"
            type="primary"
            @click="save"
            size="small"
            :loading="saveLoading"
          >
            {{ saveText }}
          </el-button>
        </slot>
      </div>
    </div>
  </el-drawer>
</template>

<script>
export default {
  props: {
    title: String,
    direction: {
      type: String,
      default: 'rtl'
    },
    visible: {
      type: Boolean,
      default: false
    },
    size: {
      type: String,
      default: '70%'
    },
    cancelText: {
      type: String,
      default: '取消'
    },
    saveText: {
      type: String,
      default: '保存'
    },
    isLoading: {
      type: Boolean,
      default: false
    },
    isShowCancel: {
      type: Boolean,
      default: true
    },
    isShowSave: {
      type: Boolean,
      default: true
    },
    isShowHeader: {
      type: Boolean,
      default: true
    },
    isShowFooter: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      innerVisible: this.visible,
      saveLoading: this.isLoading
    };
  },
  watch: {
    visible() {
      this.innerVisible = this.visible;
    },
    innerVisible() {
      this.$emit('update:visible', this.innerVisible);
    },
    isLoading() {
      this.saveLoading = this.isLoading;
    }
  },
  methods: {
    close() {
      this.$refs.drawer.closeDrawer();
    },
    handleClose() {
      this.close();
      this.$emit('close', null);
    },
    save() {
      this.$emit('save', null);
    }
  }
};
</script>
