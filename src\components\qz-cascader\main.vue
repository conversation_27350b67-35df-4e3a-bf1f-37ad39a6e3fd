<template>
  <elp-cascader
    v-if="!beforeDestroyFlag"
    v-model="selectedValues"
    :options="cascaderOptions"
    v-bind="$attrs"
    v-on="$listeners"
    ref="elpCascader"
    @change="cascaderChange"
  />
</template>

<script>
export default {
  name: 'QzCascader',
  props: {
    value: {
      type: Array,
      default: () => []
    },
    options: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      selectedValues: this.value,
      beforeDestroyFlag: false
    };
  },
  computed: {
    cascaderOptions() {
      return this.beforeDestroyFlag ? [] : this.options;
    }
  },
  watch: {
    value(newVal) {
      this.selectedValues = newVal;
    },
    selectedValues(newVal) {
      this.$emit('input', newVal);
    }
  },
  methods: {
    cascaderChange(val = []) {
      if (val.length == 0 && this.$refs[`elpCascader`]) {
        this.$nextTick(() => {
          // 解决elp - cascader值清空时placeholder(inputValue)不展示的问题;
          this.$refs[`elpCascader`].inputValue = null;
        });
      }
    }
  },
  beforeDestroy() {
    this.beforeDestroyFlag = true;
  }
};
</script>
