<template>
  <div class="qz-container" :class="{ 'has-nav': navs.length > 0 }">
    <div v-if="navs.length > 0" class="nav">
      <template v-for="(nav, idx) of navs">
        <span
          :key="nav.title"
          class="nav-item"
          @click="handleNavItemClick(nav)"
        >
          {{ nav.title }}
        </span>
        <span
          v-if="idx !== navs.length - 1"
          :key="nav.title + 1"
          class="nav-separator"
        >
          {{ navSeparator }}
        </span>
      </template>
    </div>
    <div class="main-content">
      <slot></slot>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    /**
     * 面包屑配置，单个元素结构为{title: '面包屑名称', to: '点击面包屑的跳转地址或者对象配置'}
     * 其中，to可以是字符串也可以是对象，字符串代表跳转地址，对象配置如下
     * {
     *   path: 跳转地址，字符串形式,
     *   query: 跳转地址后的query参数，对象形式
     *   type: 与a标签的target相同，字符串形式，默认为'_self'
     * }
     */
    navs: {
      type: Array,
      default: () => []
    },
    /**
     * 面包屑的分隔符
     */
    navSeparator: {
      type: String,
      default: '>'
    }
  },
  methods: {
    handleNavItemClick(nav) {
      if (nav.to) {
        if (typeof nav.to === 'function') {
          nav.to();
        } else {
          this.$linkTo(nav.to);
        }
      }
    }
  }
};
</script>

<style lang="less" scoped>
.qz-container {
  margin: 20px 0;
  padding: 0;
  &.has-nav {
    margin-top: 0;
  }
}
.main-content {
  position: relative;
  background: #fff;
  border: 1px solid #ececee;
  padding: 20px;
  margin-bottom: 0;
}
.nav {
  font-family: PingFangSC-Regular, Avenir, Helvetica, Arial, sans-serif;
  font-size: 12px;
  color: #666;
  padding: 10px 0;
  text-align: left;

  .nav-separator {
    margin-left: 5px;
    margin-right: 5px;
  }

  .nav-item {
    cursor: default;

    &:hover {
      &:not(:last-of-type) {
        text-decoration: underline;
      }
    }

    &:last-of-type {
      color: #999;
    }
  }
}
</style>
