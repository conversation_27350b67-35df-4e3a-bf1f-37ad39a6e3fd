<template>
  <el-drawer
    v-bind="$attrs"
    v-on="$listeners"
    :with-header="false"
    :size="size"
    :visible.sync="innerVisible"
    direction="rtl"
  >
    <div class="qz-drawer">
      <div class="header">
        <div class="title">{{ title }}</div>
        <div class="close" @click="close">&times;</div>
      </div>
      <div class="body">
        <slot></slot>
      </div>
      <div v-if="$slots.footer" class="footer">
        <slot name="footer"></slot>
      </div>
    </div>
  </el-drawer>
</template>

<script>
export default {
  props: {
    title: String,
    visible: {
      type: Boolean,
      default: false
    },
    size: {
      type: String,
      default: '992px'
    }
  },
  data() {
    return {
      innerVisible: this.visible
    };
  },
  watch: {
    visible() {
      this.innerVisible = this.visible;
    },
    innerVisible() {
      this.$emit('update:visible', this.innerVisible);
    }
  },
  methods: {
    close() {
      this.innerVisible = false;
    }
  }
};
</script>

<style lang="less" scoped>
.qz-drawer {
  display: flex;
  flex-direction: column;
  height: 100%;
  .header {
    flex: none;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-sizing: border-box;
    padding: 15px 20px;
    border-bottom: 1px solid #d3d4d5;
    .title {
      flex: auto;
      font-family: PingFangSC-Medium, Avenir, Helvetica, Arial, sans-serif;
      font-size: 16px;
      color: #333333;
      letter-spacing: 0;
    }
    .close {
      color: #979797;
      font-size: 22px;
      flex: none;
      cursor: pointer;
    }
  }
  .body {
    flex: auto;
    overflow-x: hidden;
    overflow-y: auto;
    box-sizing: border-box;
    padding: 15px 20px;
  }
  .footer {
    flex: none;
    box-sizing: border-box;
    padding: 10px 20px;
    background: #f9f8f8;
    border: 1px solid #ededed;
    box-shadow: 0 -1px 4px 1px rgba(0, 0, 0, 0.14);
  }
}
</style>
