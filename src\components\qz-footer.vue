<!--
 * @Fileoverview: 页脚
 * @Description: 通用页脚
-->
<template>
  <div class="qz-footer">
    <div class="copyright">Copyright © {{ years }} All rights reserved.</div>
    <div class="version">版本 {{ version }}</div>
  </div>
</template>
<script>
import { getLicense } from '@/service/system-auth-service';
export default {
  data() {
    const currentYear = new Date().getFullYear();
    return {
      years: `2017-${currentYear}`,
      version: ''
    };
  },
  mounted() {
    getLicense().then((res) => {
      this.version = res?.data?.version || '';
    });
  }
};
</script>
<style lang="less">
.qz-footer {
  display: flex;
  justify-content: space-between;
  height: 46px;
  line-height: 46px;
  padding: 0 20px;
  font-weight: 600;
  font-size: 12px;
  color: @text-primary-color;
  background: @white-color;
  box-shadow: @box-shadow-page-footer;
  z-index: 100;
}
</style>
