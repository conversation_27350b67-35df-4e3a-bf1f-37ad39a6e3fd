<!--
 * @Fileoverview: 页眉
 * @Description: 通用页眉
-->
<template>
  <div class="qz-header">
    <div class="qz-header__wrapper">
      <div class="qz-header__left flex-auto">
        <img class="qz-header__logo" :src="logoImg" alt="logo" />
        <div ref="menuContainer" class="qz-header__menu flex-auto">
          <div
            v-for="(menu, i) in visibleMenuList"
            :key="i"
            class="qz-header__menu-item"
            @click="openMenu([], menu.code, 'code', true)"
          >
            <div
              class="qz-header__menu-title"
              :class="{ active: menu.code === topMenuActive }"
            >
              {{ menu.name }}
            </div>
          </div>
          <div
            v-if="moreMenu"
            class="qz-header__menu-item"
            @click="openMenu([], moreMenu.code, 'code', true)"
          >
            <div
              class="qz-header__menu-title"
              :class="{
                active: moreMenu.code === topMenuActive
              }"
            >
              {{ moreMenu.name }}
            </div>
          </div>
          <el-dropdown
            v-if="hiddenMenuList.length"
            class="qz-header__menu-item"
            trigger="click"
          >
            <div class="qz-header__menu-title">
              更多
              <i class="el-icon-arrow-down el-icon--right"></i>
            </div>
            <el-dropdown-menu class="qz-header__menu-more" slot="dropdown">
              <el-dropdown-item
                v-for="(menu, index) in hiddenMenuList"
                :key="index"
                :class="{
                  active: menu.name === topMenuActive
                }"
                @click.native="openMenu([], menu.code, 'code', true)"
              >
                {{ menu.name }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
      </div>
      <div class="qz-header__right">
        <!--           v-if="userType === USER_TYPE_NORMAL" -->
        <div class="qz-header__message mr10">
          <!-- <el-badge v-if="unReadCount > 0" :value="unReadCount" :max="99">
            <i class="fa fa-bell"></i>
          </el-badge>
          <i v-else class="fa fa-bell"></i> -->
        </div>
        <div
          v-if="isShowConfig"
          class="qz-header__config"
          :class="{ active: topMenuActive == 'config' }"
          @click="goConfig"
        >
          <i class="qz-iconfont icon-shezhi"></i>
        </div>
        <el-divider direction="vertical" class="right-divider"></el-divider>
        <el-dropdown class="qz-header__user">
          <div class="ml10">
            {{ userInfo.username || '未登录' }}
            <i class="icon fa fa-caret-down"></i>
          </div>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item @click.native="goUserProfile">
              <i class="fa fa-user"></i>
              个人信息
            </el-dropdown-item>
            <el-dropdown-item divided @click.native="doLogout">
              <i class="fa fa-power-off"></i>
              退出登录
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
    </div>
    <!-- <global-warning
      v-if="userType === USER_TYPE_NORMAL"
      ref="warning"
      @state-change="handleWarningStateChange"
    /> -->
    <template>
      <div class="qz-header__sub-menu" v-show="subMenuList.length > 0">
        <span
          v-for="(menu, i) in subMenuList"
          :key="i"
          :class="{ active: menu.code === subMenuActive }"
          class="qz-header__sub-menu__item"
          @click="openMenu([], menu.code, 'code', true)"
        >
          <qz-icon
            v-if="menu.icon"
            class="qz-header__sub-menu__icon"
            :class="'icon-' + menu.icon"
          ></qz-icon>
          {{ menu.name }}
        </span>
      </div>
    </template>
  </div>
</template>
<script>
import { mapState } from 'vuex';
import {
  PAGE_URL_USER_PROFILE,
  PAGE_URL_FORGET_PASSWORD,
  PAGE_URL_LOGIN,
  PAGE_URL_AMIS_PRE,
  PAGE_URL_AMIS_EDIT,
  PAGE_URL_SYSTEM_CONFIG,
  PAGE_URL_OVERVIEW
} from '@/constant/page-url-constants';
import { USER_TYPE_NORMAL } from '@/constant/common-constants';
import GlobalWarning from './global-warning.vue';
import { staticRoutesList } from '@/router/index';
import { doLoginOut } from '@/service/login-service';
import { getUrlParms } from '@/utils/string-utils';
import {
  getLocalUserInfo,
  getMenuData,
  clearAllLocalStorage
} from '@/utils/storage-utils';
export default {
  props: {
    visible: {
      type: Boolean,
      default: true
    }
  },
  components: { GlobalWarning },
  data() {
    return {
      USER_TYPE_NORMAL,
      PAGE_URL_USER_PROFILE,
      PAGE_URL_FORGET_PASSWORD,
      unReadCount: 10,
      warningHeight: 0,
      visibleMenuList: [],
      hiddenMenuList: [],
      subMenuList: [],
      thirdMenuList: [],
      topMenuActive: '',
      subMenuActive: '',
      //如果是内置菜单，直接根据路由跳转，否则根据code获取amis配置渲染
      staticMenuList: staticRoutesList,
      systemMenuList: [],
      isShowConfig: false,
      whiteList: [PAGE_URL_AMIS_EDIT]
    };
  },
  mounted() {
    this.$nextTick(() => {
      const menuDataList = this.getMenuData();
      this.isShowConfig = menuDataList.some((item) => item.code === 'config');
      this.systemMenuList = menuDataList.filter(
        (item) => item.code !== 'config'
      );
      //先判断是否内置的，如果是，根据路由去找并将对应菜单显示，否则根据code去找对应的菜单
      const urlPath = window.location.pathname;
      const urlSearch = window.location.search;
      if (this.systemMenuList.length > 0) {
        let pkey = 'path';
        let pVal = urlPath;
        let list = this.staticMenuList;
        if (urlPath != PAGE_URL_OVERVIEW) {
          //amis配置的页面
          if (urlSearch.includes('acode')) {
            pVal = getUrlParms(urlSearch, 'acode');
            pkey = 'code';
            list = this.systemMenuList;
          } else {
            console.log(urlPath, 'urlPath');
          }
        } else {
          //如果是/layout/overview路径，默认打开第一个菜单,判断第一个菜单中是不是内置的
          const firstMenu = this.systemMenuList[0];
          const isStaticMenu = this.findCurrentMenuTree(
            this.staticMenuList,
            firstMenu.code,
            'code'
          );
          if (isStaticMenu) {
            const { self, parent, children } = isStaticMenu;
            list = this.staticMenuList;
            pVal = self.path;
            pkey = 'path';
          } else {
            list = this.systemMenuList;
            pVal = firstMenu.code;
            pkey = 'code';
          }
        }
        this.openMenu(list, pVal, pkey, false);

        // 计算可见菜单
        this.updateVisibleMenuList();
        window.addEventListener('resize', this.updateVisibleMenuList);
      } else {
        this.$message.warning('未获取到菜单数据，请联系管理员');
      }
    });
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.updateVisibleMenuList);
  },
  computed: {
    ...mapState(['logoImg', 'iconImg', 'userInfo']),
    headerHeight() {
      const topMenuHeight = 46;
      const subMenuHeight = this.subMenuList.length > 0 ? 42 : 0;
      return topMenuHeight + subMenuHeight + this.warningHeight;
    },
    moreMenu() {
      return this.hiddenMenuList.find(
        (item) => item.name === this.topMenuActive
      );
    }
  },
  watch: {
    headerHeight: {
      handler(val) {
        this.$emit('header-height', val);
      },
      immediate: true
    }
  },
  methods: {
    getMenuData,
    goConfig() {
      this.topMenuActive = 'config';
      this.subMenuActive = '';
      this.subMenuList = [];
      this.$router.push({
        path: PAGE_URL_SYSTEM_CONFIG
      });
    },
    updateVisibleMenuList() {
      const menuContainerWidth = this.$refs.menuContainer?.offsetWidth; // 菜单容器宽度
      const maxVisibleItems = Math.floor(menuContainerWidth / 100); // 假设每个菜单项宽度为100px
      this.visibleMenuList = this.systemMenuList.slice(0, maxVisibleItems - 2); // 留一个位置给“其他”菜单
      this.hiddenMenuList = this.systemMenuList.slice(maxVisibleItems - 2);
    },

    openMenu(menuList, code, key, isClick) {
      let mList = [];
      //当点击一级菜单时，判断是否内置菜单
      if (this.staticMenuList.find((item) => item.code === code)) {
        mList = this.staticMenuList;
      } else {
        mList = menuList.length == 0 ? this.systemMenuList : menuList;
      }
      const findResult = this.findCurrentMenuTree(mList, code, key);
      // console.log(findResult,'findResult')
      if (findResult) {
        const { self, parent, children } = findResult;
        let routeCode = '';
        //父节点存在，则当前是子节点
        if (parent) {
          this.topMenuActive = `${parent.code}`;
          this.subMenuActive = `${self.code}`;
          routeCode = self.code;
          this.subMenuList = parent?.children || [];
        } else {
          this.topMenuActive = `${self.code}`;
          routeCode = self.code;
          if (children && children?.length > 0) {
            this.subMenuActive = `${children[0].code}`;
            routeCode = children[0].code;
          } else {
            this.subMenuActive = '';
          }
          this.subMenuList = children || [];
        }
        //说明是内置菜单页面
        if (self.path && self.component) {
          this.$router.push({
            path: self.path,
            query: this.$route.query
          });
        } else {
          const query = isClick ? {} : this.$route.query;
          this.$router.push({
            path: PAGE_URL_AMIS_PRE,
            query: {
              ...query,
              acode: routeCode
            }
          });
        }
      }
    },

    // 查找当前节点及其父节点，子节点
    findCurrentMenuTree(treeArray, target, targetKey) {
      let targetNode = null;
      let parentNode = null;

      // 递归查找目标节点及其父节点
      function traverse(nodes, parent) {
        for (const node of nodes) {
          if (node[targetKey] === target) {
            targetNode = node;
            parentNode = parent;
            return;
          }
          if (node.children && node.children.length > 0) {
            traverse(node.children, node);
          }
        }
      }

      traverse(treeArray, null);

      if (!targetNode) {
        return null;
      }

      const result = {
        self: targetNode
      };

      if (parentNode) {
        result.parent = parentNode;
      }

      if (targetNode.children && targetNode.children.length > 0) {
        result.children = targetNode.children;
      }

      return result;
    },
    doLogout() {
      const userInfo = getLocalUserInfo();
      doLoginOut(userInfo)
        .then((res) => {
          clearAllLocalStorage();
          this.$router.push({
            path: PAGE_URL_LOGIN
          });
        })
        .catch((err) => {
          console.log(err);
          this.$message.error(err?.msg || '操作失败');
        });
    },
    goUserProfile() {
      this.$router.push({
        path: PAGE_URL_USER_PROFILE
      });
    },

    handleWarningStateChange() {
      this.warningHeight = this.$refs.warning.$el.offsetHeight || 0;
    }
  }
};
</script>
<style lang="less" scoped>
.qz-header {
  padding: 0;
  background-color: @bg-dark-color;
  color: @white-color;
  font-size: 14px;
  font-weight: 500;
  z-index: 100;

  &__logo {
    height: 24px;
  }

  &__wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
    box-sizing: border-box;
    width: 100%;
    height: 46px;
    .icon {
      font-size: 14px;
    }
  }

  &__left,
  &__right {
    display: flex;
    align-items: center;
    height: 100%;
  }

  &__search {
    display: flex;
    align-items: center;
    margin-right: 20px;
    padding-top: 2px;
    cursor: pointer;

    &:hover {
      color: @white-color;
      font-weight: 600;
    }
  }

  &__message {
    display: flex;
    align-items: center;
    cursor: pointer;

    i {
      font-size: 16px;
      color: @white-color;
    }

    ::v-deep .el-badge__content {
      font-size: 8px;
      height: 12px;
      line-height: 12px;
    }
  }
  &__config {
    padding: 0 15px;
    height: 30px;
    line-height: 30px;
    border-radius: 2px;
    letter-spacing: 1px;
    color: #fff;
    cursor: pointer;
    &.active {
      background: @bg-blue-color;

      &:hover {
        background: @bg-blue-color;
      }
    }

    &:hover {
      background: @bg-dark-hover-color;
    }
  }

  &__user {
    position: relative;
    display: flex;
    align-items: center;
    font-size: 14px;
    color: @white-color;
    cursor: pointer;

    i {
      font-size: 18px;

      font-weight: 550;
    }
  }

  &__menu {
    margin-left: 50px;
    display: flex;
  }
  &__menu-item {
    cursor: pointer;
    padding: 0 5px;
    text-decoration: none;
    display: flex;
    align-items: center;
  }
  &__menu-title {
    padding: 0 15px;
    height: 30px;
    line-height: 30px;
    border-radius: 2px;
    letter-spacing: 1px;
    color: @white-color;

    &.active {
      background: @bg-blue-color;

      &:hover {
        background: @bg-blue-color;
      }
    }

    &:hover {
      background: @bg-dark-hover-color;
    }
  }

  &__sub-menu {
    display: flex;
    align-items: center;
    width: 100%;
    padding: 0 20px;
    height: 42px;
    background: @white-color;
    box-sizing: border-box;
    box-shadow: @box-shadow-menu;

    &__icon {
      color: @text-tip-color;
      font-size: 14px;
    }

    &__item {
      display: inline-block;
      cursor: pointer;
      padding: 0 10px;
      text-decoration: none;
      font-size: 12px;
      color: @text-regular-color;

      &.active {
        font-weight: 600;
        font-size: 12px;
        color: @border-deep-blue-color;

        .qz-header__sub-menu__icon {
          color: @border-deep-blue-color;
          font-size: 14px;
        }
      }

      + .qz-header__sub-menu__item {
        margin-left: 30px;
      }
    }
  }

  .right-divider {
    align-self: center;
  }
}
</style>
<style lang="less">
.el-dropdown-menu.qz-header__menu-more {
  border: none;
  padding: 10px;
  background-color: @bg-dark-color;
  color: @white-color;
  font-size: 14px;
  min-width: 100px;
  .el-dropdown-menu__item {
    margin-top: 10px;
    padding: 0 15px;
    height: 30px;
    line-height: 30px;
    border-radius: 2px;
    letter-spacing: 1px;
    color: @white-color;
    &:first-of-type {
      margin-top: 0;
    }
    &:hover {
      background-color: @bg-dark-hover-color;
    }
    &.active {
      background: @bg-blue-color;
      &:hover {
        background: @bg-blue-color;
      }
    }
  }
}
</style>
