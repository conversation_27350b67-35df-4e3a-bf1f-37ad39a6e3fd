<!--
 * @Fileoverview: 通用左右结构模板
 * @Description: 通用左右结构模板
-->
<template>
  <el-container class="qz-layout-template">
    <el-aside
      ref="left"
      width="250px"
      :class="isExpand ? 'aside--expand' : 'aside--arrow'"
    >
      <div ref="asideResize" class="aside-resize" @click="isExpand = !isExpand">
        <i class="el-icon-caret-left" v-if="isExpand"></i>
        <i class="el-icon-caret-right" v-if="!isExpand"></i>
      </div>
      <div v-if="$slots.templateAside && isExpand">
        <slot name="templateAside"></slot>
      </div>
    </el-aside>
    <div class="qz-layout-template__resize" @mousedown="dragSide"></div>
    <el-main>
      <div v-if="$slots.templateMain">
        <slot name="templateMain"></slot>
      </div>
    </el-main>
  </el-container>
</template>

<script>
export default {
  data() {
    return {
      isExpand: true
    };
  },
  watch: {
    isExpand(val) {
      const asideResize = this.$refs.asideResize;
      const left = this.$refs.left.$el;
      if (val) {
        asideResize.style.left = '230px';
        left.style.width = '250px';
      } else {
        asideResize.style.left = '-10px';
      }
    }
  },
  methods: {
    dragSide(e) {
      const left = this.$refs.left.$el;
      const asideResize = this.$refs.asideResize;
      const boxWidth = left.offsetWidth;
      document.onmousemove = function onMouseMove(moveEvent) {
        if (moveEvent.x >= 249 && moveEvent.x <= 501) {
          const leftWidth = boxWidth + (moveEvent.x - e.x);
          left.style.width = leftWidth + 'px';
          asideResize.style.left = leftWidth - 20 + 'px';
        }
      };
      document.onmouseup = () => {
        document.onmousemove = document.onmouseup = null;
      };
    }
  }
};
</script>

<style lang="less" scoped>
.qz-layout-template {
  height: 100%;
  border: 1px solid #e3e3e3;
  min-height: 500px;
  position: relative;
  &__resize {
    cursor: col-resize;
    background-color: transparent;
    transition-delay: 0.1s;
    height: 100%;
    width: 5px;
    user-select: none; /* disable selection */
    &:hover {
      background-color: rgba(175, 184, 193, 0.2);
    }
  }
  .el-aside {
    overflow-y: auto;
    background: #fff;
    border-right: 1px solid @table-border-color;
    max-height: 820px;
    &:hover {
      .aside-resize {
        opacity: 1;
      }
    }
  }
  .el-main {
    height: 100%;
    overflow: auto;
  }
  .aside--expand {
    min-width: 250px;
    max-width: 500px;
    .aside-resize {
      position: absolute;
      left: 230px;
      top: 250px;
    }
  }
  .aside--arrow {
    width: 0px !important;
    .aside-resize {
      position: absolute;
      opacity: 1;
      left: -10px;
      top: 250px;
    }
  }
  .aside-resize {
    height: 44px;
    width: 14px;
    background-color: #fff;
    border: 1px solid #e7e9e8;
    box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.06);
    border-radius: 8px;
    line-height: 44px;
    text-align: center;
    z-index: 999;
    margin-left: 10px;
    opacity: 0;
    cursor: pointer;
    transition: opacity 0.2s;
  }
}
</style>
