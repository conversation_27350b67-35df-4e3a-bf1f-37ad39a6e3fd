<!--
 * @Fileoverview: 视图组件
 * @Description: 视图组件
-->
<template>
  <div class="qz-view">
    <div class="qz-view__header">
      <el-input
        placeholder="搜索视图"
        v-model="filterText"
        class="qz-view__filter"
        size="small"
        suffix-icon="el-icon-search"
      ></el-input>
    </div>
    <el-tree
      :data="viewList"
      :props="defaultProps"
      :filter-node-method="filterNode"
      default-expand-all
      current-node-key="all"
      @node-click="clickNode"
      node-key="id"
      ref="tree"
    >
      <div
        :class="{
          'custom-tree-node': data.isDefault,
          'custom-tree-node custom-tree-node--static': !data.isDefault
        }"
        slot-scope="{ node, data }"
      >
        <el-tooltip
          :content="data.name"
          placement="right"
          :disabled="!isShowTooltip"
        >
          <div
            class="custom-tree-node__label text-hidden"
            @mouseenter="visibilityChange($event)"
          >
            {{ data.name }}
          </div>
        </el-tooltip>
        <!-- <div
          class="custom-tree-node__count"
          :id="'count--' + data.id"
        >
          {{ data.count }}
        </div> -->
      </div>
    </el-tree>
  </div>
</template>

<script>
export default {
  props: ['viewList'],
  data() {
    return {
      filterText: '',
      isShowTooltip: false,
      defaultProps: {
        children: 'children',
        label: 'name'
      }
    };
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val);
    },
    viewList() {
      this.$nextTick(() => {
        // this.setCurrentView('全部风险')
        //   for (let i = 0; i < this.viewList.length; i++) {
        //     if (this.viewList[i].type === 'category') {
        //       this.treeExpandData = [this.viewList[i].id];
        //       break;
        //     }
        //   }
      });
    }
  },
  mounted() {},
  methods: {
    // 设置id这个节点的选中状态，外部调用
    setCurrentView(id) {
      this.$nextTick(() => {
        if (id) {
          this.$refs.tree.setCurrentKey(id);
          const selected = this.$refs.tree.getNode(id);
          this.clickNode(selected.data);
          if (
            this.$refs.tree.getNode(selected) &&
            this.$refs.tree.getNode(selected).parent
          ) {
            this.expandParents(this.$refs.tree.getNode(selected).parent);
          }
        }
      });
    },
    expandParents(node) {
      node.expanded = true;
      if (node.parent) {
        this.expandParents(node.parent);
      }
    },
    visibilityChange(event) {
      const ev = event.target;
      const ev_weight = ev.scrollWidth;
      const content_weight = ev.clientWidth;
      if (ev_weight > content_weight) {
        this.isShowTooltip = true;
      } else {
        this.isShowTooltip = false;
      }
    },
    clickNode(data, node) {
      if (!data.hasChildren) {
        if (JSON.stringify(data) !== '{}') {
          this.$emit('changeView', data, node);
        }
      }
    },
    filterNode(value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    }
  }
};
</script>

<style lang="less" scoped>
.el-dropdown-menu {
  margin: 0;
  padding: 0;
}
.el-button {
  min-width: 0;
}
</style>
<style lang="less">
.qz-view {
  padding-bottom: 20px;
  &__header {
    margin: 20px;
    display: flex;
  }
  .custom-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-right: 8px;
    height: 24px;
    line-height: 24px;
    width: 0;
    &__label {
      flex: 1 1 auto;
      min-width: 50px;
    }
    &__count {
      max-width: 130px;
      display: block;
      padding-left: 10px;
    }
    &__edit {
      font-size: 16px;
      font-weight: 800;
      display: none;
    }
    &--static {
      &:hover {
        .custom-tree-node__edit {
          display: block !important;
          color: #409eff;
        }
        .custom-tree-node__count {
          display: none !important;
        }
      }
    }
  }
  // cover element-ui style
  .el-tree {
    padding: 0 20px;
    margin-top: -5px;
    .el-tree-node__content {
      height: 40px;
      align-items: center;
      border-radius: 5px;
    }
    .el-tree-node__expand-icon {
      color: #272f68;
      padding-right: 6px !important;
    }
    .el-tree-node__expand-icon.is-leaf {
      color: transparent;
    }
    .el-tree-node__label {
      font-family: PingFangSC-Regular;
      font-size: 14px;
      color: #2e3444;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .el-tree-node > .el-tree-node__content:hover {
      background: @tree-hover-click-bg-color;
      color: #409eff;
    }
    .el-tree-node.is-current > .el-tree-node__content {
      background: @tree-hover-click-bg-color;
      color: #409eff;
    }
  }
  .el-input__inner::placeholder {
    color: #767676;
    font-size: 13px;
  }
  .el-button {
    padding: 9px 0;
    width: 70px;
    margin-left: 10px;
    min-width: 0;
  }
  .el-input__icon {
    font-size: 16px;
    color: #62656f;
  }
  .el-input__inner {
    border: 1px solid #dedddd;
    color: #767676;
  }
}
.options-popover {
  &__footer {
    margin-top: 10px;
    display: flex;
    justify-content: flex-end;
  }
  &__delete {
    i {
      color: @warning-color;
      margin-right: 10px;
      font-size: 14px;
    }
  }
  &__edit {
    display: flex;
    align-items: center;
    &__label {
      width: 100px;
    }
  }
}
</style>
