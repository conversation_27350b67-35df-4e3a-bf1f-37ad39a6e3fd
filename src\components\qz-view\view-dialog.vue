<!--
 * @Fileoverview: 保存&另存为视图对话框
 * @Description: 与通用视图配套的保存&另存为视图对话框
-->
<template>
  <div class="view-dialog">
    <el-form
      ref="form"
      :model="viewForm"
      :rules="viewFormRules"
      label-width="100px"
      v-if="params.type === 'save'"
    >
      <el-form-item prop="name" label="视图名称">
        <el-input
          v-model.trim="viewForm.name"
          placeholder="请输入视图名称"
          size="small"
          style="width: 100%"
        ></el-input>
      </el-form-item>
      <el-form-item prop="type" label="分类">
        <el-select
          v-model="viewForm.type"
          placeholder="新增分类直接输入内容回车即可"
          size="small"
          filterable
          allow-create
          default-first-option
          style="width: 100%"
        >
          <el-option
            v-for="type in params.viewTypeList"
            :key="type.id"
            :label="type.label"
            :value="type.id"
          ></el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <div v-else class="view-dialog__content">
      <div>
        <i class="el-icon-warning"></i>
        <span>确定覆盖视图吗？</span>
      </div>
      <div>覆盖当前视图原有的条件</div>
    </div>
    <div class="align-right">
      <el-button size="small" @click="cancel">取消</el-button>
      <el-button size="small" type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>

<script>
export default {
  props: ['params'],
  data() {
    return {
      viewForm: {
        name: '',
        type: ''
      },
      viewFormRules: {
        name: [
          { required: true, message: '请输入视图名称', trigger: 'blur' },
          { min: 1, max: 10, message: '长度在 1 到 10 个字符', trigger: 'blur' }
        ]
      }
    };
  },
  methods: {
    cancel() {
      this.params.close();
    },
    confirm() {
      if (this.$refs.form) {
        // 保存视图
        this.$refs.form.validate((valid) => {
          if (valid) {
            if (this.viewForm.type.length && !this.viewForm.type.trim()) {
              this.$message.error('分类名称不可为空');
              return false;
            }
            if (this.viewForm.type.length > 15) {
              this.$message.error('分类名称长度不可超过15字符');
              return false;
            }
            const typeIndex = this.params.viewTypeList.findIndex((item) => {
              return item.id === this.viewForm.type;
            });
            this.params.callBack({
              viewInfo: this.viewForm,
              isAddNewType: typeIndex === -1
            });
            this.cancel();
          } else {
            return false;
          }
        });
      } else {
        // 覆盖视图
        this.params.callBack({
          type: this.params.type
        });
        this.cancel();
      }
    }
  }
};
</script>

<style lang="less">
.view-dialog {
  &__content {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
  }
  i {
    color: @warning-color;
    font-size: 16px;
    margin-right: 10px;
  }
}
</style>
