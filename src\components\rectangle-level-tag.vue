<!--
 * @Fileoverview: 敏感/风险等级组件
 * @Description: 敏感/风险等级组件
-->
<template>
  <span class="rectangle-level-tag">
    <span
      v-if="levelName"
      :class="`rectangle-level-tag--${className}`"
      :style="`width:${tagWidth}px`"
    >
      {{ levelName }}
    </span>
    <span v-else-if="!levelName && showEmpty">--</span>
  </span>
</template>

<script>
import {
  SENSI_LEVEL_NAME_CLASS_MAP,
  COMMON_RISK_LEVEL_CLASS_MAP,
  COMMON_RISK_LEVEL_NAME_CLASS_MAP,
  OTHER_COMMON_RISK_LEVEL_NAME_CLASS_MAP,
  WEAKNESS_COMMON_RISK_LEVEL_NAME_CLASS_MAP
} from '@/constant/common-constants';

export default {
  props: {
    levelType: {
      type: String,
      default: 'sensiLevel' // sensiLevel | otherRiskLevel | riskLevel | riskNameLevel
    },
    level: {
      type: String | Number,
      default: ''
    },
    showEmpty: {
      type: <PERSON><PERSON><PERSON>,
      default: true
    },
    tagWidth: {
      type: Number,
      default: 45
    }
  },
  data() {
    return {
      SENSI_LEVEL_NAME_CLASS_MAP,
      COMMON_RISK_LEVEL_CLASS_MAP,
      COMMON_RISK_LEVEL_NAME_CLASS_MAP,
      OTHER_COMMON_RISK_LEVEL_NAME_CLASS_MAP,
      WEAKNESS_COMMON_RISK_LEVEL_NAME_CLASS_MAP
    };
  },
  computed: {
    levelName() {
      if (this.levelType) {
        switch (this.levelType) {
          case 'sensiLevel':
            return this.level;
          case 'riskLevel':
            return WEAKNESS_COMMON_RISK_LEVEL_NAME_CLASS_MAP[this.level];
          case 'riskNameLevel':
            return this.level;
          case 'otherRiskLevel':
            return OTHER_COMMON_RISK_LEVEL_NAME_CLASS_MAP[this.level];
          default:
        }
      }
    },
    className() {
      if (this.levelType) {
        switch (this.levelType) {
          case 'sensiLevel':
            return SENSI_LEVEL_NAME_CLASS_MAP[this.level];
          case 'riskLevel':
            return COMMON_RISK_LEVEL_CLASS_MAP[this.level];
          case 'riskNameLevel':
            return COMMON_RISK_LEVEL_NAME_CLASS_MAP[this.level];
          case 'otherRiskLevel':
            return COMMON_RISK_LEVEL_CLASS_MAP[this.level];
          default:
        }
      }
    }
  }
};
</script>

<style lang="less">
.rectangle-level-tag(@background,@font-color) {
  color: @font-color;
  background: @background;
  height: 20px;
  padding: 0 5px;
  flex-shrink: 0;
  font-size: 12px;
  line-height: 20px;
  text-align: center;
  border-radius: 11px;
  margin-right: 10px;
  font-family: PingFangSC-Regular;
}

.rectangle-level-tag {
  display: flex;
  &--high {
    .rectangle-level-tag(@high-level-color-bg,@high-level-color-font);
  }
  &--mid {
    .rectangle-level-tag(@mid-level-color-bg,@mid-level-color-font);
  }
  &--low {
    .rectangle-level-tag(@low-level-color-bg,@low-level-color-font);
  }
  &--non {
    .rectangle-level-tag(@non-level-color-bg,@non-level-color-font);
  }
  &--other {
    .rectangle-level-tag(@other-level-color-bg,@other-level-color-font);
  }
}
</style>
