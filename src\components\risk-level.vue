<template>
  <div :class="'level-' + internalLevel" class="risk-level">
    {{ levelMap[internalLevel] }}
  </div>
</template>

<script>
import { LEVEL_MAP } from '@/constant/common-constants';

export default {
  props: {
    level: [Number, String]
  },
  data() {
    return {
      internalLevel: this.level,
      levelMap: LEVEL_MAP
    };
  },
  watch: {
    level() {
      this.internalLevel = this.level;
    }
  }
};
</script>

<style lang="less" scoped>
.risk-level {
  display: inline-block;
  font-size: 12px;
  width: 16px;
  height: 16px;
  line-height: 16px;
  text-align: center;
  border-radius: 3px;
  color: #fff;
  &.level-3 {
    background-color: #ff4545;
  }
  &.level-2 {
    background-color: #feaa00;
  }
  &.level-1 {
    background-color: #6bd000;
  }
}
</style>
