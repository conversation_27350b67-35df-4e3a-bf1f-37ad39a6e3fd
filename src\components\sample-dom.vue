<template>
  <div class="qz-sample" :class="'sample-' + randomId">
    <div class="flex-top">
      <i class="el-icon-close close-btn ml20" @click="closeSample"></i>
      <div class="row title-row" v-show="tabs.includes(API_TAB_TITLE)">
        {{ rowData.apiUrl }}
      </div>
      <div class="sample-grid-btns" v-show="params.sampleType == 'api'">
        <span
          class="circle-bg"
          :class="{ disabled: sampleGridSetting.params.page == 1 }"
          @click="handleChangeSampleClick('up')"
          v-show="showChangeSampleBtn"
        >
          <i class="fa fa-angle-left"></i>
        </span>
        <el-popover
          ref="sample-list-popover"
          placement="bottom-end"
          trigger="click"
          popper-class="qz-sample-table-wrap"
          :offset="65"
        >
          <qz-pro-table
            ref="sample-grid-table"
            :data-source="SAMPLE_EVENT_GET_SAMPLE_LIST_URL"
            :requestParamsFormatter="handleSampleParamsBeforeSearch"
            :requestImmediately="false"
            :afterFetch="handleAfterFetch"
            request-method="get"
            pageLayout="prev, pager, next"
            @filter-change="filterSampleLabels"
          >
            <qz-table-column prop="timestamp" width="200" label="事件时间">
              <template slot-scope="{ row }">
                {{ formatTime(row.timestamp) }}
              </template>
            </qz-table-column>
            <qz-table-column
              prop="method"
              width="80"
              label="请求方法"
            ></qz-table-column>
            <qz-table-column
              prop="reqDataLabels"
              width="180"
              label="请求数据标签"
              show-overflow-tooltip
              :filter-multiple="false"
              :filters="sampleGridSetting.reqLabelsFilter"
              filter-placement="bottom-end"
              column-key="reqDataLabels"
            >
              <template slot-scope="{ row }">
                {{ formatSampleGridLabel(row.upLabels) }}
              </template>
            </qz-table-column>
            <qz-table-column
              prop="rspDataLabels"
              width="180"
              label="返回数据标签"
              show-overflow-tooltip
              :filter-multiple="false"
              :filters="sampleGridSetting.rspLabelsFilter"
              filter-placement="bottom-end"
              column-key="rspDataLabels"
            >
              <template slot-scope="{ row }">
                {{ formatSampleGridLabel(row.downLabels) }}
              </template>
            </qz-table-column>
            <qz-table-column
              prop="net.srcIp"
              label="源IP"
              width="140"
            ></qz-table-column>
            <qz-table-column prop="" label="操作" width="60">
              <template slot-scope="{ $index, row }">
                <el-tooltip
                  class="item"
                  effect="dark"
                  content="查看"
                  placement="top"
                >
                  <i class="el-icon-document" @click="showSample($index)"></i>
                </el-tooltip>
              </template>
            </qz-table-column>
          </qz-pro-table>
          <span
            slot="reference"
            v-show="showSampleGridBtn"
            class="square-bg ml10 mr10"
          >
            <i class="fa fa-list-ul"></i>
          </span>
        </el-popover>
        <span
          class="circle-bg"
          :class="{
            disabled:
              !sampleGridSetting.totalCount ||
              sampleGridSetting.params.page == sampleGridSetting.totalCount
          }"
          @click="handleChangeSampleClick('down')"
          v-show="showChangeSampleBtn"
        >
          <i class="fa fa-angle-right"></i>
        </span>
      </div>
      <div class="row tabs tab-row">
        <span
          :class="{ active: activeTab == tab }"
          class="tab-pane"
          v-for="tab in tabs"
          :key="tab"
          @click="activeTab = tab"
          >{{ tab }}</span
        >
      </div>
      <div class="sample-info-side">
        事件时间：{{ formatTime(sampleResponseData.timestamp) }}
        <span
          v-if="canSensitize && isDesensitize"
          class="action-link ml20"
          @click="desensitize()"
          >取消脱敏</span
        >
      </div>
    </div>
    <div class="flex-bottom mt20">
      <SampleDom
        ref="sample"
        :active-tab="activeTab"
        :related-modules="{
          [EVENT_TAB_TITLE]: ['sample', 'net'],
          [API_TAB_TITLE]: ['sample', 'net'],
          [SENSI_TAB_TITLE]: ['file', 'sensi']
        }"
      />
    </div>
  </div>
</template>

<script>
import {
  BIZ_LABEL_FILE_UPLOAD,
  BIZ_LABEL_FILE_DOWNLOAD
} from '@/constant/common-constants.js';
import { DATA_PRIVILEGE_SENSITIZED } from '@/constant/permission-code-constants';
import {
  SAMPLE_EVENT_GET_SAMPLE_LIST_URL,
  SAMPLE_EVENT_GET_SAMPLE_LABELS_URL,
  API_CONFIG_EXTRACT_CONTENT_URL,
  GET_SAMPLE_BY_ID,
  FILE_DOWNLOAD,
  LINK_ALERT_RELATE_EVENT_DETAIL,
  TRACE_TASK_GET_SAMPLES,
  TRACE_TASK_EXTRACT_CONTENT,
  LINK_ALERT_RELATE_EVENT_EXTRACT_CONTENT,
  WEB_LOGS_FULL_DETAIL,
  SAMPLE_EVENT_LGO_DETAIL,
  WEAKNESS_GET_SAMPLE,
  UNIFIED_EXIT_RISK_SAMPLE,
  WEAKNESS_APP_SENSIDETAIL
} from '@/constant/data-url-constants.js';
import { getLocalUserInfo } from '@/utils/storage-utils';
import SampleDom from '@/components/sample/index';
import * as labelService from '@/service/data-label-service';
import {
  getLogValue,
  getEventValue,
  getWeaknessValue
} from '@/service/sample-event-service';

const EVENT_TAB_TITLE = '事件详情';
const API_TAB_TITLE = '接口详情';
const SENSI_TAB_TITLE = '敏感数据';
export default {
  components: {
    SampleDom
  },
  props: ['params', 'showFullTextLocationLabelKeyValueBoolean'],
  data() {
    return {
      EVENT_TAB_TITLE,
      API_TAB_TITLE,
      SENSI_TAB_TITLE,
      SAMPLE_EVENT_GET_SAMPLE_LIST_URL,
      randomId: '',
      labels: [],
      dataLabelMap: {},
      rowData: {},

      canSensitize: true,
      isDesensitize: true,
      showSampleListBlock: true,

      showChangeSampleBtn: false,
      showSampleGridBtn: false,
      getCurrentSampleFunc: null,
      getUrlRelatedLabels: null,
      getSampleList: null,
      sampleResponseData: {},
      tabs: [],
      activeTab: '',
      // 样例列表参数
      sampleGridSetting: {
        params: {
          limit: 10,
          page: 1
        },
        totalCount: 0,
        reqLabelsFilter: [],
        rspLabelsFilter: []
      }
    };
  },
  watch: {
    params() {
      this.setTabs();
    }
  },
  methods: {
    formatTime(timestamp) {
      const time =
        (timestamp &&
          moment(Number(timestamp)).format('YYYY-MM-DD HH:mm:ss')) ||
        '';
      return time;
    },
    desensitize() {
      this.isDesensitize = false;
      this.initSample();
    },
    initSample() {
      const urlSample = this.$refs['sample'];
      const self = this;
      const singleRowData = this.rowData;
      const isFile =
        (singleRowData.classifications &&
          (singleRowData.classifications.includes(BIZ_LABEL_FILE_UPLOAD) ||
            singleRowData.classifications.includes(BIZ_LABEL_FILE_DOWNLOAD))) ||
        false;
      const settingParams = {
        element: '.sample-' + self.randomId,
        showChangeSampleBtn: self.showChangeSampleBtn,
        showSampleGridBtn: self.showSampleGridBtn,
        getCurrentSampleFunc: self.getCurrentSampleFunc,
        getUrlRelatedLabels: self.getUrlRelatedLabels,
        showFullTextLocationLabelKeyValueBoolean:
          self.showFullTextLocationLabelKeyValueBoolean,

        showLocationTabToggleBtnStr: '1,4,5',
        isFile: isFile,

        getFullTextValueFromJava: self.getFullTextValueFromJava,
        isOriginBeTrueStr: '1,4,5',
        fileDownloadedUrl: FILE_DOWNLOAD,
        dataLabelMap: this.dataLabelMap,
        columns: [
          {
            name: 'key',
            label: '字段名',
            width: '30%',
            render: function (value) {
              let res = '';
              res = value.slice(2, value.length);
              res = res.replaceAll('\\-', '-');
              return res;
            }
          },
          {
            name: 'value',
            label: '字段值',
            width: '30%',
            render: function (value, singleRowData, keyName) {
              if (!self.isDesensitize) {
                value = singleRowData['keyValues'][keyName];
              }
              const html = `<div class="value-wrap">
                          <div class="value-left_content">${value}</div>
                        </div>`;
              return html;
            }
          },
          {
            name: 'location',
            label: '位置',
            width: '15%'
          },
          {
            name: 'type',
            label: '类型',
            width: '10%'
          }
        ],
        afterLoad: async function () {
          // 针对事件样例的敏感数据做处理
          if (['log_id', 'event_id'].includes(self.params.requestType)) {
            // plan A:es样例需高亮 start
            // let primary = self.rowData.primary;
            // const sensiRes = await getEventLabelValue(primary, self.isDesensitize, self.params.collectionName);
            // self.esEventSensiLabelValuesMap = sensiRes.data?.labelValues||{};
            // this.$refs['req-sample'].init();
            // this.$refs['rsp-sample'].init();
            // plan A end

            // plan B:es样例不需高亮 start
            let sensiRes = {};
            const primary =
              self.params.requestType == 'log_id'
                ? JSON.parse(self.params.rowData)
                : self.params;
            const logParams = {
              desensitize: self.isDesensitize,
              sysIp: primary.sysIp,
              id: primary.id,
              risk: self.params.risk
            };
            if (self.params.requestType == 'log_id') {
              sensiRes = await getLogValue(logParams);
            } else {
              const eventParams = {
                desensitize: self.isDesensitize,
                id: self.params.id,
                ip: self.params.ip,
                risk: self.params.risk
              };
              sensiRes = await getEventValue(eventParams);
            }
            const req = sensiRes.data?.labelValues?.req || {};
            const rsp = sensiRes.data?.labelValues?.rsp || {};
            const reqfullTextSensiDataList = [];
            const rspfullTextSensiDataList = [];
            for (const k in req) {
              reqfullTextSensiDataList.push({
                labelName: self.dataLabelMap[k].name || 'k',
                labelId: k,
                labels: req[k],
                count: req[k].length
              });
            }
            for (const k in rsp) {
              rspfullTextSensiDataList.push({
                labelName: self.dataLabelMap[k].name,
                labelId: k,
                labels: rsp[k],
                count: rsp[k].length
              });
            }
            this.reqfullTextSensiDataList = reqfullTextSensiDataList;
            this.rspfullTextSensiDataList = rspfullTextSensiDataList;
            // plan B end
          }

          // 此处注意self和this的使用，self指向sample-dom里的数据对象，this指向sample里的数据对象
          self.sampleResponseData = this.responseData;
          self.$emit('after-load', this.responseData);
        },
        bodyHtmlClickCb(nodePath, pathValueObj, e, location, domId) {
          // html内容点击回调
          self.$emit(
            'html-body-click',
            nodePath,
            pathValueObj,
            e,
            location,
            domId
          );
        },
        jsonFormatHtmlClickCb(
          selectorPath,
          location,
          pathValue,
          originValueType,
          e
        ) {
          // JSON内容点击回调
          self.$emit(
            'json-body-click',
            selectorPath,
            location,
            pathValue,
            originValueType,
            e
          );
        }
      };
      urlSample && urlSample.render(settingParams);
    },
    // type_id
    fetchSampleById(element) {
      const self = this;
      return this.$doGet({
        url: GET_SAMPLE_BY_ID,
        params: {
          id: self.rowData.id,
          desensitize: self.isDesensitize
        },
        loadingLayer: element
      }).then((res) => {
        return {
          data: res.data ? [res.data] : [],
          totalCount: res.data?.totalCount || 0
        };
      });
    },
    // url
    fetchSampleByUrl(element) {
      const self = this,
        searchParams = JSON.parse(
          JSON.stringify(this.sampleGridSetting.params)
        );
      searchParams.limit = 1;
      searchParams.page = searchParams.page || 1;
      return self
        .$doGet({
          url: SAMPLE_EVENT_GET_SAMPLE_LIST_URL,
          params: {
            uri: self.rowData.uri,
            desensitize: self.isDesensitize,
            ...searchParams,
            ...self.rowData.sampleGridParams
          },
          loadingLayer: element
        })
        .then((res) => {
          return {
            data: res.data?.rows || [],
            totalCount: res.data?.totalCount || 0
          };
        });
    },
    // es_id
    fetchSampleByEs(element) {
      const self = this;
      return self
        .$doPost({
          url: TRACE_TASK_GET_SAMPLES,
          params: {
            desensitize: self.isDesensitize,
            eventPrimary: self.params.eventId
          },
          loadingLayer: element
        })
        .then((res) => {
          const rows =
            res.data && JSON.stringify(res.data) != '{}' ? [res.data] : [];
          return {
            data: rows,
            totalCount: rows.length
          };
        });
    },
    // link_id
    fetchSampleByLink(element) {
      const self = this;
      return self
        .$doPost(
          {
            url: LINK_ALERT_RELATE_EVENT_DETAIL,
            params: {
              desensitize: self.isDesensitize,
              primary: self.params.primary
            },
            loadingLayer: element
          },
          true
        )
        .then((res) => {
          const rows =
            res.data && JSON.stringify(res.data) != '{}' ? [res.data] : [];
          return {
            data: rows,
            totalCount: rows.length
          };
        });
    },
    // log_id
    fetchSampleByLog(element) {
      const self = this;
      const paramsInfo = JSON.parse(self.params.rowData);
      return self
        .$doPost(
          {
            url: WEB_LOGS_FULL_DETAIL,
            params: {
              type: paramsInfo.type,
              sysIp: paramsInfo.sysIp,
              params: {
                id: paramsInfo.id,
                desensitize: self.isDesensitize
              }
            },
            loadingLayer: element
          },
          true
        )
        .then((res) => {
          const rows =
            res.data && JSON.stringify(res.data) != '{}' ? [res.data] : [];
          return {
            data: rows,
            totalCount: rows.length
          };
        });
    },
    // event_id
    fetchSampleByEvent(element) {
      const self = this;
      return self
        .$doPost(
          {
            url: SAMPLE_EVENT_LGO_DETAIL,
            params: {
              desensitize: self.isDesensitize,
              id: self.params.id,
              ip: self.params.ip,
              risk: self.params.risk
            },
            loadingLayer: element
          },
          true
        )
        .then((res) => {
          const rows =
            res.data?.data && JSON.stringify(res.data?.data) != '{}'
              ? [res.data?.data]
              : [];
          return {
            data: rows,
            totalCount: rows.length
          };
        });
    },
    // weakness_id
    fetchSampleByWeaknessId(element) {
      const self = this;
      const paramsInfo = JSON.parse(self.params.rowData);
      return self
        .$doPost(
          {
            url: WEAKNESS_GET_SAMPLE,
            params: {
              type: paramsInfo.type,
              sysIp: paramsInfo.sysIp,
              params: {
                id: paramsInfo.id,
                desensitize: self.isDesensitize
              }
            },
            loadingLayer: element
          },
          true
        )
        .then((res) => {
          const rows =
            res.data && JSON.stringify(res.data) != '{}' ? [res.data] : [];
          return {
            data: rows,
            totalCount: rows.length
          };
        });
    },
    fetchSampleByExitRiskId(element) {
      const self = this;
      const paramsInfo = JSON.parse(self.params.rowData);
      return self
        .$doPost(
          {
            url: UNIFIED_EXIT_RISK_SAMPLE,
            params: {
              riskType: 'EXIT',
              logId: paramsInfo.logId,
              desensitize: self.isDesensitize
            },
            loadingLayer: element
          },
          true
        )
        .then((res) => {
          const rows =
            res.data && JSON.stringify(res.data) != '{}' ? [res.data] : [];
          return {
            data: rows,
            totalCount: rows.length
          };
        });
    },
    reload() {
      const self = this;
      this.$nextTick(() => {
        if (this.params.rowData) {
          this.rowData = JSON.parse(this.params.rowData);
        }
        // 重置sampleGridSetting
        this.sampleGridSetting = {
          params: {
            limit: 10,
            page: 1
          },
          totalCount: 0,
          reqLabelsFilter: [],
          rspLabelsFilter: []
        };
        if (
          ![
            'type_id',
            'es_id',
            'link_id',
            'log_id',
            'event_id',
            'weakness_id',
            'exit_risk_id'
          ].includes(this.params.requestType) &&
          !this.rowData.uri
        )
          return;

        switch (this.params.requestType) {
          case 'type_id':
            self.showSampleGridBtn = false;
            self.showChangeSampleBtn = false;
            self.getCurrentSampleFunc = self.fetchSampleById;
            break;
          case 'es_id':
            self.showSampleGridBtn = false;
            self.showChangeSampleBtn = false;
            self.getCurrentSampleFunc = self.fetchSampleByEs;
            break;
          case 'link_id':
            self.showSampleGridBtn = false;
            self.showChangeSampleBtn = false;
            self.getCurrentSampleFunc = self.fetchSampleByLink;
            break;
          case 'log_id':
            self.showSampleGridBtn = false;
            self.showChangeSampleBtn = false;
            self.getCurrentSampleFunc = self.fetchSampleByLog;
            break;
          case 'event_id':
            self.showSampleGridBtn = false;
            self.showChangeSampleBtn = false;
            self.getCurrentSampleFunc = self.fetchSampleByEvent;
            break;
          case 'weakness_id':
            self.showSampleGridBtn = false;
            self.showChangeSampleBtn = false;
            self.getCurrentSampleFunc = self.fetchSampleByWeaknessId;
            break;
          case 'exit_risk_id':
            self.showSampleGridBtn = false;
            self.showChangeSampleBtn = false;
            self.getCurrentSampleFunc = self.fetchSampleByExitRiskId;
            break;
          default:
            self.showSampleGridBtn = true;
            self.showChangeSampleBtn = true;
            self.getCurrentSampleFunc = self.fetchSampleByUrl;
            break;
        }
        this.initSample();
        // 接口样例才有样例列表
        if (this.params.sampleType == 'api' && this.showSampleGridBtn) {
          this.initSampleGrid();
        }
      });
    },

    // extractContent，传内容数据获取内容敏感数据
    getFullTextValueFromJava(element, locationObj = {}, fullTextLabelArr) {
      const self = this,
        originValue = locationObj.data.originValue,
        originValueDesensiKey = locationObj.data.originValueDesensiKey;
      if (
        locationObj.value &&
        fullTextLabelArr.length > 0 &&
        !['log_id', 'event_id'].includes(this.params.requestType)
      ) {
        if (this.params.requestType == 'es_id') {
          return self.$doPost({
            url: TRACE_TASK_EXTRACT_CONTENT,
            loadingLayer: document.querySelector(element),
            params: {
              desensitize: self.isDesensitize,
              eventPrimary: self.params.eventId,
              location: locationObj.value
            }
          });
        } else if (this.params.requestType == 'link_id') {
          // return self.$doPost({
          //   url: LINK_ALERT_RELATE_EVENT_EXTRACT_CONTENT,
          //   loadingLayer: document.querySelector(element),
          //   params: {
          //     desensitize: self.isDesensitize,
          //     primary: self.params.primary,
          //     location: locationObj.value
          //   }
          // }, true).catch((err)=>{
          //   return {}
          // })
          return Promise.resolve({});
        } else if (
          this.params.requestType == 'weakness_id' &&
          originValue &&
          originValue != '{}' &&
          fullTextLabelArr.length
        ) {
          return self
            .$doPost(
              {
                url: WEAKNESS_APP_SENSIDETAIL,
                loadingLayer: document.querySelector(element),
                params: {
                  id: this.rowData.id,
                  desensitize: self.isDesensitize,
                  location: locationObj.value,
                  content: self.isDesensitize ? '' : originValue,
                  originValueDesensiKey: self.isDesensitize
                    ? originValueDesensiKey
                    : '',
                  sysIp: this.rowData.sysIp
                }
              },
              true
            )
            .then((res) => {
              return {
                data: res.data.rows
              };
            });
        } else {
          return self
            .$doPost({
              url: API_CONFIG_EXTRACT_CONTENT_URL,
              loadingLayer: document.querySelector(element),
              params: {
                id: self.sampleResponseData.sampleEventId,
                location: locationObj.value,
                desensitize: self.isDesensitize
              }
            })
            .catch((err) => {
              console.error(err);
              return {};
            });
        }
      } else {
        return Promise.resolve({});
      }
    },
    closeSample() {
      this.params.close();
    },
    // 样例列表methods
    handleAfterFetch(res) {
      this.sampleGridSetting.totalCount = res.data.totalCount || 0;
      return res;
    },
    initSampleGrid() {
      this.$refs['sample-grid-table'] &&
        this.$refs['sample-grid-table'].reload();
      // 获取相关标签，供样例列表做标签筛选
      this.$doGet({
        url: SAMPLE_EVENT_GET_SAMPLE_LABELS_URL,
        params: { uri: this.rowData.uri }
      }).then((res) => {
        // 请求
        res.data?.reqDataLabelSets?.forEach((list) => {
          this.sampleGridSetting.reqLabelsFilter.push({
            text: this.formatSampleGridLabel(list),
            value: list.join(',')
          });
        });
        // 返回
        res.data?.rspDataLabelSets?.forEach((list) => {
          this.sampleGridSetting.rspLabelsFilter.push({
            text: this.formatSampleGridLabel(list),
            value: list.join(',')
          });
        });
      });
    },
    showSample(index) {
      const page = this.$refs['sample-grid-table'].pageNo,
        pageSize = this.$refs['sample-grid-table'].pageSize;
      this.sampleGridSetting.params.page = (page - 1) * pageSize + index + 1;
      this.initSample();
      this.$refs['sample-list-popover'].doClose();
    },
    handleSampleParamsBeforeSearch(params) {
      this.sampleGridSetting.params.uri = this.rowData.uri;
      this.sampleGridSetting.params.page = params.page;
      this.sampleGridSetting.params.limit = params.limit;
      return this.sampleGridSetting.params;
    },
    filterSampleLabels(filter) {
      this.sampleGridSetting.params.page = 1;
      if (filter['reqDataLabels']) {
        this.sampleGridSetting.params['reqDataLabels'] =
          filter['reqDataLabels'][0] || '';
      }
      if (filter['rspDataLabels']) {
        this.sampleGridSetting.params['rspDataLabels'] =
          filter['rspDataLabels'][0] || '';
      }
      this.$refs['sample-grid-table'].reload();
    },
    formatSampleGridLabel(labels) {
      if (labels && labels.length) {
        const labelNameArr = labels.map((item) => {
          return this.dataLabelMap[item]?.name || item;
        });
        return labelNameArr.join('，');
      } else {
        return '--';
      }
    },
    handleChangeSampleClick(direction) {
      if (direction == 'up') {
        if (this.sampleGridSetting.params.page == 1) return;
        this.sampleGridSetting.params.page -= 1;
      } else {
        if (
          this.sampleGridSetting.params.page >=
          this.sampleGridSetting.totalCount
        )
          return;
        this.sampleGridSetting.params.page += 1;
      }
      this.initSample();
    },
    setTabs() {
      if (this.params.sampleType == 'api') {
        this.tabs = [API_TAB_TITLE, SENSI_TAB_TITLE];
      } else if (
        this.params.requestType == 'link_id' ||
        this.params.requestType == 'exit_risk_id'
      ) {
        this.tabs = [EVENT_TAB_TITLE];
      } else {
        this.tabs = [EVENT_TAB_TITLE, SENSI_TAB_TITLE];
      }
    }
  },
  created() {
    const { permissions } = getLocalUserInfo();
    this.setTabs();
    if (permissions.includes(DATA_PRIVILEGE_SENSITIZED)) {
      this.canSensitize = false;
    }
    this.randomId = new Date().getTime();
    labelService
      .getDataLabelsForSelect()
      .then((res) => {
        if (res.data && res.data.length > 0) {
          const dataLabelMap = {};
          for (let i = 0; i < res.data.length; i++) {
            const singleDataLabel = res.data[i];
            dataLabelMap[singleDataLabel.id] = singleDataLabel;
          }
          this.dataLabelMap = dataLabelMap;
        }
      })
      .finally((_) => {
        this.reload();
      });
  },
  mounted() {
    this.activeTab = this.tabs[0];
  }
};
</script>
<style lang="less">
.exit-risk-drawer {
  .qz-sample {
    position: relative;
    margin-top: -20px;
    .el-icon-close {
      display: none;
    }
    & .sample-info-side {
      right: 30px;
      top: 80px;
    }
  }
}
.qz-sample {
  position: relative;

  // 不同样式头部排版
  &.qz-alert__body__main,
  &.drawer-sample {
    padding: 0;
    display: flex;
    flex-direction: column;

    & .title-row {
      text-overflow: ellipsis;
      overflow: hidden;
      padding: 10px 20px;
    }

    & .tab-row {
      padding-left: 20px;
      padding-right: 20px;
    }

    & .close-btn {
      top: 10px;
      right: 10px;
    }

    & .sample-grid-btns {
      right: 40px;
      top: 10px;
    }

    & .flex-bottom {
      padding: 0 20px 20px 20px;
    }
  }

  &.log-sample {
    & .title-row,
    .close-btn {
      display: none;
    }

    & .sample-info-side {
      right: 10px;
      top: 20px;
    }
  }

  &.drawer-sample {
    & .sample-info-side {
      right: 10px;
      top: 60px;
    }
  }

  // 没有apiUrl
  &.sample-without-api-header,
  &.sample-log-header {
    & .title-row,
    .close-btn {
      display: none;
    }

    & .sample-grid-btns,
    .sample-info-side {
      top: 20px;
      z-index: 1;
    }

    & .sample-grid-btns {
      right: 0;
    }

    & .sample-info-side {
      right: 80px;
    }
  }

  // 事件样例
  &.qz-alert__body__main {
    & .sample-info-side {
      right: 40px;
      top: 20px;
    }
  }

  & .action-link {
    font-size: 12px;
  }

  & .tabs {
    position: relative;
    z-index: 0;
    display: flex;
    width: 100%;

    & .tab-pane {
      padding: 15px 0;
      cursor: pointer;
      position: static;
      z-index: 1;

      &.active {
        color: #4a97eb;
        border-bottom: 2px solid #4a97eb;
      }

      & + .tab-pane {
        margin-left: 30px;
      }
    }

    &::after {
      position: absolute;
      bottom: 0;
      left: 0;
      right: -50px;
      content: '';
      width: 100%;
      height: 2px;
      background: #d3d4d5;
      z-index: 0;
    }
  }

  & .sample-grid-btns {
    position: absolute;
    font-size: 12px;
  }

  & .sample-info-side {
    position: absolute;
    font-size: 12px;
  }

  & .circle-bg,
  .square-bg {
    display: inline-flex;
    cursor: pointer;
    background: #4a97eb;
    color: #fff;
    justify-content: center;
    align-items: center;
    vertical-align: middle;
  }

  & .circle-bg {
    width: 16px;
    height: 16px;
    border-radius: 50%;

    &.disabled {
      background: #ccc;
      cursor: not-allowed;
    }
  }

  & .square-bg {
    padding: 1px 2px;
    border-radius: 3px;
  }

  & .title-row {
    padding-bottom: 10px;
    border-bottom: 2px solid #d3d4d5;
    font-size: 14px;
    color: #000;
  }

  & .close-btn {
    position: absolute;
    font-size: 20px;
    color: #979797;
    cursor: pointer;
    font-weight: bold;
    z-index: 1;
  }

  & .flex-top {
    display: flex;
    flex-wrap: wrap;

    & .row {
      width: 100%;
    }
  }

  & .flex-bottom {
    flex: 1 1 0%;
    overflow: auto;
  }
}

.qz-sample-table-wrap {
  .qz-pro-table .el-table:not(.border) th.is-leaf {
    padding-top: 0px;
    padding-bottom: 0px;
  }

  .qz-pro-table .el-table th > .cell {
    font-size: 12px;
  }

  .qz-pro-table .el-table .cell {
    font-size: 12px;
    color: #54667c;
  }

  .qz-pro-table .el-table td,
  .qz-pro-table .el-table th {
    padding-top: 5px;
    padding-bottom: 5px;
  }

  .qz-pro-table .table-pagination .demonstration {
    font-size: 12px;
  }

  .qz-pro-table .table-pagination {
    margin-top: 10px;
  }

  .qz-pro-table .table-pagination .demonstration,
  .qz-pro-table .table-pagination .el-pagination {
    margin-bottom: 0px;
  }

  .el-pagination button,
  .el-pagination span:not([class*='suffix']) {
    font-size: 12px;
  }

  .el-pager li {
    font-size: 12px;
  }

  .qz-pro-table__header {
    margin-bottom: 0;
  }
}

.el-table-filter__list {
  & .el-table-filter__list-item {
    font-family: SourceHanSansCN-Regular;
    font-size: 12px;
    color: #606266;
    letter-spacing: 0;
    font-weight: 400;

    &.is-active {
      background-color: #fff;
      color: #4a97eb;
    }

    &:hover,
    .is-hover {
      background: #f5f7fa;
    }
  }
}
</style>
