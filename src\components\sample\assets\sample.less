@border-color: rgba(227,227,227,1);
@active-color: #4A97EB;
//灰度从上往下加深
@deep-grey:#979797;
@inactive-grey:#c4c6c9;
@font-grey:#666;
.sample-container{
	&__main{
		border: 1px solid @border-color;
		& .left{
			border-right: 3px solid #E6E6E6;
		}
		& .right{
			border-left: 3px solid #E6E6E6;
		}
		& .title{
			background: #F7F8FC;
			padding: 5px 0 5px 15px;
			font-size: 12px;
			color: #303133;
			font-weight: 500;
		}
		& .location{
			border-top: 1px solid @border-color;
			border-bottom: 1px solid @border-color;
			& .location-item{
				cursor: pointer;
				display: inline-block;
				padding: 7px 0;
				font-size: 13px;
				margin-left: 15px;
				font-weight: 500;
				&.active{
					color: @active-color;
					border-bottom: 2px solid @active-color;
				}
			}
		}
		& .content{
			display: none;
			height: 480px;
			overflow: auto;
			&.active{
				display: block;
			}
			& .content-wrap{
				position: relative;
				display: flex;
				flex-direction: column;
				height: 100%;
				&__top{
					display: flex;
					align-items: center;
					height: 56px;
					white-space: nowrap;
					padding: 0 12px;
					overflow: auto;
					border-bottom: 1px solid @border-color;
				}
				&__bottom{
					flex: 1 1 0%;
					overflow: auto;
					& .formatted-btn{
						position: absolute;
						right: 20px;
						bottom: 20px;
						cursor: pointer;
					}
					& i.sample-icon-brush {
					    width: 24px;
					    height: 24px;
					    display: inline-block;
					    background-size: 100%;
					    background-image: url('./img/brush.png');
					}

					& i.sample-icon-earth {
					    width: 24px;
					    height: 24px;
					    display: inline-block;
					    background-size: 100%;
					    background-image: url('./img/earth.png');
					}
				}
			}
			& .color-box{
				&.active{
					background: rgb(109, 133, 225);
	    			color: #fff;
	    			border-color: rgb(109, 133, 225);
				}
				display: inline-block;
				cursor: pointer;
				background: #f0f3ff;
			    border: 1px solid #787e98;
			    border-radius: 4px;
			    height: 30px;
			    line-height: 30px;
			    padding: 0 12px;
			    white-space: nowrap;
			    font-family: PingFangSC-Regular,Avenir,Helvetica,Arial,sans-serif;
			    font-size: 12px;
			    color: #787e98;
			    &+.color-box{
			    	margin-left: 12px;
			    }
			   
			}
			&__code{
				word-break: break-word;
				white-space: break-spaces;
				height: 100%;
			}
			&__formatted{
				height: 100%;
				width: 100%;
			}
		}
	}
	& .flex{
		display: flex;
	}
	&__netinfo{
		display: flex;
		text-align: center;
		border: 1px solid @border-color;
		& .title{
			background: #F7F8FC;
			padding: 5px 0 5px 15px;
			font-size: 12px;
			color: #303133;
			font-weight: 500;
			border-bottom: 1px solid @border-color;
		}
		& .left{
			flex: 1 1 0%;
		}
		& .right{
			width: 16.66%;
			border-left: 1px solid @border-color;
		}
		& .column{
			flex: 1 1 0%;
			&+.column{
				border-left: 1px solid @border-color;
			}
		}
		& .item{
			padding: 5px;
			font-size: 12px;
			color: #1A1A1A;
			word-break: break-all;
			&+.item{
				padding: 8px;
				border-top: 1px solid @border-color;
			}
		}
	}
	&__sensidata{
		background: #fff;
    	font-size: 12px;
    	color: #303133;
    	& .title-tr{
    		background: #F7F8FC;
    	}
	}
	&__filelist{
		& .row{
			display: flex;
			justify-content: space-between;
			padding: 8px;
			background: #F7F8FC;
			border: 1px solid rgb(227,227,227);
			font-size: 12px;
			color: #333;
			&+.row{
				margin-top: 12px;
			}
			& .right{
				width: 40%;
				display: flex;
				justify-content: space-between;
			}
			& .action-btn{
				font-size: 12px;
			}
		}
	}
	& .no-data{
		height: 100%;
	}
	& .ke-container{
		border: none;
	}
	
	& .sample-table{
	    border: 1px solid #e6e6e6;
	    border-collapse: collapse;
	    width: 100%;
	    & tr,td,th{
	      border: 1px solid #e6e6e6;
	      padding: 6px 8px;
	    }
	    & td{
	      word-break: break-all;
	      line-height: 2;
	    }
	}
	& .action-btn {
	  	font-family: PingFangSC-Regular;
	    font-size: 14px;
	  	color: @active-color;
	  	cursor: pointer;
	}
	& .pagination {
	    display: flex;
	    justify-content: flex-end;
	    align-items: center;
	    flex-wrap: wrap;
	    margin-top: 15px;
	    font-size: 12px;
	    .el-pagination,
	    .el-pagination__sizes,
	    .el-pagination__jump {
	      font-size: 13px;
	      color: @font-grey;
	    }
	    .el-pagination {
	      padding: 0;
	      margin-left: 8px;
	    }
	    .el-pagination .btn-prev:first-child {
	      margin-left: 0;
	    }
	    .el-pagination.is-background .el-pager li,
	    .el-pagination.is-background .btn-prev,
	    .el-pagination.is-background .btn-next {
	      background-color: white;
	      border: 1px solid @inactive-grey;
	      font-family: PingFangSC-Regular;
	      font-size: 12px;
	      color: @font-grey;
	      text-align: center;
	      border-radius: 4px;
	      &.active {
	        color: white;
	      }
	    }
	    .el-pagination.is-background .btn-prev:not(:disabled):hover,
	    .el-pagination.is-background .btn-next:not(:disabled):hover {
	      color: #409eff;
	    }
	    .el-pagination.is-background .btn-prev:disabled,
	    .el-pagination.is-background .btn-next:disabled {
	      background-color: #f4f4f5;
	      color: #c0c4cc;
	    }
	    .el-pagination.is-background .el-pager li.more.btn-quicknext {
	      border: none;
	      color: @deep-grey;
	      margin: 0;
	    }
	    .el-pagination.is-background .el-pager li:not(.disabled).active {
	      background-color: @active-color;
	      border-color: @active-color;
	    }
	    .el-pagination__editor.el-input {
	      width: 34px;
	    }
	    .el-pagination__jump {
	      margin-left: 0;
	    }
	}
	& .sample-search-form{
		display: flex;
		padding: 0 15px;
		position: absolute;
		bottom: 20px;
		width: 100%;
		align-items: center;
		& .search-input{
			margin-right: 15px;
			& .el-input__inner{
				border-color: rgba(211,212,213,1)
			}
		}
		& .circle{
			text-decoration: none;
			display: inline-flex;
			justify-content: center;
			align-items: center;
			border-radius: 18px;
			width: 18px;
			height: 18px;
			cursor: pointer;
			background: #fff;
			color:  #939393;
			border: 1px solid rgb(211,212,213);
			&:focus{
				border-color: #4A97EB;
			}
			&:active{
				border-color: rgb(211,212,213);
			}
			i {
				font-size: 18px;
			}
		}
		& .count-text{
			background: #fff;
			margin: 0 8px;
			color: #333;
		}
		& .color-orange{
			color: #FA6400;
		}

	}
	.monaco-editor .view-overlays .current-line{
		border: none;
		background-color: #F7F8FC;
	}
}
.alert-sample{
  z-index: 3000;
  &.qz-alert{
    display: flex;
    justify-content:center;
    align-items: center;
  }
  .qz-alert__body,.qz-alert__body__main{
    max-height: 95%;
    margin: 0;
  }
}