<template>
  <!-- 样例主体 -->
  <div>
    <div class="title">{{ title }}</div>
    <div class="location">
      <span
        class="location-item"
        :class="{ active: currentTab == locationObj.value }"
        v-for="(locationObj, i) in locationArr"
        :key="locationObj.value"
        :data-location="locationObj.value"
        @click="handleTabClick(locationObj)"
        >{{ locationObj.label_193 }}</span
      >
    </div>
    <div
      class="content"
      :class="{ active: currentTab == locationObj.value }"
      :id="`content-item__${locationObj.value}__${randomId}`"
      v-for="(locationObj, i) in locationArr"
      :key="locationObj.value"
    >
      <div
        class="content-wrap"
        v-if="
          locationObj.isFile ||
          (JSON.stringify(locationObj.data) != '{}' &&
            locationObj.data.isOrigin &&
            hasOwnProperty(locationObj.data, 'originValue'))
        "
      >
        <div
          class="content-wrap__top"
          v-if="locationObj.sensiLabelData && locationObj.sensiLabelData.length"
        >
          <span
            @click="matchLabel($event, sensiLabel, locationObj)"
            class="color-box"
            v-for="sensiLabel in locationObj.sensiLabelData"
            :key="sensiLabel.id"
            >{{ sensiLabel.name || sensiLabel.id }}({{
              sensiLabel.count
            }})</span
          >
        </div>
        <div class="content-wrap__bottom">
          <!-- <div class="content__code"
                        :id="`content-code__${locationObj.value}__${randomId}`"
                        v-show="(locationObj.contentType == 'html'&&locationObj.isFormatted
                               ||locationObj.contentType!='html'&&!locationObj.isFormatted)
                               &&![8,9].includes(locationObj.value)
                               &&!locationObj.isFile">{{locationObj.data.originValue}}</div> -->
          <div
            class="content__code"
            :id="`content-code__${locationObj.value}__${randomId}`"
            v-show="
              ((locationObj.contentType == 'html' && locationObj.isFormatted) ||
                (locationObj.contentType != 'html' &&
                  !locationObj.isFormatted)) &&
              ![8, 9].includes(locationObj.value) &&
              !locationObj.isFile
            "
          ></div>
          <div
            v-show="
              (locationObj.contentType == 'html' && !locationObj.isFormatted) ||
              (locationObj.contentType != 'html' && locationObj.isFormatted) ||
              [8, 9].includes(locationObj.value) ||
              locationObj.isFile
            "
            class="content__formatted"
            :id="`content-formatted__${locationObj.value}__${randomId}`"
          ></div>
          <i
            v-if="
              sampleConf.showLocationTabToggleBtnStr.includes(
                locationObj.value
              ) &&
              ['xml', 'jsonp', 'html'].includes(locationObj.contentType) &&
              !locationObj.isFile
            "
            class="formatted-btn"
            :class="
              locationObj.isFormatted
                ? 'sample-icon-earth'
                : 'sample-icon-brush'
            "
            @click="formatOriginCode(locationObj)"
          ></i>
          <!-- 面板req-raw和rsp-raw里的查询模块 -->
          <div
            class="sample-search-form"
            v-if="[8, 9].includes(locationObj.value)"
          >
            <el-input
              @input="handleSearchFormInput(locationObj)"
              class="search-input"
              size="small"
              placeholder="请输入内容进行查找"
              v-model="locationObj._searchWord"
              clearable
            >
              <i slot="prefix" class="el-input__icon el-icon-search"></i>
            </el-input>
            <a
              class="circle"
              @click="searchMatch('prev', locationObj)"
              href="javascript:;"
              ><i class="fa fa-angle-left"></i
            ></a>
            <div class="count-text">
              <span class="color-orange">{{
                (locationObj._currentMatchIndex !== null &&
                  locationObj._currentMatchIndex + 1) ||
                0
              }}</span
              >/<span>{{ locationObj._foundCount || 0 }}</span>
            </div>
            <a
              class="circle"
              @click="searchMatch('next', locationObj)"
              href="javascript:;"
              ><i class="fa fa-angle-right"></i
            ></a>
          </div>
        </div>
      </div>
      <!-- 列表 -->
      <div class="content-wrap" v-else style="padding: 15px; overflow: auto">
        <table
          v-if="
            sampleConf.columns.length > 0 &&
            JSON.stringify(locationObj.data) != '{}' &&
            hasOwnProperty(locationObj.data, locationRenderKey) &&
            JSON.stringify(locationObj.data[locationRenderKey]) != '{}'
          "
          class="sample-table"
          style="text-align: center; background: #fff"
        >
          <thead>
            <tr>
              <th
                v-for="(col, colIndex) in sampleConf.columns"
                :key="colIndex"
                class="column-title"
                scope="col"
                :style="{ textAlign: 'center', width: col.width }"
              >
                {{ col.label || '' }}
              </th>
            </tr>
          </thead>
          <tbody>
            <tr
              v-for="(pObj, p) in locationObj.data['sensiKeyValues']"
              :key="p"
              class="item-data"
              :data-location="locationObj.data.location"
              :data-type="locationObj.data.location.type"
              :data-path="p"
            >
              <td v-for="(col, colIndex) in sampleConf.columns" :key="colIndex">
                <div
                  v-if="
                    hasOwnProperty(col, 'colType') && col.colType == 'button'
                  "
                >
                  <span
                    v-for="(btn, bIndex) in col.buttons"
                    :key="bIndex"
                    class="table-btn"
                    :class="btn.name"
                    >{{ btn.label }}</span
                  >
                </div>
                <div v-else>
                  <div
                    v-if="col.name == 'key'"
                    v-html="
                      col.render
                        ? col.render(filterHtml(p), locationObj.data, p)
                        : filterHtml(p)
                    "
                  ></div>
                  <div
                    v-if="col.name == 'value'"
                    v-html="
                      col.render
                        ? col.render(
                            filterHtml(pObj['value']),
                            locationObj.data,
                            p
                          )
                        : filterHtml(pObj['value'])
                    "
                  ></div>
                  <div
                    v-if="col.name == 'location'"
                    v-html="
                      col.render
                        ? col.render(
                            filterHtml(locationObj[locationObj.data[col.name]]),
                            locationObj.data,
                            p
                          )
                        : filterHtml(p)
                    "
                  ></div>
                  <div
                    v-if="col.name == 'type'"
                    v-html="
                      col.render
                        ? col.render(
                            filterHtml(typeObj[locationObj.data[col.name]]),
                            locationObj.data,
                            p
                          )
                        : filterHtml(typeObj[locationObj.data[col.name]])
                    "
                  ></div>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
        <div v-else class="no-data">暂无数据</div>
      </div>
    </div>
  </div>
</template>
<script>
import { SAMPLE_LOCATION } from '@/constant/common-constants.js';
/*test monaco start*/
import * as monaco from 'monaco-editor/esm/vs/editor/editor.api.js';
//
/*test monaco end*/
/* 通过script标签静态引入的变量，需要通过下面的声明从eslint中忽略掉 */
/* global XML, KindEditor */
import * as QzMethod from '../method.js';
// eslint-disable-next-line
const parse5 = require('parse5');
const defaultAdapters = require('parse5/lib/tree-adapters/default');
// 不想在KindEditor中渲染的节点列表
const nodesToBeDropped = ['script', 'link'];
export default {
  name: 'QzSample',
  props: {
    renderLocationArr: {
      type: Array,
      default: () => []
    },
    title: {
      type: String,
      default: ''
    },
    sampleConf: {
      type: Object,
      default: () => {}
    },
    locationStr: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      typeObj: {
        1: 'JSON',
        2: 'XML',
        3: 'HTML',
        4: '正则匹配',
        5: 'JSONP'
      },
      locationRenderKey: 'keyValues', //每个location data中渲染所用的对象
      // 重构新增
      locationEditorMap: {},
      findMatchMap: {
        labelId: '',
        currentMatch: null
      },
      jsonIndentSize: 4,
      currentTab: '',
      locationArr: [],
      randomId: '',
      sampleThemeName: 'sampleSpecificTheme',
      extractValuesCollects: []
    };
  },
  watch: {
    renderLocationArr() {
      if (this.renderLocationArr.length) {
        this.init();
      }
    },
    extractValuesCollects() {
      if (
        this.extractValuesCollects.length &&
        this.extractValuesCollects.length == this.locationArr.length
      ) {
        const extractValuesCollectsMap = {};
        this.extractValuesCollects.forEach((item) => {
          extractValuesCollectsMap[item.location] = item.extractValues;
        });
        this.$emit(
          'update-sensi-data',
          extractValuesCollectsMap,
          this.locationStr
        );
      }
    }
  },
  methods: {
    handleTabClick(locationObj) {
      const id = `content-formatted__${locationObj.value}__${this.randomId}`;
      // TODO:避免重复渲；判断当前tab有没有初始化内容，没有则调用initEditor；
      if (
        !this.locationEditorMap[id] &&
        (this.sampleConf.isOriginBeTrueStr.includes(locationObj.value) ||
          [8, 9].includes(locationObj.value))
      ) {
        this.initEditor(locationObj);
      }
      this.currentTab = locationObj.value;
    },
    hasOwnProperty(target, key) {
      return Object.prototype.hasOwnProperty.call(target, key);
    },
    getLineKey(str) {
      str = str.trim();
      const splits = str.split('":');
      if (splits.length > 1) {
        str = splits[0];
        str = str.replace(/"/g, '');
      } else {
        str = '';
      }
      str = this.strToRegEx(str, [
        '/',
        '.',
        '$',
        '@',
        '[',
        ']',
        '*',
        ',',
        '?',
        '(',
        ')',
        '-',
        '_',
        '=',
        '<',
        '>',
        '!',
        '&',
        '%',
        '#',
        '{',
        '}',
        ':',
        ';',
        '|',
        '^',
        '~'
      ]);
      return str;
    },
    calculateSpaceCount(value) {
      const indentGuideRe = /\s\S| \t|\t |\s$/;
      var cols = value.search(indentGuideRe);
      return cols;
    },

    filterHtml(msg) {
      if (msg == undefined) {
        return '';
      } else {
        msg = msg.toString();
      }
      var str = msg;
      str = str.replace(/"/g, '&quot;');
      str = str.replace(/</g, '&lt;');
      str = str.replace(/>/g, '&gt;');

      return str;
    },

    strToRegEx(str, needTransformChar) {
      needTransformChar = needTransformChar || [
        '(',
        ')',
        '{',
        '}',
        '[',
        ']',
        '|',
        '+',
        '*',
        '\\',
        '$',
        '.',
        '^',
        '?'
      ];
      let strArr = str.split('');
      strArr = strArr.map((item) => {
        if (needTransformChar.includes(item)) {
          item = '\\' + item;
          return item;
        } else {
          return item;
        }
      });
      return strArr.join('');
    },
    registerNewEditorLang(id, tokenizer) {
      id = id.replace(/[_.]/g, '') + new Date().getTime();
      const langId = 'lang' + id,
        self = this;
      monaco.languages.register({ id: langId });
      monaco.languages.setMonarchTokensProvider(langId, tokenizer);
      monaco.editor.defineTheme(self.sampleThemeName, {
        base: 'vs',
        inherit: false,
        rules: [
          { token: `keyword-highlight`, foreground: 'ff0000' },
          { token: 'brown-text', foreground: 'D10034', fontStyle: 'bold' },
          { token: 'black-text', foreground: '303133' },
          { token: 'blue-text', foreground: '3249D7' }
        ],
        colors: {
          'editor.foreground': '#303133'
        }
      });
      return langId;
    },
    matchLabel(event, labelInfo, locationObj) {
      $(event.target).addClass('active').siblings().removeClass('active');
      if (locationObj.contentType == 'html') {
        locationObj.isFormatted = false;
      } else if (['xml', 'jsonp'].includes(locationObj.contentType)) {
        locationObj.isFormatted = true;
      }
      const editor =
          this.locationEditorMap[
            `content-formatted__${locationObj.value}__${this.randomId}`
          ],
        labelIdKey = location + '_' + labelInfo.id,
        labelValues = labelInfo.values;
      if (this.findMatchMap['labelId'] != labelIdKey) {
        this.findMatchMap['labelId'] = labelIdKey;
        this.findMatchMap['currentMatch'] = null;
      }
      let values = [];
      labelValues.forEach((val) => {
        values.push(this.strToRegEx(val));
      });
      // 按照字符长短降序，避免包含关系的字符查找不到
      values.sort((a, b) => {
        const diff = a.length - b.length;
        if (diff < 0) {
          return 1;
        } else if (diff === 0) {
          return 0;
        } else {
          return -1;
        }
      });

      values = values.join('|');
      // 从正文第一行开始
      let searchStart = {
        column: 1,
        lineNumber: 1
      };
      if (this.findMatchMap['currentMatch']) {
        // 从当前的end position开始查找下一个match
        searchStart = this.findMatchMap['currentMatch'].range.getEndPosition();
      }
      const match = editor.getModel().findNextMatch(values, searchStart, true);
      if (!match) {
        this.$message.warning('没有匹配到数据！');
        return;
      }
      editor.setSelection(match.range);
      editor.revealRangeInCenterIfOutsideViewport(match.range, 0);
      this.findMatchMap.currentMatch = match;
    },
    formatContent() {
      const self = this,
        extractValuesCollectsMap = {};
      for (let i = 0; i < self.locationArr.length; i++) {
        const locationObj = self.locationArr[i],
          fullTextLabelArr = [];
        let extractValues = [];
        for (const k in this.sampleConf.dataLabelMap) {
          const dataLabel = this.sampleConf.dataLabelMap[k];
          if (
            dataLabel.locations.includes(SAMPLE_LOCATION[locationObj.value])
          ) {
            fullTextLabelArr.push(k);
          }
        }
        // 全文提取
        this.sampleConf
          .getFullTextValueFromJava(
            `#content-item__${locationObj.value}__${self.randomId}`,
            locationObj,
            fullTextLabelArr
          )
          .then((res) => {
            extractValues = res.data || [];
            this.extractValuesCollects.push({
              location: locationObj.value,
              extractValues
            });
            // 针对isOriginBeTrueStr包含的和location= 8 或9的面板做文本处理
            if (
              this.sampleConf.isOriginBeTrueStr.includes(locationObj.value) ||
              [8, 9].includes(locationObj.value)
            ) {
              let sensiLabelData = [];
              const editorContent = locationObj.data.originValue || '',
                contentType = editorContent
                  ? QzMethod.judgeStringTypeFunc(editorContent)
                  : 'html';
              sensiLabelData = (extractValues || []).map((item) => {
                return {
                  id: item.dataLabelIds[0],
                  name: this.sampleConf.dataLabelMap[item.dataLabelIds[0]]
                    ?.name,
                  values: item.values,
                  count: item.values.length
                };
              });
              this.$set(locationObj, 'sensiLabelData', sensiLabelData);
              this.$set(locationObj, 'contentType', contentType);
              this.$set(locationObj, 'isFormatted', true);
              // 数据变更初始化时，仅初始化当前展示tab
              if (this.currentTab == locationObj.value) {
                this.initEditor(locationObj);
              }
            }
          });
      }
    },
    // 初始化Monaco Editor面板
    initEditor(locationObj) {
      const self = this,
        matchStrCollects = [],
        locationEditor = self.locationEditorMap[id],
        sensiLabelData = locationObj.sensiLabelData;
      let id = `content-formatted__${locationObj.value}__${self.randomId}`,
        editorContent = locationObj.data.originValue || '',
        matchStrExpr = '',
        contentType = locationObj.contentType;

      let lang;
      (sensiLabelData || []).forEach((item) => {
        (item.values || []).forEach((val) => {
          matchStrCollects.push(self.strToRegEx(val));
        });
      });
      if (matchStrCollects.length > 0) {
        // 按照字符长短降序，避免包含关系的字符查找不到
        matchStrCollects.sort((a, b) => {
          const diff = a.length - b.length;
          if (diff < 0) {
            return 1;
          } else if (diff === 0) {
            return 0;
          } else {
            return -1;
          }
        });
        matchStrExpr = new RegExp(
          self.desensiAtStr(matchStrCollects.join('|'))
        );
      }

      // 处理jsonp 和 xml格式的文本，转为json格式
      if (contentType == 'jsonp') {
        const jsonpStrArr = editorContent.match(
          /^[$_a-zA-Z][$_a-zA-Z0-9]*\((.*)\)$/
        );
        editorContent = jsonpStrArr[1];
      } else if (contentType == 'xml') {
        const xotree = new XML.ObjTree();
        const jsonFromXml = xotree.parseXML(editorContent);
        // 判断xml转换是否报错，有错则重置为html格式
        if (jsonFromXml.html?.body?.parsererror) {
          contentType = 'html';
          locationObj.contentType = 'html';
        } else {
          editorContent = JSON.stringify(jsonFromXml);
        }
      }

      // 使用js_beautify格式化达到json-format的格式效果，需多次转换得到。
      if (['json', 'jsonp', 'xml'].includes(contentType)) {
        editorContent = js_beautify(editorContent, {
          indent_size: self.jsonIndentSize,
          brace_style: 'expand'
        });
        editorContent = js_beautify(editorContent, {
          keep_array_indentation: true
        });
      }
      if (locationEditor) {
        locationEditor.dispose();
      }

      // 敏感数据高亮
      if (matchStrExpr) {
        lang = this.registerNewEditorLang(id, {
          tokenizer: {
            root: [[matchStrExpr, `keyword-highlight`]]
          }
        });
      }
      // req-raw和rsp-raw面板主题设置
      // raw面板editorContent为保证识别字符规范统一，自行拼接editoContent内容
      if ([8, 9].includes(locationObj.value)) {
        const originData = locationObj.data || {},
          brownTextCollects = [];
        if (originData.rawGeneral) {
          editorContent = originData.rawGeneral;
        }
        if (originData.rawHeader) {
          let headerStr = '';
          for (const k in originData.rawHeader) {
            headerStr += `${k}:${originData.rawHeader[k]}\n`;
            brownTextCollects.push([
              new RegExp(
                `${self.strToRegEx(k)}:(?=${self.desensiAtStr(self.strToRegEx(originData.rawHeader[k]))})`
              ),
              'brown-text'
            ]);
          }
          editorContent += `\n${headerStr}\n\n`;
        }
        if (originData.rawBody) {
          editorContent += `${originData.rawBody}`;
        }
        lang = self.registerNewEditorLang(id, {
          tokenizer: {
            root: brownTextCollects
          }
        });
      }

      if (document.getElementById(id)) {
        self.locationEditorMap[id] = monaco.editor.create(
          document.getElementById(id),
          {
            value: editorContent,
            theme: self.sampleThemeName,
            language: lang,
            automaticLayout: true,
            readOnly: true,
            minimap: {
              enabled: false
            },
            wordWrap: 'on',
            disableLayerHinting: true
          }
        );
      }

      // 面板点击事件绑定&获取数据路径回调
      if (['xml', 'jsonp', 'json'].includes(contentType)) {
        $(`#${id}`)
          .unbind('click')
          .click(function (e) {
            const editor = self.locationEditorMap[id],
              pos = editor.getPosition(),
              lines = editor.getModel().getLinesContent(),
              originCount = self.calculateSpaceCount(originLine),
              originLine = lines[lineIndex],
              lineIndex = pos.lineNumber - 1;
            let path = '',
              totalCount = originCount;
            if (originLine.indexOf(':') == -1) {
              return;
            }
            path = self.getLineKey(originLine);
            function calcuPath(lineIndex, originCount, isCollect) {
              let curLine = null,
                count = null,
                collectCount = 0;
              while (lineIndex > -1) {
                curLine = lines[lineIndex];
                count = self.calculateSpaceCount(curLine);
                totalCount = totalCount + count;
                if (
                  isCollect &&
                  originCount == count &&
                  curLine.trim() == '{'
                ) {
                  collectCount++;
                }
                if (originCount - self.jsonIndentSize == count) {
                  if (curLine.trim() == '{') {
                    calcuPath(lineIndex, count, true);
                    return;
                  } else if (
                    (curLine.trim().length > 1 &&
                      ['[', '{'].includes(curLine.substr(-1, 1))) ||
                    curLine.trim() == '['
                  ) {
                    switch (curLine.substr(-1, 1)) {
                      case '[':
                        path =
                          self.getLineKey(curLine) +
                          '[' +
                          (collectCount - 1) +
                          ']' +
                          '.' +
                          path;
                        break;
                      case '{':
                        path = self.getLineKey(curLine) + '.' + path;
                        break;
                      default:
                    }
                    calcuPath(lineIndex, count);
                    return;
                  }
                }
                lineIndex--;
              }
            }
            calcuPath(lineIndex, originCount);
            path = path[0] == '[' ? '$' + path : '$.' + path;

            let originValue = locationObj.data.originValue;
            if (contentType == 'xml') {
              var xotree = new XML.ObjTree();
              originValue = xotree.parseXML(originValue);
              originValue = JSON.stringify(originValue);
            } else if (contentType == 'jsonp') {
              var regexp = /^[$_a-zA-Z][$_a-zA-Z0-9]*\((.*)\)$/;
              var jsonpStrArr = originValue.match(regexp);
              originValue = jsonpStrArr[1];
            }
            self.sampleConf.jsonFormatHtmlClickCb &&
              self.sampleConf.jsonFormatHtmlClickCb(
                path,
                locationObj.value,
                '',
                contentType,
                e
              );
          });
      }

      // 针对html格式内容，默认展示网页形式
      if (
        contentType == 'html' &&
        this.sampleConf.isOriginBeTrueStr.includes(locationObj.value) &&
        !locationObj.isFile
      ) {
        editorContent = editorContent.replace(/\.ico/g, '');
        id = `content-code__${locationObj.value}__${self.randomId}`;
        const documentEl = parse5.parse(editorContent);
        const newData = parse5.serialize(documentEl, {
          treeAdapter: {
            ...defaultAdapters,
            getAttrList(node) {
              const attrs = node.attrs;
              attrs.forEach((item) => {
                // meta标签 refresh类型 会导致页面重定向到其content定义的路径
                if (item.name === 'http-equiv' && item.value === 'refresh') {
                  item.value = '';
                }
                // 将所有的target="_blank"属性改为空白
                if (item.name === 'target' && item.value === '_blank') {
                  item.value = '';
                }
                // 将所有iframe的src属性无效化
                if (node.tagName === 'iframe' && item.name === 'src') {
                  item.value = 'javascript:;';
                }
                if (item.name.startsWith('on')) {
                  item.value = 'javascript:;';
                }
              });

              return attrs;
            },
            getChildNodes(node) {
              return node.childNodes.filter(
                (item) => !nodesToBeDropped.includes(item.tagName)
              );
            }
          }
        });
        var callback = function () {
          var meta = document.createElement('meta'); //禁止发送referrer
          meta.name = 'referrer';
          meta.content = 'never';
          document
            .getElementById(id)
            .getElementsByClassName('ke-edit-iframe')[0]
            .contentWindow.document.body.appendChild(meta);
          document
            .getElementById(id)
            .getElementsByClassName('ke-edit-iframe')[0].contentWindow.onclick =
            function (e) {
              const nodePath = QzMethod.getNodePath(e.target, '');
              // QzMethod.getPathValue中，有使用jQuery.append方法构建节点，导致html重新渲染，进而触发了原始数据originVal中代码执行，此处也需对originVal作脱敏处理
              const pathValueObj = QzMethod.getPathValue({
                // originValue: originalVal,
                originalValue: newData,
                originValueType: 'html',
                path: nodePath
              });
              self.sampleConf.bodyHtmlClickCb &&
                self.sampleConf.bodyHtmlClickCb(
                  nodePath,
                  pathValueObj,
                  e,
                  locationObj.value,
                  id
                );
            };
          $(
            document
              .getElementById(id)
              .getElementsByClassName('ke-edit-iframe')[0].contentWindow
              .document.body
          )
            .find('a')
            .attr('href', 'javascript:;')
            .attr('target', '');

          $(
            document
              .getElementById(id)
              .getElementsByClassName('ke-edit-iframe')[0].contentWindow
              .document.body
          )
            .find(':input')
            .attr('type', 'button');

          $(
            document
              .getElementById(id)
              .getElementsByClassName('ke-edit-iframe')[0].contentWindow
              .document.body
          )
            .find(':input')
            .unbind('click')
            .click(function () {});
        };
        $(`#${id}`).html(
          `<textarea id="defined-textarea-${id}" class="ckeditor" style="display:block;"></textarea>`
        );
        KindEditor.create(`#defined-textarea-${id}`, {
          filterMode: false,
          items: [],
          height: (document.getElementById(id)?.clientHeight || 0) + 'px',
          width: '100%',
          resizeType: 1,
          cssData: 'body { overflow:auto !important; }',
          afterChange: callback,
          afterCreate: function () {
            // beforeSetHtml会引起不规范html的渲染卡死，先设置成null
            this.edit.beforeSetHtml = null;
            this.html(newData);
            this.readonly(true);
            var doc = this.edit.doc;
            KindEditor(doc).click(function () {});
          }
        });
      }
    },
    init() {
      const locationArr = JSON.parse(JSON.stringify(this.renderLocationArr));
      console.log(locationArr, 112);
      this.locationArr = locationArr.filter((item) => {
        return this.locationStr.includes(item.value);
      });
      this.currentTab =
        this.currentTab || (this.locationArr[0] && this.locationArr[0].value);
      // 销毁editor，重置this.locationEditorMap
      for (const k in this.locationEditorMap) {
        this.locationEditorMap[k].dispose();
      }
      this.locationEditorMap = {};
      this.extractValuesCollects = [];
      this.$nextTick((_) => {
        this.formatContent();
      });
      this.findMatchMap = {
        labelId: '',
        currentMatch: null
      };
    },
    handleSearchFormInput(locationObj) {
      const editor =
          this.locationEditorMap[
            `content-formatted__${locationObj.value}__${this.randomId}`
          ],
        searchWord = locationObj._searchWord;
      const matches = editor.getModel().findMatches(searchWord, true);
      this.$set(locationObj, '_foundCount', matches.length);
      this.$set(locationObj, '_matches', matches || []);
      if (matches.length) {
        const currentMatchIndex = 0,
          match = matches[currentMatchIndex];
        editor.getModel().findNextMatch(
          searchWord,
          {
            column: 1,
            lineNumber: 1
          },
          true
        );
        editor.setSelection(match.range);
        editor.revealRangeInCenter(match.range, 0);
        //标记当前editor查询索引
        this.$set(locationObj, '_currentMatchIndex', currentMatchIndex);
      } else {
        const range = new monaco.Range(0, 0, 0, 0);
        this.$set(locationObj, '_currentMatchIndex', null);
        editor.setSelection(range);
      }
    },
    searchMatch(type, locationObj) {
      if (!locationObj._matches) return;
      let currentMatchIndex = locationObj._currentMatchIndex;
      const editor =
        this.locationEditorMap[
          `content-formatted__${locationObj.value}__${this.randomId}`
        ];
      if (type == 'prev') {
        currentMatchIndex--;
        if (currentMatchIndex < 0) {
          // 查询上一个，超出0时，重置为查询数组最后一个
          currentMatchIndex = locationObj._foundCount - 1;
        }
      } else if (type == 'next') {
        currentMatchIndex++;
        if (currentMatchIndex >= locationObj._foundCount) {
          // 查询下一个，超出数组长度时，重置为查询数组第一个
          currentMatchIndex = 0;
        }
      }
      const currentMatch = locationObj._matches[currentMatchIndex];
      editor.setSelection(currentMatch.range);
      editor.revealRangeInCenter(currentMatch.range, 0);
      locationObj._currentMatchIndex = currentMatchIndex;
    },
    // 在Monaco Editor 解析中，会对@字符做特殊处理，需对做@脱敏处理。
    desensiAtStr(str) {
      if (/@(\w+)/g.test(str)) {
        str = str.replace(/@/g, '\x01');
      }
      return str;
    },
    // 针对xml和jsonp的原文做展示
    formatOriginCode(locationObj) {
      if (['xml', 'jsonp'].includes(locationObj.contentType)) {
        const id = `content-code__${locationObj.value}__${this.randomId}`;
        if (!this.locationEditorMap[id]) {
          this.locationEditorMap[id] = monaco.editor.create(
            document.getElementById(id),
            {
              value: locationObj.data.originValue,
              automaticLayout: true,
              readOnly: true,
              minimap: {
                enabled: false
              },
              wordWrap: 'on',
              disableLayerHinting: true
            }
          );
        } else {
          this.locationEditorMap[id]
            .getModel()
            .setValue(locationObj.data.originValue);
        }
      }
      locationObj.isFormatted = !locationObj.isFormatted;
    }
  },
  mounted() {
    this.randomId = new Date().getTime();
  },
  beforeDestroy() {
    for (const k in this.locationEditorMap) {
      this.locationEditorMap[k].dispose();
    }
  }
};
</script>
