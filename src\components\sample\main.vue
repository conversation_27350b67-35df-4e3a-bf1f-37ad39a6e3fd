<template>
  <div class="qz-sample-main sample-wrapper sample-container">
    <!-- 样例主体 -->
    <div
      class="sample-container__main mb20"
      v-show="currentModules.includes('sample')"
    >
      <el-row>
        <el-col class="left" :span="12">
          <sample-content
            ref="req-sample"
            title="请求"
            :location-str="requestLocationStr"
            :render-location-arr="renderLocationArr"
            :sample-conf="{
              getFullTextValueFromJava,
              columns,
              dataLabelMap,
              showLocationTabToggleBtnStr,
              isOriginBeTrueStr,
              jsonFormatHtmlClickCb,
              bodyHtmlClickCb
            }"
            @update-sensi-data="updateSensiData"
          ></sample-content>
        </el-col>
        <el-col class="right" :span="12">
          <sample-content
            ref="rsp-sample"
            title="返回"
            :location-str="responseLocationStr"
            :render-location-arr="renderLocationArr"
            :sample-conf="{
              getFullTextValueFromJava,
              columns,
              dataLabelMap,
              showLocationTabToggleBtnStr,
              isOriginBeTrueStr,
              jsonFormatHtmlClickCb,
              bodyHtmlClickCb
            }"
            @update-sensi-data="updateSensiData"
          ></sample-content>
        </el-col>
      </el-row>
    </div>
    <!-- 五元组-->
    <div
      class="sample-container__netinfo"
      v-show="currentModules.includes('net')"
    >
      <div class="left">
        <div class="title">五元组</div>
        <div class="flex">
          <div class="column">
            <div class="item">源IP（网关IP）</div>
            <div class="item">{{ netInfo.srcIp }}</div>
          </div>
          <div class="column">
            <div class="item">源端口</div>
            <div class="item">{{ netInfo.srcPort }}</div>
          </div>
          <div class="column">
            <div class="item">目的IP</div>
            <div class="item">{{ netInfo.dstIp }}</div>
          </div>
          <div class="column">
            <div class="item">目的端口</div>
            <div class="item">{{ netInfo.dstPort }}</div>
          </div>
          <div class="column">
            <div class="item">协议</div>
            <div class="item">{{ netInfo.tcp }}</div>
          </div>
        </div>
      </div>
      <div class="right">
        <div class="title">解析IP</div>
        <div class="item">真实源IP</div>
        <div class="item">{{ netInfo.ip }}</div>
      </div>
    </div>
    <!-- 文件列表 -->
    <div
      class="sample-container__filelist mb20"
      v-show="currentModules.includes('file') && fileObj.list.length"
    >
      <div class="row" v-for="(file, i) in fileObj.list" :key="i">
        <div class="left">文件名：{{ decodeURI(file.fileName) }}</div>
        <div class="right">
          <span>文件类型：{{ file.fileType }}</span>
          <span>文件大小：{{ file.fileLen }}字节</span>
          <el-popover
            popper-class="download-file-tip"
            placement="top"
            width="300"
            trigger="manual"
            v-model="file._showTip"
          >
            <div class="popover-tip">
              <i
                class="el-icon-warning mr5"
                style="color: #f7b500; font-size: 20px"
              ></i>
              <span>文件被截断，下载后可能无法打开原始文件，仍需下载？</span>
            </div>
            <div style="text-align: right; margin: 0">
              <el-button size="mini" type="text" @click="file._showTip = false"
                >取消</el-button
              >
              <el-button
                type="primary"
                size="mini"
                @click="
                  () => {
                    downloadFile(file);
                    file._showTip = false;
                  }
                "
                >确定</el-button
              >
            </div>
            <span slot="reference" @click="getFileInfo(file)" class="action-btn"
              >下载</span
            >
          </el-popover>
        </div>
      </div>
      <div class="pagination" v-if="fileObj.totalCount > fileObj.limit">
        共<span style="color: #4a97eb">{{ fileObj.totalCount }}</span
        >条
        <el-pagination
          ref="pagination"
          background
          :current-page="fileObj.page"
          :page-size="fileObj.limit"
          :total="fileObj.totalCount"
          layout="prev, pager, next"
          @current-change="changeFilePage"
        ></el-pagination>
      </div>
    </div>
    <!--fullTextLabelValueHtml 底部 -->
    <div
      class="sample-container__sensidata"
      v-show="currentModules.includes('sensi')"
      v-if="showFullTextLocationLabelKeyValueBoolean"
    >
      <table class="sample-table" style="text-align: left">
        <colgroup>
          <col width="80" />
          <col width="150" />
        </colgroup>
        <thead>
          <tr class="title-tr">
            <th colspan="3">敏感数据</th>
          </tr>
          <tr>
            <th>位置</th>
            <th>标签</th>
            <th class="align-center">值（去重）</th>
          </tr>
        </thead>
        <tbody>
          <tr
            v-for="(fullTextData, i) in reqfullTextSensiDataList"
            :key="'req' + fullTextData.location + i"
          >
            <th :rowspan="reqfullTextSensiDataList.length" v-if="i == 0">
              请求
            </th>
            <td>{{ fullTextData.labelName }}({{ fullTextData.count }})</td>
            <td>{{ fullTextData.labels.join('，') }}</td>
          </tr>
          <tr
            v-for="(fullTextData, i) in rspfullTextSensiDataList"
            :key="'rsp' + fullTextData.location + i"
          >
            <th :rowspan="rspfullTextSensiDataList.length" v-if="i == 0">
              返回
            </th>
            <td>{{ fullTextData.labelName }}({{ fullTextData.count }})</td>
            <td>{{ fullTextData.labels.join('，') }}</td>
          </tr>
          <tr
            v-if="
              !reqfullTextSensiDataList.length &&
              !rspfullTextSensiDataList.length
            "
          >
            <th colspan="3" class="align-center">暂无数据</th>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>
<script>
import sampleContent from './components/sample-content';
import { SAMPLE_LOCATION } from '@/constant/common-constants.js';

/* 通过script标签静态引入的变量，需要通过下面的声明从eslint中忽略掉 */
import '@babel/polyfill';
import { Message } from 'element-ui';
import * as QzMethod from './method.js';
// eslint-disable-next-line

export default {
  name: 'QzSample',
  components: {
    sampleContent
  },
  props: {
    setting: {
      type: Object,
      default: function () {
        return {};
      }
    },
    activeTab: {
      //当前显示的面板，与relatedModules关联显示当前面板需展示的模块
      type: String,
      default: ''
    },
    relatedModules: {
      //关联显示当前面板需展示的模块，sample-样例主体，net-五元组，file-文件清单，sensi-敏感数据
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  computed: {
    currentModules() {
      let modules = [];
      modules = this.relatedModules[this.activeTab] || [];
      return modules;
    }
  },
  data() {
    return {
      element: '',
      locationSet: new Set([1, 3, 7, 9, 5, 4, 2, 6, 8]),
      locationObj: {
        1: 'BODY',
        3: 'SET-COOKIE',
        7: 'RSP-HEADER',
        9: 'RSP-RAW',
        5: 'POST',
        4: 'GET',
        2: 'COOKIE',
        6: 'REQ-HEADER',
        8: 'REQ-RAW'
      },
      //193版本response和request都有body，header。。
      location_label_193: {
        1: 'Body',
        3: 'Set-Cookie',
        7: 'Header',
        9: 'Rsp-Raw',
        5: 'Body',
        4: 'Param',
        2: 'Cookie',
        6: 'Header',
        8: 'Req-Raw'
      },
      requestLocationStr: '2,4,5,6,8',
      responseLocationStr: '1,3,7,9',

      columns: [
        {
          name: 'key',
          label: '字段名',
          width: '35%'
        },
        {
          name: 'value',
          label: '字段值',
          width: '35%'
        },
        {
          name: 'location',
          label: '位置',
          width: '15%'
        },
        {
          name: 'type',
          label: '类型',
          width: '15%'
        }
      ],
      params: {
        page: 1
      },
      responseData: {},
      renderLocationArr: [], //内部渲染所用数组，由locationObj和请求返回的responseData组合
      isOriginBeTrueStr: '',
      showLocationTabToggleBtnStr: '', //tab内部可以切换格式化内容和原始内容

      fullTextLocationLabelKeyValueMap: new Map(), //保存body，get，post的全文提取的key-value值
      showFullTextLocationLabelKeyValueBoolean: true, //是否展示全文提取的key-value值

      isFile: false, //展示文件样例，190版本是请求接口后根据response中isFile来判断。。。但根据标签筛不到样例，只能通过外部判断后传入

      // 重构新增
      netInfo: {},
      getCurrentSampleFunc: null,
      getFullTextValueFromJava: null,

      fileDownloadedUrl: '',
      reqfullTextSensiDataList: [],
      rspfullTextSensiDataList: [],
      dataLabelMap: {},
      jsonFormatHtmlClickCb: null,
      bodyHtmlClickCb: null,
      fileList: [],
      fileObj: {
        list: [],
        limit: 5,
        page: 1,
        totalCount: 0
      }
    };
  },
  watch: {
    fileList() {
      this.fileObj.page = 1;
      this.fileObj.totalCount = this.fileList.length;
      this.fileObj.list = this.fileList.slice(
        (this.fileObj.page - 1) * this.fileObj.limit,
        this.fileObj.page * this.fileObj.limit
      );
    }
  },
  methods: {
    setConf(obj) {
      this.element = obj['element'];
      if (this.element == undefined || !this.element || this.element == '') {
        this.errorMsg = 'element is empty';
        return false;
      }

      if (obj['locationObj'] != undefined) {
        this.locationObj = obj['locationObj'];
      }
      if (obj['params'] != undefined) {
        this.params = obj['params'];
      }

      if (obj['columns'] != undefined) {
        this.columns = obj['columns'];
      }

      if (obj['afterLoad'] != undefined) {
        this.afterLoad = obj['afterLoad'];
      }
      if (obj['afterFileLoad'] != undefined) {
        this.afterFileLoad = obj['afterFileLoad'];
      }

      if (obj['bodyHtmlClickCb'] != undefined) {
        this.bodyHtmlClickCb = obj['bodyHtmlClickCb'];
      }

      if (obj['jsonFormatHtmlClickCb'] != undefined) {
        this.jsonFormatHtmlClickCb = obj['jsonFormatHtmlClickCb'];
      }
      if (obj['isOriginBeTrueStr'] != undefined) {
        this.isOriginBeTrueStr = obj['isOriginBeTrueStr'];
      }

      if (obj['showLocationTabToggleBtnStr'] != undefined) {
        this.showLocationTabToggleBtnStr = obj['showLocationTabToggleBtnStr'];
      }

      if (obj['isFile'] != undefined) {
        this.isFile = obj['isFile'];
      }
      if (obj['showFullTextLocationLabelKeyValueBoolean'] != undefined) {
        this.showFullTextLocationLabelKeyValueBoolean =
          obj['showFullTextLocationLabelKeyValueBoolean'];
      }

      if (obj['getCurrentSampleFunc'] != undefined) {
        this.getCurrentSampleFunc = obj['getCurrentSampleFunc'];
      }

      if (obj['getFullTextValueFromJava'] != undefined) {
        this.getFullTextValueFromJava = obj['getFullTextValueFromJava'];
      }
      // if (obj['getDataLabelListFunc'] != undefined) {
      //   this.getDataLabelListFunc = obj['getDataLabelListFunc'];
      // }

      if (obj['fileDownloadedUrl'] != undefined) {
        this.fileDownloadedUrl = obj['fileDownloadedUrl'];
      }
      if (obj['dataLabelMap'] != undefined) {
        this.dataLabelMap = obj['dataLabelMap'];
      }
      return true;
    },
    figureRenderLocationArr() {
      const self = this,
        renderLocationArr = [];
      let fileList = [];
      for (const p of this.locationSet.keys()) {
        const singleLocationObj = {
          value: p,
          label: this.locationObj[p],
          isFile: 0,
          label_193: this.location_label_193[p],
          file: {},
          _searchWord: ''
        };

        if (this.responseData.samples && this.responseData.samples.length > 0) {
          for (let i = 0; i < this.responseData.samples.length; i++) {
            if (this.responseData.samples[i].location == p) {
              singleLocationObj.data = this.responseData.samples[i];
              if (
                this.isOriginBeTrueStr &&
                this.isOriginBeTrueStr.indexOf(p) != -1
              ) {
                singleLocationObj.data.isOrigin = true;
              }
              break;
            }
          }
        }
        if ((p == 1 || p == 5) && (self.responseData.isFile || self.isFile)) {
          singleLocationObj.isFile = 1;
          singleLocationObj.file = self.responseData.file;
          // 判断文件数据类型
          if (Array.isArray(self.responseData.file)) {
            fileList = self.responseData.file;
          } else {
            fileList = [self.responseData.file];
          }
          if (singleLocationObj.data == undefined) singleLocationObj.data = {};
        }
        if (!Object.prototype.hasOwnProperty.call(singleLocationObj, 'data'))
          singleLocationObj.data = {};
        renderLocationArr.push(singleLocationObj);
      }
      this.fileList = fileList;
      this.renderLocationArr = renderLocationArr;
    },

    render(obj) {
      const self = this;
      if (!self.setConf(obj)) {
        Message.error(self.errorMsg ? self.errorMsg : '样例渲染错误');
        return false;
      }
      self.reloadSample();
    },

    reloadSample() {
      const self = this;
      self.fullTextLocationLabelKeyValueMap.clear();
      self
        .getCurrentSampleFunc(document.querySelector(self.element))
        .then((res) => {
          if (!res.data.length) {
            res.data[0] = { samples: [], totalCount: 0 };
            for (const location in self.locationObj) {
              res.data[0].samples.push({
                location: location,
                isOrigin: false,
                originValue: '{}'
              });
            }
          }
          self.responseData = res.data[0];
          this.netInfo = this.responseData.net || {};
          this.netInfo.ip = this.responseData.ip;
          this.netInfo.tcp = 'TCP';
          if (self.afterLoad != undefined) {
            self.afterLoad();
          }
          self.figureRenderLocationArr();
        });
    },

    renderFullTextKeyValueHtml(locationStr) {
      const self = this,
        fullTextSensiDataList = [];
      if (
        self.fullTextLocationLabelKeyValueMap.size != 0 &&
        self.showFullTextLocationLabelKeyValueBoolean
      ) {
        const dataMap = {};
        for (const [
          location,
          locationMap
        ] of self.fullTextLocationLabelKeyValueMap.entries()) {
          if (locationStr.includes(Number(location))) {
            for (const [labelKey, labelValue] of locationMap.entries()) {
              const labelValueList = [];
              for (const item of labelValue.keys()) {
                labelValueList.push(item);
              }
              const count = labelValueList.length;
              // 结尾最后一个拼接地址信息
              const _labelValuesStr =
                labelValueList.join(
                  `(${self.location_label_193[location]})，`
                ) + `(${self.location_label_193[location]})`;
              if (!dataMap[labelKey]) {
                dataMap[labelKey] = {
                  labelName: self.dataLabelMap[labelKey].name,
                  labelId: labelKey,
                  labels: [],
                  count: 0
                };
              }
              dataMap[labelKey]['count'] = dataMap[labelKey]['count'] + count;
              dataMap[labelKey]['labels'].push(_labelValuesStr);
            }
          }
        }
        for (const k in dataMap) {
          fullTextSensiDataList.push(dataMap[k]);
        }
      }
      return fullTextSensiDataList;
    },

    insertFullTextLocationLabelKeyValueMap(arr, location) {
      const self = this;
      for (let i = 0; i < arr.length; i++) {
        const item = arr[i];
        if (item.dataLabelIds && item.dataLabelIds.length > 0) {
          const labelId = item.dataLabelIds[0];
          const labelValueArr = item.values;
          if (self.fullTextLocationLabelKeyValueMap.has(Number(location))) {
            if (
              self.fullTextLocationLabelKeyValueMap
                .get(Number(location))
                .has(labelId)
            ) {
              if (labelValueArr && labelValueArr.length > 0) {
                labelValueArr.forEach((cell) => {
                  self.fullTextLocationLabelKeyValueMap
                    .get(Number(location))
                    .get(labelId)
                    .add(cell);
                });
              }
            } else {
              self.fullTextLocationLabelKeyValueMap
                .get(Number(location))
                .set(labelId, new Set(labelValueArr || []));
            }
          } else {
            self.fullTextLocationLabelKeyValueMap.set(
              Number(location),
              new Map([[labelId, new Set(labelValueArr || [])]])
            );
          }
        }
      }
    },

    downloadFile(fileInfo) {
      // FILE_DOWNLOAD
      const url =
        this.fileDownloadedUrl +
        '?fileId=' +
        fileInfo.fileId +
        '&fileName=' +
        fileInfo.fileName +
        '&fileType=' +
        fileInfo.fileType;
      window.open(url);
    },
    getFileInfo(fileInfo) {
      // 文件大于100M时提示
      if (fileInfo.fileLen > 104857600) {
        this.$set(fileInfo, '_showTip', true);
      } else {
        this.downloadFile(fileInfo);
      }
    },
    // 重构新增
    updateSensiData(obj, locationStr) {
      for (const k in obj) {
        this.insertFullTextLocationLabelKeyValueMap(obj[k], k);
      }
      let type = '';
      const fullTextSensiDataList =
        this.renderFullTextKeyValueHtml(locationStr);
      if (locationStr == this.requestLocationStr) {
        this.reqfullTextSensiDataList = fullTextSensiDataList;
      } else if (locationStr == this.responseLocationStr) {
        type = 'rsp';
        this.rspfullTextSensiDataList = fullTextSensiDataList;
      }
    },
    changeFilePage(page) {
      this.fileObj.page = page;
      this.renderChart();
    }
  }
};
</script>
<style lang="less">
@import './assets/sample.less';
.download-file-tip {
  height: 120px;
  // padding: 0px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  .popover-tip {
    margin-top: 20px;
    display: flex;
    span {
      margin-left: 5px;
      font-size: 14px;
      color: #2e3444;
    }
  }
}
.qz-sample-table-wrap {
  z-index: 3001 !important;
  & .el-table__column-filter-trigger i {
    font-size: 14px;
  }
}
.el-table-filter {
  z-index: 3002 !important;
  & .el-table-filter__list {
    max-height: 500px;
    overflow: auto;
  }
}
.el-tooltip__popper.is-dark {
  z-index: 3002 !important;
}
.el-select-dropdown {
  z-index: 3002 !important;
}
</style>
