/* 通过script标签静态引入的变量，需要通过下面的声明从eslint中忽略掉 */
/* global jsonPath, XML */
export const hasOwnProperty = (target, key) => {
  return Object.prototype.hasOwnProperty.call(target, key);
};
//因为html是xml的子集，需要判断类似 <section><p>1</p></section> 这种是html而不是xml。主要通过标签识别
export const figurateHtmlAndXmlDiff = function (str) {
  const HTML_LABEL = [
    '!DOCTYPE',
    'a',
    'abbr',
    'acronym',
    'address',
    'applet',
    'area',
    'article',
    'aside',
    'audio',
    'b',
    'base',
    'basefont',
    'bdi',
    'bdo',
    'big',
    'blockquote',
    'body',
    'br',
    'button',
    'canvas',
    'caption',
    'center',
    'cite',
    'code',
    'col',
    'colgroup',
    'command',
    'datalist',
    'dd',
    'del',
    'details',
    'dir',
    'div',
    'dfn',
    'dialog',
    'dl',
    'dt',
    'em',
    'embed',
    'fieldset',
    'figcaption',
    'figure',
    'font',
    'footer',
    'form',
    'frame',
    'frameset',
    'h1',
    'h2',
    'h3',
    'h4',
    'h5',
    'h6',
    'head',
    'header',
    'hr',
    'html',
    'i',
    'iframe',
    'img',
    'input',
    'ins',
    'isindex',
    'kbd',
    'keygen',
    'label',
    'legend',
    'li',
    'link',
    'map',
    'mark',
    'menu',
    'meta',
    'meter',
    'nav',
    'noframes',
    'noscript',
    'object',
    'ol',
    'optgroup',
    'option',
    'output',
    'p',
    'param',
    'pre',
    'progress',
    'q',
    'rp',
    'rt',
    'ruby',
    's',
    'samp',
    'script',
    'section',
    'select',
    'smail',
    'source',
    'span',
    'strike',
    'strong',
    'style',
    'sub',
    'summary',
    'sup',
    'table',
    'tbody',
    'td',
    'textarea',
    'tfont',
    'th',
    'thead',
    'time',
    'title',
    'tr',
    'track',
    'tt',
    'u',
    'ul',
    'var',
    'video',
    'wbr',
    'xmp'
  ];

  const label_start_index =
    (str.match(/(?=<)/) && str.match(/(?=<)/)['index']) || 0;
  const label_end_index =
    (str.match(/(?=\s)|(?=>)/) && str.match(/(?=\s)|(?=>)/)['index']) ||
    str.length - 1;

  if (label_end_index > label_start_index) {
    const labelStr = str.slice(label_start_index + 1, label_end_index);
    const labelIndex = HTML_LABEL.findIndex(function (value) {
      return value == labelStr;
    });
    if (labelIndex != -1) {
      return 'html';
    } else {
      return 'xml';
    }
  } else {
    return 'html';
  }
};
//判断返回的数据是 json，xml还是html
export const judgeStringTypeFunc = function (originalData) {
  //判断是否是json字符串
  function _isJSON(str) {
    if (typeof str == 'string') {
      try {
        var obj = JSON.parse(str);
        if (typeof obj == 'object' && obj) {
          return true;
        } else {
          return false;
        }
      } catch (e) {
        return false;
      }
    }
  }
  if (originalData) {
    if (_isJSON(originalData)) {
      return 'json';
    } else if (
      originalData.indexOf('?xml') != -1 &&
      originalData.indexOf('xml') < 10
    ) {
      //xml文档类型只能通过查询 ?xml 有就基本判断是xml文档
      return 'xml';
    } else {
      var regexp = /^[$_a-zA-Z][$_a-zA-Z0-9]*\((.*)\)$/;
      var jsonpStrArr = originalData.match(regexp);

      if (
        jsonpStrArr &&
        jsonpStrArr.length > 0 &&
        jsonpStrArr[1] &&
        jsonpStrArr[1] != ''
      ) {
        var jsonpString = jsonpStrArr[1];
        if (_isJSON(jsonpString)) {
          return 'jsonp';
        } else {
          return 'html';
        }
      } else {
        var xmlTree = new XML.ObjTree();
        try {
          var xmlFormatObj = xmlTree.parseXML(originalData);
          var xmlFormatObjStr = JSON.stringify(xmlFormatObj);
          if (
            (xmlFormatObjStr.indexOf('html') != -1 &&
              xmlFormatObjStr.indexOf('html') < 5) ||
            xmlFormatObjStr.indexOf('parsererror') != -1
          ) {
            return 'html';
          } else {
            return figurateHtmlAndXmlDiff(originalData);
          }
        } catch (e) {
          return 'html';
        }
      }
    }
  } else {
    return 'html';
  }
};
function getNodeIndex(element) {
  var index = 0,
    siblings = element.parentNode.children || [];
  if (siblings.length > 0) {
    for (var i = 0; i < siblings.length; i++) {
      var sibling = siblings[i];
      if (sibling == element) {
        var indexSelector = ':nth-of-type(' + (index + 1) + ')';
        return siblings.length > 1 ? indexSelector : ''; //如果同级只有element自己，不用加索引，在多兄弟姐妹情况才加
      }
      //在计算同类型元素节点索引时，ke-script为编辑器处理script标签时特定类名
      else if (
        sibling.nodeType == 1 &&
        sibling.localName == element.localName &&
        sibling.className.indexOf('ke-script') == -1
      ) {
        index++;
      }
    }
  } else {
    return '';
  }
}

export const getNodePath = function (node, nodePath) {
  var selectorpath = '',
    curNodePath = '',
    connector = '';

  if (node.localName && node.localName != 'html' && node.localName != 'body') {
    if (node.localName != 'html') {
      connector = '>'; //html元素前不加">"，加”>“是为了排除选中隔代相同元素的情况

      var nodeId = hasOwnProperty(node.attributes, 'id')
        ? node.attributes['id']['value']
        : '';
      if (nodeId && nodeId.indexOf('ke-') == -1) {
        selectorpath = '#' + nodeId;
      }
      if (node.classList && node.classList.length > 0) {
        for (var k = 0; k < node.classList.length; k++) {
          if (node.classList[k].indexOf('ke-') == -1) {
            selectorpath += '.' + node.classList[k];
          }
        }
      }
    }
    curNodePath =
      connector + node.localName + getNodeIndex(node) + selectorpath; //加了索引和">"
    nodePath = curNodePath + nodePath;
    if (
      node.parentNode.localName != 'body' &&
      node.parentNode.localName != 'html'
    ) {
      return getNodePath(node.parentNode, nodePath);
    } else {
      nodePath = 'html>body' + nodePath;
    }
  } else {
    nodePath = 'html>body' + nodePath;
  }
  return nodePath;
};
export const getPathValue = function (pathObj) {
  let pathValue = [];
  const pathNode = []; //存放所选路径的html节点，如果配置的是attrName 就从节点中取attrName
  const localNameSet = new Set();

  let originValue = pathObj.originValue;
  const originValueType = pathObj.originValueType;
  let pathStr = pathObj.path;

  if (originValueType == 'html' || originValueType == 'js') {
    if (originValueType == 'js') {
      const pathObj = JSON.parse(pathStr);
      const scriptIndex = pathObj.path;
      const keyword = pathObj.keyword;
      const keywordIndex = pathObj.index;

      const scriptStrArr = originValue.match(/<script[\s\S]*?<\/script>/g);
      let scriptStr = scriptStrArr[parseInt(scriptIndex)];

      scriptStr = scriptStr.match(/>[\s\S]*?<\/script>/)[0];
      scriptStr = scriptStr.slice(1, scriptStr.length - 9);

      const keywordStrArr = scriptStr.split(keyword);
      let keywordStr = keywordStrArr[keywordIndex + 1].trim();

      keywordStr = keywordStr.replace(/\\"/g, 'QZKJ');

      let startIcon = '';
      let startIndex = 0;
      let endIndex = 0;
      let leftIconNum = 0;
      let rightIconNum = 0;
      for (let i = 0; i < keywordStr.length; i++) {
        if (startIndex == 0) {
          if (
            keywordStr[i] == "'" ||
            keywordStr[i] == '"' ||
            keywordStr[i] == '{' ||
            keywordStr[i] == '['
          ) {
            startIndex = i;
            startIcon = keywordStr[i];
            leftIconNum += 1;
          } else if (/[0-9]/.test(keywordStr[i])) {
            startIndex = i;
            startIcon = 'number';
          }
        } else {
          if (startIcon == '"' && keywordStr[i] == '"') {
            endIndex = i;
            break;
          } else if (startIcon == "'" && keywordStr[i] == "'") {
            endIndex = i;
            break;
          } else if (startIcon == '{') {
            if (keywordStr[i] == '{') {
              leftIconNum += 1;
            } else if (keywordStr[i] == '}') {
              rightIconNum += 1;
              if (rightIconNum == leftIconNum) {
                endIndex = i;
                break;
              }
            }
          } else if (startIcon == '[') {
            if (keywordStr[i] == '[') {
              leftIconNum += 1;
            } else if (keywordStr[i] == ']') {
              rightIconNum += 1;
              if (rightIconNum == leftIconNum) {
                endIndex = i;
                break;
              }
            }
          } else if (startIcon == 'number' && !/[0-9]/.test(keywordStr[i])) {
            endIndex = i - 1;
            break;
          }
        }
      }

      let finalStr = '';
      if (startIcon == '"' || startIcon == "'") {
        finalStr = keywordStr.slice(startIndex + 1, endIndex);
      } else {
        finalStr = keywordStr.slice(startIndex, endIndex + 1);
      }
      finalStr = finalStr.replace(/QZKJ/g, '"'); //截取的字符串中原来转义的双引号提出来后就不需要转义符了

      return {
        pathValue: finalStr
      };
    } else {
      var pathTempArr = pathStr.split('>');
      if (pathTempArr[0].toLowerCase() == 'html') pathTempArr.splice(0, 1);
      if (pathTempArr[0].toLowerCase() == 'head') pathTempArr.splice(0, 1);
      if (pathTempArr[0].toLowerCase() == 'body') pathTempArr.splice(0, 1);
      pathStr = pathTempArr.join('>');

      var selectorDom = $($.parseHTML(`<div>${originValue}<div>`));

      if (selectorDom.find(pathStr).length > 0) {
        for (let i = 0; i < selectorDom.find(pathStr).length; i++) {
          const nodeLocalName = selectorDom
            .find(pathStr)
            .eq(i)
            .get(0).localName;
          localNameSet.add(nodeLocalName);

          if (selectorDom.find(pathStr).eq(i).text().trim() != '') {
            pathValue.push(selectorDom.find(pathStr).eq(i).text().trim());
          } else {
            pathValue.push(
              selectorDom.find(pathStr).eq(i).get(0).outerHTML.trim()
            );
          }

          pathNode.push(
            selectorDom.find(pathStr).eq(i).get(0).outerHTML.trim()
          );
        }
      }
    }
  } else {
    if (pathStr[0] != '$') {
      //jsonpath 可能没有以$开头，需要补上
      pathStr = '$' + pathStr;
    }

    if (originValueType == 'xml') {
      var xotree = new XML.ObjTree();
      originValue = xotree.parseXML(originValue);
      originValue = JSON.stringify(originValue);
    } else if (originValueType == 'jsonp') {
      var regexp = /^[$_a-zA-Z][$_a-zA-Z0-9]*\((.*)\)$/;
      var jsonpStrArr = originValue.match(regexp);
      originValue = jsonpStrArr[1];
    }

    pathValue = jsonPath(JSON.parse(originValue), pathStr)
      ? jsonPath(JSON.parse(originValue), pathStr)
      : [];
    pathValue = pathValue.map((item) => {
      if (typeof item == 'object') {
        item = JSON.stringify(item);
      }
      return item;
    });
  }

  return {
    pathValue: pathValue,
    localNameSet: localNameSet,
    pathNode: pathNode
  };
};
