<template>
  <div class="search-section">
    <el-form :model="filterObj" size="small">
      <el-form-item
        v-for="schema of schemas"
        :key="schema.key"
        :label="schema.label"
      >
        <el-input
          v-if="schema.type == 'text' || schema.type == 'number'"
          :type="schema.type"
          v-model.trim="filterObj[schema.key]"
          :placeholder="schema.placeholder || '请输入'"
          clearable
          @keydown.enter.native.stop="search"
          @clear="search"
        ></el-input>
        <el-select
          v-model="filterObj[schema.key]"
          v-if="schema.type.indexOf('selection') > -1 && !schema.filterFn"
          size="small"
          clearable
          filterable
          :multiple="schema.type.indexOf('multi') > -1"
          @keydown.enter.native="search"
          @clear="search"
          :placeholder="schema.placeholder || '请输入'"
        >
          <el-option
            v-for="item in Object.keys(schema.options)"
            :label="schema.options[item]"
            :value="item"
            :key="item"
          ></el-option>
        </el-select>
        <el-select
          v-model="filterObj[schema.key]"
          v-if="schema.type.indexOf('selection') > -1 && !!schema.filterFn"
          size="small"
          clearable
          filterable
          :multiple="schema.type.indexOf('multi') > -1"
          :filterable="!!schema.filterFn"
          :remote="!!schema.filterFn"
          :remote-method="schema.filterFn"
          @keydown.enter.native="search"
          @clear="search"
          :placeholder="schema.placeholder || '请输入'"
        >
          <el-option
            v-for="item in Object.keys(schema.options)"
            :label="schema.options[item]"
            :value="schema.options[item]"
            :key="schema.options[item]"
          ></el-option>
        </el-select>
        <el-date-picker
          size="small"
          v-if="schema.type.indexOf('date-picker') > -1"
          v-model="filterObj[schema.key]"
          type="datetimerange"
          :picker-options="DATE_PICKER_OPTIONS"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="['00:00:00', '23:59:00']"
          :start-placeholder="schema.placeholder[0] || '开始时间'"
          :end-placeholder="schema.placeholder[1] || '结束时间'"
          :value-format="schema.valueFormat || 'timestamp'"
          @keydown.enter.native="search"
          @clear="search"
        >
        </el-date-picker>
        <el-date-picker
          size="small"
          v-if="schema.type.indexOf('date-single') > -1"
          v-model="filterObj[schema.key]"
          type="date"
          value-format="timestamp"
          @keydown.enter.native="search"
          placeholder="选择日期"
          @clear="search"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button size="small" @click="reset">重置</el-button>
        <el-button size="small" @click="search" type="primary">查询</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
import { DATE_PICKER_OPTIONS } from '@/constant/common-constants';

export default {
  props: {
    // 筛选项数据
    schemas: {
      type: Array,
      required: true,
      default: []
    }
  },
  data() {
    const filterObj = {};
    this.schemas.forEach((schema) => {
      if (schema.type.indexOf('multi') > -1) {
        filterObj[schema.key] = [];
      } else {
        filterObj[schema.key] = '';
      }
    });
    return {
      filterObj,
      DATE_PICKER_OPTIONS
    };
  },
  methods: {
    reset() {
      this.schemas.forEach((schema) => {
        if (schema.type.indexOf('multi') > -1 || schema.type == 'date-picker') {
          this.filterObj[schema.key] = this.filterObj[schema.defaultVal] || [];
        } else {
          this.filterObj[schema.key] = this.filterObj[schema.defaultVal] || '';
        }
      });
      this.$emit('search', this.filterObj);
    },

    search() {
      this.$emit('search', this.filterObj);
    },

    // 从外部进入调用搜索
    manualSearch(params = {}) {
      Object.keys(params).forEach((key) => {
        this.filterObj[key] = params[key];
      });
      this.$emit('search', this.filterObj);
    }
  }
};
</script>
<style lang="less" scoped>
.search-section {
  .el-form {
    margin: 0;
    display: flex;
    flex-wrap: wrap;
    .el-form-item {
      margin-bottom: 10px;
      margin-right: 15px;
    }
    .el-input,
    .el-select {
      width: 100%;
    }
  }
}
</style>
