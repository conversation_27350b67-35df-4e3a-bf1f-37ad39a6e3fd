<template>
  <el-drawer
    v-on="$listeners"
    :visible.sync="innerVisible"
    :with-header="false"
    :size="size"
    direction="rtl"
  >
    <div v-loading="loading" ref="container" class="custom-container">
      <div class="drawer-header">
        <div class="drawer-title">{{ title }}</div>
        <div class="drawer-close" @click="closeDrawer">&times;</div>
      </div>
      <div ref="body" class="drawer-body">
        <div class="drawer-panel" style="background-color: #f5f5f5">
          <!-- 上半部分的内容 -->
          <slot name="topPart"></slot>
        </div>
        <div class="drawer-panel-dragger" @mousedown="draggerBegin"></div>
        <div
          v-if="$slots.bottomPart"
          class="drawer-panel"
          :style="{ height: dynamicHeight, flex: 'none' }"
        >
          <!-- 下半部分的内容 -->
          <slot name="bottomPart"></slot>
        </div>
      </div>
      <div class="drawer-footer text-right">
        <slot name="footer"></slot>
      </div>
    </div>
  </el-drawer>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      default: '侧边弹窗'
    },
    visible: {
      type: Boolean,
      default: false
    },
    loading: {
      type: Boolean,
      default: false
    },
    draggerMinHeight: {
      type: [Number, String],
      default: '200px'
    },
    draggerInitialHeight: {
      type: [Number, String],
      default: '200px'
    },
    size: {
      type: [Number, String],
      default: '992px'
    }
  },
  data() {
    return {
      innerVisible: this.visible,

      dynamicHeight: parseInt(this.draggerInitialHeight) + 'px',
      minHeight: parseInt(this.draggerMinHeight),
      draggerOffset: 0
    };
  },
  watch: {
    visible() {
      this.innerVisible = this.visible;
    },
    innerVisible() {
      this.$emit('update:visible', this.innerVisible);
    }
  },
  mounted() {
    // mounted后再显示面板内容，防止抖动
    this.innerVisible = true;

    // 去除窗口滚动
    const htmlBody = document.querySelector('body');
    const originalOverflow = htmlBody.style.overflow;
    htmlBody.style.overflow = 'hidden';
    this.$once('hook:destroyed', () => {
      htmlBody.style.overflow = originalOverflow;
    });
  },
  methods: {
    draggerBegin(evt) {
      this.$refs.body.style.userSelect = 'none';
      // 记录鼠标点击位置和容器下边框的偏移量
      this.draggerOffset = 20 - (evt.clientY - evt.target.offsetTop);
      document.addEventListener('mousemove', this.draggerMove);
      document.addEventListener('mouseup', this.draggerEnd);
    },
    draggerMove(evt) {
      const editorHeight = this.$refs.container.offsetHeight;
      const dynamicHeight =
        editorHeight - evt.clientY - this.draggerOffset - 52;
      if (dynamicHeight <= this.minHeight) {
        this.dynamicHeight = this.minHeight + 'px';
      } else {
        this.dynamicHeight = dynamicHeight + 'px';
      }
    },
    draggerEnd() {
      this.$refs.body.style.userSelect = 'auto';
      document.removeEventListener('mousemove', this.draggerMove);
      document.removeEventListener('mouseup', this.draggerEnd);
    },

    closeDrawer() {
      this.innerVisible = false;
    }
  }
};
</script>

<style lang="less" scoped>
.custom-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  .drawer-header {
    flex: none;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-sizing: border-box;
    padding: 15px 20px;
    border-bottom: 1px solid #d3d4d5;
    .drawer-title {
      flex: auto;
      font-family: PingFangSC-Medium, Avenir, Helvetica, Arial, sans-serif;
      font-size: 16px;
      color: #333333;
      letter-spacing: 0;
    }
    .drawer-close {
      color: #979797;
      font-size: 22px;
      flex: none;
      cursor: pointer;
    }
  }
  .drawer-body {
    flex: auto;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    box-sizing: border-box;
  }
  .drawer-footer {
    flex: none;
    box-sizing: border-box;
    padding: 10px 20px;
    background: #f9f8f8;
    border: 1px solid #ededed;
    box-shadow: 0 -1px 4px 1px rgba(0, 0, 0, 0.14);
  }
}
.el-drawer__wrapper @{deep} .el-drawer {
  outline: none;
  max-width: 100%;
  .el-drawer__body {
    height: 100%;
    overflow: hidden;
  }
}
.drawer-panel {
  flex: auto;
  position: relative;
  overflow-x: hidden;
  overflow-y: auto;
  padding: 15px 20px;
}
.drawer-panel-dragger {
  flex: none;
  position: relative;
  height: 20px;
  background: #fafafa;
  border: 1px solid #d3d4d5;
  cursor: ns-resize;
  .dragger() {
    display: block;
    content: ' ';
    position: absolute;
    left: 50%;
    width: 15px;
    height: 1.5px;
    background: #b1b1b1;
    transform: translateX(-50%);
  }
  &::before {
    .dragger();
    top: 5px;
  }
  &::after {
    .dragger();
    top: 10px;
  }
}
</style>
