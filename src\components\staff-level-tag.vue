<!--
 * @Fileoverview: 用户标签展示模板
 * @Description: 用户标签展示模板
-->
<template>
  <div class="staff-level-tag">
    <div v-if="dataTags.length > 0">
      <div
        v-for="(item, index) in dataTagList"
        :key="index"
        class="levle-tag"
        :class="'tag-' + item.id"
        :style="`width:${tagWidth}px`"
      >
        <span>{{ item.name }}</span>
      </div>
    </div>
    <div v-else>--</div>
  </div>
</template>

<script>
const LEVEL_MAP = {
  0: '普通',
  1: '关注',
  inactive: '失活',
  active: '活跃',
  'high-risk': '高风险',
  'medium-risk': '中风险',
  'low-risk': '低风险',
  'no-risk': '无风险'
};
export default {
  props: ['dataTags'],
  data() {
    return {
      dataTagList: [],
      tagWidth: '50',
      LEVEL_MAP
    };
  },

  created() {},

  mounted() {
    this.formatDataTags();
  },
  watch: {
    dataTags() {
      this.formatDataTags();
    }
  },
  methods: {
    formatDataTags() {
      const tagData = JSON.parse(JSON.stringify(this.dataTags));
      this.dataTagList = tagData.map((item) => {
        return {
          id: item,
          name: LEVEL_MAP[item]
        };
      });
    }
  }
};
</script>

<style lang="less" scoped>
.staff-level-tag {
  width: 200px;
  & .levle-tag {
    display: flex;
    height: 25px;
    padding: 0 5px;
    flex-shrink: 0;
    font-size: 12px;
    line-height: 25px;
    text-align: center;
    border-radius: 2px;
    // margin-bottom: 10px;
    margin-right: 10px;
    font-family: PingFangSC-Regular;
    display: inline-block;

    &.tag-0 {
      color: #409eff;
      background: #e0edf9;
    }

    &.tag-1 {
      color: #f14d1b;
      background: #f8e9e6;
    }

    &.tag-inactive {
      color: #909399;
      background: #f4f4f5;
    }

    &.tag-active {
      color: #dc9f03;
      background: #f6f1e1;
    }

    &.tag-high-risk {
      color: #e84738;
      background: #fff1f0;
    }

    &.tag-medium-risk {
      color: #d46b08;
      background: #fff7e6;
    }

    &.tag-low-risk {
      color: #d48806;
      background: #fffbe6;
    }

    &.tag-no-risk {
      color: #5eb023;
      background: #f0fde9;
    }
  }
}
</style>
