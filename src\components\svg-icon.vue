<template>
  <div class="svg-wrapper">
    <svg class="svg-icon">
      <use :xlink:href="'#icon-' + icon"></use>
    </svg>
  </div>
</template>

<script>
const req = require.context('@/assets/svg', false, /\.svg$/);
const requierAll = (requireContext) =>
  requireContext.keys().map(requireContext);
requierAll(req);

export default {
  props: {
    icon: String
  }
};
</script>

<style lang="less" scoped>
.svg-wrapper {
  display: inline-block;
}
.svg-icon {
  width: 100%;
  height: 100%;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
</style>
