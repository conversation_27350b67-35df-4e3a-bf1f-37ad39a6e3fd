<template>
  <el-tooltip
    v-model="isShow"
    placement="top"
    :manual="true"
    @mouseenter.native="handleMouseEnter"
    @mouseleave.native="handleMounseLeave"
  >
    <div slot="content">{{ $slots.default[0].text }}</div>
    <span>{{ $slots.default[0].text }}</span>
  </el-tooltip>
</template>
<script>
export default {
  name: 'QzTooltip',
  data() {
    return {
      isShow: false
    };
  },
  methods: {
    handleMounseLeave() {
      this.isShow = false;
    },
    handleMouseEnter(event) {
      const cellChild = event.fromElement || event.target;
      const range = document.createRange();
      range.setStart(cellChild, 0);
      range.setEnd(cellChild, cellChild.childNodes.length);
      const rangeWidth = range.getBoundingClientRect().width;
      if (
        rangeWidth > cellChild.offsetWidth ||
        cellChild.scrollWidth > cellChild.offsetWidth
      ) {
        this.isShow = true;
      } else {
        this.isShow = false;
      }
    }
  },
  mounted() {}
};
</script>
<style type="text/css">
.el-tooltip {
  outline: none;
}
</style>
