<template>
  <div class="transfer-container">
    <div class="left-box">
      <div class="header">
        <el-input
          placeholder="请输入"
          size="mini"
          v-model="filterText"
          suffix-icon="el-icon-search"
          class="mt5"
        ></el-input>
      </div>
      <div class="body">
        <el-tree
          ref="tree"
          :data="leftDataList"
          :filter-node-method="filterNode"
          node-key="key"
          show-checkbox
          @check="handleCheckLeft"
        >
          <div class="org-tree-node" slot-scope="{ node, data }">
            <i class="type-icon el-icon-folder-opened"></i>
            <span>{{ node.label }}</span>
          </div>
        </el-tree>
      </div>
    </div>
    <div class="transfer-btns">
      <el-button
        class="btn-to-left mb10"
        type="primary"
        size="mini"
        icon="el-icon-arrow-left"
        :disabled="rightCheckedKeyList.length === 0"
        @click="handleLeftChange"
      ></el-button>
      <el-button
        class="btn-to-right"
        type="primary"
        size="mini"
        icon="el-icon-arrow-right"
        :disabled="leftCheckedList.length === 0"
        @click="handleRightChange"
      ></el-button>
    </div>
    <div class="right-box">
      <el-checkbox-group v-model="rightCheckedKeyList">
        <div
          v-for="{ label, type, key } of rightDataList"
          :key="key"
          class="right ml10"
        >
          <el-checkbox :label="key">
            <i class="type-icon el-icon-folder-opened"></i>
            {{ label }}
          </el-checkbox>
        </div>
      </el-checkbox-group>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    defaultProps: {
      type: Object,
      required: true,
      default: () => ({
        children: 'children',
        label: 'label',
        value: 'value',
        key: 'key'
      })
    },
    leftOriginalList: {
      type: Array,
      default: []
    },
    rightOriginalList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      filterText: '',
      leftDataList: [], // left 所有数据
      leftCheckedList: [], // left 选中的数据
      rightDataList: [], // right 所有数据
      rightCheckedKeyList: [] //right 选中数据,
    };
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val);
    },
    leftOriginalList: {
      immediate: true,
      deep: true,
      handler(newVal) {
        this.leftDataList = JSON.parse(JSON.stringify(newVal));
        this.leftCheckedList = [];
      }
    },
    rightOriginalList: {
      immediate: true,
      deep: true,
      handler(newVal) {
        const rightIdList = JSON.parse(JSON.stringify(newVal));
        this.getTreeId(this.leftDataList, rightIdList);
        this.rightCheckedKeyList = [];
      }
    }
  },
  created() {},

  mounted() {},

  methods: {
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    getTreeId(orginData, ids) {
      for (let i = 0; i < orginData.length; i++) {
        if (ids.includes(Number(orginData[i].value))) {
          this.rightDataList.push(orginData[i]);
        }
        if (orginData[i].children) {
          this.getTreeId(orginData[i].children, ids);
        }
      }
    },
    handleCheckLeft() {
      // 处理左边选中的数据
      const checkedNodes = this.$refs.tree.getCheckedNodes();
      this.leftCheckedList = checkedNodes.map((item) => {
        return {
          value: item.value,
          label: item.label,
          type: item.type,
          key: item.key
        };
      });
    },
    handleLeftChange() {
      // 将右边选中的数据删除
      this.rightDataList = this.rightDataList.filter((item) => {
        return !this.rightCheckedKeyList.some((e) => e === item.key);
      });
      // 清空选中数组
      this.rightCheckedKeyList = [];
      // 传递当前数据分布
      this.$emit('change', this.rightDataList);
    },
    handleRightChange() {
      for (const item of this.leftCheckedList) {
        if (!this.rightDataList.some((ele) => ele.value === item.value)) {
          this.rightDataList.push(item);
        }
      }
      // 清空tree选中数据
      this.leftCheckedList = [];
      this.$refs.tree.setCheckedKeys([]);
      // 传递当前数据分布
      this.$emit('change', this.rightDataList);
    }
  }
};
</script>

<style lang="less" scoped>
.transfer-container {
  display: flex;
  align-items: stretch;
  height: 400px;

  .left-box,
  .right-box {
    flex: none;
    width: 44%;
    max-height: 400px;
    box-sizing: border-box;
    padding: 5px;
    border: 1px solid #dcdcdc;
  }

  .right-box {
    overflow-y: auto;
  }
  .right {
    height: 30px;
  }
  /deep/ .el-tree-node {
    &__content {
      height: 30px !important;
    }
  }

  .transfer-btns {
    flex: none;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin: 0 10px;

    .el-button {
      padding: 5px;
      margin: 0;
      min-width: 50px !important;
      height: 30px;
    }
  }
}

.transfer-container .left-box {
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .body {
    flex: auto;
    max-height: 300px;
    padding: 5px;
    overflow-y: auto;
  }
}

.el-radio-group {
  display: flex;

  /deep/ .el-radio-button {
    width: 50%;

    .el-radio-button__inner {
      width: 100%;
    }
  }
}

.type-icon {
  color: #2b95fd;
  margin-right: 5px;
  font-size: 14px;
}

.el-checkbox {
  font-weight: normal;
  &__label {
    line-height: 13px !important;
  }
}
</style>
