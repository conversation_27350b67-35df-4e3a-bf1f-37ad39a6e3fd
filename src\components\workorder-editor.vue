<template>
  <div ref="el" class="workorder-container"></div>
</template>

<script>
import { loadMicroApp } from 'qiankun';
import {
  MICRO_APPS_WORK_ORDER_NAME,
  MICRO_APPS_WORK_ORDER_TEMPLATE_DETAIL
} from '@/constant/micro-apps-url-constants';
import { ENTRY_MICRO_APPS_WORK_ORDER } from '@/utils/micro-apps-loading';

export default {
  props: ['params'],
  data() {
    return {
      appInst: null
    };
  },
  mounted() {
    this.appInst = loadMicroApp({
      name: MICRO_APPS_WORK_ORDER_NAME,
      entry: ENTRY_MICRO_APPS_WORK_ORDER,
      container: this.$refs.el,
      props: {
        route: {
          path: this.params.path,
          params: {
            ...this.params,
            templateUrl: MICRO_APPS_WORK_ORDER_TEMPLATE_DETAIL,
            onlyCallback: (type, payload, id = '') => {
              if (this.params.isDraw) {
                this.params.closeOutDrawer();
              } else {
                this.params.close();
              }
              if (type === 'submit') {
                this.params.callback && this.params.callback(id);
              } else if (type === 'resubmit') {
                this.params.resubmit && this.params.resubmit(payload, id);
              }
            }
          }
        }
      }
    });
  },
  beforeDestroy() {
    this.appInst && this.appInst.unmount && this.appInst.unmount();
  }
};
</script>

<style lang="less" scoped>
.workorder-container /deep/ .page-body {
  position: relative;
}
</style>
