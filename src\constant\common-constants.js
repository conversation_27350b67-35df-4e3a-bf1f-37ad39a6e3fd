/**
 * 授权相关
 */
// 大模块授权
export const MODULE_OVERVIEW = 'overview'; //概览
export const MODULE_RISK = 'risk'; //风险
export const MODULE_ASSETS = 'assets'; //资产
export const MODULE_WEAKNESS = 'weakness'; //弱点
export const MODULE_AUDIT = 'audit'; //审计
export const MODULE_AUDIT_ASSETS = 'audit-assets'; //审计资产交叉模块
export const MODULE_AUDIT_RISK = 'audit-risk'; //审计风险交叉模块
export const MODULE_REPORT = 'report'; //报告
export const MODULE_SITUATION = 'situation'; //态势
export const MODULE_SYSTEM_MODE = 'system-mode'; //高速模式
export const MODULE_EO_LINK = 'eolink';

// 菜单和页面模块显示与否
export const PAGE_MENU_AND_BLOCK_CONFIGS = {
  [MODULE_OVERVIEW]: 1,
  [MODULE_RISK]: 0,
  [MODULE_ASSETS]: 1,
  [MODULE_WEAKNESS]: 1,
  [MODULE_AUDIT]: 0,
  [MODULE_AUDIT_ASSETS]: 0,
  [MODULE_AUDIT_RISK]: 0,
  [MODULE_REPORT]: 0,
  [MODULE_SITUATION]: 0,
  [MODULE_SYSTEM_MODE]: 0,
  [MODULE_EO_LINK]: 0
};

// 快速筛选时间筛选
export const QS_TIME_SEARCH_MAP = {
  custom: '自定义时间',
  recentOneDay: '近一日',
  recentThreeDays: '近三日',
  recentOneWeek: '近七日',
  recentOneMonth: '最近一个月',
  recentThreeMonths: '最近三个月',
  recentSixMonths: '最近半年'
};

/**
 * 所有等级枚举值
 */
// 敏感等级
export const SENSI_LEVEL_OPTIONS = [
  '高敏感',
  '中敏感',
  '低敏感',
  '非敏感',
  '其他'
].map((item) => {
  return {
    id: item,
    label: item,
    value: item,
    name: item
  };
});
// 敏感等级class与名称映射
export const SENSI_LEVEL_CLASS_NAME_MAP = {
  high: '高敏感',
  mid: '中敏感',
  low: '低敏感',
  non: '非敏感',
  other: '其他'
};
// 敏感等级名称与class映射
export const SENSI_LEVEL_NAME_CLASS_MAP = {
  高敏感: 'high',
  中敏感: 'mid',
  低敏感: 'low',
  非敏感: 'non',
  其他: 'other'
};
// 通用风险等级枚举值
export const COMMON_RISK_LEVEL_OPTIONS = [
  {
    label: '高风险',
    value: 3
  },
  {
    label: '中风险',
    value: 2
  },
  {
    label: '低风险',
    value: 1
  },
  {
    label: '无风险',
    value: 0
  },
  {
    label: '其他',
    value: 4
  }
];
// 通用风险等级名称与class映射map
export const COMMON_RISK_LEVEL_NAME_CLASS_MAP = {
  无风险: 'non',
  低风险: 'low',
  中风险: 'mid',
  高风险: 'high',
  其他: 'other'
};
// 通用风险等级名称与等级映射map
export const COMMON_RISK_LEVEL_NAME_MAP = {
  无风险: 0,
  低风险: 1,
  中风险: 2,
  高风险: 3,
  其他: 4
};
// 通用风险等级与class映射map
export const COMMON_RISK_LEVEL_CLASS_MAP = {
  4: 'other',
  0: 'non',
  1: 'low',
  2: 'mid',
  3: 'high'
};
// 通用风险等级与其名称映射map
export const OTHER_COMMON_RISK_LEVEL_NAME_CLASS_MAP = {
  4: '其他',
  0: '无风险',
  1: '低风险',
  2: '中风险',
  3: '高风险'
};

// 弱点和风险等级枚举值
export const WEAKNESS_RISK_LEVEL_OPTIONS = [
  {
    label: '高危',
    value: 3
  },
  {
    label: '中危',
    value: 2
  },
  {
    label: '低危',
    value: 1
  }
];
// 数据敏感等级枚举值
export const DATA_SENSI_LEVEL_OPTIONS = [
  {
    value: 'level_four',
    label: '四级'
  },
  {
    value: 'level_three',
    label: '三级'
  },
  {
    value: 'level_two',
    label: '二级'
  },
  {
    value: 'level_one',
    label: '一级'
  }
];
// 弱点和风险等级与class映射map
export const WEAKNESS_RISK_LEVEL_CLASS_MAP = {
  1: 'low',
  2: 'mid',
  3: 'high'
};
// 弱点和风险等级及其名称映射map
export const WEAKNESS_COMMON_RISK_LEVEL_NAME_CLASS_MAP = {
  1: '低危',
  2: '中危',
  3: '高危'
};

// 等级icon map
export const LEVEL_ICON_MAP = {
  1: 'icon-diwei',
  2: 'icon-zhongwei',
  3: 'icon-gaowei'
};

export const API_TYPE_MAP = {
  1: 'API',
  2: '动态页面',
  3: '静态页面',
  4: '文件',
  5: '其他',
  6: '重定向',
  7: '单页面应用页面'
};

// 通用操作符枚举值operator
export const COMMON_OPERATOR_IN = [
  {
    id: 'set_all_in',
    name: '包含全部'
  },
  {
    id: 'set_any_in',
    name: '命中其一'
  }
];
export const COMMON_OPERATOR = [
  {
    id: '>',
    name: '大于'
  },
  {
    id: '>=',
    name: '大于等于'
  },
  {
    id: '<',
    name: '小于'
  },
  {
    id: '<=',
    name: '小于等于'
  },
  {
    id: '==',
    name: '等于'
  }
];

export const API_FORMAT_OPTIONS = [
  'RESTful',
  'SOAP',
  'GraphQL',
  'gRPC',
  'Dubbo3'
].map((item) => {
  return {
    label: item,
    value: item
  };
});

export const API_TYPE_OPTIONS = [
  {
    label: 'API',
    value: 1
  },
  {
    label: '动态页面',
    value: 2
  },
  {
    label: '静态页面',
    value: 3
  },
  {
    label: '文件',
    value: 4
  },
  {
    label: '其他',
    value: 5
  },
  {
    label: '重定向',
    value: 6
  },
  {
    label: '单页面应用页面',
    value: 7
  }
];

export const API_LIFE_FLAG_OPTIONS = [
  {
    label: '活跃',
    value: 1
  },
  {
    label: '新增',
    value: 2
  },
  {
    label: '失活',
    value: 3
  },
  {
    label: '复活',
    value: 4
  }
];

export const API_LIFE_FLAG_MAP = {
  1: '活跃',
  2: '新增',
  3: '失活',
  4: '复活'
};

export const TODAY = {
  text: '今日',
  onClick(picker) {
    picker.$emit('pick', [moment().startOf('day'), moment().endOf('day')]);
  }
};

export const LAST_3_DAYS = {
  text: '最近3天',
  onClick(picker) {
    const end = new Date();
    const start = new Date();
    start.setTime(start.getTime() - 3600 * 1000 * 24 * 2);
    picker.$emit('pick', [start, end]);
  }
};

export const LAST_7_DAYS = {
  text: '最近7天',
  onClick(picker) {
    const end = new Date();
    const start = new Date();
    start.setTime(start.getTime() - 3600 * 1000 * 24 * 6);
    picker.$emit('pick', [start, end]);
  }
};

export const LAST_14_DAYS = {
  text: '最近14天',
  onClick(picker) {
    const end = new Date();
    const start = new Date();
    start.setTime(start.getTime() - 3600 * 1000 * 24 * 13);
    picker.$emit('pick', [start, end]);
  }
};

export const LAST_30_DAYS = {
  text: '最近30天',
  onClick(picker) {
    const end = new Date();
    const start = new Date();
    start.setTime(start.getTime() - 3600 * 1000 * 24 * 29);
    picker.$emit('pick', [start, end]);
  }
};

export const LAST_90_DAYS = {
  text: '最近90天',
  onClick(picker) {
    const end = new Date();
    const start = new Date();
    start.setTime(start.getTime() - 3600 * 1000 * 24 * 89);
    picker.$emit('pick', [start, end]);
  }
};

export const PORTRAIT_DATE_QUICK_SEARCH = [
  TODAY,
  LAST_3_DAYS,
  LAST_7_DAYS,
  LAST_14_DAYS,
  LAST_30_DAYS,
  LAST_90_DAYS
];

export const LAST_THREE_DAYS = {
  text: '最近三天',
  onClick(picker) {
    const end = new Date();
    const start = new Date();
    start.setTime(start.getTime() - 3600 * 1000 * 24 * 3);
    picker.$emit('pick', [start, end]);
  }
};

export const LAST_WEEK = {
  text: '最近一周',
  onClick(picker) {
    const end = new Date();
    const start = new Date();
    start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
    picker.$emit('pick', [start, end]);
  }
};

export const LAST_MONTH = {
  text: '最近一个月',
  onClick(picker) {
    const end = new Date();
    const start = new Date();
    start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
    picker.$emit('pick', [start, end]);
  }
};

export const LAST_THREE_MONTHS = {
  text: '最近三个月',
  onClick(picker) {
    const end = new Date();
    const start = new Date();
    start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
    picker.$emit('pick', [start, end]);
  }
};

export const LAST_HALF_YEAR = {
  text: '最近半年',
  onClick(picker) {
    const end = new Date();
    const start = new Date();
    start.setTime(start.getTime() - 3600 * 1000 * 24 * 180);
    picker.$emit('pick', [start, end]);
  }
};

// 快捷时间筛选
export const DATE_QUICK_SEARCH = [
  LAST_THREE_DAYS,
  LAST_WEEK,
  LAST_MONTH,
  LAST_THREE_MONTHS,
  LAST_HALF_YEAR
];

// 查询条件组合关系
export const CONDITION_LOGIC_AND = 'and';
export const CONDITION_LOGIC_OR = 'or';
export const CONDITION_LOGIC_MAP = {
  [CONDITION_LOGIC_AND]: '且',
  [CONDITION_LOGIC_OR]: '或'
};

export const RISK_ATTACK_OPTIONS = [
  {
    label: '攻击失败',
    value: 0
  },
  {
    label: '攻击成功',
    value: 1
  },
  {
    label: '未知',
    value: 2
  }
];

export const RISK_ATTACK_MAP = {
  0: '攻击失败',
  1: '攻击成功',
  2: '未知'
};

// 表格网段树状图格式
export const TABLE_TREE_PROPS = {
  children: 'children',
  value: 'id',
  label: 'name'
};

// 异常类型
// API单次响应大量敏感数据
export const RISK_SINGLE_RSP = '1';
// 登录API存在弱密码登录账号
export const RISK_WEAKNESS_LOGIN = '1007';
// IP执行参数遍历
export const RISK_PARAM_TRAVERSAL = '1000';
// IP执行翻页遍历
export const RISK_PAGE_TRAVERSAL = '1001';
// 异常请求查询敏感数据
export const RISK_EXCEPTION_REQ = '1004';
// IP访问次数异常
export const RISK_VISIT_COUNT = '6';
// IP高频撞库
export const RISK_HIGH_FREQUENCY = '7';
// 账号暴力破解
export const RISK_ACCOUNT = '1005';
// 境外IP数据拉取大量数据
export const RISK_PULL_DATA = '12';
// Web攻击
export const WEB_ATTACK = '1010';
// 验证码爆破
export const VERIFICATION_CODE_BLASTING = '1013';
// 异常参数获取敏感数据
export const EXCEPTION_PARAMS = '1003';
// session多终端访问
export const SESSION_MULTI_TERMINALS = '16';
// session异地访问
export const SESSION_OFFSITE = '17';
// IP使用多账号
export const IP_MULTI_ACCOUNT = '1008';
// 账号异地登录
export const ACCOUNT_OFFSITE_LOGIN = '1009';
// 短信炸弹
export const MESSAGE_BOMB = '1012';
// 渗透测试尝试
export const PENETRATION_TEST_ATTEMPT = '1011';
export const ATTACK_RESULT_OPTION = [
  {
    label: '攻击失败',
    value: 0
  },
  {
    label: '攻击成功',
    value: 1
  },
  {
    label: '未知',
    value: 2
  }
];

// 请求方法options
export const REQ_METHODS_OPTIONS = [
  'GET',
  'POST',
  'PUT',
  'DELETE',
  'HEAD',
  'PATCH',
  'OPTIONS',
  'TRACE'
].map((item) => {
  return {
    value: item,
    label: item
  };
});

// 置顶状态options
export const FOLLOWED_OPTIONS = [
  {
    label: '已置顶',
    value: true
  },
  {
    label: '未置顶',
    value: false
  }
];

// 弱点状态options
export const WEAKNESS_STATE_OPTIONS = [
  { name: '待确认', value: 'NEW' },
  { name: '已忽略', value: 'IGNORED' },
  { name: '待修复', value: 'REPAIRING' },
  { name: '已修复', value: 'FIXED' },
  { name: '再复现', value: 'REOPEN' }
];

export const WEAKNESS_STATE_MAP = {
  NEW: '待确认',
  IGNORED: '已忽略',
  REPAIRING: '待修复',
  FIXED: '已修复',
  REOPEN: '再复现'
};

// 风险状态MAP
export const RISK_STATE_MAP = {
  0: '待确认',
  1: '已忽略',
  2: '已确认'
};

export const RISK_STATE_CLASS_MAP = {
  待确认: 0,
  已忽略: 1,
  已确认: 2
};

// 风险状态options
export const RISK_STATE_OPTIONS = [
  {
    label: '待确认',
    value: 0
  },
  {
    label: '已忽略',
    value: 1
  },
  {
    label: '已确认',
    value: 2
  }
];

// 登录结果Options
export const LOGIN_RESULT_OPTIONS = [
  {
    value: 'LOGIN_SUCCESS',
    label: '登录成功'
  },
  {
    value: 'LOGIN_FAIL',
    label: '登录失败'
  },
  {
    value: 'UNKNOWN',
    label: '未知'
  }
];

// 登录结果Map
export const LOGIN_RESULT_MAP = {
  LOGIN_FAIL: '登录失败',
  LOGIN_SUCCESS: '登录成功',
  UNKNOWN: '未知'
};

// 风险主体类型
export const ENTITY_TYPE_APP = 'APP';
export const ENTITY_TYPE_IP = 'IP';
export const ENTITY_TYPE_API = 'API';
export const ENTITY_TYPE_ACCOUNT = 'ACCOUNT';
export const ENTITY_TYPE_SESSION = 'SESSION';
export const ENTITY_TYPE_NETWORK_SEGMENT = 'NETWORK_SEGMENT';
export const ENTITY_MAP = {
  APP: '应用',
  IP: 'IP',
  API: 'API',
  ACCOUNT: '账号',
  NETWORK_SEGMENT: '网段',
  SESSION: 'SESSION',
  DATA: '数据'
};

// 网段IP类型
export const NETWORK_SEGMENT_IP_TYPE_SEGMENT = 2; // IP段

// 任务状态
export const TASK_NAME_MAP = new Map([
  ['RUNNABLE', '待执行'],
  ['RUNNING', '运行中'],
  ['CANCELED', '已取消'],
  ['TERMINATED', '已成功'],
  ['ERROR', '错误']
]);

// 样例-文件上传
export const BIZ_LABEL_FILE_UPLOAD = 'fileupload';
// 样例-文件下载
export const BIZ_LABEL_FILE_DOWNLOAD = 'filedownload';

// 标签配置类型
export const LABEL_CONFIG_TYPE_LIST = ['JSON', 'XML', 'JSONP', 'REGEX'];

// 账号/组织架构解析位置
export const RESOLVE_LOCATION_LIST = [
  { value: 'POST', label: 'Request-Body' },
  { value: 'COOKIE', label: 'Request-Cookie' },
  { value: 'REQ_HEADER', label: 'Request-Header' },
  { value: 'GET', label: 'Request-Param' },
  { value: 'URL', label: 'Request-Path' },
  { value: 'BODY', label: 'Response-Body' },
  { value: 'RSP_HEADER', label: 'Response-Header' },
  { value: 'SET_COOKIE', label: 'Response-SetCookie' }
];

// 样例返回值location枚举
export const SAMPLE_LOCATION = {
  1: 'BODY',
  2: 'COOKIE',
  3: 'SET_COOKIE',
  4: 'GET',
  5: 'POST',
  6: 'REQ_HEADER',
  7: 'RSP_HEADER',
  8: 'REQ_RAW',
  9: 'RSP_RAW',
  BODY: 1,
  COOKIE: 2,
  SET_COOKIE: 3,
  GET: 4,
  POST: 5,
  REQ_HEADER: 6,
  RSP_HEADER: 7,
  REQ_RAW: 8,
  RSP_RAW: 9,
  FILE_DOWN: 1,
  FILE_UP: 5
};

// 清理类型
export const DATA_TYPE = {
  DATA_LABEL: '数据标签',
  FEATURE_LABEL: 'API标签',
  APP_FEATURE_LABEL: '应用标签',
  LEVEL: 'API敏感等级',
  VISIT_DOMAIN: '访问域',
  DEPLOY_DOMAIN: '部署域'
};

// 审计模板任务状态
export const AUDIT_TASK_STATE_CN_MAP = {
  WAIT: '等待中',
  RUNNING: '进行中',
  FINISH: '已完成',
  ERROR: '异常',
  CANCEL: '已暂停',
  TERMINATED: '已终止'
};

// 资产定义
export const ASSETS_TYPE_APP = 1;
export const ASSETS_TYPE_API = 2;
export const ASSETS_DEFINITION_COMPOSITE_TYPE_MERGE = 1;
export const ASSETS_DEFINITION_COMPOSITE_TYPE_SPLIT = 2;

// 基础配置
export const BASIC_INFO_TYPE_NAME = 1; // 产品名称
export const BASIC_INFO_TYPE_LOGO = 2; // 产品logo
export const BASIC_INFO_TYPE_ICON = 3; // 产品icon
export const BASIC_INFO_TYPE_BACK_IMG = 4; // 底图

// 用户类型
export const USER_TYPE_SYSADMIN = 1; // 系统管理员
export const USER_TYPE_SYSAUDIT = 2; // 审计管理员
export const USER_TYPE_NORMAL = 3; // 普通用户

// 角色类型
export const ROLE_TYPE_INTERNAL = 1; // 内置角色
export const ROLE_TYPE_CUSTOM = 2; // 自定义角色

// 应用/API状态
export const LIFE_FLAG_ACTIVE = 1;
export const LIFE_FLAG_NEW = 2;
export const LIFE_FLAG_INACTIVE = 3;
export const LIFE_FLAG_REVIVE = 4;
export const LIFE_FLAG_MAP = {
  [LIFE_FLAG_ACTIVE]: '活跃',
  [LIFE_FLAG_NEW]: '新增',
  [LIFE_FLAG_INACTIVE]: '失活',
  [LIFE_FLAG_REVIVE]: '复活'
};

// 角色权限定义
// 仅可脱敏查看
export const DATA_PERMISSION_DESENSITIZE = 'sample:sampleList:desensitize';
// 可取消脱敏
export const DATA_PERMISSION_CANCEL_DESENS =
  'sample:sampleList:cancelDesensitize';
// 只读
export const OPERATION_PERMISSION_READ = 'READ';
// 可编辑
export const OPERATION_PERMISSION_WRITE = 'WRITE';

// 导出任务模块名称
const EXPORT_TASK_APP = 'EXPORT_APP';
const EXPORT_TASK_URL_STRUCTURE = 'EXPORT_URL_STRUCTURE';
const EXPORT_TASK_SYSLOG = 'EXPORT_SYSLOG';
const EXPORT_TASK_WEAKNESS = 'EXPORT_API_WEAKNESS';
const EXPORT_TASK_API = 'EXPORT_API';
export const EXPORT_TASK_TYPE = {
  [EXPORT_TASK_APP]: '导出应用',
  [EXPORT_TASK_URL_STRUCTURE]: '导出应用API列表',
  [EXPORT_TASK_WEAKNESS]: '导出web应用弱点',
  [EXPORT_TASK_SYSLOG]: '导出审计日志',
  [EXPORT_TASK_API]: '导出API'
};

// 文件格式枚举值
export const FILE_TYPE = [
  'xlsx',
  'xls',
  'pdf',
  'csv',
  'txt',
  'docx',
  'doc',
  'rar',
  'zip'
];

export const AUDIT_TASK_STATE_CLASS_MAP = {
  WAIT: 'wait',
  RUNNING: 'running',
  FINISH: 'finish',
  ERROR: 'error',
  CANCEL: 'cancel',
  TERMINATED: 'terminated'
};

// 审计模板内置代号
export const OFF_ACCOUNT = 'OFF_ACCOUNT'; //离职人员审计
export const SPECIAL_ACCOUNT = 'SPECIAL_ACCOUNT'; //特权账户审计
export const ENTITY_TRACE = 'ENTITY_TRACE'; //主体溯源审计
export const SENSI_DATA_TRACE = 'SENSI_DATA_TRACE'; //数据溯源
export const RESOURCE_AND_WEAKNESS = 'RESOURCE_AND_WEAKNESS'; //资产与弱点

// 过滤规则-方式
export const FILTER_RULE_TYPE_REQ = [
  {
    value: 'APP',
    label: '应用'
  },
  {
    value: 'API',
    label: 'API'
  },
  {
    value: 'IP',
    label: '源IP'
  },
  {
    value: 'REQ_SUFFIX',
    label: '请求后缀'
  },
  {
    value: 'REQ_UA',
    label: '请求UA'
  },
  {
    value: 'REQ_URL',
    label: '请求参数'
  },
  {
    value: 'REQ_METHOD',
    label: '请求方法'
  },
  {
    value: 'REQ_CUSTOM_HEADER',
    label: '自定义请求头字段'
  }
];
export const FILTER_RULE_TYPE_RSP = [
  {
    value: 'DST_IP',
    label: '目的IP'
  },
  {
    value: 'RSP_CODE',
    label: '响应状态码'
  },
  {
    value: 'RSP_CONTENT',
    label: '响应内容'
  },
  {
    value: 'RSP_CONTENT_GREATER',
    label: '响应内容长度大于'
  },
  {
    value: 'RSP_CONTENT_LESS',
    label: '响应内容长度小于'
  },
  {
    value: 'RSP_CONTENT_EQUAL',
    label: '响应内容长度等于'
  },
  {
    value: 'RSP_CONTENT_TYPE',
    label: '响应content-type'
  },
  {
    value: 'RSP_LOCATION',
    label: '响应location'
  },
  {
    value: 'RSP_CUSTOM_HEADER',
    label: '自定义响应头字段'
  }
];
// 通过名单的提示
export const INCLUDE_LIST_TIPS = {
  APP: `内容说明：支持输入应用域名，支持使用通配符“*”进行域名的模糊匹配；
  后续影响：不在输入范围的应用域名的流量将会被过滤掉；`,
  DST_IP: `内容说明：支持输入目的IP、目的IP段、目的IP子网掩码，支持IPv4和IPv6格式的IP；
  后续影响：不在输入范围的目的IP的流量将会被过滤掉；`
};
// 排除名单的提示
export const EXCLUDE_LIST_TIPS = {
  APP: `内容说明：支持输入应用域名，支持使用通配符“*”进行域名的模糊匹配；
        后续影响：匹配输入范围的应用域名的流量将会被过滤掉；`,
  API: `内容说明：支持输入API，支持使用通配符“*”进行模糊匹配；
        后续影响：匹配输入范围的API流量将会被过滤掉；`,
  IP: `内容说明：支持输入源IP、源IP段、源IP子网掩码，支持IPv4和IPv6（不支持子网掩码格式）格式的IP；
       后续影响：匹配输入范围的源IP的流量将会被过滤掉；`,
  DST_IP: `内容说明：支持输入目的IP、目的IP段、目的IP子网掩码，支持IPv4和IPv6（不支持子网掩码格式）格式的IP；
           后续影响：匹配输入范围的目的IP的流量将会被过滤掉；`,
  RSP_CODE: `内容说明：支持输入状态码内容，支持区间的形式进行输入；
             后续影响：匹配输入范围的状态码的流量将会被过滤掉；`,
  RSP_CONTENT: `内容说明：支持输入响应body内容，支持输入正则表达式进行响应body内容的匹配；
                后续影响：匹配输入范围的响应body内容的流量将会被过滤掉；`,
  REQ_METHOD: `内容说明：支持输入请求方法，不区分大小写；
               后续影响：匹配输入范围的请求方法的流量将会被过滤掉；`,
  REQ_SUFFIX: `内容说明：支持输入请求后缀名；
               后续影响：匹配输入范围的请求后缀名的流量将会被过滤掉；`,
  REQ_UA: `内容说明：支持输入User-Agent内容；
           后续影响：匹配输入范围的User-Agent的流量将会被过滤掉；`,
  REQ_URL: `内容说明：支持输入请求参数内容；
            后续影响：匹配输入范围的请求参数内容的流量将会被过滤掉；`,
  RSP_CONTENT_GREATER: `内容说明：支持输入响应内容的长度；
                        后续影响：大于输入响应内容长度的流量将会被过滤掉；`,
  RSP_CONTENT_LESS: `内容说明：支持输入响应内容的长度；
                     后续影响：小于输入响应内容长度的流量将会被过滤掉；`,
  RSP_CONTENT_EQUAL: `内容说明：支持输入响应内容的长度；
                      后续影响：等于输入响应内容长度的流量将会被过滤掉；`,
  RSP_CONTENT_TYPE: `内容说明：支持输入Content-type内容；
                     后续影响：不在输入范围内的content-type的流量将会被过滤掉；`,
  REQ_CUSTOM_HEADER: `内容说明：支持输入自定义的请求头字段和字段内容，字段key和字段value之间使用英文冒号(:)分割；
                      后续影响：若您仅输入字段key，则请求头中包含已输入字段key的所有日志都会被过滤掉；若您输入的是一组key和value组合，则请求中包含已输入组合的所有日志都会被过滤掉；`,
  RSP_CUSTOM_HEADER: `内容说明：支持输入自定义的响应头字段和字段内容，字段key和字段value之间使用英文冒号(:)分割；
                      后续影响：若您仅输入字段key，则响应头中包含已输入字段key的所有日志都会被过滤掉；若您输入的是一组key和value组合，则响应中包含已输入组合的所有日志都会被过滤掉；`,
  RSP_LOCATION: `内容说明：支持输入响应location内容，支持使用通配符“*”进行模糊匹配；
                 后续影响：匹配输入范围的响应location内容的流量将会被过滤掉；`
};

// 授权信息错误码字典
export const LICENSE_ERROR_MESSAGE_MAP = {
  /* 通用错误码 -101 ～ -200 */
  '-101': '已经执行过初始化函数',
  '-102': '没有执行初始化函数',
  '-103': '输入的参数非法',
  '-104': '申请空间失败',
  '-105': '创建JSON对象失败',
  '-106': '序列化产品对象失败',
  '-107': 'base64编码失败',
  '-108': '产品ID不匹配',
  '-109': '初始化互斥锁失败',
  '-110': '获取锁资源失败',
  '-111': '获取锁资源超时',

  /* 加载license文件功能错误码范围 -201 ～ -300 */
  '-201': 'license文件不存在',
  '-202': '获取license状态失败',
  '-203': '打开license文件失败',
  '-204': '读取license文件内容失败',
  '-205': '创建BIO 对象失败',
  '-206': '获取RSA公钥失败',

  /* 获取机器码功能的错误码范围 -301 ～ -400 */
  '-301': '获取MAC地址连接sock失败',
  '-302': '获取MAC地址连接IOCTL失败',
  '-303': '获取MAC地址失败',
  '-304': '创建机器码JSON对象失败',
  '-305': '序列化机器码JSON对象失败',
  '-306': '使用AES算法加密机器码失败',
  '-307': '使用RSA算法加密md5产生的hash失败',
  '-308': 'base64编码机器码密文失败',
  '-309': 'base64编码hash密文失败',
  '-310': '压缩机器码失败',

  /* 验证签名功能的错误码范围 -401 ～ -500 */
  '-401': '解压授权文件失败',
  '-402': '解析license文件的JSON格式失败',
  '-403': '获取加密授权内容失败',
  '-404': '获取签名内容失败',
  '-405': '获取aes密钥种子失败',
  '-406': '授权信息base64解码失败',
  '-407': '签名信息base64解码失败',
  '-408': 'aes密钥种子解码失败',
  '-409': 'rsa解密aes密钥种子失败',
  '-410': 'aes解密授权内容失败',
  '-411': 'rsa解密签名信息失败',
  '-412': '验证签名信息失败',

  /* 更新license文件内容功能的错误码范围 -501 ～ -600 */
  '-501': '解析授权信息的JSON格式失败',
  '-502': '产品信息不存在',
  '-503': '产品版本号信息不存在',
  '-504': '产品版本号信息不匹配',
  '-505': '产品序列号信息不存在',
  '-506': '产品序列号信息不匹配',
  '-507': '机器码信息不存在',
  '-508': '机器码信息不匹配',

  /* 获取license文件内容功能的错误码范围 -601 ～ -700 */
  '-601': '获取功能限制点不存在',
  '-602': '获取功能现在点信息失败',
  '-603': '获取基础限制点信息失败',

  /* 检查产品过期或功能点过期功能的错误范围 -701 ～ -800 */
  '-701': '没有到使用产品的时间',
  '-702': '产品时间过期',
  '-703': '没有到使用功能的时间',
  '-704': '功能模块过期',
  '-705': '获取当前时间失败'
};

// 同步任务类型
export const SUBSCRIBE_TYPE_APP = 'APP';
export const SUBSCRIBE_TYPE_API = 'RESOURCE';
export const SUBSCRIBE_TYPE_WEAKNESS = 'WEAKNESS';
export const SUBSCRIBE_TYPE_RISK = 'RISK';
export const SUBSCRIBE_TYPE_EVENT = 'EVENT';
export const SUBSCRIBE_TYPE_MAP = {
  [SUBSCRIBE_TYPE_API]: 'API清单',
  [SUBSCRIBE_TYPE_APP]: '应用清单',
  [SUBSCRIBE_TYPE_WEAKNESS]: '弱点清单',
  [SUBSCRIBE_TYPE_RISK]: '风险清单',
  [SUBSCRIBE_TYPE_EVENT]: '审计日志'
};

// 大屏背景框定位列表
export const BOX_COORD_LIST = [
  [-105, 120],
  [140, 120]
];

// 大屏标题定位列表
export const TEXT_COORD_LIST = [
  [-105, 150],
  [140, 150]
];

// 大屏右侧应用域名节点列表
const RIGHT_COORD_LIST_5 = [
  [90, 110],
  [140, 125],
  [185, 110],
  [90, 135],
  [185, 135]
];
const RIGHT_COORD_LIST_4 = [
  [105, 110],
  [175, 110],
  [105, 140],
  [175, 140]
];
const RIGHT_COORD_LIST_3 = [
  [140, 135],
  [175, 120],
  [105, 120]
];
const RIGHT_COORD_LIST_2 = [
  [175, 125],
  [105, 125]
];
const RIGHT_COORD_LIST_1 = [[140, 115]];
export const RIGHT_COORD_MAP = {
  1: RIGHT_COORD_LIST_1,
  2: RIGHT_COORD_LIST_2,
  3: RIGHT_COORD_LIST_3,
  4: RIGHT_COORD_LIST_4,
  5: RIGHT_COORD_LIST_5
};

// 大屏左侧应用域名节点列表
const LEFT_COORD_LIST_5 = [
  [-60, 110],
  [-105, 125],
  [-150, 110],
  [-60, 140],
  [-150, 140]
];
const LEFT_COORD_LIST_4 = [
  [-60, 110],
  [-150, 110],
  [-60, 140],
  [-150, 140]
];
const LEFT_COORD_LIST_3 = [
  [-150, 120],
  [-60, 120],
  [-105, 135]
];
const LEFT_COORD_LIST_2 = [
  [-150, 125],
  [-60, 125]
];
const LEFT_COORD_LIST_1 = [[-105, 115]];
export const LEFT_COORD_MAP = {
  1: LEFT_COORD_LIST_1,
  2: LEFT_COORD_LIST_2,
  3: LEFT_COORD_LIST_3,
  4: LEFT_COORD_LIST_4,
  5: LEFT_COORD_LIST_5
};

export const CYCLE_TIMES = [
  { label: '周一', value: 2 },
  { label: '周二', value: 3 },
  { label: '周三', value: 4 },
  { label: '周四', value: 5 },
  { label: '周五', value: 6 },
  { label: '周六', value: 7 },
  { label: '周日', value: 1 }
];

// 视图moduleId
export const API_MODULE_ID = 'ApiViewMenuProcess';
export const APP_MODULE_ID = 'AppViewMenuProcess';
export const DATA_MODULE_ID = 'DataViewMenuProcess';
export const ACCOUNT_MODULE_ID = 'AccountViewMenuProcess';
export const FILE_MODULE_ID = 'FileMenuProcess';
export const IP_MODULE_ID = 'IpViewMenuProcess';
export const RISK_MODULE_ID = 'AggRiskViewMenuProcess';
export const EXCEPTION_MODULE_ID = 'RiskViewMenuProcess';
export const THREAT_MODULE_ID = 'ThreatInfoViewMenuProcess';
export const WEAKNESS_MODULE_ID = 'WeaknessViewMenuProcess';

/**
 * JS中颜色
 */
export const CHART_COLOR_BG_GREY = '#efefef';
export const CHART_COLOR_BORDER_GREY = '#ECECEE';
export const CHART_COLOR_TEXT_LIGHT_GREY = '#B7C2D1';
export const CHART_COLOR_TEXT_REGULAR_COLOR = '#606266';
export const CHART_COLOR_TEXT_PRIMARY_COLOR = '#303133';
export const CHART_COLOR_LIGHTER_PURPLE = '#dfe2f5';
export const CHART_COLOR_LIGHT_PURPLE = '#b5c1eb';
export const CHART_COLOR_DEEP_PURPLE = '#839aee';
export const CHART_COLOR_PURPLE = '#7a7ec0';
export const CHART_COLOR_DEEPER_PURPLE = '#BE32E4';
export const CHART_COLOR_LIGHT_BLUE = '#71bffe';
export const CHART_COLOR_HIGHLIGHT_BLUE = '#3FB6E9';
export const CHART_COLOR_LIGHTER_BLUE = '#6AA4CF';
export const CHART_COLOR_THEME_COLOR = '#47a5e7';
export const CHART_COLOR_BLUE = '#437dd4';
export const CHART_COLOR_DEEP_BLUE = '#285395';
export const CHART_COLOR_RED = '#c23c35';
export const CHART_COLOR_ORANGE = '#eb8e48';
export const CHART_COLOR_GREEN = '#97d67a';
export const CHART_COLOR_DEEP_GREEN = '#2B908F';
export const CHART_COLOR_LIGHT_YELLOW = '#feddb3';
export const CHART_COLOR_YELLOW = '#e8cf61';
export const CHART_COLOR_TRANSPARENT = 'rgba(255,255,255,0)';
export const CHART_COLOR_SHALLOW_GREY = 'rgba(255,255,255,0.2)';
export const CHART_COLOR_SHALLOW_BLUE = 'rgba(71,148,255,0.06)';
export const CHART_COLOR_SITUATION_PIE_LIST = [
  '#6568CE',
  '#577AFF',
  '#3793D8',
  '#309A91',
  '#D59F10',
  '#e64f63',
  '#ebec59',
  '#77dced'
];
export const CHART_COLOR_SANKEY_LIST = ['#c2e8be', '#a7c7df', '#f8aca6'];
export const LABEL_DEFAULT_BG_COLOR = '#F9F9F9';
export const LABEL_BORDER_COLOR_LIST = [
  '#709CF9',
  '#27CC89',
  '#9367DA',
  '#F2C02B',
  '#EB7F65',
  '#37C9C9',
  '#FA6AAA'
];
export const LABEL_COLOR_ACTIVE_LIST = [
  //高亮颜色列表
  'rgba(112,156,249,0.15)',
  'rgba(39,204,137,0.15)',
  'rgba(147,103,218,0.15)',
  'rgba(242,192,43,0.15)',
  'rgba(235,127,101,0.15)',
  'rgba(55,201,201,0.15)',
  'rgba(250,106,170,0.15)'
];

// 数据主体类型
export const ENTITY_TYPE_DATA = 'data';

// 快捷时间筛选
export const DATE_PICKER_OPTIONS = {
  disabledDate(time) {
    return time.getTime() > moment().endOf('day').unix() * 1000;
  },
  shortcuts: [
    {
      text: '最近24小时',
      onClick(picker) {
        const end = new Date();
        const start = new Date();
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 1);
        picker.$emit('pick', [start, end]);
      }
    },
    {
      text: '最近一周',
      onClick(picker) {
        const end = new Date();
        const start = new Date();
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
        picker.$emit('pick', [start, end]);
      }
    },
    {
      text: '最近一个月',
      onClick(picker) {
        const end = new Date();
        const start = new Date();
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
        picker.$emit('pick', [start, end]);
      }
    },
    {
      text: '最近三个月',
      onClick(picker) {
        const end = new Date();
        const start = new Date();
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
        picker.$emit('pick', [start, end]);
      }
    },
    {
      text: '最近半年',
      onClick(picker) {
        const end = new Date();
        const start = new Date();
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 180);
        picker.$emit('pick', [start, end]);
      }
    },
    {
      text: '最近一年',
      onClick(picker) {
        const end = new Date();
        const start = new Date();
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 360);
        picker.$emit('pick', [start, end]);
      }
    }
  ]
};

const MONTH_LIST = [];
for (let i = 1; i < 32; i++) {
  MONTH_LIST.push({ label: i, value: String(i) });
}
MONTH_LIST.push({ label: '每月最后一天', value: 'L' });
export default MONTH_LIST;
