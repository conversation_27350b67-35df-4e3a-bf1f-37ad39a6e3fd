/**
 * 路由路径
 */

// 通用的错误页
export const PAGE_URL_ERROR = '/error';

// 登录
export const PAGE_URL_LOGIN = '/login';

/**
 * 项目layout
 */
export const PAGE_URL_OVERVIEW = '/layout/overview';

/**
 * home
 */
export const PAGE_URL_HOME = '/home';

export const PAGE_URL_USER_PROFILE = '/user';
export const PAGE_URL_FORGET_PASSWORD = '/password';

// 账号管理
export const PAGE_URL_ACCOUNT_MANAGEMENT = '/account/index';

// 角色管理
export const PAGE_URL_ROLE_MANAGEMENT = '/role';

// 数据管理
export const PAGE_URL_DATA_MANAGEMENT = '/data';

//菜单管理
export const PAGE_URL_MENU_MANAGEMENT = '/menu-manage';

// 安全配置
export const PAGE_URL_SAFE_CONFIG = '/safe-config';
// 操作日志
export const PAGE_URL_AUDIT_RECORD = '/audit/audit-record';

//系统配置
export const PAGE_URL_SYSTEM_CONFIG = '/system/config';

export const PAGE_URL_SYSTEM_UPDATE = '/system/update';
export const PAGE_URL_SYSTEM_AUTH = '/system/auth';
export const PAGE_URL_SYSTEM_DATA_SEND = '/system/data-send';
export const PAGE_URL_SYSTEM_NOTICE = '/system/notice';
export const PAGE_URL_SYSTEM_BASE_CONFIG = '/system/base-config';
export const PAGE_URL_SYSTEM_OPEN_API = '/system/open-api';
export const PAGE_URL_SYSTEM_PAGE_DEV = '/system/page-dev';
/**
 *
 *
 * AMIS
 */
export const PAGE_URL_AMIS = '/amis';
export const PAGE_URL_AMIS_EDIT = '/amis_edit';
export const PAGE_URL_AMIS_PRE = '/page_pre';

export const PAGE_URL_DESGIN = '/desgin';

export const PAGE_URL_APP_MANAGE = '/application_manage';
export const PAGE_URL_CONFIG_MANAGE = '/config_manage';
