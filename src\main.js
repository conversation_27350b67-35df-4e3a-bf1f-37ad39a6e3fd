import Vue from 'vue';
import App from './App.vue';
import ElementUI from 'element-ui';
import router from './router';
import store from './store';
import 'element-ui/lib/theme-chalk/index.css';
import '@/assets/css/common.less';
import '@/assets/css/cover.less';
import '@/api/index.js';
import '@/utils/api-table-formatter';
import QzElement from '@quanzhiFE/qz-frontend';
import '@quanzhiFE/qz-frontend/dist/index.css';
import '@/assets/css/font-awesome/css/fontawesome.min.css';
import '@/assets/css/font-awesome/css/solid.min.css';
import '@/components/qz-api-drawer';
import '@/utils/handle-table-response';
import xss from 'xss';
import VJsoneditor from 'v-jsoneditor';
import VScaleScreen from 'v-scale-screen';
import DataLabelTemplate from '@/components/data-label-template.vue';
import QzTips from '@/components/qz-tips.vue';
import staffLevelTag from '@/components/staff-level-tag.vue';
import SvgIcon from '@/components/svg-icon';
import ApiTable from '@/components/api-table';
import TableColumn from '@/components/api-table/table-column';
import TableToolRegister from '@/components/api-table/table-tool-register';
import TableSearchItem from '@/components/api-table/table-search-item';
import ColumnOverflow from '@/components/column-overflow';
import RectangleLevelTag from '@/components/rectangle-level-tag';
import QzLayoutTemplate from '@/components/qz-layout-template';
import JsonSchemaExtraParam from '@/components/json-schema/extra-param-widget.vue';
import QzView from '@/components/qz-view/main.vue';
import LabelShow from '@/components/label-show.vue';
import ReactProxy from '@/components/react-proxy.vue';
import VueForm from '@lljj/vue-json-schema-form';
import { openPage } from './utils/dom-utils';

Vue.config.productionTip = false;
Vue.use(ElementUI);
Vue.use(QzElement);
Vue.use(ApiTable);
Vue.use(TableColumn);
Vue.use(TableToolRegister);
Vue.use(TableSearchItem);
Vue.use(VJsoneditor);
Vue.use(VScaleScreen);
Vue.component('QzTips', QzTips);
Vue.component('DataLabelTemplate', DataLabelTemplate);
Vue.component('staffLevelTag', staffLevelTag);
Vue.component('SvgIcon', SvgIcon);
Vue.component('ColumnOverflow', ColumnOverflow);
Vue.component('RectangleLevelTag', RectangleLevelTag);
Vue.component('QzLayoutTemplate', QzLayoutTemplate);
Vue.component('ExtraParam', JsonSchemaExtraParam);
Vue.component('QzView', QzView);
Vue.component('LabelShow', LabelShow);
Vue.component('ColumnOverflow', ColumnOverflow);
Vue.component('ReactProxy', ReactProxy);
Vue.component('VueForm', VueForm);
//	eslint-disable-next-line
Vue.prototype.$linkTo = function ({ path, query, type }) {
  if (typeof arguments[0] != 'object') {
    path = arguments[0];
  }
  query = query || {};
  type = type || '_self';
  const routeData = router.resolve({
    path: path,
    query: query || {}
  });

  // 使用window.open打开_blank页面时，新开页会共享当前页的内存和CPU
  openPage(routeData.href, type);
};
//	eslint-disable-next-line
// Vue.prototype.$toPage = function ({ id, query, type }) {
//   if (typeof arguments[0] != 'object') {
//     id = arguments[0];
//   }
// };
//解决使用v-html时报错
Vue.prototype.xss = xss;
new Vue({
  router,
  store,
  $route() {
    this.$dialogAlert.closeAll();
    this.$DrawAlert.closeAll();
  },
  render: (h) => h(App)
}).$mount('#app');
