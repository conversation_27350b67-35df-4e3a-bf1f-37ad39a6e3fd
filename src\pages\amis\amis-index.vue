<template>
  <div class="amis-container">
    <api-table
      ref="table"
      table-id="openApiList"
      :data-source="getDataList"
      :border="true"
      :search-input-options="{
        key: 'name',
        placeholder: '请输入模版名称查询'
      }"
      toolsLayout="searchInput,openApi"
    >
      <api-table-tool-register id="openApi">
        <el-button
          type="primary"
          size="mini"
          class="mr10"
          @click="editAmis('add', {})"
        >
          新增模版
        </el-button>
      </api-table-tool-register>
      >
      <api-table-column label="模版名称" prop="name"></api-table-column>
      <api-table-column label="模版描述" prop="description">
        <template slot-scope="{ row }">
          <span>{{ row.description || '--' }}</span>
        </template>
      </api-table-column>
      <!-- <qz-table-column label="发布状态" prop="publishStatus">
          <template slot-scope="{row}">
            <span>{{row.publishStatus?'已发布':'未发布'}}</span>
          </template>
        </qz-table-column> -->
      <api-table-column label="操作" width="200">
        <template slot-scope="{ row }">
          <span class="action-link" @click="editAmis('edit', row)">编辑</span>
          <!-- <span class="action-link" @click="publishAmis(row)">{{row.publishStatus?'取消发布':'发布'}}</span> -->
          <span class="action-link danger" @click="delAmis(row)">删除</span>
        </template>
      </api-table-column>
    </api-table>
  </div>
</template>

<script>
import {
  PAGE_URL_AMIS_EDIT,
  PAGE_URL_AMIS_PRE
} from '@/constant/page-url-constants';
import {
  deleteStrategy,
  getStrategyDetail,
  getStrategyList
} from '@/service/strategy-template';
export default {
  data() {
    return {
      searchInfo: {},
      checkedRows: []
    };
  },

  components: {},
  computed: {
    searchSchemas() {
      return [
        {
          key: 'name',
          name: '模版名称',
          label: '',
          type: 'text',
          width: '25%',
          placeholder: '请输入模版名称查询'
        }
      ];
    }
  },
  mounted() {},

  methods: {
    getDataList(params) {
      const param = {
        pageSize: params.limit,
        pageNum: params.page,
        name: params.name
      };
      return getStrategyList(param);
    },
    search(searchInfo) {
      this.$refs['table'].reload();
    },
    editAmis(type, row) {
      localStorage.removeItem('amisdata');
      if (type == 'add' || type == 'edit') {
        this.$linkTo({
          path: PAGE_URL_AMIS_EDIT,
          type: '_blank',
          query: {
            id: row?.id
          }
        });
      } else {
        this.$linkTo({
          path: PAGE_URL_AMIS_PRE,
          query: {
            type,
            id: row?.id
          },
          type: '_blank'
        });
      }
    },
    delAmis(row) {
      this.$confirm('确定删除吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteStrategy({ id: row.id })
          .then((res) => {
            this.$message({
              type: 'success',
              message: '删除成功'
            });
            this.$refs['table'].reload();
          })
          .catch((err) => {
            this.$message({
              type: 'error',
              message: err.msg
            });
          });
      });
    }
  }
};
</script>
<style lang="less" scoped>
.amis-container {
  padding: 20px;
  background: #fff;
}
</style>
