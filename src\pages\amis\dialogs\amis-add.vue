<template>
  <div class="amis-edit">
    <el-form
      ref="form"
      :model="amisForm"
      :rules="rules"
      size="small"
      label-position="right"
      label-width="100px"
    >
      <el-form-item label="模版名称:" prop="name">
        <el-input v-model="amisForm.name" placeholder="请输入模版名称" />
      </el-form-item>
      <el-form-item label="模版描述:" prop="description">
        <el-input
          v-model="amisForm.description"
          :rows="4"
          placeholder="请输入模版描述"
        />
      </el-form-item>
      <!--      <el-form-item label="是否内置" prop="builtIn">-->
      <!--        <el-radio-group v-model="amisForm.builtIn">-->
      <!--          <el-radio :label="true">是</el-radio>-->
      <!--          <el-radio :label="false">否</el-radio>-->
      <!--        </el-radio-group>-->
      <!--      </el-form-item>-->
      <el-form-item label="页面Code" prop="code">
        <el-input
          :disabled="isDisabled"
          v-model="amisForm.code"
          placeholder="请输入页面Code"
        />
      </el-form-item>
      <el-form-item label="默认跳转步骤" prop="step">
        <el-input-number
          v-model="amisForm.step"
          :min="1"
          :max="10"
          label="步骤"
        ></el-input-number>
        <div class="danger">仅限步骤条使用</div>
      </el-form-item>
    </el-form>
    <div class="text-right">
      <el-button size="small" @click="cancel">取消</el-button>
      <el-button type="primary" size="small" @click="save">提交</el-button>
    </div>
  </div>
</template>

<script>
import { saveStrategy } from '@/service/strategy-template';
import { config } from './test';
export default {
  props: ['params'],
  data() {
    return {
      config,
      amisForm: {
        name: '',
        description: '',
        code: '',
        step: 1,
        status: 1
      },
      rules: {
        name: [{ required: true, message: '请输入模版名称', trigger: 'blur' }],
        code: [{ required: true, message: '请输入页面Code', trigger: 'blur' }]
      },
      isDisabled: false
    };
  },
  watch: {},

  components: {},
  async mounted() {
    if (this.params?.info?.id) {
      this.isDisabled = true;
      this.amisForm.name = this.params?.info?.name || '';
      this.amisForm.description = this.params?.info?.description || '';
      this.amisForm.code = this.params?.info?.code || '';
      this.amisForm.step = this.params?.info?.extend?.step || 1;
    }
  },

  methods: {
    cancel() {
      this.params.closeOutDrawer();
    },
    save() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          // let fun = saveStrategy();
          const p = {
            ...this.amisForm,
            extend: JSON.stringify({ step: this.amisForm.step }),
            pageConfig: JSON.stringify(this.params?.info?.pageConfig)
          };
          if (this.params?.info?.id) {
            p.id = this.params?.info?.id;
          }
          saveStrategy(p)
            .then((res) => {
              this.$message.success(res?.msg || '操作成功');
              this.params.closeOutDrawer();
            })
            .catch((err) => {
              this.$message.error(err?.msg || '操作失败');
              console.log(err);
            });
        }
      });
    }
  }
};
</script>
<style lang="less" scoped>
.amis-edit {
  padding: 20px;
  .treeContent {
    padding: 10px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
  }
}
</style>
