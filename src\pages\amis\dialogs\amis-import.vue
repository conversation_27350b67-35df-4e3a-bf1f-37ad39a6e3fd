<template>
  <div class="report-import">
    <el-form>
      <el-form-item label="导入文件：" label-width="120px">
        <el-upload
          ref="upload"
          :action="API_AMIS_IMPORT"
          :auto-upload="false"
          name="file"
          :file-list="fileList"
          :multiple="false"
          :show-file-list="true"
          accept=".zip"
          :on-change="handleChangeStatus"
          :on-success="handleSuccess"
          :limit="1"
        >
          <el-button size="small">
            <qz-icon class="icon-upload icon"></qz-icon>点击上传
          </el-button>
          <div slot="tip" class="el-upload__tip tips">支持扩展名：.zip</div>
        </el-upload>
      </el-form-item>
    </el-form>
    <div class="text-right mt20">
      <el-button size="small" @click="cancel">取消</el-button>
      <el-button size="small" type="primary" @click="save"> 确定 </el-button>
    </div>
  </div>
</template>

<script>
import { API_AMIS_IMPORT } from '@/constant/data-url-constants';
export default {
  props: ['params'],
  data() {
    return {
      API_AMIS_IMPORT,
      fileList: []
    };
  },
  mounted() {},
  methods: {
    cancel() {
      this.params.close();
    },
    handleChangeStatus(file, fileList) {
      this.fileList = fileList;
    },

    handleSuccess(res) {
      if (res.success) {
        // this.reportLoading = true;
        this.$message.success('导入成功');
        this.params.callBack();
        this.params.close();
      } else {
        this.$message.error(res.msg || '导入失败');
      }
    },
    save() {
      if (!this.fileList.length) {
        this.$message.error('请上传文件');
        return;
      }
      this.$refs.upload.submit();
    }
  }
};
</script>

<style lang="less" scoped>
.report-import {
  .btn {
    border: none;
  }

  .tips {
    color: #888;
  }
}
</style>
