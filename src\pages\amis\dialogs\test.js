export const config = {
  id: 'u:d861c93d4fa4',
  asideResizor: false,
  pullRefresh: {
    disabled: true
  },
  body: [
    {
      type: 'wizard',
      steps: [
        {
          title: '基本配置',
          body: [
            {
              type: 'tpl',
              tpl: '基本信息',
              inline: true,
              wrapperComponent: '',
              id: 'u:6280db6039d5',
              themeCss: {
                baseControlClassName: {
                  'font:default': {
                    fontSize: '14px'
                  }
                }
              }
            },
            {
              type: 'input-text',
              label: '规则名称',
              name: 'policyName',
              id: 'u:972d4d6d45fd',
              mode: 'horizontal',
              labelAlign: 'left',
              required: true,
              themeCss: {
                labelClassName: {
                  'padding-and-margin:default': {
                    marginLeft: '1.4285714285714286rem'
                  }
                },
                inputControlClassName: {
                  'padding-and-margin:default': {}
                }
              },
              autoFill: {},
              onEvent: {
                change: {
                  weight: 0,
                  actions: []
                }
              }
            },
            {
              type: 'select',
              label: '风险等级',
              name: 'riskLevel',
              id: 'u:f8d827992c66',
              multiple: false,
              mode: 'horizontal',
              labelAlign: 'left',
              themeCss: {
                labelClassName: {
                  'padding-and-margin:default': {
                    marginLeft: '1.4285714285714286rem'
                  }
                }
              },
              source: {
                method: 'get',
                url: '/app-operation/api/event/risk/list',
                requestAdaptor: '',
                adaptor:
                  'return {\n    status:0,\n    msg:"",\n    data:{\n        items:response.data.map(item=>{\n            return {\n                label:item.value,\n                value:item.code\n            }\n        })\n    }\n}',
                messages: {}
              },
              onEvent: {}
            },
            {
              type: 'input-text',
              label: '规则描述',
              name: 'policyDesc',
              id: 'u:9b302ad4f3f7',
              mode: 'horizontal',
              labelAlign: 'left',
              themeCss: {
                labelClassName: {
                  'padding-and-margin:default': {
                    marginLeft: '1.4285714285714286rem'
                  }
                }
              }
            },
            {
              type: 'textarea',
              label: '整改建议',
              name: 'suggest',
              id: 'u:bc7432b43ab3',
              minRows: 3,
              maxRows: 20,
              mode: 'horizontal',
              labelAlign: 'left',
              themeCss: {
                labelClassName: {
                  'padding-and-margin:default': {
                    marginLeft: '1.4285714285714286rem'
                  }
                }
              }
            },
            {
              type: 'tpl',
              tpl: '配置信息',
              inline: true,
              wrapperComponent: '',
              id: 'u:f833c7b03e86'
            },
            {
              type: 'select',
              label: '策略分类',
              name: 'classifyId',
              id: 'u:f0cc0c3cdfcc',
              multiple: false,
              required: true,
              mode: 'horizontal',
              labelAlign: 'left',
              source: {
                method: 'post',
                url: '/app-operation/api/ops-risk-policy/classify/list',
                requestAdaptor: '',
                adaptor:
                  'return{\n    status:0,\n    msg:"success",\n    data:{\n        text:\'selectval\',\n        items:response.data.rows.map(item=>{\n            return {\n                label:item.name,\n                value:item.id\n            }\n        })\n    }\n}',
                messages: {},
                sendOn: '',
                dataType: 'json',
                replaceData: false,
                silent: true,
                data: {
                  limit: 10000,
                  page: 1
                }
              },
              themeCss: {
                labelClassName: {
                  'padding-and-margin:default': {
                    marginLeft: '1.4285714285714286rem'
                  }
                }
              }
            },
            {
              type: 'select',
              label: '主体',
              name: 'subjectId',
              id: 'u:1a1d946c72ca',
              multiple: false,
              required: true,
              mode: 'horizontal',
              labelAlign: 'left',
              source: {
                method: 'post',
                url: '/app-operation/api/ops-risk-policy/subject/list',
                requestAdaptor: '',
                adaptor:
                  'return{\n    status:0,\n    msg:\'\',\n    data:{\n        items:response.data.rows.map(item=>{\n            let fnames="--";\n            if(item?.asset?.assetFields){\n                fnames=item?.asset?.assetFields?.map(item=>item.assetFieldName)?.join()\n            }\n            return {\n                assetFieldNames:fnames,\n                label:item.name,\n                value:item.id\n            }\n        })\n    }\n}',
                messages: {},
                dataType: 'json',
                silent: true,
                data: {
                  limit: 10000,
                  page: 1
                },
                replaceData: false
              },
              themeCss: {
                labelClassName: {
                  'padding-and-margin:default': {
                    marginLeft: '1.4285714285714286rem'
                  }
                }
              },
              onEvent: {
                change: {
                  weight: 0,
                  actions: [
                    {
                      ignoreError: true,
                      script:
                        "/* 自定义JS使用说明：\n  * 1.动作执行函数doAction，可以执行所有类型的动作\n  * 2.通过上下文对象context可以获取当前组件实例，例如context.props可以获取该组件相关属性\n  * 3.事件对象event，在doAction之后执行event.stopPropagation();可以阻止后续动作执行\n*/\n//根据componentId更新组件的值，componentId可在编辑器左侧查看\n\nconst props = context?.props\nsetTimeout(()=>{\n  let info = props.options.find(item=>item.value==props.formStore.data.subjectId);\n  doAction({\n    actionType: 'setValue',\n    componentId:\"u:77e8dc9c06c5\",\n    args: {\n      value: info?info.assetFieldNames:'--'\n    }\n  });\n  event.stopPropagation();\n},500)\n\n",
                      actionType: 'custom',
                      args: {}
                    }
                  ]
                }
              }
            },
            {
              type: 'input-text',
              label: '主体映射字段',
              name: 'subjectField',
              id: 'u:77e8dc9c06c5',
              mode: 'horizontal',
              labelAlign: 'left',
              themeCss: {
                labelClassName: {
                  'padding-and-margin:default': {
                    marginLeft: '1.4285714285714286rem'
                  }
                }
              },
              readOnly: true
            },
            {
              type: 'select',
              label: '日志类型',
              name: 'logTypes',
              id: 'u:18a483abac27',
              multiple: true,
              required: true,
              mode: 'horizontal',
              labelAlign: 'left',
              source: {
                method: 'post',
                url: '/app-operation/api/data-process-policy/ops-account-risk-policy/log-type',
                requestAdaptor: '',
                adaptor:
                  "return{\n    status:0,\n    msg:'',\n    data:{\n        items:response.data.map(item=>{\n            return {\n                label:item.name,\n                value:item.id\n            }\n        })\n    }\n}",
                messages: {},
                dataType: 'json',
                silent: true
              },
              themeCss: {
                labelClassName: {
                  'padding-and-margin:default': {
                    marginLeft: '1.4285714285714286rem'
                  }
                }
              },
              checkAll: false
            }
          ],
          id: 'u:e3e71e611c25',
          mode: 'normal',
          horizontal: {
            leftFixed: 'normal'
          },
          initApi: {
            method: 'post',
            url: '/app-operation/api/data-process-policy/ops-account-risk-policy/detail',
            sendOn: '',
            responseData: null,
            data: null,
            dataType: 'json',
            requestAdaptor: 'api.data={\n    id:window.amis_id\n}\nreturn api;',
            adaptor:
              'return {\n    policyName:payload?.policyName,\n    riskLevel:payload?.riskLevel,\n    policyDesc:payload?.policyDesc,\n    suggest:payload?.suggest,\n    classifyId:payload?.classifyId,\n    subjectId:payload?.subjectId,\n    logTypes:payload?.logTypes?.join()\n}'
          },
          initFetch: true
        },
        {
          title: '告警模版',
          body: [
            {
              type: 'my-renderer-base-info',
              api: {
                url: '/app-operation/api/data-process-result/ops-account-risk-result/detail',
                method: 'post',
                data: ''
              },
              id: 'u:22f93f442ce8'
            },
            {
              type: 'my-renderer-alarm-info',
              api: {
                url: '/app-operation/api/data-process-result/ops-account-risk-result/detail',
                method: 'post',
                data: ''
              },
              id: 'u:57b9fc49db39'
            }
          ],
          mode: 'normal',
          id: 'u:37c72e9a71ca',
          initFetch: false
        },
        {
          title: '策略定义',
          items: [
            {
              type: 'input-text',
              name: 'var1',
              label: '文本'
            }
          ],
          mode: 'normal',
          id: 'u:27f76b140fb9',
          actions: [
            {
              type: 'submit',
              label: '提交',
              primary: true,
              id: 'u:ef2205cdc17c'
            }
          ],
          feat: 'Insert',
          body: [
            {
              type: 'my-renderer-task-setting',
              api: {
                url: '/app-operation/api/data-process-policy/ops-account-risk-policy/detail',
                method: 'post',
                data: '',
                odateTimep: 'a',
                otimep: 'b',
                mdateTimep: 's',
                mtimep: 'e'
              },
              id: 'u:b1b414ea93f5'
            }
          ],
          initFetch: false
        }
      ],
      id: 'u:0e31abc4dab7',
      mode: 'horizontal',
      onEvent: {
        finished: {
          weight: 0,
          actions: [
            {
              ignoreError: false,
              outputVar: 'responseResult',
              actionType: 'ajax',
              options: {},
              api: {
                url: '/app-operation/api/data-process-policy/ops-account-risk-policy/save',
                method: 'post',
                requestAdaptor:
                  '\napi.data = {\n  "pageCode":window.pageCode,\n  "policyName": context.policyName,\n  "policyDesc": context.policyDesc,\n  "cron":window.taskStorageType.taskCycle,\n  "riskLevel": context.riskLevel,\n  "rule": window.taskStorageType.code,\n  "suggest": context.suggest,\n  "enable": true,\n  "classifyId": context.classifyId,\n  "subjectId": context.subjectId,\n  "logTypes": context.logTypes.split(",")\n}\nif(context.tid){\n    api.data.id = context.tid\n}\n\n\nreturn api;',
                adaptor:
                  'if(response.success){\n    const event = new CustomEvent("amisEmit", {\n        detail:response,\n    });\n    window.dispatchEvent(event);\n}\nreturn{\n    status:response.status,\n    msg:response.msg,\n    data:response.data\n}',
                messages: {
                  success: '操作成功',
                  failed: '操作失败'
                },
                dataType: 'json'
              }
            }
          ]
        }
      },
      initFetch: true,
      initApi: {
        method: 'post',
        url: '/app-operation/api/lowCode/form/detailByCode',
        dataType: 'json',
        requestAdaptor:
          'api.data={\n    code:"SUBSCRIBE_RISK",\n    isReturnStep:window.isSkipStep\n}\nreturn api;',
        adaptor:
          'let step=1;\nif(payload?.extend?.step){\n    step=payload?.extend?.step||1;\n}else{\n    step=1;\n}\nreturn {\n    step:step\n}'
      }
    }
  ]
};
