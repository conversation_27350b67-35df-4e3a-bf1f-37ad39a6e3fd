<template>
  <div class="amis-report-editor">
    <div class="header">
      <div class="title">新增模板</div>
      <div class="toole">
        <el-button size="mini" type="primary" @click="save">保存</el-button>
        <el-button size="mini" type="primary" @click="pre">预览</el-button>
      </div>
    </div>
    <react-proxy
      :component="ReportEditor"
      :props="{ schema, onChange: handleSchemaChange }"
      class="body"
    ></react-proxy>
  </div>
</template>
<script>
import ReportEditor from '@/components-react/report-editor.jsx';
import { PAGE_URL_AMIS_PRE } from '@/constant/page-url-constants';
import { getStrategyDetail } from '@/service/strategy-template';

export default {
  data() {
    return {
      ReportEditor,
      schema: {},
      templateInfo: {},
      amisInfo: {
        pageConfig: {}
      }
    };
  },
  async mounted() {
    if (this.$route.query?.id) {
      const resAmisData = await getStrategyDetail({
        id: this.$route.query?.id
      });
      this.amisInfo = resAmisData.data;
      console.log(this.amisInfo);
      this.schema = JSON.parse(this.amisInfo.pageConfig);
      this.isShow = true;
    } else {
      this.schema = JSON.parse(localStorage.getItem('amisdata'));
    }
  },
  methods: {
    handleSchemaChange(schema) {
      this.schema = schema;
      this.amisInfo.pageConfig = schema;
    },
    save() {
      console.log('-----', this.amisInfo);
      this.$DrawAlert({
        params: {
          info: this.amisInfo,
          callBack(res) {
            //清除自定义中的绑定在window的数据
            window.taskStorageType = {};
            localStorage.setItem('amisdata', JSON.stringify(this.schema));
          }
        },
        title: '保存',
        width: 70,
        componentObj: {
          component: () => import('./dialogs/amis-add.vue')
        }
      });
    },
    pre() {
      localStorage.setItem('amisdata', JSON.stringify(this.schema));
      this.$linkTo({
        path: PAGE_URL_AMIS_PRE,
        query: {},
        type: '_blank'
      });
    }
  }
};
</script>

<style lang="less" scoped>
.amis-report-editor {
  height: calc(100vh - 105px);
  overflow: hidden;
  background: #fff;
  display: flex;
  flex-direction: column;
  .header {
    flex: none;
  }
  .body {
    flex: auto;
    overflow-y: auto;
  }
}
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #e0e0e0;
  padding: 5px 10px;
  .title {
    font-size: 16px;
    font-weight: bold;
  }
}
::v-deep .ae-Editor {
  height: 100%;
  .ae-RendererList-item .icon-box::before {
    display: none;
  }
}
</style>
