<template>
  <react-proxy
    :component="ReportEditor"
    :props="{
      onChange: handleSchemaChange
    }"
  ></react-proxy>
</template>
<script>
import ReportEditor from '@/components-react/report-editor.jsx';

export default {
  data() {
    return {
      ReportEditor,
      varedName: 'Vue'
    };
  },
  methods: {
    handleSchemaChange(schema) {
      console.log(schema);
    }
  }
};
</script>

<style lang="less" scoped>
::v-deep .ae-Editor {
  height: 100%;
  .ae-RendererList-item .icon-box::before {
    display: none;
  }
}
</style>
