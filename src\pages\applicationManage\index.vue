<template>
  <qz-pro-table :data-source="[{}]">
    <qz-table-column prop="name" label="应用名称" />
    <qz-table-column prop="province" label="描述" />
    <qz-table-column prop="age" label="创建时间" />
    <qz-table-column prop="salary" label="操作">
      <template slot-scope="{ row }">
        <el-button type="primary">编辑</el-button>
        <el-button type="danger">删除</el-button>
      </template>
    </qz-table-column>
  </qz-pro-table>
</template>

<script>
export default {
  data() {
    return {};
  },

  components: {},

  mounted() {},

  methods: {}
};
</script>
<style lang="less" scoped></style>
