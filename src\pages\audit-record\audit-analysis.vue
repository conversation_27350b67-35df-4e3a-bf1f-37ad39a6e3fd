<!--
 * @Fileoverview: 审计分析
 * @Description: 审计分析
-->
<template>
  <div class="audit-analysis" v-loading="isLoading">
    <qz-chart :option="dateChartOptions" height="350px"></qz-chart>
    <qz-chart :option="moduleChartOptions" height="700px"></qz-chart>
  </div>
</template>

<script>
import {
  getAuditRecord,
  getAuditRecordCriteria
} from '@/service/audit-record-service';
export default {
  data() {
    return {
      isLoading: false,
      dateChartOptions: {},
      moduleChartOptions: {},
      moduleSearchParams: [],
      dateMap: {},
      moduleMap: {}
    };
  },
  async mounted() {
    await getAuditRecordCriteria('MODULE')
      .then((res) => {
        this.moduleSearchParams = res.data
          .filter((item) => item.value && item.value !== '登录')
          .map((item) => item.value);
      })
      .catch((err) => {
        console.error(err);
        this.$message.error(err.msg || '获取模块枚举值失败');
      });
    this.init();
  },
  methods: {
    async init() {
      this.isLoading = true;
      let totalCnt = 0;
      await getAuditRecord({
        page: 1,
        limit: 1000,
        username: '',
        ip: '',
        module: this.moduleSearchParams,
        type: []
      })
        .then((res) => {
          totalCnt = res.data.totalCount;
          this.handleLogs(res.data.rows);
        })
        .catch((err) => {
          console.error(err);
          this.$message.error(err.msg || '获取日志失败');
        });
      const travelTimes = Math.floor(totalCnt / 1000) + 1;
      if (travelTimes > 1) {
        for (let i = 1; i < travelTimes; i++) {
          await getAuditRecord({
            page: i + 1,
            limit: 1000,
            username: '',
            ip: '',
            module: this.moduleSearchParams,
            type: []
          })
            .then((res) => {
              this.handleLogs(res.data.rows);
            })
            .catch((err) => {
              console.error(err);
              this.$message.error(err.msg || '获取日志失败');
            });
        }
      }
      this.paintDateChart();
      this.paintModuleChart();
      this.isLoading = false;
    },
    handleLogs(logs) {
      logs.forEach((item) => {
        const date = moment(item.gmtCreate).format('YYYY-MM-DD');
        if (this.dateMap[date]) {
          this.dateMap[date] += 1;
        } else {
          this.dateMap[date] = 1;
        }
        if (this.moduleMap[item.module]) {
          this.moduleMap[item.module].operateCnt += 1;
          if (this.moduleMap[item.module][item.type]) {
            this.moduleMap[item.module][item.type] += 1;
          } else {
            this.moduleMap[item.module][item.type] = 1;
          }
        } else {
          this.moduleMap[item.module] = {};
          this.moduleMap[item.module].operateCnt = 1;
          this.moduleMap[item.module][item.type] = 1;
        }
      });
    },
    paintDateChart() {
      const dateChartData = [];
      let maxDateCnt = 0;
      for (const date in this.dateMap) {
        maxDateCnt = Math.max(maxDateCnt, this.dateMap[date]);
        dateChartData.push([date, this.dateMap[date]]);
      }
      this.dateChartOptions = {
        toolbox: {
          show: true,
          top: 30,
          right: 100,
          bottom: 30,
          feature: {
            saveAsImage: {}
          }
        },
        title: {
          top: 30,
          left: 'center',
          text: '使用频次'
        },
        tooltip: {},
        visualMap: {
          min: 0,
          max: maxDateCnt,
          type: 'piecewise',
          orient: 'horizontal',
          left: 'center',
          top: 65
        },
        calendar: {
          top: 120,
          left: 100,
          right: 100,
          cellSize: ['auto', 20],
          range: moment().format('YYYY'),
          itemStyle: {
            borderWidth: 0.5
          },
          yearLabel: { show: false }
        },
        series: {
          type: 'heatmap',
          coordinateSystem: 'calendar',
          data: dateChartData
        }
      };
    },
    paintModuleChart() {
      const moduleChartData = [];
      for (const module in this.moduleMap) {
        const moduleData = {
          name: module,
          children: []
        };
        for (const type in this.moduleMap[module]) {
          if (type !== 'operateCnt') {
            moduleData.children.push({
              name: `${module}/${type}`,
              value: this.moduleMap[module][type]
            });
          }
        }
        moduleChartData.push(moduleData);
      }
      this.moduleChartOptions = {
        title: {
          top: 30,
          text: '模块使用统计',
          left: 'center'
        },
        toolbox: {
          show: true,
          top: 30,
          right: 100,
          feature: {
            saveAsImage: {},
            dataView: { readOnly: true }
          }
        },
        tooltip: {
          show: true
        },
        series: [
          {
            top: 80,
            left: 100,
            right: 100,
            bottom: 50,
            name: '模块使用统计',
            type: 'treemap',
            roam: 'move',
            label: {
              show: true
            },
            itemStyle: {
              borderColor: 'white'
            },
            levels: [
              {
                itemStyle: {
                  borderWidth: 0,
                  gapWidth: 5
                }
              },
              {
                itemStyle: {
                  gapWidth: 1
                }
              },
              {
                colorSaturation: [0.35, 0.5],
                itemStyle: {
                  gapWidth: 1,
                  borderColorSaturation: 0.6
                }
              }
            ],
            data: moduleChartData
          }
        ]
      };
    }
  }
};
</script>

<style lang="less" scoped>
.audit-analysis {
  margin-bottom: 20px;
}
</style>
