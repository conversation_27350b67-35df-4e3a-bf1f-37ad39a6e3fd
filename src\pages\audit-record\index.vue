<!--
 * @Fileoverview: 操作日志页面
 * @Description: 审计管理员-操作日志
-->
<template>
  <div class="audit-record">
    <api-table
      border
      title="操作日志"
      ref="table"
      table-id="audit-record"
      :data-source="getDataList"
      :search-input-options="{
        key: 'operationDesc',
        placeholder: '请输入具体内容进行查询'
      }"
      toolsLayout="searchInput, divider, filter, refresh, colConfig, divider, tools"
    >
      <api-table-tool-register id="tools">
        <el-button size="mini" type="primary" @click="downloadAuditLog">
          导出日志
        </el-button>
      </api-table-tool-register>
      <api-table-column
        :search-config="{
          type: 'datetimerange',
          label: '时间',
          startPlaceholder: '开始时间',
          endPlaceholder: '结束时间',
          pickerOptions: dateQuickChoose
        }"
        label="时间"
        prop="requestTime"
        min-width="230"
        formatter="formatTime"
      ></api-table-column>
      <api-table-column
        :search-config="{
          label: '操作账号'
        }"
        label="操作账号"
        prop="username"
        min-width="150"
      ></api-table-column>
      <api-table-column
        :search-config="{
          label: '操作IP'
        }"
        label="操作IP"
        prop="requestIp"
        min-width="160"
      ></api-table-column>
      <api-table-column
        label="具体内容"
        prop="operationDesc"
        min-width="300"
      ></api-table-column>
      <api-table-column
        label="操作模块"
        prop="module"
        :search-config="{
          type: 'multi-selection',
          options: moduleOptions
        }"
        min-width="160"
      ></api-table-column>
      <api-table-column
        label="操作类型"
        :search-config="{
          type: 'multi-selection',
          options: typeOptions
        }"
        prop="operationType"
        min-width="160"
      ></api-table-column>
    </api-table>
  </div>
</template>
<script>
import { convertTimestampToDateTime } from '@/utils/string-utils';
import {
  getAuditRecordCriteria,
  postAuditLogList
} from '@/service/audit-log-service';
import { DATA_URL_SYSLOG_EXPORT } from '@/constant/data-url-constants';
import axios from 'axios';
import moment from 'moment';

export default {
  data() {
    return {
      dateQuickChoose: {
        disabledDate(time) {
          return time.getTime() > moment().endOf('day').valueOf(); // 等同于 .unix() * 1000
        }
      },
      moduleOptions: [],
      typeOptions: []
    };
  },
  methods: {
    convertTimestampToDateTime,

    handlerConvertTimestampToDateTime(info) {
      return this.convertTimestampToDateTime(info.requestTime);
    },
    getDataList(params) {
      if (params.requestTime.length > 0) {
        params.startTime = moment(params.requestTime[0]).valueOf();
        params.endTime = moment(params.requestTime[1]).valueOf();
      }
      const p = {
        startTime: params.startTime,
        endTime: params.endTime,
        username: params.username,
        operationDesc: params.operationDesc,
        requestIp: params.requestIp,
        operation: params.operation,
        modules: params.module,
        operationTypes: params.operationType,

        requestParams: params.requestParams,
        pageNum: params.page,
        pageSize: params.limit
      };
      return postAuditLogList(p);
    },
    // exportRecords() {
    //   const params = this.$refs.table.getRequestParams();
    //   if (params.gmtCreate && params.gmtCreate.length > 0) {
    //     params.startTime = moment(params.gmtCreate[0]).valueOf();
    //     params.endTime = moment(params.gmtCreate[1])
    //       .add({
    //         hours: 23,
    //         minutes: 59,
    //         seconds: 59
    //       })
    //       .valueOf();
    //   }
    //   const queryString = qs.stringify({
    //     page: params.page,
    //     limit: 10000,
    //     // params: JSON.stringify({
    //     //   startDate: params.startDate,
    //     //   endDate: params.endDate,
    //     //   username: params.username,
    //     //   ip: params.ip,
    //     //   operation: params.operation,
    //     //   modules: params.module,
    //     //   types: params.type
    //     // })
    //   });
    //   let p = {
    //     startTime: params.startDate,
    //     endTime: params.endDate,
    //     username: params.username,
    //     operationDesc: params.operationDesc,
    //     ip: params.ip,
    //     operation: params.operation,
    //
    //     requestParams:params.requestParams,
    //     pageNum:params.page,
    //     pageSize:params.limit
    //   }
    //   console.log('------',postAuditLogExport(p))
    //   postAuditLogExport(p).then(response => {
    //     // 创建一个 Blob 对象
    //     const blob = new Blob([response.data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
    //     // 创建一个 URL 对象
    //     const url = window.URL.createObjectURL(blob);
    //     // 创建一个 <a> 元素
    //     const link = document.createElement('a');
    //     link.href = url;
    //     // 设置文件名
    //     link.setAttribute('download', 'audit_log_export.xlsx');
    //     // 模拟点击下载
    //     link.click();
    //     // 释放 URL 对象
    //     window.URL.revokeObjectURL(url);
    //   }).catch(error => {
    //     console.error('下载失败:', error);
    //     this.$message.error('下载失败，请稍后重试');
    //   });
    //
    //   // const downLoadPath = `${DATA_URL_SYSLOG_EXPORT}?${queryString}`;
    //   // var openNewWindow = window.open(downLoadPath);
    //   // if (
    //   //   !openNewWindow ||
    //   //   openNewWindow.closed ||
    //   //   typeof openNewWindow.closed === 'undefined'
    //   // ) {
    //   //   this.$alert(
    //   //     '<a class="exportHrefBtn" href="' +
    //   //       downLoadPath +
    //   //       '" download="">生成文件成功，请点击下载</a>'
    //   //   );
    //   // }
    // },
    getFilterData() {
      getAuditRecordCriteria().then(
        (res) => {
          this.moduleOptions = (res.data?.[0]?.modules || []).map((item) => ({
            label: item,
            value: item
          }));

          this.typeOptions = (res.data?.[0]?.operationTypes || []).map(
            (item) => ({
              label: item,
              value: item
            })
          );
        },
        (err) => {
          console.error(err);
          this.$message.error(err.msg || '获取数据失败');
        }
      );
    },
    downloadAuditLog() {
      const params = this.$refs.table.getRequestParams();

      const p = {
        startDate: params.startDate,
        endDate: params.endDate,
        username: params.username,
        operationDesc: params.operationDesc,
        ip: params.ip,
        operation: params.operation,

        requestParams: params.requestParams,
        pageNum: params.page,
        pageSize: params.limit
      };
      axios({
        url: DATA_URL_SYSLOG_EXPORT, // 👉 改成你实际的后端接口地址
        method: 'post',
        data: p,
        responseType: 'blob' // 👈 一定要 blob，才能处理文件流
      })
        .then((response) => {
          // 解析文件名（后端 Content-Disposition 头已设置）
          let fileName = '审计日志.csv';
          const disposition = response.headers['content-disposition'];
          if (disposition) {
            const match = disposition.match(/filename="?([^"]+)"?/);
            if (match && match[1]) {
              fileName = decodeURIComponent(match[1]);
            }
          }

          // 创建 blob 对象并生成下载链接
          const blob = new Blob([response.data], {
            type: 'text/csv;charset=utf-8'
          });
          const url = window.URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.href = url;
          a.download = fileName;
          a.style.display = 'none';
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
          window.URL.revokeObjectURL(url);
        })
        .catch((error) => {
          console.error('导出失败', error);
        });
    }
  },
  mounted() {
    this.getFilterData();
  }
};
</script>
<style lang="less">
.audit-record {
  .header {
    margin-bottom: 15px;
    font-size: 14px;
    color: @text-regular-color;
  }
  .flex {
    display: flex;
    align-items: center;
  }
  .flex-none {
    flex: none;
  }
  .align-start {
    align-items: flex-start;
  }
}
.el-picker-panel.el-date-range-picker {
  z-index: 3001 !important;
}
</style>
