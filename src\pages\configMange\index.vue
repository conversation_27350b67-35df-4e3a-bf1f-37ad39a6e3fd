<template>
  <div class="desgin-container">
    <div class="desgin-header">
      <el-button>生成应用</el-button>
    </div>
    <div class="desgin-item">
      <div class="desgin-item-title mb20">登录模块</div>
      <div class="desgin-item-content">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>卡片名称</span>
            <el-button style="float: right; padding: 3px 0" type="text"
              >操作按钮</el-button
            >
          </div>
          <div v-for="o in 4" :key="o" class="text item">
            {{ '列表内容 ' + o }}
          </div>
        </el-card>
      </div>
    </div>
    <div class="desgin-item">
      <div class="desgin-item-title mb20">AMIS</div>
      <div class="desgin-item-content">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>AMIS配置</span>
            <el-button
              style="float: right; padding: 3px 0"
              type="text"
              @click="editAmis"
              >添加</el-button
            >
          </div>
          <div>低代码配置</div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script>
import {
  PAGE_URL_AMIS_EDIT,
  PAGE_URL_AMIS_PRE,
  PAGE_URL_DESGIN
} from '@/constant/page-url-constants';
export default {
  data() {
    return {};
  },

  components: {},

  mounted() {},

  methods: {
    editAmis() {
      window.open(PAGE_URL_AMIS_EDIT, '_blank');
      // this.$router.push({
      //     path:PAGE_URL_AMIS_EDIT,

      // })
    }
  }
};
</script>
<style lang="less" scoped>
.desgin-container {
  .desgin-header {
    padding: 10px;
    text-align: right;
    box-shadow: 2px;
  }
  .desgin-item {
    padding: 20px;
    box-shadow: 2px;
    .desgin-item-title {
      font-size: 20px;
      color: #000;
    }
    .desgin-item-content {
      display: flex;
      flex-wrap: wrap;
      .box-card {
        flex: 1;
      }
    }
  }
}
</style>
