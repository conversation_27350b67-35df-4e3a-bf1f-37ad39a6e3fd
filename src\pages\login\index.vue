<!--
 * @Fileoverview: 登陆
 * @Description: 登陆页面
-->
<template>
  <div class="login">
    <div class="login__logo">
      <img :src="logoImg" />
    </div>
    <div class="login__bg" ref="loginBg"></div>
    <div class="login__content">
      <div class="login__box">
        <div class="login__content__title">
          {{ productName }}
        </div>
        <div class="login__content__desc">{{ productDesc }}</div>

        <el-form
          ref="form"
          class="login__content__form"
          label-width="70px"
          label-position="left"
          :model="user"
          :rules="rules"
          @submit.native="doLogin"
        >
          <el-form-item prop="username">
            <div class="login__label">账号</div>
            <el-input
              autofocus
              ref="username-input"
              type="text"
              placeholder="请输入"
              v-model.trim="user.username"
            ></el-input>
            <qz-icon
              class="icon-zhanghao3 login__content__form__icon"
            ></qz-icon>
          </el-form-item>
          <el-form-item prop="password">
            <div class="login__label">密码</div>
            <el-input
              :type="isPlainPwd ? 'text' : 'password'"
              placeholder="请输入"
              v-model.trim="user.password"
            ></el-input>
            <qz-icon class="icon-mima1 login__content__form__icon"></qz-icon>
            <qz-icon
              v-if="!isPlainPwd"
              class="icon-tuomin login__content__form__pwd"
              @click.native="isPlainPwd = !isPlainPwd"
            ></qz-icon>
            <qz-icon
              v-if="isPlainPwd"
              class="icon-quxiaotuomin login__content__form__pwd"
              @click.native="isPlainPwd = !isPlainPwd"
            ></qz-icon>
          </el-form-item>
          <el-form-item prop="vcode">
            <div class="login__label">验证码</div>
            <div class="flex">
              <el-input
                placeholder="请输入"
                type="text"
                v-model.trim="user.vcode"
              ></el-input>
              <img
                class="login__content__code"
                :src="vcodeUrl"
                alt=""
                @click="setVcodeUrl"
              />
            </div>
            <qz-icon
              class="icon-yanzhengma1 login__content__form__icon"
            ></qz-icon>
          </el-form-item>
          <button
            type="submit"
            class="login__content__btn"
            :disabled="loading"
            @click="doLoginSystem"
          >
            {{ loading ? '登录中...' : '登录' }}
          </button>
        </el-form>
      </div>
    </div>
  </div>
</template>
<script>
import md5 from 'md5';
import { mapState } from 'vuex';
import { DATA_URL_CAPTCHA } from '@/constant/data-url-constants';
import { doLogin } from '@/service/login-service';
import {
  setLocalUserInfo,
  setMenuData,
  setPageMenuAndBlockConfigs
} from '@/utils/storage-utils';
import { getLicense } from '@/service/system-auth-service';
import { PAGE_URL_OVERVIEW } from '@/constant/page-url-constants';
import { formatTime } from '@/utils/string-utils';
export default {
  data() {
    return {
      // 定时器
      intervalId: null,
      // 是否显示明文密码
      isPlainPwd: false,
      vcodeUrl: '',
      productDesc: '',
      user: {
        username: '',
        password: '',
        vcode: '',
        googleCode: ''
      },
      rules: {
        username: [{ required: true, message: '请输入账号', trigger: 'blur' }],
        password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
        vcode: [{ required: true, message: '请输入验证码', trigger: 'blur' }],
        googleCode: [
          { required: true, message: '请输入动态码', trigger: 'blur' }
        ]
      },
      loading: false,
      isGoogleAuth: false,
      enableLdap: false, // 是否启用ldap
      isLdapLogin: false // 是否为ldap登录
    };
  },
  watch: {
    backImg() {
      this.$refs.loginBg.style.backgroundImage = `url("${this.backImg}")`;
    }
  },
  computed: {
    ...mapState(['logoImg', 'productName', 'backImg'])
  },
  created() {},
  mounted() {
    if (this.backImg) {
      this.$refs.loginBg.style.backgroundImage = `url("${this.backImg}")`;
    } else {
      this.$refs.loginBg.style.backgroundImage = 'url("../assets/img/bg.png")';
    }
    this.setVcodeUrl();
    this.doRefresh();
  },
  destroyed() {
    // 在页面销毁后，清除计时器
    this.clear();
  },
  methods: {
    setLocalUserInfo,
    setVcodeUrl() {
      this.vcodeUrl =
        DATA_URL_CAPTCHA + '?' + 'clickFlag=true' + '&' + Math.random();
    },
    // 定时刷新数据
    doRefresh() {
      // 计时器正在进行中，退出函数
      if (this.intervalId != null) {
        return;
      }
      // 计时器为空，则在5分钟后重新刷新验证码
      this.intervalId = setInterval(() => {
        this.setVcodeUrl();
      }, 1000 * 300);
    },
    // 停止定时器
    clear() {
      clearInterval(this.intervalId); //清除计时器
      this.intervalId = null; //设置为null
    },

    // 登陆
    async doLoginSystem(event) {
      event.preventDefault();
      this.$refs.form.validate((valid) => {
        if (valid) {
          // 转换成json字符串
          const p = {
            username: this.user.username,
            password: md5(this.user.password),
            captcha: this.user.vcode
          };
          // 切换为登录中...
          this.loading = true;
          // 发起登录请求
          doLogin(p)
            .then(async (res) => {
              if (res.status != -1) {
                //将返回的信息存储到本地，退出的时候要用
                this.setLocalUserInfo(res.data, this.$store);
                //存储菜单
                setMenuData(res.data?.resourceList || []);
                //获取授权
                await getLicense()
                  .then((ares) => {
                    this.version = ares.data.version;
                    this.startTime = formatTime(parseInt(ares.data.start_time));
                    this.endTime = formatTime(parseInt(ares.data.end_time));
                    this.systemTime = formatTime(ares.data.systemTime);
                    this.state = ares.data.state;

                    setPageMenuAndBlockConfigs(ares.data);
                  })
                  .catch((err) => {
                    this.$message.error(err.msg || '获取授权信息失败');
                  });
                this.$router.push({
                  path: PAGE_URL_OVERVIEW
                });
              } else {
                this.$message.error(res?.msg || '登录失败');
                this.loading = false;
                this.setVcodeUrl();
              }
            })
            .catch((err) => {
              console.log(err);
              this.$message.error(err?.msg || '登录失败');
              this.loading = false;
              this.setVcodeUrl();
            });
        }
      });
    }
  }
};
</script>
<style lang="less" scoped>
.login {
  position: relative;
  height: 100%;
  width: 100%;
  display: flex;
  &__label {
    font-size: 14px;
    color: #000000;
  }
  .el-form-item__error {
    margin-top: 0;
  }

  &__logo {
    position: absolute;
    top: 20px;
    left: 20px;
    height: 34px;

    img {
      height: 100%;
    }
  }

  &__bg {
    width: 50%;
    height: 100%;
    overflow: hidden;
    background-size: cover;
    background-repeat: no-repeat;
    background-color: @bg-blue-deep-color;
  }

  &__box {
    width: 75%;
    margin: 100px 0;
    margin-left: 50%;
    transform: translateX(-50%);
    display: flex;
    flex-direction: column;
  }

  &__content {
    width: 50%;
    height: 100%;
    background: @white-color;
    position: relative;
    overflow-y: auto;
    overflow-x: hidden;

    &__title {
      font-weight: 600;
      font-size: 30px;
    }

    &__desc {
      color: @text-tip-color;
      margin-top: 10px;
      font-size: 16px;
    }

    &__form {
      margin-top: 30px;

      &__icon {
        position: absolute;
        left: 10px;
        top: 40px;
        color: @text-tip-color;
      }

      &__pwd {
        position: absolute;
        right: 10px;
        top: 40px;
        color: @text-tip-color;
        cursor: pointer;
      }
    }

    &__code {
      width: 78px;
      height: 40px;
      margin-left: 10px;
    }

    &__btn {
      margin-top: 30px;
      width: 100%;
      height: 40px;
      color: @white-color;
      background: @theme-color;
      border: none;
      border-radius: 4px;
      font-size: 16px;
      line-height: 40px;
      text-align: center;
      letter-spacing: 0.42px;
      cursor: pointer;
    }

    & .flex {
      display: flex;
      width: 100%;
    }

    ::v-deep & .el-form-item {
      &__content {
        margin-left: 0 !important;
        position: relative;
      }

      // &__label {
      //   font-size: 16px;
      //   color: @text-primary-color;
      //   letter-spacing: 1.19px;
      //   padding-right: 0px;
      // }

      & .el-input__inner {
        border-radius: 5px;
        padding-left: 35px;
      }
    }
  }
}
</style>
