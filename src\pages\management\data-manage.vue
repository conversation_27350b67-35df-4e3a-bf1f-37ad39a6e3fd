<template>
  <div class="dataManage">
    <api-table
      ref="table"
      table-id="data-list"
      :data-source="getDataList"
      :search-input-options="{
        key: 'name',
        placeholder: '请输入分组名进行筛选'
      }"
      title="数据管理"
      tools-layout="searchInput,divider,delete,divider,assign,divider,add"
      @selection-change="handleSelectionChange"
    >
      <api-table-tool-register id="add">
        <el-button type="primary" size="mini" @click="openDataDrawalert({})"
          >新增分组</el-button
        >
      </api-table-tool-register>
      <api-table-tool-register id="assign">
        <el-button
          size="mini"
          @click="assignAccount()"
          :disabled="selectedRows.length === 0"
          >分配账号</el-button
        >
      </api-table-tool-register>
      <api-table-tool-register id="delete">
        <el-button
          size="mini"
          @click="batchDelete"
          :disabled="selectedRows.length === 0"
          >批量删除</el-button
        >
      </api-table-tool-register>
      <api-table-column type="selection"> </api-table-column>
      <api-table-column label="分组名称" prop="name"
        ><template #default="{ row }">
          <span @click="openDataDrawalert(row)">{{
            row.name || '--'
          }}</span>
        </template></api-table-column
      >
      <api-table-column label="分组描述" prop="description" show-overflow-tooltip>
        <template #default="{ row }">
          {{ row.description || '--' }}
        </template>
      </api-table-column>
      <api-table-column label="账号" prop="users">
        <template #default="{ row }">
          <span v-if="row.users && row.users.length > 0">
            {{ row.users.map((user) => user.username).join(', ') }}
          </span>
          <span v-else>--</span>
        </template>
      </api-table-column>
      <api-table-column label="操作" width="180" fixed="right">
        <template #default="{ row }">
          <span class="action-link" @click="assignAccount(row)">
            分配账号
          </span>
          <span class="action-link" @click="openDataDrawalert(row)">
            编辑
          </span>
          <span class="action-link" @click="deleteItem(row)">删除</span>
        </template>
      </api-table-column>
    </api-table>
  </div>
</template>
<script>
import {
  postDataManageList,
  postDataManageAdd,
  postDataManageEdit,
  postDataManageDelete,
  postDataManageBatchDelete
} from '@/service/data-manage-service';
export default {
  data() {
    return {
      selectedRows: [] // 选中行
    };
  },

  components: {},

  mounted() {},

  methods: {
    getDataList(params) {
      return postDataManageList(params);
    },
    handleSelectionChange(rows) {
      this.selectedRows = rows;
    },
    batchDelete() {
      if (this.selectedRows.length === 0) {
        this.$message.warning('请先选择要删除的数据');
        return;
      }
      const ids = this.selectedRows.map((row) => row.id);
      const names = this.selectedRows.map((row) => row.name).join('、');
      this.$confirm(`确认删除分组"${names}"？`).then(() => {
        postDataManageBatchDelete({ ids: ids })
          .then((res) => {
            this.$message.success('删除成功');
            this.$refs.table.reloadCurrentPage();
          })
          .catch((err) => {
            this.$message.error(err.msg || '删除失败');
          });
      });
    },
    openDataDrawalert(row) {
      this.$DrawAlert({
        title: row.id ? '编辑分组' : '新增分组',
        params: {
          detail: row || {},
          callback: () => {
            if (row.id) {
              this.$refs.table.reloadCurrentPage();
            } else {
              this.$refs.table.reload();
            }
          }
        },
        componentObj: {
          component: () => import('./packages/data-manage-group.vue')
        }
      });
    },
    assignAccount(row) {
      this.$dialogAlert({
        params: {
          detail: row,
          selectedRows: this.selectedRows,
          callBack: () => {
            this.$refs.table.reloadCurrentPage();
          }
        },
        component: () => import('./packages/assign-account-detail.vue'),
        alertTitle: '分配账号',
        alertWidth: '600px',
        alertHeight: 'auto'
      });
    },
    deleteItem(row) {
      this.$confirm(
        '删除分组后，此分组下账号的资产权限会设定为全部资产，确认删除？'
      ).then(() => {
        postDataManageDelete({ id: row.id })
          .then((res) => {
            this.$message.success('删除成功');
            this.$refs.table.reloadCurrentPage();
          })
          .catch((err) => {
            this.$message.error(err.msg || '删除失败');
          });
      });
    }
  }
};
</script>
<style lang="less" scoped>

</style>
