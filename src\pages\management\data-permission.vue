<template>
  <div class="data-permission">
    <api-table
      ref="table"
      table-id="data-permission-list"
      :data-source="getDataList"
      :search-input-options="{
        key: 'name',
        placeholder: '请输入分组名称进行筛选'
      }"
      :operations="opeartions"
      title="分组列表"
      tools-layout="searchInput,divider,refresh,divider,operations, add"
      @selection-change="handleSelectionChange"
    >
      <api-table-tool-register id="add">
        <el-button type="primary" size="mini" @click="showDetail({})">
          新增分组
        </el-button>
      </api-table-tool-register>
      <api-table-column
        :selectable="(row) => row.type !== 1"
        type="selection"
      ></api-table-column>
      <api-table-column label="分组名称" prop="name">
        <template #default="{ row }">
          <span class="action-link" @click="showDetail(row)">{{
            row.name
          }}</span>
        </template>
      </api-table-column>
      <api-table-column label="分组描述" prop="description"></api-table-column>
      <api-table-column label="分组状态" prop="status">
        <template #default="{ row }">
          <el-tag :type="row.status === 1 ? 'success' : 'danger'" size="mini">{{
            row.status === 1 ? '启用' : '停用'
          }}</el-tag>
        </template>
      </api-table-column>
      <api-table-column
        label="创建时间"
        prop="createTime"
        formatter="formatTime"
      ></api-table-column>
      <api-table-column label="操作" width="80" fixed="right">
        <template #default="{ row }">
          <span
            v-if="row.type !== 1"
            class="action-link el-icon-delete"
            @click="deleteItem(row)"
          ></span>
        </template>
      </api-table-column>
    </api-table>
  </div>
</template>

<script>
import * as managementService from '@/service/management-service';
import { mapState } from 'vuex';

export default {
  data() {
    return {
      selectList: []
    };
  },
  computed: {
    ...mapState(['isMaster']),
    opeartions() {
      return [
        {
          name: '删除',
          disabled: !this.selectList.length,
          handler: () => {
            this.batchDelete();
          }
        }
      ];
    }
  },
  methods: {
    getDataList(params) {
      return managementService.getDataGroupList(params);
    },
    handleSelectionChange(rows) {
      this.selectList = rows.map((item) => item.id);
    },
    batchDelete() {
      this.$confirm(
        '删除分组后，分组下的账号的资产权限会设定为全部资产，确认删除？'
      ).then(() => {
        this.removeDataGroup(this.selectList);
      });
    },
    removeDataGroup(ids) {
      this.$refs.table.loading = true;
      managementService
        .deleteDataGroup(ids)
        .then(
          () => {
            this.$refs.table.reloadCurrentPage();
          },
          (err) => {
            this.$message.error(err.msg || '操作失败');
          }
        )
        .finally(() => {
          this.$refs.table.loading = false;
        });
    },
    showDetail(row) {
      this.$DrawAlert({
        title: row.id ? `编辑分组-${row.name}` : '新增分组',
        customClass: 'overflow-hidden-drawer',
        params: {
          isMaster: this.isMaster,
          detail: row || {},
          callback: () => {
            if (row.id) {
              this.$refs.table.reloadCurrentPage();
            } else {
              this.$refs.table.reload();
            }
          }
        },
        component: () => import('./packages/data-group.vue')
      });
    },
    deleteItem(row) {
      managementService.getDataGroupRelatedAccount(row.id).then(
        (res) => {
          const count = res.data || 0;
          if (count === 0) {
            this.removeDataGroup([row.id]);
          } else {
            this.$confirm(
              `删除分组后，此分组下的${count}个账号的资产权限会设定为全部资产，确认删除？`
            ).then(() => {
              this.removeDataGroup([row.id]);
            });
          }
        },
        (err) => {
          this.$message.error(err.msg || '操作失败');
        }
      );
    }
  }
};
</script>

<style lang="less" scoped>
.data-permission {
  padding: 20px;
  height: @non-submenu-height;
}
</style>
