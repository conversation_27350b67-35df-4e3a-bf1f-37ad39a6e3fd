<template>
  <div class="menu-manage">
    <api-table
      ref="table"
      table-id="role-list"
      :data-source="[]"
      :search-input-options="{
        key: 'roleName',
        placeholder: '请输入菜单名称进行筛选'
      }"
      :operations="opeartions"
      title="菜单列表"
      tools-layout="searchInput,divider,refresh,divider,operations,add"
      @selection-change="handleSelectionChange"
    >
      <api-table-tool-register id="add">
        <el-button type="primary" size="mini" @click="editMenu">
          新增菜单
        </el-button>
      </api-table-tool-register>
      <api-table-column type="selection"></api-table-column>
      <api-table-column label="菜单名称" prop="roleName">
        <template #default="{ row }">
          <span class="action-link" @click="editMenu(row)">{{
            row.roleName
          }}</span>
        </template>
      </api-table-column>
      <api-table-column label="菜单描述" prop="remark"></api-table-column>
      <api-table-column label="操作" width="70" fixed="right">
        <template #default="{ row }">
          <span
            class="action-link el-icon-delete"
            @click="deleteItem(row.id)"
          ></span>
        </template>
      </api-table-column>
    </api-table>
  </div>
</template>

<script>
export default {
  data() {
    return {
      selectList: []
    };
  },
  computed: {
    opeartions() {
      return [
        {
          name: '删除',
          disabled: !this.selectList.length,
          handler: () => {
            this.batchDelete();
          }
        }
      ];
    }
  },
  methods: {
    getDataList(params) {},
    handleSelectionChange(rows) {
      this.selectList = rows.map((item) => item.id);
    },
    batchDelete() {
      this.$confirm('确认删除？').then(() => {
        this.removeRole(this.selectList);
      });
    },
    editMenu(row) {
      this.$DrawAlert({
        title: row ? '编辑菜单' : '新增菜单',
        params: {
          detail: row || {},
          callback: () => {
            if (row?.id) {
              this.$refs.table.reloadCurrentPage();
            } else {
              this.$refs.table.reload();
            }
          }
        },
        componentObj: {
          component: () => import('./packages/menu-edit.vue')
        }
      });
    },
    removeRole(ids) {
      // this.$refs.table.loading = true;
    },
    deleteItem(id) {
      // this.$refs.table.loading = true;
    }
  }
};
</script>

<style lang="less" scoped>
.romenu-manage {
  padding: 20px;
  height: @non-submenu-height;
}
</style>
