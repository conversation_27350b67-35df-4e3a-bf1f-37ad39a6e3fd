<template>
  <div class="account">
    <el-form
      ref="form"
      :model="formInfo"
      :rules="rules"
      size="small"
      label-width="100px"
    >
      <el-form-item prop="username" label="账号名">
        <el-input
          v-model="formInfo.username"
          placeholder="请输入账号名，最长支持100个字符"
        ></el-input>
      </el-form-item>
      <el-form-item prop="remark" label="账号描述">
        <el-input
          v-model="formInfo.remark"
          placeholder="请输入账号描述"
        ></el-input>
      </el-form-item>
      <el-form-item prop="roleIdList" label="账号角色">
        <el-select
          v-model="formInfo.roleIdList"
          placeholder="请选择账号角色"
          class="full-width"
        >
          <el-option
            v-for="role in roles"
            :key="role.value"
            :value="role.value"
            :label="role.label"
            :disabled="
              role.label === '系统管理员' || role.label === '审计管理员'
            "
          ></el-option>
        </el-select>
      </el-form-item>
      <!-- <el-form-item prop="dataGroupIds" label="数据权限">
        <div class="data-group-selector">
          <div class="group-options" v-if="!isAllSelected">
            <el-select
              v-model="formInfo.dataGroupIds"
              placeholder="请选择数据权限组"
              class="full-width"
              multiple
              collapse-tags
              filterable
              :disabled="isAllSelected"
            >
              <el-option
                v-for="group in dataGroups"
                :key="group.id"
                :value="group.id"
                :label="group.name"
              >
              </el-option>
            </el-select>
          </div>
        </div>
      </el-form-item> -->
      <el-form-item label=" ">
        <div v-if="roleRemark" class="tips">{{ roleRemark }}</div>
      </el-form-item>
      <el-form-item prop="email" label="账号邮箱">
        <el-input
          v-model="formInfo.email"
          placeholder="请输入账号邮箱"
        ></el-input>
      </el-form-item>
      <el-form-item label="账号密码" prop="password">
        <el-input
          v-model.trim="formInfo.password"
          type="password"
          placeholder="请输入密码"
        ></el-input>
        <div v-if="formInfo.password" class="pwd-intensity">
          <div
            :class="
              pwdIntensity >= 1
                ? 'pwd-intensity--low'
                : 'pwd-intensity--default'
            "
          ></div>
          <div
            :class="
              pwdIntensity >= 2
                ? 'pwd-intensity--mid'
                : 'pwd-intensity--default'
            "
          ></div>
          <div
            :class="
              pwdIntensity >= 3
                ? 'pwd-intensity--high'
                : 'pwd-intensity--default'
            "
          ></div>
          <div class="pwd-intensity__text">
            {{ intensityText[pwdIntensity] }}
          </div>
        </div>
      </el-form-item>
      <el-form-item label="确认密码" prop="password2">
        <el-input
          v-model.trim="formInfo.password2"
          type="password"
          placeholder="请再次输入密码"
        ></el-input>
      </el-form-item>
      <el-form-item v-if="isGoogleAuth && formInfo.id" label="用户密钥">
        <div class="account__secret-key">
          <el-input
            v-model="formInfo.googleAuthSecret"
            size="small"
            disabled
          ></el-input>
          <div
            class="action-link cancel-sensi"
            v-if="isAuthSensi"
            @click="cancelSensi"
          >
            取消脱敏
          </div>
          <el-popover
            placement="bottom"
            width="200"
            trigger="click"
            @show="showAuthQrCode"
          >
            <canvas ref="authQrCode"></canvas>
            <div class="action-link account__secret-key__view" slot="reference">
              <i class="fa fa-qrcode"></i>
              <span>查看密钥二维码</span>
            </div>
          </el-popover>
          <el-popconfirm
            title="确定重置此账号密钥吗？"
            style="outline: none"
            class="action-link"
            @confirm="resetKey"
            icon="el-icon-warning"
          >
            <div
              class="action-link account__secret-key__reset"
              slot="reference"
            >
              <i class="el-icon-refresh"></i>
              <span>重置密钥</span>
            </div>
          </el-popconfirm>
        </div>
      </el-form-item>
      <el-form-item v-if="formInfo.username !== 'sysadmin'" label="账号有效期">
        <el-select v-model="formInfo.validTimeType">
          <el-option label="永久有效" value="FOREVER"></el-option>
          <el-option label="指定有效期" value="SPECIFY"></el-option>
        </el-select>
        <el-date-picker
          v-if="formInfo.validTimeType === 'SPECIFY'"
          type="date"
          v-model="formInfo.expireTime"
          value-format="timestamp"
        ></el-date-picker>
      </el-form-item>
      <el-form-item prop="status" label="锁定状态">
        <el-switch
          v-model="formInfo.status"
          :active-value="0"
          :inactive-value="1"
        ></el-switch>
      </el-form-item>
    </el-form>
    <div class="footer align-right">
      <el-button @click="cancel" size="small">取消</el-button>
      <el-button
        :loading="saveLoading"
        type="primary"
        size="small"
        @click="save"
      >
        保存
      </el-button>
    </div>
  </div>
</template>

<script>
import { AESUtil } from '@/utils/AESUtil';
import { getPwdIntensity, pwdValidators } from '@/utils/password-utils';
import { validateEmail, formatTimestamp } from '@/utils/string-utils';
import { cloneDeep } from 'lodash-es';
import md5 from 'md5';
import QRCode from 'qrcode';
import { postSaveAccount, getAccountDetail } from '@/service/account-service';
import { postRoleList } from '@/service/role-service';
import { getLoginSecurityInfoById } from '@/service/login-service';
export default {
  props: ['params'],
  data() {
    return {
      pwdValidators,
      formInfo: {
        username: '',
        remark: '',
        roleIdList: '',
        email: '',
        password: '',
        password2: '',
        expireTime: '',
        validTimeType: 'FOREVER',
        status: 1,
        googleAuthSecret: ''
      },
      pwdLevel: 3,
      intensityText: ['强度', '低强度', '中强度', '高强度'],
      saveLoading: false,
      roles: [],
      // dataGroups: [],
      // isAllSelected: false,
      isGoogleAuth: false, //是否为双因素认证
      isAuthSensi: true, //用户密钥是否脱敏，默认为脱敏
      compareName: '' //用于比较是否修改账号名称，由于第三方工具限制，修改名称后必须重置密钥
    };
  },
  async mounted() {
    //获取密码强度
    getLoginSecurityInfoById('/passwordSecurity').then((res) => {
      this.pwdLevel = JSON.parse(res.data).passwordLevel;
    });
    // 获取角色菜单
    const roleIdListRes = await postRoleList({ pageNum: 1, pageSize: 1000 });
    this.roles = roleIdListRes?.data?.rows.map((item) => {
      return {
        label: item.roleName,
        value: item.id
      };
    });
    if (this.params?.detail?.id) {
      const accountRes = await getAccountDetail({ id: this.params.detail.id });
      const info = accountRes?.data;
      this.formInfo = {
        id: info.id,
        username: info.username,
        remark: info.remark,
        roleIdList: info.roleList[0].id,
        email: info.email,
        password: '',
        expireTime: info.expireTime ? new Date(info.expireTime).valueOf() : '',
        status: info.status,
        googleAuthSecret: info.googleAuthSecret
      };
      if (info.expireTime) {
        this.$set(this.formInfo, 'validTimeType', 'SPECIFY');
      } else {
        this.$set(this.formInfo, 'validTimeType', 'FOREVER');
      }
    }
    this.$nextTick(() => {
      this.$refs.form.clearValidate(); // 清除初始校验
    });
    this.getAuthMethod();
  },
  computed: {
    roleRemark() {
      const role = this.roles.find(
        (item) => item.id === this.formInfo.roleIdList
      );
      return role?.remark;
    },
    rules() {
      return {
        username: [
          {
            required: true,
            validator: this.validateUsername,
            trigger: 'blur'
          },
          {
            max: 100,
            message: '账号名最长支持100个字符',
            trigger: 'blur'
          }
        ],
        roleIdList: [
          { required: true, message: '请选择角色', trigger: 'blur' }
        ],
        password: [
          {
            required: this.formInfo.id ? false : true,
            validator: this.validatePass,
            trigger: 'blur'
          }
        ],
        password2: [
          {
            required: this.formInfo.id ? false : true,
            validator: this.validatePass2,
            trigger: 'blur'
          }
        ],
        email: [
          {
            required: false,
            validator: this.emailValidator,
            trigger: 'blur'
          }
        ]
      };
    },
    pwdIntensity() {
      return getPwdIntensity(this.formInfo.password);
    },
    pwdValidator() {
      return this.pwdValidators[this.pwdLevel];
    }
  },
  methods: {
    formatTimestamp,
    // 查询认证
    getAuthMethod() {},
    // 记录查看密钥日志
    recordAuthLog() {
      // checkAuthLog(this.formInfo.username).then(() => {});
    },
    // 重置密钥
    resetKey() {
      if (this.formInfo.username) {
        this.compareName = this.formInfo.username;
      } else {
        this.$message.error('账号为空，无法重置密钥');
      }
    },
    // 取消用户密钥脱敏
    cancelSensi() {
      this.formInfo.googleAuthSecret = AESUtil.decrypt(
        this.formInfo.googleAuthSecret
      );
      this.isAuthSensi = false;
      this.recordAuthLog();
    },
    // 查看密钥二维码
    showAuthQrCode() {
      let googleAuthSecretTmp = '';
      if (this.isAuthSensi) {
        googleAuthSecretTmp = AESUtil.decrypt(this.formInfo.googleAuthSecret);
        this.recordAuthLog();
      } else {
        googleAuthSecretTmp = this.formInfo.googleAuthSecret;
      }
      const url =
        'otpauth://totp/' +
        this.userForm.username +
        '?secret=' +
        googleAuthSecretTmp;
      // 将指定的url生成二维码并渲染到canvas中
      QRCode.toCanvas(this.$refs.authQrCode, url, function (error) {
        if (error) console.error(error);
      });
    },
    validateUsername(_rule, _value, callback) {
      if (!this.formInfo.username) {
        callback(new Error('请输入账号名'));
        return;
      } else if (this.formInfo.username.indexOf(' ') !== -1) {
        callback(new Error('账号名不允许包含空格'));
        return;
      }
      callback();
    },
    validatePass(_rule, value, callback) {
      if (!this.formInfo.id && !value) {
        // 新建用户时，一定要设置密码
        callback(new Error('新建用户请设置密码!'));
        return;
      } else {
        if (this.pwdValidators[value]) {
          const { valid, err } = this.pwdValidators[value];
          if (!valid && value) {
            return callback(new Error(err));
          }
        }
      }
      callback();
    },
    validatePass2(_rule, _value, callback) {
      if (this.formInfo.password && !this.formInfo.password2) {
        callback(new Error('请输入确认密码!'));
        return;
      }
      if (
        this.formInfo.password &&
        this.formInfo.password2 &&
        this.formInfo.password !== this.formInfo.password2
      ) {
        callback(new Error('两次输入密码不一致!'));
        return;
      }
      callback();
    },
    emailValidator(_rule, _value, callback) {
      if (this.formInfo.email && !validateEmail(this.formInfo.email)) {
        callback(new Error('邮箱格式不正确'));
        return;
      }
      callback();
    },
    cancel() {
      this.params.closeOutDrawer();
    },
    save() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          const params = cloneDeep(this.formInfo);
          delete params.password2;

          if (!params.id) {
            delete params.id;
          }
          if (params.validTimeType === 'FOREVER') {
            params.expireTime = '';
          } else {
            params.expireTime = this.formatTimestamp(params.expireTime);
            delete params.validTimeType;
          }

          // if (this.formInfo.id) {
          //   // 编辑用户时，不修改密码
          //   params.password = '';
          // } else {
          //   params.password = md5(params.password);
          // }
          if (this.formInfo.password) {
            params.password = md5(params.password);
          } else {
            params.password = null;
          }
          if (
            this.formInfo.id &&
            this.isGoogleAuth &&
            this.compareName !== this.formInfo.username
          ) {
            this.$message.error('用户名变更后请重置密钥！');
            return;
          }
          params.roleIdList = [params.roleIdList];
          postSaveAccount(params)
            .then((res) => {
              this.$message.success(res.msg || '新增成功');
              this.params.callback();
              this.params.closeOutDrawer();
            })
            .catch((err) => {
              this.$message.error(err.msg || '新增失败');
            });
        }
      });
    }
  }
};
</script>

<style lang="less" scoped>
.account {
  height: 100%;
  display: flex;
  flex-direction: column;
  .el-form {
    flex: auto;
    width: 100%;
    overflow-y: auto;
    padding: 20px;
    box-sizing: border-box;
    padding-bottom: 0;
  }
  .footer {
    flex: none;
    padding: 10px;
    border-top: 1px solid @border-base-color;
  }
}
.intensity-style(@color) {
  background: @color;
  width: 40px;
  height: 3px;
}
.pwd-intensity {
  display: flex;
  align-items: center;
  position: absolute;
  margin-top: -8px;
  justify-content: flex-end;
  width: 100%;
  &--default {
    .intensity-style(@border-base-color);
  }
  &--low {
    .intensity-style(@red-color);
  }
  &--mid {
    .intensity-style(@orange-color);
  }
  &--high {
    .intensity-style( @green-color);
  }
  &__text {
    font-size: 12px;
    color: @text-regular-color;
    margin-left: 5px;
  }
}
.tips {
  color: @text-regular-color;
  font-size: 12px;
}
</style>
