<template>
  <div class="data-group">
    <el-form
      ref="form"
      :model="formInfo"
      :rules="rules"
      :disabled="formInfo.type === 1"
      size="small"
      label-width="100px"
    >
      <el-form-item prop="name" label="分组名称">
        <el-input
          v-model="formInfo.name"
          placeholder="请输入分组名称"
        ></el-input>
      </el-form-item>
      <el-form-item prop="description" label="分组描述">
        <el-input
          v-model="formInfo.description"
          placeholder="请输入分组描述"
        ></el-input>
      </el-form-item>
      <el-form-item prop="conditionValues" label="资产权限">
        <div class="flex-row">
          <el-select
            v-model="formInfo.conditionType"
            @change="handleConditionTypeChange"
          >
            <el-option value="app" label="按应用分配"></el-option>
            <el-option
              v-if="params.isMaster"
              value="node"
              label="按来源节点分配"
            ></el-option>
          </el-select>
          <template v-if="formInfo.conditionType === 'app'">
            <el-select
              v-model="formInfo.conditionFeature"
              @change="handleConditionFeatureChange"
            >
              <el-option value="all" label="全部"></el-option>
              <el-option value="department" label="应用部门"></el-option>
              <el-option value="domain" label="部署域"></el-option>
              <el-option value="specify" label="指定应用"></el-option>
            </el-select>
            <template v-if="formInfo.conditionFeature !== 'all'">
              <el-select
                v-if="formInfo.conditionFeature === 'department'"
                v-model="formInfo.conditionValues"
                key="deparment"
                collapse-tags
                multiple
              >
                <el-option
                  v-for="department of departmentList"
                  :key="department.id"
                  :value="department.id"
                  :label="department.name"
                ></el-option>
              </el-select>
              <el-select
                v-else-if="formInfo.conditionFeature === 'domain'"
                v-model="formInfo.conditionValues"
                key="domain"
                collapse-tags
                multiple
              >
                <el-option
                  v-for="domain of domainList"
                  :key="domain.id"
                  :value="domain.id"
                  :label="domain.name"
                ></el-option>
              </el-select>
              <tag-input
                v-else-if="formInfo.conditionFeature === 'specify'"
                v-model="formInfo.conditionValues"
                :suggestion-getter="searchAppUri"
                type="APP"
                placeholder="输入内容后按回车确认"
                class="flex-auto"
              ></tag-input>
            </template>
          </template>
          <el-select
            v-else
            v-model="formInfo.conditionValues"
            key="nodeList"
            collapse-tags
            multiple
          >
            <el-option
              v-for="node of nodeList"
              :key="node.id"
              :value="node.id"
              :label="node.name"
            ></el-option>
          </el-select>
        </div>
      </el-form-item>
      <el-form-item prop="status" label="分组状态">
        <template #label>
          <span>分组状态</span>
          <el-tooltip
            effect="dark"
            content="关闭分组后，新增账号不可再使用此分组进行资产权限划分（原在有效期内的账号还可按照当前资产权限正常使用）"
          >
            <i class="el-icon-question"></i>
          </el-tooltip>
        </template>
        <el-switch
          v-model="formInfo.status"
          :active-value="1"
          :inactive-value="0"
        ></el-switch>
      </el-form-item>
    </el-form>
    <div class="footer align-right">
      <el-button @click="cancel" size="small">取消</el-button>
      <el-button
        v-if="formInfo.type !== 1"
        :loading="saveLoading"
        type="primary"
        size="small"
        @click="save"
      >
        保存
      </el-button>
    </div>
  </div>
</template>

<script>
import { saveDataGroup } from '@/service/management-service';
import { getNodeList } from '@/service/multi-node-service';
import { getNetworkSegmentList } from '@/service/network-segment-service';
import {
  getAppList,
  getImportedDepartmentList
} from '@/service/webapp-service';
import { cloneDeep } from 'lodash-es';

function getDefaultFormInfo() {
  return {
    id: '',
    name: '',
    description: '',
    status: 1,
    type: 2,
    conditions: [],
    conditionType: 'app',
    conditionFeature: 'all',
    conditionValues: []
  };
}

export default {
  props: ['params'],
  data() {
    return {
      formInfo: this.getFormInfo(),
      rules: {
        name: [{ required: true, message: '请输入分组名称', trigger: 'blur' }],
        conditionValues: [
          {
            required: true,
            message: '请选择资产权限',
            validator: this.conditionValues,
            trigger: 'blur'
          }
        ]
      },
      saveLoading: false,

      departmentList: [],
      domainList: [],
      nodeList: []
    };
  },
  created() {
    // 获取部门清单
    getImportedDepartmentList().then((res) => {
      this.departmentList = (res.data || []).map((item) => ({
        id: item,
        name: item
      }));
    });

    // 获取网段清单
    getNetworkSegmentList({
      page: 1,
      limit: 1000,
      needInternet: true
    }).then((res) => {
      this.domainList = res.data?.rows || [];
    });

    // 获取节点清单
    getNodeList({ page: 1, limit: 1000 }).then((res) => {
      this.nodeList = res.data?.rows || [];
    });
  },
  methods: {
    getFormInfo() {
      const formInfo = Object.assign(
        {},
        getDefaultFormInfo(),
        this.params.detail || {}
      );
      formInfo.conditions = formInfo.conditions || [];
      formInfo.conditionType = 'app';
      formInfo.conditionFeature = 'all';
      formInfo.conditionValues = [];
      const condition = formInfo.conditions[0];
      if (condition) {
        formInfo.conditionValues = condition.value;
        if (condition.key === 'NODE_ID') {
          formInfo.conditionType = 'node';
        } else {
          formInfo.conditionType = 'app';
          if (condition.key === 'APP_DEPARTMENT') {
            formInfo.conditionFeature = 'department';
          } else if (condition.key === 'DEPLOY_DOMAIN') {
            formInfo.conditionFeature = 'domain';
          } else if (condition.key === 'APP_SPECIFY') {
            formInfo.conditionFeature = 'specify';
          }
        }
      }

      return formInfo;
    },
    handleConditionTypeChange() {
      this.formInfo.conditionValues = [];
      if (this.formInfo.conditionType === 'app') {
        this.formInfo.conditionFeature = 'all';
      }
    },
    handleConditionFeatureChange() {
      this.formInfo.conditionValues = [];
    },
    conditionValues(rule, value, callback) {
      if (
        this.formInfo.conditionType === 'app' &&
        this.formInfo.conditionFeature === 'all'
      ) {
        callback();
      } else {
        if (this.formInfo.conditionValues.length) {
          callback();
        } else {
          callback(new Error('请选择资产权限'));
        }
      }
    },
    // 搜索应用
    searchAppUri(keywords) {
      return getAppList({
        page: 1,
        limit: 10,
        host: keywords
      }).then((res) => {
        return res.data?.rows?.map((item) => item.host);
      });
    },
    cancel() {
      this.params.close();
    },
    save() {
      this.$refs.form.validate(async (valid) => {
        if (!valid) return;

        const params = cloneDeep(this.formInfo);

        params.conditions = [];
        if (params.conditionType === 'node') {
          params.conditions.push({
            key: 'NODE_ID',
            operator: 'in',
            value: params.conditionValues
          });
        } else {
          if (params.conditionFeature === 'department') {
            params.conditions.push({
              key: 'APP_DEPARTMENT',
              operator: 'in',
              value: params.conditionValues
            });
          } else if (params.conditionFeature === 'domain') {
            params.conditions.push({
              key: 'DEPLOY_DOMAIN',
              operator: 'in',
              value: params.conditionValues
            });
          } else if (params.conditionFeature === 'specify') {
            params.conditions.push({
              key: 'APP_SPECIFY',
              operator: 'in',
              value: params.conditionValues
            });
          }
        }
        delete params.conditionType;
        delete params.conditionFeature;
        delete params.conditionValues;
        if (!params.id) {
          delete params.id;
        }

        // 编辑状态下，如果分组状态变更了，且关闭了，需要提示
        let shouldSave = true;
        if (
          this.params.detail?.id &&
          this.params.detail.status === 1 &&
          params.status === 0
        ) {
          try {
            await this.$confirm(
              '关闭分组后，新增账号不可再使用此分组进行资产权限划分（原在有效期内的账号还可按照当前资产权限正常使用），确认关闭？',
              '提示',
              {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
              }
            );
          } catch (err) {
            shouldSave = false;
          }
        }
        if (!shouldSave) return;

        this.saveLoading = true;
        saveDataGroup(params)
          .then(
            () => {
              this.$message.success('保存成功');
              this.params.callback && this.params.callback();
              this.params.close();
            },
            (err) => {
              this.$message.error(err.msg || '保存失败');
            }
          )
          .finally(() => {
            this.saveLoading = false;
          });
      });
    }
  }
};
</script>

<style lang="less" scoped>
.data-group {
  height: 100%;
  display: flex;
  flex-direction: column;
  .el-form {
    flex: auto;
    width: 100%;
    overflow-y: auto;
    padding: 20px;
    box-sizing: border-box;
    padding-bottom: 0;
  }
  .footer {
    flex: none;
    padding: 10px;
    border-top: 1px solid @border-base-color;
  }
}
.tag-input {
  display: inline-block;
}
</style>
