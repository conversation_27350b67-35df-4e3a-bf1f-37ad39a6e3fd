<template>
  <div class="data-manage-group">
    <el-form
      ref="form"
      :model="formInfo"
      :rules="rules"
      :disabled="formInfo.type === 1"
      size="small"
      label-width="100px"
    >
      <el-form-item prop="name" label="分组名称">
        <el-input
          v-model="formInfo.name"
          placeholder="请输入分组名称"
        ></el-input>
      </el-form-item>
      <el-form-item prop="description" label="分组描述">
        <el-input
          v-model="formInfo.description"
          placeholder="请输入分组描述"
        ></el-input>
      </el-form-item>
      <el-form-item
        prop="assetPermission"
        label="资产权限"
        class="asset-permission-form-item"
      >
        <div class="asset-permission-container">
          <div
            v-for="(permission, index) in formInfo.assetPermission"
            :key="index"
            class="permission-row"
          >
            <el-input
              value="按服务分配"
              class="permission-type-input"
              readonly
              disabled
            />
            <el-select
              v-model="permission.assetType"
              placeholder="请选择服务类型"
              class="asset-type-select"
              @change="handleAssetTypeChange(index)"
            >
              <el-option label="服务名称" value="SERVER_NAME"></el-option>
              <el-option label="服务类型" value="SERVER_TYPE"></el-option>
              <el-option label="业务系统" value="SERVER_SYSTEM"></el-option>
            </el-select>

            <el-select
              v-model="permission.assetValues"
              placeholder="请选择具体值"
              class="asset-values-select"
              multiple
              filterable
              clearable
              collapse-tags
            >
              <el-option
                v-for="item in assetValueOptions[index]"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>

            <el-button
              v-if="formInfo.assetPermission.length > 1"
              type="text"
              icon="el-icon-delete"
              @click="removePermission(index)"
              class="delete-btn"
            >
              删除
            </el-button>
          </div>

          <el-button
            type="text"
            @click="addPermission"
            class="add-condition-btn"
          >
            添加条件
          </el-button>
        </div>
      </el-form-item>
    </el-form>
    <div class="footer align-right">
      <el-button @click="cancel" size="small">取消</el-button>
      <el-button
        v-if="formInfo.type !== 1"
        :loading="saveLoading"
        type="primary"
        size="small"
        @click="save"
      >
        保存
      </el-button>
    </div>
  </div>
</template>

<script>
import {
  postDataManageSave,
  postDataManageServiceList,
  postDataManageServiceTypeList
} from '@/service/data-manage-service';

export default {
  props: ['params'],
  data() {
    return {
      formInfo: {
        id: '',
        name: '',
        groupDesc: '',
        userIds: [],
        assetPermission: [
          {
            assetType: '',
            assetValues: []
          }
        ],
        creator: 0,
        modifier: 0
      },
      rules: {
        name: [
          { required: true, message: '请输入分组名称', trigger: 'blur' }
        ],
        assetPermission: [
          {
            required: true,
            message: '请配置资产权限',
            validator: this.validateAssetPermission,
            trigger: 'blur'
          }
        ]
      },
      // 资产值选项数据
      assetValueOptions: [[]],
      // 资产值加载状态
      // assetValuesLoading: [false],
      saveLoading: false,
      serverNameList: [],
      serverTypeList: [],
      serverSystemList: []
    };
  },
  async created() {
    try {
      await Promise.all([
        this.loadServerNames(),
        this.loadServerTypes(),
        this.loadServerSystems()
      ]);
    } catch (error) {
      console.error('加载数据失败:', error);
      this.$message.error('加载数据失败');
    }
  },
  mounted() {
    if (this.params.detail.id) {
      this.loadExistingAssetValues();
    }
  },
  methods: {
    // 获取服务名称列表
    async loadServerNames() {
      try {
        const params = {
          isPageQuery: false,
          page: 1,
          searchConditionList: [],
          sortList: [],
          limit: 10,
          columnList: ['id', 'name']
        };
        const res = await postDataManageServiceList(params);
        this.serverNameList = res.data?.rows || [];
      } catch (e) {
        console.log('获取服务名称失败', e);
        this.serverNameList = [];
      }
    },
    // 业务系统
    async loadServerSystems() {
      try {
        const params = {
          isPageQuery: false,
          page: 1,
          searchConditionList: [
            {
              fieldName: 'business_system',
              columnExp: '!=',
              value: ''
            }
          ],
          sortList: [],
          limit: 10,
          columnList: ['id', 'business_system']
        };
        const res = await postDataManageServiceList(params);
        this.serverSystemList = res.data?.rows || [];
      } catch (e) {
        console.log('获取业务系统数据失败', e);
        this.serverSystemList = [];
      }
    },
    // 服务类型
    async loadServerTypes() {
      try {
        const params = {
          isPageQuery: false,
          page: 1,
          searchConditionList: [],
          sortList: [],
          limit: 10,
          columnList: ['id', 'name']
        };
        const res = await postDataManageServiceTypeList(params);
        this.serverTypeList = res.data?.rows || [];
      } catch (e) {
        console.log('获取服务类型失败', e);
        this.serverTypeList = [];
      }
    },
    // 切换数据源
    handleAssetTypeChange(index) {
      const permission = this.formInfo.assetPermission[index];
      permission.assetValues = [];

      if (!permission.assetType) {
        this.$set(this.assetValueOptions, index, []);
        return;
      }

      // 根据资产类型切换对应的数据源
      let dataSource = [];
      switch (permission.assetType) {
        case 'SERVER_NAME':
          dataSource = this.serverNameList;
          break;
        case 'SERVER_TYPE':
          dataSource = this.serverTypeList;
          break;
        case 'SERVER_SYSTEM':
          dataSource = this.serverSystemList;
          break;
        default:
          dataSource = [];
      }

      const options = dataSource?.map((item) => ({
        label: item.name || item.business_system,
        value: item.id || item.value || item
      }));

      this.$set(this.assetValueOptions, index, options);
    },

    addPermission() {
      this.formInfo.assetPermission.push({
        assetType: '',
        assetValues: []
      });
      this.assetValueOptions.push([]);
    },

    removePermission(index) {
      this.formInfo.assetPermission.splice(index, 1);
      this.assetValueOptions.splice(index, 1);
    },

    validateAssetPermission(rule, value, callback) {
      if (!value || value.length === 0) {
        callback(new Error('请至少配置一个资产权限'));
        return;
      }

      for (let i = 0; i < value.length; i++) {
        const permission = value[i];
        if (!permission.assetType) {
          callback(new Error(`第${i + 1}个权限条件的资产类型不能为空`));
          return;
        }
        if (!permission.assetValues || permission.assetValues.length === 0) {
          callback(new Error(`第${i + 1}个权限条件的具体值不能为空`));
          return;
        }
      }

      callback();
    },

    loadExistingAssetValues() {
      for (let i = 0; i < this.formInfo.assetPermission.length; i++) {
        const permission = this.formInfo.assetPermission[i];
        if (permission.assetType) {
          this.handleAssetTypeChange(i);
        }
      }
    },

    // 保存方法
    save() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.saveLoading = true;

          const params = {
            ...this.formInfo
          };

          // 调用保存接口
          postDataManageSave(params)
            .then(() => {
              this.$message.success('保存成功');
              this.params.callback && this.params.callback();
              this.params.closeOutDrawer();
            })
            .catch((error) => {
              this.$message.error('保存失败');
              console.error('保存失败:', error);
            })
            .finally(() => {
              this.saveLoading = false;
            });
        }
      });
    },

    cancel() {
      this.params.closeOutDrawer();
    }
  }
};
</script>

<style lang="less" scoped>
.asset-permission-form-item {
  /deep/ .el-form-item__content {
    width: 100%;
  }
}

.asset-permission-container {
  width: 100%;

  .permission-row {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    width: 100%;
    gap: 5px;

    .permission-type-input {
      width: 101px;
    }

    .asset-type-select {
      width: 150px;
      flex-shrink: 0;
    }

    .asset-values-select {
      flex: 1;
      min-width: 200px;
    }

    .delete-btn {
      flex-shrink: 0;
      color: #f56c6c;
      padding: 0 8px;

      &:hover {
        color: #f78989;
      }
    }
  }

  .add-condition-btn {
    margin-top: 8px;
    width: 100%;
    border:1px dashed #02a7f0;
    background-color: #eef9fe;
  }
}
</style>
