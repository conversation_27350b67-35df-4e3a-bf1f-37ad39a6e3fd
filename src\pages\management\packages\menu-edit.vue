<template>
  <div class="menu-edit p20">
    <el-form
      ref="menuForm"
      :model="form"
      :rules="rules"
      label-width="120px"
      size="small"
    >
      <el-form-item label="菜单名称" prop="menuName">
        <el-input
          v-model="form.menuName"
          placeholder="请输入菜单名称"
        ></el-input>
      </el-form-item>
      <el-form-item label="菜单页面(path)" prop="path">
        <el-select
          class="width-full"
          v-model="form.path"
          placeholder="请选择菜单页面"
        >
          <el-option label="页面1" value="page1"></el-option>
          <el-option label="页面2" value="page2"></el-option>
          <el-option label="页面3" value="page3"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="配置子菜单" prop="path">
        <el-row :gutter="10" type="flex" justify="space-between">
          <el-col :span="4">
            <el-input-number
              v-model="form.order"
              :min="1"
              :max="10"
              label="排序"
            ></el-input-number>
          </el-col>
          <el-col :span="20">
            <el-input
              v-model="form.subMenuName"
              placeholder="请输入子菜单名称"
            />
          </el-col>
          <el-col :span="10">
            <el-select
              class="width-full"
              v-model="form.subPath"
              placeholder="请选择子菜单页面"
            >
              <el-option label="页面1" value="page1"></el-option>
              <el-option label="页面2" value="page2"></el-option>
              <el-option label="页面3" value="page3"></el-option>
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-button class="span-danger" type="text" @click="deleteSubMenu"
              >删除</el-button
            >
          </el-col>
        </el-row>
        <div class="add_btn" size="small" @click="addSubMenu">添加子菜单</div>
      </el-form-item>
      <el-form-item label="菜单图标" prop="icon">
        <el-input v-model="form.icon" placeholder="请输入菜单图标"></el-input>
      </el-form-item>
      <el-form-item label="菜单排序" prop="order">
        <el-input-number
          v-model="form.order"
          @change="handleChange"
          :min="1"
          :max="10"
          label="排序文字"
        ></el-input-number>
      </el-form-item>
      <el-form-item label="菜单描述" prop="description">
        <el-input
          type="textarea"
          rows="3"
          v-model="form.description"
          placeholder="请输入菜单描述"
        ></el-input>
      </el-form-item>
    </el-form>
    <div class="align-right mt20">
      <el-button @click="cancle" size="small">取消</el-button>
      <el-button @click="submitForm" size="small" type="primary"
        >确定</el-button
      >
    </div>
  </div>
</template>

<script>
export default {
  props: ['params'],
  data() {
    return {
      form: {
        menuName: '',
        path: '',
        icon: '',
        description: '',
        order: ''
        // position: 'top'
      },
      rules: {
        menuName: [
          { required: true, message: '请输入菜单名称', trigger: 'blur' }
        ],
        path: [
          { required: true, message: '请选择菜单页面', trigger: 'change' }
        ],
        order: [{ required: true, message: '请输入菜单排序', trigger: 'blur' }]
      }
    };
  },
  methods: {
    addSubMenu() {},
    deleteSubMenu(index) {},
    cancle() {
      this.params.closeOutDrawer();
    },
    submitForm() {
      this.$refs.menuForm.validate((valid) => {
        if (valid) {
          this.params.closeOutDrawer();
          // Form is valid, submit the data
          // Add your submit logic here
        } else {
          // Form is invalid, show error messages
        }
      });
    }
  }
};
</script>
<style lang="less" scoped>
.add_btn {
  width: 100%;
  height: 32px;
  text-align: center;
  line-height: 32px;
  background: #f5f7fa;
  color: #1789ec;
  cursor: pointer;
}
</style>
