<!--
 * @Fileoverview: 安全配置
 * @Description: 系统管理员-安全配置
-->
<template>
  <div class="safe-config" v-loading="isLoading">
    <el-form
      ref="loginForm"
      class="form"
      :model="loginForm"
      label-width="150px"
      label-position="right"
    >
      <!--      <div class="container">-->
      <!--        <div id="safe">-->
      <!--          访问IP限制-->
      <!--          <qz-tips :tipsContent="ipLimitTips" placement="bottom"></qz-tips>-->
      <!--        </div>-->
      <!--        <el-form-item label="排除名单">-->
      <!--          <el-input-->
      <!--            v-model.trim="loginForm.blackIpLimitList"-->
      <!--            class="input-box"-->
      <!--            size="medium"-->
      <!--            placeholder="请输入排除名单，支持填写IP或IP段，多个之间用英文逗号隔开"-->
      <!--          ></el-input>-->
      <!--        </el-form-item>-->
      <!--        <el-form-item label="通过名单">-->
      <!--          <el-input-->
      <!--            v-model.trim="loginForm.whiteIpLimitList"-->
      <!--            class="input-box"-->
      <!--            size="medium"-->
      <!--            placeholder="请输入通过名单，支持填写IP或IP段，多个之间用英文逗号隔开"-->
      <!--          ></el-input>-->
      <!--        </el-form-item>-->
      <!--      </div>-->
      <!--      <el-divider></el-divider>-->
      <div class="container">
        <div id="safe">登录安全</div>
        <el-form-item label="登录失败次数上限">
          <el-input-number
            @change="validateNum($event, 'failCountLimit')"
            v-model.trim="loginForm.failCountLimit"
            class="input-box"
            size="medium"
            controls-position="right"
            :precision="0"
            :min="1"
            :step="1"
          ></el-input-number>
          <span class="expand-span">次</span>
        </el-form-item>
        <el-form-item label="登录失败锁定时间">
          <el-input-number
            @change="validateNum($event, 'failLockTime')"
            v-model.trim="loginForm.failLockTime"
            class="input-box"
            size="medium"
            controls-position="right"
            :precision="0"
            :min="1"
            :step="1"
          ></el-input-number>
          <span class="expand-span">分钟</span>
        </el-form-item>
        <el-form-item label="登录超时时间">
          <el-input-number
            @change="validateNum($event, 'logoutTime')"
            v-model.trim="loginForm.logoutTime"
            class="input-box"
            size="medium"
            id="time_set"
            controls-position="right"
            :precision="0"
            :min="1"
            :step="1"
          ></el-input-number>
          <span class="expand-span">分钟</span>
        </el-form-item>
        <el-form-item label="密码过期时间">
          <el-input-number
            @change="validateNum($event, 'passwordExpireTime')"
            v-model.trim="loginForm.passwordExpireTime"
            class="input-box"
            size="medium"
            controls-position="right"
            :precision="0"
            :min="1"
            :step="1"
          ></el-input-number>
          <span class="expand-span">天</span>
        </el-form-item>
        <!--        <el-form-item>-->
        <!--          <template slot="label">-->
        <!--            <div style="display: flex; justify-content: flex-end">-->
        <!--              <qz-tips-->
        <!--                class="mr5"-->
        <!--                :tipsContent="loginAuthTis"-->
        <!--                placement="bottom"-->
        <!--              ></qz-tips>-->
        <!--              <span>登录认证</span>-->
        <!--            </div>-->
        <!--          </template>-->
        <!--          <el-select-->
        <!--            v-model.trim="loginForm.multiAuthTypeEnum"-->
        <!--            size="medium"-->
        <!--            class="input-box"-->
        <!--          >-->
        <!--            <el-option-->
        <!--              v-for="{ label, value } of LOGIN_IDENTIFY"-->
        <!--              :key="value"-->
        <!--              :value="value"-->
        <!--              :label="label"-->
        <!--            ></el-option>-->
        <!--          </el-select>-->
        <!--          <div class="mt10" v-if="loginForm.authType === 'googleAuth'">-->
        <!--            选择Google-->
        <!--            Authenticator认证后，可在账号管理的sysadmin账号详情页中查看密钥，-->
        <!--            <span class="highlight">请务必记住sysadmin这个账号的密钥</span>-->
        <!--            ，因为退出后必须要使用动态码才可登录-->
        <!--            <el-checkbox v-model="resetKeyFlag">重置全部密钥</el-checkbox>-->
        <!--          </div>-->
        <!--        </el-form-item>-->
      </div>
      <el-divider></el-divider>
      <div class="container">
        <div id="safe">密码安全</div>
        <el-radio-group v-model="loginForm.passwordLevel">
          <el-radio
            v-for="level in pwdLevelOptions"
            :key="level.value"
            :label="level.value"
          >
            {{ level.label }}
          </el-radio>
        </el-radio-group>
      </div>
      <el-divider></el-divider>
      <div class="container">
        <div id="safe">数据安全</div>
        <el-checkbox v-model="loginForm.isOpenWatermark" class="ml10">
          开启页面水印
        </el-checkbox>
      </div>
      <el-divider></el-divider>
      <div class="submit-button">
        <el-button type="small" @click="submitOnclick" :loading="btnLoading">
          保存配置信息
        </el-button>
      </div>
    </el-form>
  </div>
</template>
<script>
import { validateIp } from '@/utils/string-utils';
import {
  saveLoginSecurityInfo,
  getLoginSecurityInfos
} from '@/service/login-service';
import { mapMutations } from 'vuex';
const LOGIN_IDENTIFY = [
  { value: 'DEFAULT', label: '验证码认证' },
  { value: 'GOOGLE_AUTH', label: 'Google Authenticator认证' }
];

export default {
  data() {
    return {
      LOGIN_IDENTIFY,
      btnLoading: false,
      resetKeyFlag: false,
      keys: [
        'login_error_count',
        'login_error_time',
        'server_servlet_session_timeout',
        'pwd_expire_time',
        'password_security_level',
        'page_watermark',
        'login_authType'
      ],
      pwdLevelOptions: [
        {
          label:
            '高强度密码(密码长度8位以上，必须包含大写字母、小写字母、数字、下划线中的三类)',
          value: '3'
        },
        {
          label:
            '中强度密码(密码长度8位以上，必须包含大写字母、小写字母、数字中的两类且必须包含数字)',
          value: '2'
        },
        {
          label: '低强度密码(密码长度6位以上)',
          value: '1'
        }
      ],
      ipLimitTips: `1、填写排除名单，则排除名单中的IP不可访问产品；<br/>填写通过名单，则只允许通过名单中的IP可访问产品；<br/>同时填写排除通过名单，排除名单优先级高于通过名单，比如一个IP同时存在于排除通过名单中，则这个IP不可访问产品；<br/>2、支持填写IP或IP段，一个IP段是两个用“-”隔开的IP，多个IP和IP段之间使用中英文逗号隔开，如：************-************，************-************；`,
      loginAuthTis: ` 双因素认证操作步骤：<br />1、Google Authenticator认证需要用户在手机内下载同名APP，在APP内填写API账号名称、密钥两项内容，APP内会自动生成动态码，然后用此动态码登录系统；<br />2、开启Google认证后，可在每个账号的详情中查看密钥或密钥二维码；<br />3、该功能用于提高系统登录安全性，此开关对全部账号生效；`,

      loginForm: {
        blackIpLimitList: '',
        whiteIpLimitList: '',
        failCountLimit: 1, //登录失败次数限制
        failLockTime: 1, //登录失败锁定时间
        logoutTime: 1, //登录超时时间
        passwordExpireTime: 1, //密码过期时间
        multiAuthTypeEnum: '',
        authType: 'vcode', // 双因素认证
        passwordLevel: '1',
        isOpenWatermark: false
      },
      isLoading: false
    };
  },
  mounted() {
    this.ondetail();
  },
  methods: {
    ...mapMutations(['setWatermark']),
    ondetail() {
      getLoginSecurityInfos([
        'passwordSecurity',
        'loginConfig',
        'pageWatermark'
      ]).then((res) => {
        if (res.data.length !== 0) {
          const configList = res.data.map((item) => JSON.parse(item));
          configList.forEach((item) => {
            if (item.failCountLimit) {
              this.loginForm.failCountLimit = item.failCountLimit;
            }
            if (item.failLockTime) {
              this.loginForm.failLockTime = item.failLockTime / 60;
            }
            if (item.logoutTime) {
              this.loginForm.logoutTime = item.logoutTime / 60;
            }
            if (item.passwordExpireTime) {
              this.loginForm.passwordExpireTime = item.passwordExpireTime;
            }
            // if (item.multiAuthTypeEnum) {
            //   this.loginForm.multiAuthTypeEnum = item.multiAuthTypeEnum;
            // }
            if (item.passwordLevel) {
              this.loginForm.passwordLevel = item.passwordLevel;
            }
            if (item.isOpenWatermark) {
              this.loginForm.isOpenWatermark = item.isOpenWatermark;
            }
          });
        }
      });
    },
    submitOnclick() {
      const loginConfig = [
        {
          key: 'loginConfig',
          value: JSON.stringify({
            failCountLimit:
              this.loginForm.failCountLimit == 0
                ? null
                : this.loginForm.failCountLimit,
            failLockTime:
              this.loginForm.failLockTime == 0
                ? null
                : this.loginForm.failLockTime * 60,
            logoutTime:
              this.loginForm.logoutTime == 0
                ? null
                : this.loginForm.logoutTime * 60,
            passwordExpireTime: this.loginForm.passwordExpireTime
            // multiAuthTypeEnum: this.loginForm.multiAuthTypeEnum,
          })
        }
      ];

      const passwordSecurity = [
        {
          key: 'passwordSecurity',
          value: JSON.stringify({
            passwordLevel: this.loginForm.passwordLevel
          })
        }
      ];

      const pageWatermark = [
        {
          key: 'pageWatermark',
          value: JSON.stringify({
            isOpenWatermark: this.loginForm.isOpenWatermark
          })
        }
      ];

      // 所有异步请求打包
      Promise.all([
        saveLoginSecurityInfo(loginConfig),
        saveLoginSecurityInfo(passwordSecurity),
        saveLoginSecurityInfo(pageWatermark)
      ])
        .then(() => {
          this.$message.success('保存配置成功！');
          //刷新表单
          this.ondetail();
          this.setWatermark(this.loginForm.isOpenWatermark);
        })
        .catch((err) => {
          console.error(err);
          this.$message.error('保存失败，请稍后重试！');
        });
    },
    validateLimitIp(ipLimitList) {
      if (ipLimitList) {
        const ipList = ipLimitList.split(',');
        for (const ip of ipList) {
          const ipSegList = ip.split('-');
          if (ipSegList.length > 2) {
            this.$message.warning('请输入合法的网段');
            return false;
          } else if (ipSegList.length === 2) {
            const [ip1, ip2] = ipSegList;
            if (!validateIp(ip1)) {
              this.$message.warning(`网段${ip}中的IP：${ip1}不是有效IP！`);
              return false;
            }
            if (!validateIp(ip2)) {
              this.$message.warning(`网段${ip}中的IP：${ip2}不是有效IP！`);
              return false;
            }
          } else {
            if (!validateIp(ip)) {
              this.$message.warning(`IP：${ip}不是有效IP！`);
              return false;
            }
          }
        }
        return true;
      }
    },
    validateNum(num, key) {
      if (num === undefined) {
        this.loginForm[key] = 0;
      }
    }
  }
};
</script>
<style lang="less" scoped>
.safe-config {
  .highlight {
    color: @orange-deep-color;
    margin: 0 2px;
  }
  .el-form-item__content {
    line-height: 25px;
  }
  .input-box {
    width: 440px;
  }
  .container {
    width: 700px;
    margin: auto;
    .form {
      width: 700px;
    }
    #safe {
      height: 20px;
      margin-top: 30px;
      margin-bottom: 20px;
      cursor: default;
      display: flex;
      align-items: center;
      i {
        margin-left: 5px;
      }
    }
    .el-radio {
      margin: 12px;
    }
    .el-radio-group {
      margin: 10px 0;
    }
    .expand-span {
      display: 'inline-block';
      margin: 0px 16px;
    }
    .el-input-number .el-input__inner {
      text-align: left;
    }
  }
  .el-divider--horizontal {
    display: block;
    height: 1px;
    width: 100%;
    margin: 50px 0;
  }
  .icon-tishi-lv {
    margin-right: 5px;
    color: @text-tip-color;
  }
  .submit-button {
    display: flex;
    justify-content: space-around;
    margin-bottom: 100px;
  }
}
</style>
