<!--
 * @Fileoverview: 基本信息配置
 * @Description: 配置-基础配置-基本信息配置
-->
<template>
  <div class="basic-info-base" v-loading="isLoading">
    <el-form class="qz-form" label-width="150px">
      <el-form-item label="系统名称">
        <el-input
          v-model.trim="productName"
          type="text"
          size="small"
          placeholder="请输入系统名称"
        ></el-input>
      </el-form-item>
      <!--      <el-form-item label="系统版权">-->
      <!--        <el-input-->
      <!--            v-model.trim="copyright"-->
      <!--            type="text"-->
      <!--            size="small"-->
      <!--            placeholder="请输入系统版权"-->
      <!--        ></el-input>-->
      <!--      </el-form-item>-->
      <template>
        <el-form-item label="系统Logo">
          <el-upload
            ref="logoUpload"
            :action="DATA_URL_BASIC_CONFIG_SAVE"
            :file-list="logoList"
            :on-change="handleLogoChange"
            :on-remove="() => clearFiles('logoUpload')"
            :auto-upload="false"
            name="multipartFile"
            accept=".png"
          >
            <el-button size="small">
              <qz-icon class="icon-upload icon"></qz-icon>
              点击上传
            </el-button>
            <div slot="tip" class="el-upload__tip">
              文件格式.png，大小不超过1M，尺寸为26px～94px
            </div>
          </el-upload>
        </el-form-item>
        <el-form-item label="Logo预览">
          <div class="bg-color">
            <img class="logo" :src="logoImg" alt="logo" />
          </div>
        </el-form-item>
        <el-form-item label="系统Icon">
          <el-upload
            ref="iconUpload"
            :action="DATA_URL_BASIC_CONFIG_SAVE"
            :file-list="iconList"
            :on-change="handleIconChange"
            :on-remove="() => clearFiles('iconUpload')"
            :auto-upload="false"
            name="multipartFile"
            accept=".ico"
          >
            <el-button size="small">
              <qz-icon class="icon-upload icon"></qz-icon>
              点击上传
            </el-button>
            <div slot="tip" class="el-upload__tip">
              文件格式.ico，大小不超过1M
            </div>
          </el-upload>
        </el-form-item>
        <el-form-item label="Icon预览">
          <img class="logo" :src="iconImg" alt="icon" />
        </el-form-item>
      </template>
      <el-form-item label="登录页底图">
        <el-upload
          ref="backImgUpload"
          :action="DATA_URL_BASIC_CONFIG_SAVE"
          :file-list="backList"
          :on-change="handleBackChange"
          :on-remove="() => clearFiles('backImgUpload')"
          :auto-upload="false"
          name="multipartFile"
          accept=".png,.gif"
        >
          <el-button size="small">
            <qz-icon class="icon-upload icon"></qz-icon>
            点击上传
          </el-button>
          <div slot="tip" class="el-upload__tip">
            文件格式.png、.gif，大小不超过3M
          </div>
        </el-upload>
      </el-form-item>
      <el-form-item label="底图预览">
        <img class="back-img" :src="backImg" alt="底图" />
      </el-form-item>
    </el-form>
    <div class="align-center mt20">
      <el-button
        type="primary"
        @click="save"
        size="small"
        :loading="isButtonLoading"
      >
        保存
      </el-button>
    </div>
  </div>
</template>

<script>
import { getConfigInfo, saveConfigInfo } from '@/service/basic-config-service';
import {
  BASIC_INFO_TYPE_NAME,
  BASIC_INFO_TYPE_LOGO,
  BASIC_INFO_TYPE_ICON,
  BASIC_INFO_TYPE_BACK_IMG
} from '@/constant/common-constants';
import { DATA_URL_BASIC_CONFIG_SAVE } from '@/constant/data-url-constants';
const defaultIcon = require('@/assets/imgs/qz_icon_blue.png');
const defaultLogo = require('@/assets/imgs/qz_logo_w.png');
const defaultBack = require('@/assets/imgs/qz_bg.png');
import { mapMutations } from 'vuex';
export default {
  data() {
    return {
      BASIC_INFO_TYPE_NAME,
      BASIC_INFO_TYPE_LOGO,
      BASIC_INFO_TYPE_ICON,
      DATA_URL_BASIC_CONFIG_SAVE,
      logoImg: defaultLogo,
      iconImg: defaultIcon,
      backImg: defaultBack,
      backImage: defaultBack,
      logoConfigId: '',
      iconConfigId: '',
      nameConfigId: '',
      backConfigId: '',

      logoList: [],
      iconList: [],
      backList: [],
      backImgList: [],
      isLoading: false,
      isButtonLoading: false,
      productName: '数据分类分级系统',
      copyright: 'copyright ©全知科技（杭州）有限责任公司 版权所有',
      productVer: ''
    };
  },
  mounted() {
    this.getConfigInfo();
  },
  methods: {
    ...mapMutations([
      'setCopyright',
      'setLogoImg',
      'setIconImg',
      'setProductName',
      'setBackImg'
    ]),
    getConfigInfo() {
      this.isLoading = true;
      let configList;
      getConfigInfo()
        .then((res) => {
          configList = res.data || [];
          if (res.data) {
            if (configList.brandLogo) {
              this.logoImg = configList.brandLogo;
            }
            if (configList.brandIcon) {
              this.iconImg = configList.brandIcon;
            }
            if (configList.brandName) {
              this.productName = configList.brandName;
            }
            if (configList.brandBackgroundPicture) {
              this.backImg = configList.brandBackgroundPicture;
            }
            if (configList.copyrightInformation) {
              this.copyright = configList.copyrightInformation;
            }
          }
        })
        .finally(() => {
          this.setLogoImg(this.logoImg);
          this.setIconImg(this.iconImg);
          this.setProductName(this.productName);
          this.setBackImg(this.backImg);
          this.setCopyright(this.copyright);
          this.isLoading = false;
        });
    },

    save() {
      if (this.productName && this.productName.length > 20) {
        this.$message.error('产品名称请限制在20个字符以内');
        return;
      }
      const params = {
        brandBackgroundPicture: this.backList[0]?.url || '',
        brandIcon: this.iconList[0]?.url || '',
        brandLogo: this.logoList[0]?.url || '',
        brandName: this.productName || '',
        copyrightInformation: this.copyright || ''
      };
      this.loading = true;
      saveConfigInfo(params)
        .then(
          () => {
            this.getConfigInfo();
            this.$message.success('保存成功');
            // location.reload();
          },
          (err) => {
            this.$message.error(err.msg || '保存失败');
          }
        )
        .finally(() => {
          this.loading = false;
        });
    },

    // 处理产品logo变化
    handleLogoChange(file, fileList) {
      this.logoList = [];
      this.validateFileSize(file.raw).then(
        () => {
          const reader = new FileReader();
          reader.onload = (res) => {
            this.logoImg = res.target.result;
          };
          reader.readAsDataURL(file.raw);
          this.getBase64(file.raw).then((res) => {
            this.logoList.push({ name: file.name, url: res });
          });
        },
        () => {
          this.$message.error('上传的文件大小请不要超过1M');
          this.clearFiles('logoUpload');
        }
      );
    },
    // 处理产品icon变化
    handleIconChange(file, fileList) {
      this.iconList = [];
      this.validateFileSize(file.raw).then(
        () => {
          const reader = new FileReader();
          reader.onload = (res) => {
            this.iconImg = res.target.result;
          };
          reader.readAsDataURL(file.raw);
          this.getBase64(file.raw).then((res) => {
            this.iconList.push({ name: file.name, url: res });
          });
        },
        () => {
          this.$message.error('上传的文件大小请不要超过1M');
          this.clearFiles('iconUpload');
        }
      );
    },

    // 处理底图变化
    handleBackChange(file, fileList) {
      this.backList = [];
      this.valibackFileSize(file.raw).then(
        () => {
          const reader = new FileReader();
          reader.onload = (res) => {
            this.backImg = res.target.result;
          };
          reader.readAsDataURL(file.raw);
          this.getBase64(file.raw).then((res) => {
            this.backList.push({ name: file.name, url: res });
          });
        },
        () => {
          this.$message.error('上传的文件大小请不要超过3M');
          this.clearFiles('backImgUpload');
        }
      );
    },

    getBase64(file) {
      return new Promise(function (resolve, reject) {
        const reader = new FileReader();
        let imgResult = '';
        reader.readAsDataURL(file);
        reader.onload = function () {
          imgResult = reader.result;
        };
        reader.onerror = function (error) {
          reject(error);
        };
        reader.onloadend = function () {
          resolve(imgResult);
        };
      });
    },

    // 校验文件大小
    validateFileSize(file) {
      const fileSizeLimt = 1 * 1024 * 1024;
      if (file.size > fileSizeLimt) {
        return Promise.reject(false);
      }
      return Promise.resolve(true);
    },
    valibackFileSize(file) {
      const fileSizeLimt = 3 * 1024 * 1024;
      if (file.size > fileSizeLimt) {
        return Promise.reject(false);
      }
      return Promise.resolve(true);
    },
    // 清除文件
    clearFiles(ref) {
      if (ref === 'logoUpload') {
        this.logoImg = '';
        this.logoList = [];
      } else if (ref === 'iconUpload') {
        this.iconImg = '';
        this.iconList = [];
      } else if (ref === 'backImgUpload') {
        this.backImage = '';
        this.backList = [];
      }
    }
  }
};
</script>

<style lang="less" scoped>
.basic-info-base {
  margin-bottom: 50px;
  .qz-form {
    width: 800px;
    margin-top: 50px;
    margin: 50px auto 0 auto;
  }
  & .bg-color {
    background: @text-primary-color;
    height: 45px;
    width: 100%;
    padding-left: 20px;
    display: flex;
    align-items: center;
  }
  .logo {
    height: 26px;
  }
  & .icon {
    font-size: 12px;
    vertical-align: top;
    margin-right: 10px;
  }
  & .el-upload__tip {
    margin-top: 0;
    line-height: 30px;
    color: @text-tip-color;
  }
  .back-img {
    width: 110px;
    height: 150px;
  }
}
</style>
