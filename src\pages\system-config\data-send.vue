<!--
 * @Fileoverview: 数据推送
 * @Description: 配置-数据推送
-->
<template>
  <div class="subscribe-configs">
    <api-table
      ref="table"
      border
      table-id="subscribeConfigList"
      :data-source="getDataList"
      toolsLayout="refresh, divider, headerPrepend"
    >
      <api-table-tool-register id="headerPrepend">
        <el-button type="primary" size="mini" @click="showSubscribeManagement">
          推送方式管理
        </el-button>
        <el-button type="primary" size="mini" @click="showSubscribeConfig">
          新增推送任务
        </el-button>
      </api-table-tool-register>
      <api-table-column
        label="任务名称"
        prop="name"
        min-width="210"
      ></api-table-column>
      <api-table-column label="推送资产" prop="scope" min-width="210">
        <template slot-scope="{ row }">
          {{ formatScope(row.scope) }}
        </template>
      </api-table-column>
      <api-table-column
        label="推送方式"
        prop="type"
        min-width="210"
      ></api-table-column>
      <api-table-column label="推送周期" prop="executionType" min-width="210">
        <template slot-scope="{ row }">
          {{ row.executionType === 'IMMEDIATE' ? '单次同步' : '周期性同步' }}
        </template>
      </api-table-column>
      <api-table-column prop="state" label="最近一次推送状态" min-width="250">
        <template slot-scope="{ row }">
          {{ stateMap[row.state] }}
        </template>
      </api-table-column>
      <api-table-column
        prop="updatedAt"
        label="最近一次推送时间"
        min-width="210"
        formatter="formatTime"
      ></api-table-column>
      <api-table-column
        prop="createdAt"
        label="任务创建时间"
        min-width="210"
        formatter="formatTime"
      ></api-table-column>
      <api-table-column key="operation" label="操作" width="200" fixed="right">
        <template slot-scope="{ row }">
          <span class="mr10 action-link" @click="showSubscribeConfig(row)">
            编辑
          </span>
          <qz-popconfirm
            title="删除后无法恢复，确定删除该数据？"
            @confirm="del(row)"
          >
            <span slot="reference" class="mr10 action-link color-danger">
              删除
            </span>
          </qz-popconfirm>
          <span class="action-link" @click="showLog(row.id)">推送日志</span>
        </template>
      </api-table-column>
    </api-table>

    <qz-api-drawer
      :title="`${id ? '编辑' : '新增'}数据推送任务`"
      :visible.sync="subscribeConfigDrawer"
      size="80%"
      destroy-on-close
      :isShowFooter="true"
      :isLoading="saveLoading"
      @save="handleSave"
    >
      <subscribe-config-edit
        ref="subscribeConfigEdit"
        :id="id"
        :isLoading.sync="saveLoading"
        :show.sync="subscribeConfigDrawer"
        @reload="reload"
      />
    </qz-api-drawer>

    <qz-api-drawer
      title="推送方式管理"
      size="70%"
      :visible.sync="subscribeManagementDrawer"
      destroy-on-close
    >
      <sync-channel />
    </qz-api-drawer>
  </div>
</template>
<script>
import {
  getSubscribeConfigList,
  deleteSubscribeTask,
  changeSubscribeConfigStatus
} from '@/service/subscribe-configs-service';
import SubscribeConfigEdit from './packages/subscribe-configs/subscribe-config-edit.vue';
import SyncChannel from './packages/subscribe-configs/packages/sync-channel.vue';
import { getList } from '@/service/data-send-service';
export default {
  components: { SubscribeConfigEdit, SyncChannel },
  data() {
    return {
      subscribeConfigDrawer: false,
      subscribeManagementDrawer: false,
      id: '',
      saveLoading: false,
      stateMap: {
        INIT: '待执行 ',
        FAIL: '失败',
        SUCCESS: '成功',
        RUNNING: '运行中'
      }
    };
  },
  mounted() {
    if (this.$route.query.curb) {
      this.showSubscribeManagement();
    }
    if (this.$route.query.new) {
      this.showSubscribeConfig();
    }
  },
  methods: {
    handleValidSuccess(formData) {
      this.subscribeConfigDrawer = false;
      this.reload();
    },

    // 获取数据标签清单数据
    getDataList(params) {
      return getList(params);
      // .then((res) => {
      //   return {
      //     data: {
      //       rows: res.data.rows,
      //       totalCount: res.data.count
      //     }
      //   };
      // });
    },
    showLog(id) {
      this.$DrawAlert({
        params: {
          id,
          callBack: () => {}
        },
        title: '推送日志',
        width: 60,
        componentObj: {
          component: () => import('./packages/data-send/index.vue')
        }
      });
    },
    handleSave() {
      this.$refs.subscribeConfigEdit.save();
    },
    reload() {
      this.$refs.table.reload();
    },
    showSubscribeConfig(row = {}) {
      this.subscribeConfigDrawer = true;
      this.id = row.id;
    },
    showSubscribeManagement() {
      this.subscribeManagementDrawer = true;
    },
    getSubscribeConfigList(params) {
      return getSubscribeConfigList(params);
    },
    del(row) {
      this.$refs.table.loading = true;
      deleteSubscribeTask(row.id)
        .then(() => {
          this.$message.success('操作成功');
          this.reload();
        })
        .catch((err) => {
          this.$message.error(err.msg || '操作失败');
        })
        .finally(() => {
          this.$refs.table.loading = false;
        });
    },
    handleChangeEnable(row) {
      this.$refs.table.loading = true;
      changeSubscribeConfigStatus(row)
        .then(
          () => {
            this.$message.success('操作成功');
            this.reload();
          },
          (err) => {
            row.enabled = !row.enabled;
            this.$message.error(err.msg || '操作失败');
          }
        )
        .finally(() => {
          this.$refs.table.loading = false;
        });
    },
    formatScope(scope) {
      const nameArr = [];
      scope.dc_data_source?.forEach((item) => {
        nameArr.push(item.name);
      });
      scope.dc_database?.forEach((item) => {
        nameArr.push(item.name);
      });
      return nameArr.filter((item) => item).join('、') || '--';
    }
  }
};
</script>
