<template>
  <div class="config-mange">
    <div class="config-container" style="display: flex">
      <el-aside class="config-aside">
        <div
          @click="toShowConfig(index)"
          :class="['config-item', { actived: activeIndex == index }]"
          v-for="(cItem, index) in configList"
          :key="index"
        >
          {{ cItem.name }}
        </div>
      </el-aside>
      <el-main>
        <template v-if="configList[activeIndex].inner">
          <component :is="configList[activeIndex].com"></component>
        </template>
        <template v-else>
          <!-- 这里展示动态的菜单，amis -->
          <react-proxy
            :component="ReportPreview"
            :props="{ schema, params: sparams }"
          ></react-proxy>
        </template>
      </el-main>
    </div>
  </div>
</template>

<script>
import { getMenuData } from '@/utils/storage-utils';
import ReportPreview from '@/components-react/report-preview.jsx';
import { getAmisConfig } from '@/service/role-service';
import configComList from '@/router/system-config';
export default {
  data() {
    return {
      ReportPreview,
      schema: {},
      sparams: {}, //额外的参数
      configList: [],
      staticList: configComList,
      activeIndex: 0
    };
  },

  components: {},

  created() {
    const menuDataList = getMenuData();
    const configMenuList =
      menuDataList.find((item) => item.code === 'config')?.children || [];
    const staticCodes = this.staticList.map((item) => item.code);
    configMenuList.forEach((item) => {
      if (staticCodes.includes(item.code)) {
        const info = this.staticList.find((sitem) => sitem.code === item.code);
        const fixInfo = {
          name: info.title,
          com: info.component,
          code: info.code,
          inner: true
        };
        item = Object.assign(item, fixInfo);
      } else {
        item = Object.assign(item, { inner: false });
      }
    });
    this.configList = configMenuList;
    if (this.$route.query?.code) {
      this.activeIndex = this.configList.findIndex(
        (item) => item.code === this.$route.query.code
      );
    }
    this.toShowConfig(this.activeIndex);
  },

  methods: {
    toShowConfig(index) {
      this.activeIndex = index;
      const currentInfo = this.configList[index];
      if (!currentInfo.inner) {
        getAmisConfig({ resourceCode: currentInfo.code }).then((res) => {
          this.schema = JSON.parse(res?.data[0]?.pageConfig) || {};
        });
      }
      //下面这个操作是为了，根据code记录日志，统一维护
      const url = new URL(window.location.href);
      url.searchParams.set('code', currentInfo.code);
      // 不刷新页面更新地址栏
      window.history.pushState(null, '', url.toString());
    }
  }
};
</script>
<style lang="less" scoped>
/deep/ .el-container {
  padding: 0 !important;
}
.config-mange {
  height: 100%;
  .config-container {
    height: 100%;
    .config-aside {
      height: 100%;
      width: 220px !important;
      border-right: 1px solid #ebe8e8;
      .config-item {
        height: 50px;
        line-height: 50px;
        text-align: center;
        cursor: pointer;
      }
      .config-item:hover {
        color: rgb(24, 192, 239);
        background: #7c7c7c14;
      }
      .actived {
        color: rgb(24, 192, 239);
        background: #7c7c7c14;
      }
    }
  }
}
</style>
