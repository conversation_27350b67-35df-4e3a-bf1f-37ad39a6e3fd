<template>
  <div class="notice-manage">
    <api-table
      ref="table"
      table-id="notice-list"
      :data-source="[]"
      :search-input-options="{
        key: 'content',
        placeholder: '请输入通知内容进行筛选'
      }"
      tools-layout="searchInput,filter,refresh, add"
    >
      <api-table-tool-register id="add">
        <el-button type="primary" size="mini" @click="readAll">
          一键全读
        </el-button>
      </api-table-tool-register>
      <api-table-column label="通知时间" prop="timers"></api-table-column>
      <api-table-column label="通知内容" prop="c"></api-table-column>
      <api-table-column
        label="通知类型"
        prop="type"
        :search-config="{
          type: 'single-selection',
          options: noticeOptions
        }"
      >
        <template slot-scope="{ row }">
          <span>{{ row.type }}</span>
        </template>
      </api-table-column>
      <api-table-column
        label="状态"
        prop="sta"
        :search-config="{
          type: 'single-selection',
          options: statusOptions
        }"
      >
      </api-table-column>
      <api-table-column label="操作" prop="option">
        <template slot-scope="{ row }">
          <el-button type="text">点击已读</el-button>
        </template>
      </api-table-column>
    </api-table>
  </div>
</template>

<script>
export default {
  data() {
    return {
      noticeOptions: [
        { value: true, label: '任务异常' },
        { value: false, label: '系统异常' }
      ],
      statusOptions: [
        { value: true, label: '已读' },
        { value: false, label: '未读' }
      ]
    };
  },

  components: {},

  mounted() {},

  methods: {
    readAll() {}
  }
};
</script>
<style lang="less" scoped>
.notice-manage {
}
</style>
