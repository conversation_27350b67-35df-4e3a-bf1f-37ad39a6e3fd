<!--
 * @Fileoverview: 文档管理
 * @Description: 配置-文档管理
-->
<template>
  <div class="data-open">
    <api-table
      ref="table"
      table-id="openApiList"
      :data-source="getDataList"
      :border="true"
      :search-input-options="{
        key: 'callerId',
        placeholder: '请输入调用ID进行筛选'
      }"
      toolsLayout="searchInput,openApi"
    >
      <api-table-tool-register id="openApi">
        <el-button type="primary" size="mini" class="mr10" @click="addNewKey">
          新增授权
        </el-button>
        <el-button
          v-if="docList.length === 1"
          type="primary"
          size="mini"
          @click="previewPdf(docList[0])"
        >
          预览开放接口
        </el-button>
        <el-dropdown
          v-if="docList.length > 1"
          type="primary"
          size="mini"
          split-button
        >
          预览开放接口
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item
              v-for="doc of docList"
              :key="doc"
              @click.native="previewPdf(doc)"
            >
              {{ doc }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </api-table-tool-register>
      <api-table-column prop="callerId" label="调用者ID">
        <template #default="{ row }">
          {{ row.callerId }}
          <el-tag v-if="row.state === 1" type="danger" size="mini"
            >已过期</el-tag
          >
        </template>
      </api-table-column>
      <api-table-column prop="expiredTime" label="使用截止日期">
        <template #default="scope">
          <span v-if="scope.row.expiredTime">{{
            convertTimestampToDateTime(scope.row.expiredTime)
          }}</span>
          <span v-else>不限</span>
        </template>
      </api-table-column>

      <api-table-column
        prop="createTime"
        label="创建时间"
        :formatter="handlerConvertTimestampToDateTime"
      ></api-table-column>

      <api-table-column label="操作" width="160">
        <template slot-scope="{ row }">
          <span class="action-link" @click="getKeyData(row)">查看</span>
          <qz-popconfirm
            title="删除后立即生效"
            content="删除动作不可撤回，你还要继续吗？"
            class="action-link"
            @confirm="deleteKey(row)"
          >
            <span class="action-link danger" slot="reference">删除</span>
          </qz-popconfirm>
        </template>
      </api-table-column>
    </api-table>
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="690px"
      height="312px"
      :before-close="closeDialog"
    >
      <div v-if="!isAdd" class="cancel">
        <span @click="cancelSensitive" class="action-link" v-if="isSensitive">
          取消脱敏
        </span>
      </div>
      <el-alert
        v-if="isError"
        :title="errorMsg"
        type="error"
        @close="isError = false"
      >
      </el-alert>
      <el-form
        :model="form"
        ref="form"
        v-loading="keyLoading"
        :disabled="!isAdd"
      >
        <el-form-item
          prop="callerId"
          :rules="[
            { required: true, trigger: 'blur', message: '调用ID不可为空' },
            { max: 32, trigger: 'blur', message: '调用ID不可超过32个字符' },
            {
              pattern: /^[A-Za-z0-9-_()]*$/,
              trigger: 'blur',
              message: '调用ID仅允许输入大小写字母、数字、特殊字符-_()'
            }
          ]"
        >
          <el-tooltip
            slot="label"
            content="不可重复，不可超过32个字符，仅允许输入大小写字母、数字、特殊字符-_()"
            placement="top"
          >
            <span>调用ID<i class="el-icon-info ml5"></i></span>
          </el-tooltip>
          <span v-if="!isAdd">{{ form.callerId }}</span>
          <el-input
            v-if="isAdd"
            v-model.trim="form.callerId"
            placeholder="不可重复，不可超过32个字符，仅允许输入大小写字母、数字、特殊字符-_()"
            size="small"
            clearable
          ></el-input>
        </el-form-item>
        <div class="tips" v-if="isAdd">*设置完成后不可修改</div>
        <el-form-item
          label="AccessKey"
          prop="accessKey"
          :rules="[
            { required: true, trigger: 'blur', message: 'accessKey不可为空' }
          ]"
        >
          {{ form.accessKey }}
        </el-form-item>
        <el-form-item
          label="SecretKey"
          prop="secretKey"
          :rules="[
            { required: true, trigger: 'blur', message: 'secretKey不可为空' }
          ]"
        >
          {{ form.secretKey }}
        </el-form-item>
        <el-form-item label="使用截止日期" prop="enable" class="is-required">
          <el-radio-group v-model="form.enable">
            <el-radio :label="false">不限</el-radio>
            <el-radio :label="true">
              限制
              <el-tooltip content="当日结束，不可使用" placement="top">
                <i class="el-icon-info"></i>
              </el-tooltip>
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          v-if="form.enable === true"
          :rules="[
            { required: true, trigger: 'blur', message: '请选择截止日期' }
          ]"
          label="选择日期"
          prop="expiredTime"
        >
          <el-date-picker
            v-model="form.expiredTime"
            type="date"
            placeholder="请选择使用截止日期，当日结束后不再可使用"
            size="small"
            value-format="timestamp"
            class="full-width"
          >
          </el-date-picker>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="closeDialog">取消</el-button>
        <el-button
          type="primary"
          :disabled="!isAdd"
          @click="submitForm"
          :loading="isSubmit"
        >
          确定
        </el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  getListKey,
  getNewKey,
  getSensitiveKey,
  saveKey,
  deleteKey,
  cancelKey,
  editKey,
  getDocList
} from '@/service/data-open-service';
import { DATA_URL_PREVIEW_AUTHORIZATION } from '@/constant/data-url-constants';
import { convertTimestampToDateTime, formatTime } from '@/utils/string-utils';
import { mapState } from 'vuex';
export default {
  data() {
    return {
      docList: [],
      dialogTitle: '新增授权',
      dialogVisible: false,
      keyLoading: false,
      isSubmit: false,
      isError: false,
      errorMsg: '错误',
      isSensitive: true,
      isAdd: true, //是否为新增授权
      form: {
        useKey: '',
        callerId: '',
        accessKey: '',
        secretKey: '',
        expiredTime: null,
        date: '',
        createDate: '',
        enable: false
      }
    };
  },
  computed: {
    ...mapState(['isMaster'])
  },
  watch: {
    'form.expiredTime'() {
      if (this.form.date == 0) {
        this.form.date = '';
      }
      if (this.form.expiredTime == 1) {
        this.form.date = '';
      }
    }
  },
  created() {
    this.form.date = '';
  },
  mounted() {
    // getDocList()
    //   .then((res) => {
    //     this.docList = res.data || [];
    //   })
    //   .catch((err) => (this.docList = err));
  },
  methods: {
    convertTimestampToDateTime,
    handlerConvertTimestampToDateTime(info) {
      return this.convertTimestampToDateTime(info.createTime);
    },
    // 取消脱敏
    cancelSensitive() {
      const paramsCancel = {
        id: this.form.id,
        callerId: this.form.callerId
      };
      cancelKey(paramsCancel).then(
        (res) => {
          this.form.accessKey = res.data.accessKey || '';
          this.form.secretKey = res.data.secretKey || '';
          this.isSensitive = false;
        },
        (err) => {
          this.$message.error(err.msg || '取消脱敏失败');
        }
      );
    },
    // 获取授权详情
    getKeyData(row) {
      this.isSensitive = true;
      this.dialogTitle = '查看授权';
      this.dialogVisible = true;
      this.isAdd = false;
      this.isError = false;
      this.isSubmit = false;
      const detailParams = {
        id: row.id,
        callerId: row.callerId
      };
      getSensitiveKey(detailParams).then(
        (res) => {
          this.form = {
            id: res.data.id,
            callerId: res.data.callerId,
            // 后端id对应前端accessKey
            accessKey: res.data.accessKey,
            // 后端key对应前端secretKey
            secretKey: res.data.secretKey,
            enable: res.data.enable,
            expiredTime: res.data.expiredTime
              ? new Date(res.data.expiredTime).getTime()
              : null,
            date: res.data.expiredTime,
            createDate: res.data.createDate
          };
        },
        (err) => {
          this.isError = true;
          this.errorMsg = err.msg || '获取授权详情失败';
        }
      );
    },
    // 获取随机生成的密钥
    addNewKey() {
      this.isAdd = true;
      this.dialogTitle = '新增授权';
      this.dialogVisible = true;
      this.isSensitive = true;
      this.isError = false;
      this.keyLoading = true;
      getNewKey()
        .then(
          (res) => {
            this.form = {
              callerId: '',
              accessKey: res.data.id,
              secretKey: res.data.key,
              date: '',
              enable: false,
              expiredTime: '',
              createTime: res.data.createTime
            };
          },
          (err) => {
            this.isError = true;
            this.errorMsg = err.msg || '获取随机生成的密匙失败';
          }
        )
        .finally(() => {
          this.keyLoading = false;
        });
    },
    // 删除密钥
    deleteKey(row) {
      deleteKey(row).then(
        () => {
          this.$message.success('删除成功');
          this.$refs.table.reloadCurrentPage();
        },
        (err) => {
          this.$message.error(err.msg || '删除失败');
        }
      );
    },
    // 预览开放接口
    previewPdf(fileName) {
      window.open(
        `${location.origin}${
          window._openRestyConfig?.basePath || ''
        }${DATA_URL_PREVIEW_AUTHORIZATION}?fileName=${fileName}`
      );
    },
    // 校验日期
    validateDate() {
      if (this.form.expiredTime === 2) {
        if (!this.form.date) {
          this.isError = true;
          this.errorMsg = '日期不可为空';
          return false;
        } else {
          this.isError = false;
          return true;
        }
      }
      return true;
    },
    // 保存授权
    submitForm() {
      this.$refs.form.validate((valid) => {
        this.isError = false;
        if (valid && this.validateDate()) {
          this.isSubmit = true;
          if (this.isAdd) {
            const key = {
              callerId: this.form.callerId,
              accessKey: this.form.accessKey,
              secretKey: this.form.secretKey,
              expiredTime: this.form.expiredTime
                ? new Date(this.form.expiredTime).toISOString().slice(0, 19) +
                  '.000000'
                : null, // "2025-04-11T15:30:00.000000"
              enable: this.form.enable
            };
            saveKey(key).then(
              () => {
                this.isSubmit = false;
                this.$message.success('新增授权成功');
                this.closeDialog();
                this.$refs.table.reload();
              },
              (err) => {
                this.isError = true;
                this.isSubmit = false;
                this.errorMsg = err.msg || '新增授权失败';
              }
            );
          }
        } else {
          return false;
        }
      });
    },
    // 关闭对话框
    closeDialog() {
      this.dialogVisible = false;
      this.$refs.form.clearValidate();
      this.$refs.form.resetFields();
    },
    // 获取数据标签清单数据
    getDataList(params) {
      return getListKey(params).then((res) => {
        if (res.data.rows && res.data.rows.length > 0) {
          res.data.rows.forEach((row) => {
            // row.expiredTime = row.enable
            //   ? formatTime(row.expiredTime, 'YYYY-MM-DD')
            //   : '不限';
          });
        }
        return res;
      });
    }
  }
};
</script>

<style lang="less" scoped>
.data-open {
  margin-bottom: 20px;
  & .cancel {
    font-weight: 600;
    font-size: 12px;
    color: @border-deep-blue-color;
    margin-top: -57px;
    position: absolute;
    right: 50px;
  }
  & .link {
    cursor: pointer;
    margin-right: 10px;
    font-size: 14px;
    color: @border-deep-blue-color;
    font-weight: 400;
  }
  .tips {
    font-size: 12px;
    color: @text-tip-color;
    font-weight: 400;
    text-align: right;
  }
  /deep/ .el-dialog {
    border-radius: 5px;
  }
  /deep/ .el-dialog__body {
    padding: 20px 40px 0 20px;
  }
  /deep/ .el-alert {
    margin-bottom: 20px;
  }
  /deep/ .el-form-item {
    display: flex;
    width: 100%;
    align-items: center;
    margin-bottom: 10px;
  }
  /deep/ .el-form-item__content {
    width: 100%;
  }
  /deep/ .el-form-item__label {
    width: 150px;
  }
}
</style>
