<template>
  <div class="data-send-log">
    <api-table
      ref="table"
      border
      table-id="data-send-log"
      :data-source="getDataList"
      toolsLayout="headerPrepend"
    >
      <api-table-tool-register id="headerPrepend">
        <div class="flex">
          <el-select
            class="mr10"
            v-model="status"
            placeholder="请选择推送状态"
            size="small"
            clearable
          >
            <el-option label="推送成功" value="SUCCESS"></el-option>
            <el-option label="推送失败" value="FAIL"></el-option>
            <el-option label="待执行" value="INIT"></el-option>
            <el-option label="执行中" value="RUNNING"></el-option>
          </el-select>
          <el-button type="primary" size="mini" @click="search">查询</el-button>
        </div>
      </api-table-tool-register>
      <api-table-column
        label="推送时间"
        prop="pushTime"
        min-width="210"
        formatter="formatTime"
      ></api-table-column>
      <api-table-column label="推送状态" prop="status" min-width="210">
        <template slot-scope="{ row }">
          {{ stateMap[row.status] }}
        </template>
      </api-table-column>
      <api-table-column
        label="失败原因"
        prop="errorMessage"
        min-width="210"
      ></api-table-column>
    </api-table>
  </div>
</template>

<script>
import { getSubscribeLogList } from '@/service/subscribe-configs-service';
export default {
  props: ['params'],
  data() {
    return {
      status: null,
      stateMap: {
        INIT: '待执行 ',
        FAIL: '推送失败',
        SUCCESS: '推送成功',
        RUNNING: '执行中'
      }
    };
  },

  methods: {
    getDataList(params) {
      if (this.status === '') {
        this.status = null;
      }

      return getSubscribeLogList({
        pageNum: params.page,
        pageSize: params.limit,
        taskId: String(this.params.id),
        status: this.status
      });
    },
    search() {
      this.$refs.table.reload();
    }
  }
};
</script>
<style lang="less" scoped>
.data-send-log {
  padding: 10px;
}
</style>
