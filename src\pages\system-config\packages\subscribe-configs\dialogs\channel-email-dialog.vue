<template>
  <div class="channel-email-dialog">
    <el-form
      ref="form"
      :model="currentConfig"
      :rules="rules"
      label-width="100px"
    >
      <el-form-item label="分组" prop="emailGroup">
        <el-select
          v-model.trim="currentConfig.emailGroup"
          class="full-width"
          placeholder="请选择分组或者新增分组"
          size="small"
          allow-create
          filterable
          clearable
          default-first-option
          transfer="true"
          :popper-append-to-body="false"
        >
          <el-option
            v-for="group of groupList"
            :key="group"
            :value="group"
            :label="group"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="收件箱" prop="email">
        <el-input
          v-model.trim="currentConfig.email"
          type="text"
          size="small"
          placeholder="请输入收件箱地址"
        />
      </el-form-item>
    </el-form>
    <div class="align-right">
      <el-button @click="close" size="small">取消</el-button>
      <el-button
        :loading="saveLoading"
        type="primary"
        size="small"
        @click="save"
      >
        保存
      </el-button>
    </div>
  </div>
</template>

<script>
import { getReceiverGroupList, saveReceiver } from '@/service/email-service';
import { validateEmail } from '@/utils/string-utils';

export default {
  props: ['params'],
  data() {
    return {
      currentConfig: {
        emailGroup: '',
        email: ''
      },
      rules: {
        emailGroup: [
          { required: true, message: '请选择分组或者新增分组' },
          {
            validator: this.groupValidator,
            message: '分组填写不可超过20个字符',
            trigger: 'change'
          }
        ],
        email: [
          { required: true, message: '请输入收件箱地址' },
          {
            validator: this.emailValidator,
            message: '请输入正确的邮箱地址',
            trigger: 'blur'
          }
        ]
      },
      groupList: [],
      saveLoading: false
    };
  },
  async mounted() {
    await getReceiverGroupList().then((res) => {
      this.groupList = res.data || [];
    });
    if (this.params.emailConfig.id) {
      this.currentConfig = JSON.parse(JSON.stringify(this.params.emailConfig));
    }
  },
  methods: {
    close() {
      this.params.close();
      this.isShowStep = false;
    },
    emailValidator(rule, value, callback) {
      if (validateEmail(value)) {
        return callback();
      } else {
        return callback(new Error('请输入正确的邮箱地址'));
      }
    },
    groupValidator(rule, value, callback) {
      if (value.length <= 20) {
        return callback();
      } else {
        return callback(new Error('分组填写不可超过20个字符'));
      }
    },
    save() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.saveLoading = true;
          saveReceiver(this.currentConfig)
            .then(
              () => {
                this.$message.success('操作成功');
                this.params.callBack();
                this.close();
              },
              (err) => {
                this.$message.error(err.msg || '操作失败');
              }
            )
            .finally(() => {
              this.saveLoading = false;
            });
        }
      });
    }
  }
};
</script>

<style lang="less" scoped>
.el-form {
  margin: 0;
}
</style>
