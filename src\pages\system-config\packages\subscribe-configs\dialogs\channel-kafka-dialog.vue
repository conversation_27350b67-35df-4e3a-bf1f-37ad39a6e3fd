<template>
  <div class="channel-kafka-dialog">
    <el-form
      ref="form"
      :model="currentConfig"
      :title="currentConfig.id ? '编辑Kafka配置' : '新增Kafka配置'"
      :rules="rules"
      class="detail-form"
      label-width="100px"
    >
      <el-form-item label="名称" prop="name">
        <el-input
          v-model.trim="currentConfig.name"
          type="text"
          size="small"
          placeholder="请输入名称"
        />
      </el-form-item>
      <el-form-item label="IP" prop="ip">
        <el-input
          v-model.trim="currentConfig.ip"
          type="text"
          size="small"
          placeholder="请输入IP"
        />
      </el-form-item>
      <el-form-item label="端口" prop="port">
        <el-input
          v-model.trim="currentConfig.port"
          type="text"
          size="small"
          placeholder="请输入端口"
        />
      </el-form-item>
      <el-form-item label="topic" prop="topic">
        <el-input
          v-model.trim="currentConfig.topic"
          type="text"
          size="small"
          placeholder="请输入topic"
        />
      </el-form-item>
      <el-form-item label="认证方式" prop="authType">
        <el-radio-group v-model="currentConfig.authType">
          <el-radio label="NO_AUTH">无认证</el-radio>
          <el-radio label="SASL_PLAINTEXT">SASL_PLAINTEXT</el-radio>
        </el-radio-group>
      </el-form-item>
      <template v-if="currentConfig.authType === 'SASL_PLAINTEXT'">
        <el-form-item label="mechanism" prop="mechanism">
          <el-radio-group v-model="currentConfig.mechanism">
            <el-radio label="PLAIN">PLAIN</el-radio>
            <el-radio label="SCRAM-SHA-256">SCRAM-SHA-256</el-radio>
            <el-radio label="SCRAM-SHA-512">SCRAM-SHA-512</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="账号" prop="authAccount">
          <el-input
            v-model.trim="currentConfig.authAccount"
            size="small"
            placeholder="请输入账号"
          />
        </el-form-item>
        <el-form-item label="密码" prop="authPassword">
          <el-input
            v-model.trim="currentConfig.authPassword"
            type="password"
            size="small"
            placeholder="请输入密码"
          />
        </el-form-item>
      </template>

      <div class="channel-kafka-dialog__test" v-if="isShowStep">
        <div class="channel-kafka-dialog__test__title">测试状态</div>
        <el-steps direction="vertical">
          <el-step
            v-for="(item, index) in orderList"
            :key="index"
            :title="item.title"
            icon="el-icon-refresh-right"
            :status="statusMap[item.status]"
          ></el-step>
        </el-steps>
      </div>
    </el-form>
    <div class="align-right flex-end align-items-center">
      <span
        v-if="hasTested"
        class="test-message"
        :class="{ success: testSuccess }"
      >
        {{ !testSuccess ? testErrorMsg || '测试不通过' : '测试通过' }}
      </span>
      <div class="flex-none">
        <el-button @click="close" size="small">取消</el-button>
        <el-button
          :loading="testLoading"
          type="primary"
          @click="test"
          size="small"
        >
          测试
        </el-button>
        <el-button
          :loading="saveLoading"
          type="primary"
          @click="save"
          size="small"
        >
          保存
        </el-button>
      </div>
    </div>
  </div>
</template>

<script>
import * as subscribeConfigsService from '@/service/subscribe-configs-service';
import {
  getSubscribeConfigSave,
  getSubscribeConfigUpdate
} from '@/service/subscribe-configs-service';

export default {
  props: ['params'],
  data() {
    return {
      currentConfig: {
        name: '',
        type: 'KAFKA',
        ip: '',
        port: '',
        topic: '',
        authType: 'NO_AUTH',
        mechanism: '',
        authAccount: '',
        authPassword: ''
      },
      saveLoading: false,
      testLoading: false,
      rules: {
        name: [{ required: true, message: '请输入Kafka名称' }],
        ip: [{ required: true, message: '请输入IP信息' }],
        port: [{ required: true, message: '请输入port信息' }],
        topic: [{ required: true, message: '请输入topic' }],
        authType: [{ required: true, message: '请选择认证方式' }],
        mechanism: [{ required: true, message: '请选择mechanism' }],
        authAccount: [{ required: true, message: '请输入账号' }],
        authPassword: [{ required: true, message: '请输入密码' }]
      },
      isShowStep: false,
      orderList: [
        // {
        //   title: 'nacos连接',
        //   status: 'WAIT'
        // },
        // {
        //   title: 'mongodb连接',
        //   status: 'WAIT'
        // },
        {
          title: 'Kafka连接',
          status: 'WAIT'
        }
      ],
      statusMap: {
        SUCCESS: 'success',
        EXCEPTION: 'error',
        WAIT: 'wait',
        CHECKING: 'process'
      },
      hasTested: false,
      testSuccess: false,
      testErrorMsg: ''
    };
  },
  mounted() {
    if (this.params.kafkaConfig.id) {
      this.currentConfig = JSON.parse(JSON.stringify(this.params.kafkaConfig));
    }
  },
  methods: {
    close() {
      this.params.close();
      this.isShowStep = false;
    },
    test() {
      this.orderList.forEach((item) => {
        item.status = 'WAIT';
      });
      this.hasTested = false;
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.testLoading = true;
          this.isShowStep = true;
          subscribeConfigsService
            .testSubscribeSyncSetting(this.currentConfig)
            .then(
              (res) => {
                if (res.data) {
                  // this.orderList[0].status = res.data.testNacosResult
                  //   ? 'SUCCESS'
                  //   : 'EXCEPTION';
                  // this.orderList[1].status = res.data.testMongoResult
                  //   ? 'SUCCESS'
                  //   : 'EXCEPTION';
                  // this.orderList[2].status = res.data.testKafkaResult
                  //   ? 'SUCCESS'
                  //   : 'EXCEPTION';
                  // if (
                  //   res.data.testNacosResult &&
                  //   res.data.testMongoResult &&
                  //   res.data.testKafkaResult
                  // ) {
                  //   this.testSuccess = true;
                  // } else {
                  //   this.testSuccess = false;
                  //   this.testErrorMsg = res.data.errorMsg;
                  // }
                  this.testSuccess = res.data;

                  this.orderList[0].status = 'SUCCESS';
                } else {
                  this.orderList[0].status = 'EXCEPTION';
                  this.testSuccess = false;
                }
              },
              () => {
                this.orderList[0].status = 'EXCEPTION';
                this.testSuccess = false;
                this.testErrorMsg = '测试连接失败';
              }
            )
            .finally(() => {
              this.hasTested = true;
              this.testLoading = false;
            });
        }
      });
    },
    save() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.saveLoading = true;
          // this.currentConfig.ipPort = this.currentConfig.ipPort.replaceAll(
          //   '，',
          //   ','
          // );
          if (this.currentConfig.id) {
            getSubscribeConfigUpdate(this.currentConfig)
              .then(
                () => {
                  this.$message.success('修改成功');
                  this.params.callBack();
                  this.close();
                },
                (err) => {
                  this.$message.error(err.msg || '操作失败');
                }
              )
              .finally(() => {
                this.saveLoading = false;
              });
          } else {
            getSubscribeConfigSave(this.currentConfig)
              .then(
                () => {
                  this.$message.success('操作成功');
                  this.params.callBack();
                  this.close();
                },
                (err) => {
                  this.$message.error(err.msg || '操作失败');
                }
              )
              .finally(() => {
                this.saveLoading = false;
              });
          }
        }
      });
    }
  }
};
</script>

<style lang="less" scoped>
.channel-kafka-dialog {
  .el-form {
    margin: 0;
  }

  &__test {
    height: 30px;
    display: flex;
    margin-left: 30px;

    &__title {
      font-size: 14px;
      color: @text-regular-color;
    }
    .el-steps {
      margin-left: 20px;
    }
    .el-step.is-vertical {
      .el-step__title {
        font-size: 14px;
      }
      .el-step__title.is-process {
        font-weight: 400;
        color: @text-regular-color;
      }
      .el-step__title.is-wait {
        color: @text-regular-color;
      }
      .el-step__icon.is-icon {
        font-size: 14px !important;
      }
      .el-icon-refresh-right:before {
        content: '\e6c8';
        font-size: 14px;
      }
    }
  }
  .test-message {
    font-size: 12px;
    color: @red-color;
    margin-right: 10px;
    &.success {
      color: @green-color;
    }
  }
}
</style>
