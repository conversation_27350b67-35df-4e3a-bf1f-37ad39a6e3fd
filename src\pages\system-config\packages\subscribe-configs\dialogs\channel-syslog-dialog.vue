<template>
  <div class="channel-syslog-dialog">
    <el-form
      ref="form"
      :model="currentConfig"
      :rules="rules"
      class="detail-form"
      label-width="100px"
    >
      <el-form-item label="名称" prop="name">
        <el-input
          v-model.trim="currentConfig.name"
          type="text"
          size="small"
          placeholder="请输入名称"
        />
      </el-form-item>
      <el-form-item label="IP" prop="ip">
        <el-input
          v-model.trim="currentConfig.ip"
          type="text"
          size="small"
          placeholder="请输入IP"
        />
      </el-form-item>
      <el-form-item label="端口" prop="port">
        <el-input
          v-model.trim="currentConfig.port"
          type="text"
          size="small"
          placeholder="请输入端口"
        />
      </el-form-item>
      <el-form-item label="协议" prop="protocol">
        <el-select
          v-model="currentConfig.protocol"
          placeholder="请选择协议"
          style="width: 100%"
          size="small"
          transfer="true"
          :popper-append-to-body="false"
        >
          <el-option label="TCP" value="TCP">
            <span>TCP</span>
            <span class="option-tips"
              >（由于传输数据量较大，建议使用TCP协议！）</span
            >
          </el-option>
          <el-option label="UDP" value="UDP"></el-option>
        </el-select>
      </el-form-item>
      <div class="channel-syslog-dialog__test" v-if="isShowStep">
        <div class="channel-syslog-dialog__test__title">测试状态</div>
        <el-steps direction="vertical">
          <el-step
            v-for="(item, index) in orderList"
            :key="index"
            :title="item.title"
            icon="el-icon-refresh-right"
            :status="statusMap[item.status]"
          ></el-step>
        </el-steps>
      </div>
    </el-form>
    <div class="align-right flex-end align-items-center">
      <span
        v-if="hasTested"
        class="test-message"
        :class="{ success: testSuccess }"
      >
        {{ !testSuccess ? testErrorMsg || '测试不通过' : '测试通过' }}
      </span>
      <div class="flex-none">
        <el-button @click="close" size="small">取消</el-button>
        <el-button
          :loading="testLoading"
          type="primary"
          @click="test"
          size="small"
        >
          测试
        </el-button>
        <el-button
          :loading="saveLoading"
          type="primary"
          @click="save"
          size="small"
        >
          保存
        </el-button>
      </div>
    </div>
  </div>
</template>

<script>
import * as subscribeConfigsService from '@/service/subscribe-configs-service';
import {
  getSubscribeConfigSave,
  getSubscribeConfigUpdate
} from '@/service/subscribe-configs-service';

export default {
  props: ['params'],
  data() {
    return {
      currentConfig: {
        name: '',
        type: 'SYSLOG',
        ip: '',
        port: '',
        protocol: ''
      },
      saveLoading: false,
      testLoading: false,
      rules: {
        name: [
          { required: true, message: '请输入Syslog名称', trigger: 'blur' }
        ],
        ip: [{ required: true, message: '请输入IP信息', trigger: 'blur' }],
        port: [{ required: true, message: '请输入端口信息', trigger: 'blur' }],
        protocol: [{ required: true, message: '请选择', trigger: 'change' }]
      },
      isShowStep: false,
      orderList: [
        // {
        //   title: 'nacos连接',
        //   status: 'WAIT'
        // },
        // {
        //   title: 'mongodb连接',
        //   status: 'WAIT'
        // },
        {
          title: 'Syslog连接',
          status: 'WAIT'
        }
      ],
      statusMap: {
        SUCCESS: 'success',
        EXCEPTION: 'error',
        WAIT: 'wait',
        CHECKING: 'process'
      },
      hasTested: false,
      testSuccess: false,
      testErrorMsg: ''
    };
  },
  mounted() {
    if (this.params.syslogConfig.id) {
      this.currentConfig = JSON.parse(JSON.stringify(this.params.syslogConfig));
    }
  },
  methods: {
    close() {
      this.params.close();
      this.isShowStep = false;
    },
    test() {
      this.orderList.forEach((item) => {
        item.status = 'WAIT';
      });
      this.hasTested = false;
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.isShowStep = true;
          this.testLoading = true;
          subscribeConfigsService
            .testSubscribeSyncSetting(this.currentConfig)
            .then(
              (res) => {
                if (res.data) {
                  // this.orderList[0].status = res.data.testNacosResult
                  //   ? 'SUCCESS'
                  //   : 'EXCEPTION';
                  // this.orderList[1].status = res.data.testMongoResult
                  //   ? 'SUCCESS'
                  //   : 'EXCEPTION';
                  // this.orderList[2].status = res.data.testSyslogResult
                  //   ? 'SUCCESS'
                  //   : 'EXCEPTION';
                  // if (
                  //   res.data.testNacosResult &&
                  //   res.data.testMongoResult &&
                  //   res.data.testSyslogResult
                  // ) {
                  //   this.testSuccess = true;
                  // } else {
                  //   this.testSuccess = res.data;
                  //   this.testErrorMsg = res.data.errorMsg;
                  // }
                  this.testSuccess = res.data;
                } else {
                  this.testSuccess = false;
                }
              },
              (err) => {
                this.testSuccess = false;
                this.testErrorMsg = err.msg;
              }
            )
            .finally(() => {
              this.hasTested = true;
              this.testLoading = false;
            });
        }
      });
    },
    save() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.saveLoading = true;
          console.log(this.currentConfig);
          if (this.currentConfig.id) {
            getSubscribeConfigUpdate(this.currentConfig)
              .then(
                () => {
                  this.$message.success('操作成功');
                  this.params.callBack();
                  this.close();
                },
                (err) => {
                  this.$message.error(err.msg || '操作失败');
                }
              )
              .finally(() => {
                this.saveLoading = false;
              });
          } else {
            getSubscribeConfigSave(this.currentConfig)
              .then(
                () => {
                  this.$message.success('操作成功');
                  this.params.callBack();
                  this.close();
                },
                (err) => {
                  this.$message.error(err.msg || '操作失败');
                }
              )
              .finally(() => {
                this.saveLoading = false;
              });
          }
        }
      });
    }
  }
};
</script>

<style lang="less" scoped>
.channel-syslog-dialog {
  .el-form {
    margin: 0;
  }
  &__test {
    height: 30px;
    display: flex;
    margin-left: 30px;
    &__title {
      font-size: 14px;
      color: @text-regular-color;
    }
    .el-steps {
      margin-left: 20px;
    }
    .el-step.is-vertical {
      .el-step__title {
        font-size: 14px;
      }
      .el-step__title.is-process {
        font-weight: 400;
        color: @text-regular-color;
      }
      .el-step__title.is-wait {
        color: @text-regular-color;
      }
      .el-step__icon.is-icon {
        font-size: 14px !important;
      }
      .el-icon-refresh-right:before {
        content: '\e6c8';
        font-size: 14px;
      }
    }
  }
  .test-message {
    font-size: 12px;
    color: @red-color;
    margin-right: 10px;
    &.success {
      color: @green-color;
    }
  }
}
</style>
<style lang="less" scoped>
.option-tips {
  font-size: 12px;
  color: @text-regular-color;
}
</style>
