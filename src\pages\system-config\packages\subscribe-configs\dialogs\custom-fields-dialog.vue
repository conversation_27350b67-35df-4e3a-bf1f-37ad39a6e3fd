<!--
 * @Fileoverview：自定义输出字段
 * @Description：数据同步-自定义输出字段
-->
<template>
  <div class="custom-fields">
    <el-table
      ref="table"
      :data="dataList"
      row-key="id"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      @selection-change="handleSelectionChange"
    >
      >
      <el-table-column type="selection" />
      <el-table-column prop="filed" label="默认字段名称" />
      <el-table-column prop="targetField" label="修改字段名称">
        <template slot-scope="{ row }">
          <el-input
            size="small"
            v-model="row.targetField"
            placeholder="请输入字段名称"
          />
        </template>
      </el-table-column>
      <el-table-column prop="type" label="字段类型" />
      <el-table-column prop="des" label="说明" />
    </el-table>
    <div class="align-right mt20">
      <el-button @click="cancel" size="small">取消</el-button>
      <el-button type="primary" @click="save" size="small">保存</el-button>
    </div>
  </div>
</template>

<script>
import { getSubscribeCustomFields } from '@/service/subscribe-configs-service';
export default {
  props: ['params'],
  data() {
    return {
      multipleSelection: [],
      dataList: new Array(100).fill(0).map(() => ({
        field: 'level',
        targetField: 'level',
        type: 'String',
        des: '日志id'
      }))
    };
  },
  async mounted() {
    const multipleSelectionTmp = [];
    await getSubscribeCustomFields().then((res) => {
      this.dataList = this.convertDataObjectToArr(res.data) || [];
    });
    const customFieldsList = this.convertDataObjectToArr(
      JSON.parse(this.params.customFields)
    );

    this.dataList.forEach((item) => {
      const findIndex = customFieldsList.findIndex((filed) => {
        if (item.filed === filed.filed) {
          item.targetField = filed.targetField;
        }
        return item.filed === filed.filed;
      });

      if (findIndex > -1) {
        multipleSelectionTmp.push(item);
      }
    });
    if (multipleSelectionTmp.length > 0) {
      this.$nextTick(() => {
        multipleSelectionTmp.forEach((row) => {
          this.$refs.table.toggleRowSelection(row);
        });
      });
    } else {
      this.$refs.table.clearSelection();
    }
  },
  methods: {
    // 关闭对话框
    cancel() {
      this.params.close();
    },
    // 保存自定义输出字段
    save() {
      this.params.callBack({
        data: this.convertDataArrToObject(this.multipleSelection)
      });
      this.cancel();
    },
    // 选择
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    convertDataObjectToArr(data) {
      return Object.keys(data).map((key) => {
        const item = data[key];
        if (item.fields) {
          return {
            ...item,
            children: this.convertDataObjectToArr(item.fields)
          };
        }
        return item;
      });
    },
    convertDataArrToObject(arr) {
      return arr.reduce((acc, item) => {
        if (item.children) {
          acc[item.filed] = {
            ...item,
            fields: this.convertDataArrToObject(item.children)
          };
        } else {
          acc[item.filed] = item;
        }
        return acc;
      }, {});
    }
  }
};
</script>

<style lang="less" scoped>
.custom-fields {
  .el-table__body-wrapper {
    height: 400px;
    overflow-y: auto;
  }
  .el-table th.el-table__cell > .el-checkbox {
    padding-left: 14px;
  }
  .el-table thead {
    .el-checkbox {
      padding-left: 5px;
    }
  }
}
</style>
