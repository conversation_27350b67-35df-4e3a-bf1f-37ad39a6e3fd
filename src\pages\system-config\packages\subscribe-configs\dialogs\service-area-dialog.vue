<!--
 * @Fileoverview：自定义输出字段
 * @Description：数据同步-自定义输出字段
-->
<template>
  <div class="select-area">
    <el-table ref="table" :data="dataList">
      <!-- @selection-change="handleSelectionChange" -->

      >
      <el-table-column type="selection" />
      <el-table-column prop="fieldName" label="字段" />
      <el-table-column prop="fieldType" label="字段类型" />
      <el-table-column prop="fieldExplain" label="说明" />
    </el-table>
    <div class="align-right mt20">
      <el-button @click="cancel" size="small">取消</el-button>
      <el-button type="primary" @click="save" size="small">保存</el-button>
    </div>
  </div>
</template>

<script>
import { getSubscribeCustomFields } from '@/service/subscribe-configs-service';
export default {
  props: ['params'],
  data() {
    return {
      multipleSelection: [],
      dataList: new Array(100).fill(0).map(() => ({
        field: 'level',
        type: 'String',
        desc: '日志id'
      }))
    };
  },
  async mounted() {
    const multipleSelectionTmp = [];
    await getSubscribeCustomFields(this.params.type).then((res) => {
      this.dataList = res.data || [];
    });
    if (this.params.isUdp) {
      this.dataList = this.dataList.filter((custom) => custom.isUdp == true);
    }
    const customFieldsList = this.params.customFields;
    this.dataList.forEach((item) => {
      const findIndex = customFieldsList.findIndex((filed) => {
        return item.fieldName === filed;
      });
      if (findIndex > -1) {
        multipleSelectionTmp.push(item);
      }
    });
    if (multipleSelectionTmp.length > 0) {
      this.$nextTick(() => {
        multipleSelectionTmp.forEach((row) => {
          this.$refs.table.toggleRowSelection(row);
        });
      });
    } else {
      this.$refs.table.clearSelection();
    }
  },
  methods: {
    // 关闭对话框
    cancel() {
      this.params.close();
    },
    // 保存自定义输出字段
    save() {
      this.params.callBack({ data: this.multipleSelection });
      this.cancel();
    },
    // 选择
    handleSelectionChange(val) {
      this.multipleSelection = val;
    }
  }
};
</script>

<style lang="less" scoped>
.custom-fields {
  .el-table__body-wrapper {
    height: 400px;
    overflow-y: auto;
  }
  .el-table th.el-table__cell > .el-checkbox {
    padding-left: 14px;
  }
  .el-table thead {
    .el-checkbox {
      padding-left: 5px;
    }
  }
}
</style>
