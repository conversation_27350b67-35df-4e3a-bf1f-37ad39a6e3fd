<!--
 * @Fileoverview: 发件箱配置
 * @Description: 配置-数据同步-发件箱配置
-->
<template>
  <qz-api-drawer
    v-loading="loading"
    :visible.sync="drawerVisible"
    :append-to-body="true"
    destroy-on-close
    title="发件箱配置"
    size="500px"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="130px">
      <el-form-item label="服务器类型" prop="serverType">
        <el-select
          v-model="form.serverType"
          size="small"
          class="full-width"
          placeholder="请选择服务器类型"
          @change="handleChange"
        >
          <el-option
            label="第三方邮件服务器"
            :value="SERVER_TYPE_OTHER"
          ></el-option>
          <el-option label="EXCHANGE" :value="SERVER_TYPE_EXCHANGE"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        :label="
          form.serverType == SERVER_TYPE_OTHER ? 'SMTP服务器' : 'Exchange服务器'
        "
        prop="server"
      >
        <el-input
          v-model.trim="form.server"
          size="small"
          type="text"
          clearable
          placeholder="请输入，如：smtp.qq.com"
        ></el-input>
      </el-form-item>
      <template v-if="form.serverType == SERVER_TYPE_OTHER">
        <el-form-item label="服务器端口" prop="port">
          <el-input
            v-model.trim="form.port"
            size="small"
            type="text"
            clearable
            placeholder="请输入，常用端口号为25、465"
          ></el-input>
        </el-form-item>
        <!-- <el-form-item prop="useSsl">
          <el-checkbox
            style="padding-left: 110px"
            v-model="form.useSsl"
            :true-label="1"
            :false-label="0"
          >
            安全链接
          </el-checkbox>
        </el-form-item> -->
      </template>
      <el-form-item label="邮箱地址" prop="email">
        <el-input
          v-model.trim="form.email"
          size="small"
          type="text"
          clearable
          placeholder="请输入，如：<EMAIL>"
        ></el-input>
      </el-form-item>
      <el-form-item
        v-if="form.serverType == SERVER_TYPE_EXCHANGE"
        label="用户名"
        prop="username"
      >
        <el-input
          v-model.trim="form.username"
          size="small"
          clearable
          placeholder="请输入用户名"
        ></el-input>
      </el-form-item>
      <el-form-item label="授权码" prop="password">
        <el-input
          v-model.trim="form.password"
          size="small"
          type="password"
          auto-complete="new-password"
          clearable
          placeholder="请输入授权码"
        ></el-input>
      </el-form-item>
    </el-form>
    <el-alert
      v-if="hasTested"
      :title="tips"
      :type="testResult"
      :closable="false"
      show-icon
      center
    />
    <div slot="footer" class="align-right">
      <el-button @click="closeConfig" size="small">取消</el-button>
      <el-button
        :loading="testLoading"
        type="primary"
        size="small"
        @click="testConfig"
      >
        测试
      </el-button>
      <el-button
        :loading="saveLoading"
        type="primary"
        size="small"
        @click="saveConfig"
      >
        保存
      </el-button>
    </div>
  </qz-api-drawer>
</template>

<script>
import {
  getSenderConfig,
  saveSenderConfig,
  testSenderConfig
} from '@/service/email-service';
import { validateEmail } from '@/utils/string-utils';

const SERVER_TYPE_EXCHANGE = 1;
const SERVER_TYPE_OTHER = 2;

const checkIsLegal = (rule, value, callback) => {
  if (!validateEmail(value)) {
    callback(new Error('无效的邮箱地址!'));
  }
  callback();
};

export default {
  data() {
    return {
      SERVER_TYPE_EXCHANGE,
      SERVER_TYPE_OTHER,

      drawerVisible: false,
      form: {
        serverType: SERVER_TYPE_OTHER,
        server: '',
        port: '',
        email: '',
        username: '',
        password: ''
        // useSsl: 1
      },
      config: {
        serverType: SERVER_TYPE_OTHER,
        server: '',
        port: '',
        email: '',
        username: '',
        password: ''
        // useSsl: 1
      },
      rules: {
        serverType: [
          { required: true, message: '请选择服务器类型', trigger: 'blur' }
        ],
        server: [
          { required: true, message: '请输入服务器地址', trigger: 'blur' }
        ],
        port: [
          { required: true, message: '请输入服务器端口号', trigger: 'blur' }
        ],
        email: [
          { required: true, message: '请输入邮箱地址', trigger: 'blur' },
          { validator: checkIsLegal, trigger: 'blur' }
        ],
        username: [
          { required: true, message: '请输入用户名', trigger: 'blur' }
        ],
        password: [{ required: true, message: '请输入密码', trigger: 'blur' }]
      },

      loading: false,
      testLoading: false,
      saveLoading: false,

      hasTested: false,
      testResult: 'success',
      tips: '邮件发送成功！'
    };
  },
  watch: {
    form: {
      deep: true,
      handler() {
        this.hasTested = false;
      }
    }
  },
  mounted() {
    // 获取之前配置好的邮箱配置
    this.loading = true;
    getSenderConfig()
      .then((res) => {
        this.config = Object.assign({}, this.config, res.data);
        this.form = JSON.parse(JSON.stringify(this.config));
      })
      .finally(() => {
        this.loading = false;
      });
  },
  methods: {
    handleChange() {
      this.form.server = '';
      this.form.port = '';
      this.form.email = '';
      this.form.username = '';
      this.form.password = '';
      this.$refs.form.clearValidate();
    },
    // 外部调用
    showConfig() {
      this.form = JSON.parse(JSON.stringify(this.config));
      this.drawerVisible = true;
    },
    closeConfig() {
      this.drawerVisible = false;
      this.$refs.form.clearValidate();
    },
    testConfig() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          this.$prompt('请输入收件箱', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            inputPattern:
              /[\w!#$%&'*+/=?^_`{|}~-]+(?:\.[\w!#$%&'*+/=?^_`{|}~-]+)*@(?:[\w](?:[\w-]*[\w])?\.)+[\w](?:[\w-]*[\w])?/,
            inputErrorMessage: '邮箱格式不正确'
          })
            .then(({ value }) => {
              this.testLoading = true;
              testSenderConfig(this.form, value)
                .then(
                  () => {
                    this.hasTested = true;
                    this.testResult = 'success';
                    this.tips = '邮件发送成功！';
                  },
                  (err) => {
                    this.hasTested = true;
                    this.testResult = 'error';
                    this.tips = err.msg || '邮件发送失败！';
                  }
                )
                .finally(() => {
                  this.testLoading = false;
                });
            })
            .catch(() => {});
        }
      });
    },
    saveConfig() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          this.saveLoading = true;
          saveSenderConfig(this.form)
            .then(
              () => {
                this.$message.success('配置保存成功');
                this.config = JSON.parse(JSON.stringify(this.form));
                this.closeConfig();
              },
              (err) => {
                this.$message.error(err.msg || '保存配置信息失败');
              }
            )
            .finally(() => {
              this.saveLoading = false;
            });
        }
      });
    }
  }
};
</script>

<style lang="less" scoped>
.el-form {
  margin: 0;
}
</style>
