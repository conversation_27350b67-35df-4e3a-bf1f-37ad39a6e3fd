<template>
  <div class="select-area-details">
    <qz-pro-table :data-source="filteredData" height="500px" ref="treeTable">
      <qz-table-column
        prop="destination"
        label="服务地址"
        width="300"
      ></qz-table-column>
      <qz-table-column prop="system" label="业务系统"></qz-table-column>
    </qz-pro-table>
  </div>
</template>

<script>
export default {
  props: ['params'],
  data() {
    return {
      tableData: [
        {
          id: '1',
          destination: '***********:8080',
          system: '系统',
          type: 'service',
          children: [
            {
              id: '1-1',
              destination: '数据库1',
              type: 'database',
              children: [
                {
                  id: '1-1-1',
                  destination: 'schema1',
                  type: 'schema',
                  children: [
                    { id: '1-1-1-1', destination: '表1', type: 'table' },
                    { id: '1-1-1-2', destination: '表2', type: 'table' }
                  ]
                }
              ]
            },
            {
              id: '1-2',
              destination: '数据库3',
              type: 'database',
              children: [
                {
                  id: '1-2-1',
                  destination: 'schema3',
                  type: 'schema',
                  children: [
                    { id: '1-2-1-1', destination: '表3', type: 'table' },
                    { id: '1-2-1-2', destination: '表4', type: 'table' }
                  ]
                }
              ]
            }
          ]
        },
        {
          id: '2',
          destination: '192.168.0.2:8080',
          system: '系统',
          type: 'service',
          children: [
            {
              id: '2-1',
              destination: '数据库2',
              type: 'database',
              children: [
                {
                  id: '2-1-1',
                  destination: 'schema2',
                  type: 'schema',
                  children: [
                    { id: '2-1-1-1', destination: '表5', type: 'table' }
                  ]
                }
              ]
            }
          ]
        }
      ],
      searchParams: {},
      defaultExpandedKeys: []
    };
  },
  computed: {
    filteredData() {
      const data = JSON.parse(JSON.stringify(this.tableData));

      const filterFunc = (items) => {
        return items.filter((item) => {
          const matchDestination =
            !this.searchParams.destination ||
            item.destination.includes(this.searchParams.destination);
          const matchSystem =
            !this.searchParams.system ||
            (item.system && item.system.includes(this.searchParams.system));

          if (item.children && item.children.length) {
            item.children = filterFunc(item.children);
            return matchDestination && matchSystem && item.children.length > 0;
          }
          return matchDestination && matchSystem;
        });
      };
      return filterFunc(data);
    }
  },
  mounted() {},
  methods: {
    handleCancel() {},
    handleAdd() {}
  }
};
</script>
<style lang="less" scoped>
.select-area-details {
  padding: 20px;
  display: flex;
  flex-direction: column;
  height: 100%;
}
</style>
