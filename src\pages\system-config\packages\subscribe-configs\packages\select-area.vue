<template>
  <div class="select-area">
    <section>
      <search-section
        :schemas="searchSchemas"
        labelWidth="10px"
        :needMore="true"
        ref="search"
        :customStyle="{ right: '14px' }"
        mb="10px"
        @search="handleSearch"
      >
      </search-section>
    </section>
    <qz-pro-table
      :data-source="filteredData"
      :default-expanded-keys="defaultExpandedKeys"
      :row-class-name="getRowClassName"
      row-key="id"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      @select="handleNodeSelect"
      ref="treeTable"
    >
      <qz-table-column
        type="selection"
        width="55"
        :selectable="selectableCheck"
        :reserve-selection="true"
      ></qz-table-column>
      <qz-table-column prop="destination" label="服务地址" width="300">
        <template #default="{ row }">
          <span
            v-if="row.type === 'service'"
            class="clickable-text"
            @click="handleServiceClick(row)"
          >
            {{ row.destination }}
          </span>
          <span v-else>
            {{ row.destination }}
          </span>
        </template>
      </qz-table-column>
      <qz-table-column
        prop="system"
        label="业务系统"
        v-if="showColumn('system')"
      ></qz-table-column>
    </qz-pro-table>

    <div class="footer-buttons">
      <el-button @click="handleCancel" size="small">取消</el-button>
      <el-button type="primary" @click="handleAdd" size="small">添加</el-button>
    </div>
  </div>
</template>

<script>
import searchSection from '@/components/search-section.vue';

export default {
  props: ['params'],
  components: { searchSection },
  data() {
    return {
      tableData: [
        {
          id: '1',
          destination: '***********:8080',
          system: '系统',
          type: 'service',
          children: [
            {
              id: '1-1',
              destination: '数据库1',
              type: 'database',
              children: [
                {
                  id: '1-1-1',
                  destination: 'schema1',
                  type: 'schema',
                  children: [
                    { id: '1-1-1-1', destination: '表1', type: 'table' },
                    { id: '1-1-1-2', destination: '表2', type: 'table' }
                  ]
                }
              ]
            },
            {
              id: '1-2',
              destination: '数据库3',
              type: 'database',
              children: [
                {
                  id: '1-2-1',
                  destination: 'schema3',
                  type: 'schema',
                  children: [
                    { id: '1-2-1-1', destination: '表3', type: 'table' },
                    { id: '1-2-1-2', destination: '表4', type: 'table' }
                  ]
                }
              ]
            }
          ]
        },
        {
          id: '2',
          destination: '192.168.0.2:8080',
          system: '系统',
          type: 'service',
          children: [
            {
              id: '2-1',
              destination: '数据库2',
              type: 'database',
              children: [
                {
                  id: '2-1-1',
                  destination: 'schema2',
                  type: 'schema',
                  children: [
                    { id: '2-1-1-1', destination: '表5', type: 'table' }
                  ]
                }
              ]
            }
          ]
        }
      ],
      searchParams: {},
      defaultExpandedKeys: [],
      selectedIds: JSON.parse(JSON.stringify([])),
      selectedRows: JSON.parse(JSON.stringify([]))
    };
  },
  computed: {
    searchSchemas() {
      return [
        {
          key: 'destination',
          label: '',
          type: 'text',
          placeholder: '服务地址查询'
        },
        {
          key: 'system',
          label: '',
          type: 'text',
          placeholder: '业务系统'
        },
        {
          key: 'database',
          label: '',
          type: 'text',
          placeholder: '数据库'
        },
        {
          key: 'schema',
          label: '',
          type: 'text',
          placeholder: 'schema'
        },
        {
          key: 'tableName',
          label: '',
          type: 'text',
          placeholder: '表名搜索'
        },
        {
          key: 'menuName',
          label: '',
          type: 'text',
          placeholder: '菜单名搜索'
        },
        {
          key: 'textName',
          label: '',
          type: 'text',
          placeholder: '字段名搜索'
        },
        {
          key: 'selected',
          label: '',
          type: 'select',
          placeholder: '勾选状态',
          options: [
            { label: '已勾选', value: 'checked' },
            { label: '未勾选', value: 'unchecked' }
          ]
        }
      ];
    },
    filteredData() {
      const data = JSON.parse(JSON.stringify(this.tableData));
      const filterFunc = (items) => {
        return items.filter((item) => {
          const matchDestination =
            !this.searchParams.destination ||
            item.destination.includes(this.searchParams.destination);
          const matchSystem =
            !this.searchParams.system ||
            (item.system && item.system.includes(this.searchParams.system));

          if (item.children && item.children.length) {
            item.children = filterFunc(item.children);
            return matchDestination && matchSystem && item.children.length > 0;
          }
          return matchDestination && matchSystem;
        });
      };
      return filterFunc(data);
    }
  },
  mounted() {
    this.initExpandedKeys();
  },
  methods: {
    // 1. 解决树节点展开问题
    initExpandedKeys() {
      const keys = [];
      const collectKeys = (nodes) => {
        nodes.forEach((node) => {
          keys.push(node.id);
          if (node.children) collectKeys(node.children);
        });
      };
      collectKeys(this.tableData);
      this.defaultExpandedKeys = keys;
      this.$nextTick(() => {
        if (this.$refs.treeTable && this.$refs.treeTable.refreshLayout) {
          this.$refs.treeTable.refreshLayout();
        }
      });
    },

    // 处理节点选择
    handleNodeSelect(row) {
      // 创建非响应式副本
      const currentIds = JSON.parse(JSON.stringify(this.selectedIds));
      const currentRows = JSON.parse(JSON.stringify(this.selectedRows));

      const isSelected = currentIds.includes(row.id);

      // 更新当前节点
      if (isSelected) {
        currentIds.splice(currentIds.indexOf(row.id), 1);
        currentRows.splice(
          currentRows.findIndex((r) => r.id === row.id),
          1
        );
      } else {
        currentIds.push(row.id);
        currentRows.push(JSON.parse(JSON.stringify(row))); // 存储纯对象
      }

      // 处理子节点
      const processChildren = (node, select) => {
        const childIndex = currentIds.indexOf(node.id);
        if (select && childIndex === -1) {
          currentIds.push(node.id);
          currentRows.push(JSON.parse(JSON.stringify(node)));
        } else if (!select && childIndex !== -1) {
          currentIds.splice(childIndex, 1);
          currentRows.splice(
            currentRows.findIndex((r) => r.id === node.id),
            1
          );
        }

        if (node.children) {
          node.children.forEach((child) => processChildren(child, select));
        }
      };

      processChildren(row, !isSelected);

      // 处理父节点
      this.checkParentSelection(row, currentIds, currentRows);

      // 更新响应式数据
      this.selectedIds = currentIds;
      this.selectedRows = currentRows;

      console.log('处理后的IDs:', this.selectedIds);
      console.log('处理后的Rows:', this.selectedRows);
    },

    // 检查父节点状态
    checkParentSelection(
      row,
      ids = this.selectedIds,
      rows = this.selectedRows
    ) {
      const allNodes = this.flattenTree(this.filteredData);
      let parent = this.findParent(row, allNodes);

      while (parent) {
        const allChildrenSelected =
          parent.children?.every((child) => ids.includes(child.id)) ?? false;

        const parentIndex = ids.indexOf(parent.id);
        if (allChildrenSelected && parentIndex === -1) {
          ids.push(parent.id);
          rows.push(JSON.parse(JSON.stringify(parent)));
        } else if (!allChildrenSelected && parentIndex !== -1) {
          ids.splice(parentIndex, 1);
          rows.splice(
            rows.findIndex((r) => r.id === parent.id),
            1
          );
        }

        parent = this.findParent(parent, allNodes);
      }
    },
    handleConfirm() {
      const selectedData = this.selectedRows.map((row) => ({
        ...row,
        destination: row.destination || row.data?.destination
      }));

      this.params.callback(selectedData);
      this.params.closeOutDrawer();
    },
    // 表单回填
    handleAdd() {
      if (this.selectedRows.length === 0) {
        this.$message.warning('请至少选择一项');
        return;
      }
      const selectedData = this.selectedRows.map((row) => ({
        ...row,
        destination: row.destination || row.data?.destination
      }));

      this.params.callback(selectedData);
      this.params.closeOutDrawer();
    },
    handleServiceClick() {
      this.$DrawAlert({
        title: '推送详情',
        params: {
          closeOutDrawer: () => {}
        },
        componentObj: {
          component: () => import('./select-area-details.vue')
        }
      });
    },
    getRowClassName({ row }) {
      return this.selectedIds.includes(row.id) ? 'selected-row' : '';
    },

    // 全选/取消全选
    handleSelectAll(selection) {
      const newIds = [];
      const newRows = [];
      if (selection.length > 0) {
        // 全选
        const selectAll = (nodes) => {
          nodes.forEach((node) => {
            if (!newIds.includes(node.id)) {
              newIds.push(node.id);
              newRows.push(JSON.parse(JSON.stringify(node)));
            }
            if (node.children) selectAll(node.children);
          });
        };
        selectAll(this.filteredData);
      }

      this.selectedIds = newIds;
      this.selectedRows = newRows;

      console.log('全选结果:', {
        ids: this.selectedIds,
        rows: this.selectedRows
      });
    },
    // 获取选中项
    getTopLevelParents(selectedItems) {
      return [...selectedItems];
    },

    // 查找父节点
    findParent(childNode, allNodes) {
      if (!childNode || !Array.isArray(allNodes)) return null;
      return (
        allNodes.find((node) =>
          node?.children?.some((child) => child.id === childNode.id)
        ) || null
      );
    },
    toggleChildrenSelection(row, isSelected) {
      if (this.$refs.treeTable?.setSelected) {
        this.$refs.treeTable.setSelected(row.id, isSelected);
      }
      if (row.children) {
        row.children.forEach((child) => {
          this.toggleChildrenSelection(child, isSelected);
        });
      }
    },
    // 获取节点的所有子节点
    getAllChildren(row) {
      let children = [];
      if (row.children && row.children.length) {
        row.children.forEach((child) => {
          children.push(child);
          children = children.concat(this.getAllChildren(child));
        });
      }
      return children;
    },
    handleSearch(params) {
      this.searchParams = params;
    },
    showColumn(col) {
      return true;
    },
    // 该行是否可选
    selectableCheck(row, index) {
      return true;
    },
    // 监听选中项的变化
    handleSelectionChange(val) {
      this.selectedItems = val;
    },
    handleCancel() {
      this.params.closeOutDrawer();
    },
    // 检查父节点选中状态
    // 将树形结构的数据扁平化
    flattenTree(treeData) {
      if (!treeData) return [];
      const result = [];
      const flatten = (nodes) => {
        nodes?.forEach((node) => {
          if (node) {
            result.push(node);
            if (node.children) {
              flatten(node.children);
            }
          }
        });
      };
      flatten(Array.isArray(treeData) ? treeData : [treeData]);
      return result;
    }
  }
};
</script>

<style lang="less" scoped>
.select-area {
  padding: 20px;
  display: flex;
  flex-direction: column;
  height: 100%;
}
.footer-buttons {
  margin-top: 20px;
  text-align: right;
  padding: 10px 0;
  border-top: 1px solid #ebeef5;
}
.clickable-text {
  color: #409eff;
  cursor: pointer;
  // text-decoration: underline;

  &:hover {
    color: #66b1ff;
  }
}
</style>
