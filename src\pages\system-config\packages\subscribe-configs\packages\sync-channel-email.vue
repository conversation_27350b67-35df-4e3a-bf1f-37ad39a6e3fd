<template>
  <div class="sync-channel-email">
    <api-table
      table-id="syncEmailList"
      ref="table"
      :data-source="getDataList"
      toolsLayout="headerPrepend"
    >
      <api-table-tool-register id="headerPrepend">
        <el-button type="primary" @click="showSenderConfig" size="mini">
          发件箱配置
        </el-button>
        <el-button type="primary" @click="showDetail()" size="mini">
          新增收件箱
        </el-button>
      </api-table-tool-register>
      <api-table-column label="分组" prop="emailGroup"></api-table-column>
      <api-table-column label="邮箱" prop="email"></api-table-column>
      <api-table-column
        label="创建时间"
        prop="createTime"
        formatter="formatTime"
      ></api-table-column>
      <api-table-column key="operation" label="操作" width="100" fixed="right">
        <template slot-scope="{ row }">
          <span class="action-link" @click="showDetail(row)">查看</span>
          <qz-popconfirm
            title="确定要删除该邮箱吗？"
            content
            class="action-link"
            @confirm="del(row.id, row.emailGroup)"
          >
            <div slot="content">邮箱删除后不可恢复！</div>
            <span slot="reference">删除</span>
          </qz-popconfirm>
        </template>
      </api-table-column>
    </api-table>
    <email-sender-drawer ref="senderDrawer"></email-sender-drawer>
  </div>
</template>

<script>
import {
  checkIsUsed,
  deleteReceiver,
  getReceiverList
} from '@/service/email-service';
import EmailSenderDrawer from './email-sender-drawer.vue';

export default {
  components: {
    EmailSenderDrawer
  },
  methods: {
    getDataList(params) {
      return getReceiverList(params);
    },
    showSenderConfig() {
      this.$refs.senderDrawer.showConfig();
    },
    showDetail(row = {}) {
      this.$dialogAlert({
        params: {
          emailConfig: row,
          callBack: () => {
            this.$refs.table.reloadCurrentPage();
          }
        },
        component: () => import('../dialogs/channel-email-dialog.vue'),
        alertWidth: '500px',
        alertHeight: 'auto',
        alertTitle: row.id ? row.emailGroup : '新增收件箱',
        alertStyle: {
          zIndex: 3000
        }
      });
    },
    async del(id, group) {
      try {
        const { data } = await checkIsUsed(group);
        if (data) {
          if (this.$refs.table) {
            this.$refs.table.loading = true;
          }
          await deleteReceiver(id);
          this.$message.success('操作成功');
          if (this.$refs.table) {
            this.$refs.table.reloadCurrentPage();
          }
        } else {
          this.$message.warning(
            '此配置正在使用中，删除后将导致原来的数据同步失效，请先删除关联的数据同步'
          );
        }
      } catch (err) {
        console.error(err);
        this.$message.error(err.msg || '操作失败');
      } finally {
        if (this.$refs.table) {
          this.$refs.table.loading = false;
        }
      }
    }
  }
};
</script>
