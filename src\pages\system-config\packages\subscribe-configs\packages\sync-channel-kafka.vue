<template>
  <div class="sync-channel-kafka">
    <api-table
      table-id="syncKafkaList"
      ref="table"
      :data-source="getDataList"
      toolsLayout="headerPrepend"
    >
      <api-table-tool-register id="headerPrepend">
        <el-button type="primary" @click="showDetail()" size="mini">
          新增Kafka同步
        </el-button>
      </api-table-tool-register>
      <api-table-column label="名称" prop="name"></api-table-column>
      <api-table-column label="IP端口" prop="ipPort">
        <template slot-scope="{ row }">
          <span>{{ row.ip }}:{{ row.port }}</span>
        </template>
      </api-table-column>
      <api-table-column label="topic" prop="topic"></api-table-column>
      <api-table-column
        label="创建时间"
        prop="createdTime"
        formatter="formatTime"
      ></api-table-column>
      <api-table-column key="operation" label="操作" width="100" fixed="right">
        <template slot-scope="{ row }">
          <span class="action-link" @click="showDetail(row)">查看</span>
          <qz-popconfirm
            title="确定要删除该配置吗？"
            content
            class="action-link"
            @confirm="del(row)"
          >
            <div slot="content">配置删除后不可恢复！</div>
            <span slot="reference">删除</span>
          </qz-popconfirm>
        </template>
      </api-table-column>
    </api-table>
  </div>
</template>
<script>
import {
  getSubscribeConfigList,
  deleteSubscribeConfig
} from '@/service/subscribe-configs-service';
export default {
  methods: {
    getDataList(params) {
      return getSubscribeConfigList({ ...params, type: 'KAFKA' });
    },
    showDetail(row = {}) {
      this.$dialogAlert({
        params: {
          kafkaConfig: row,
          callBack: () => {
            this.$refs.table.reloadCurrentPage();
          }
        },
        component: () => import('../dialogs/channel-kafka-dialog.vue'),
        alertWidth: '700px',
        alertHeight: 'auto',
        alertTitle: row.id ? '编辑Kafka同步' : '新增Kafka同步',
        alertStyle: {
          zIndex: 3000
        }
      });
    },
    async del(row) {
      if (this.$refs.table) {
        this.$refs.table.loading = true;
      }
      deleteSubscribeConfig({ id: row.id })
        .then(() => {
          this.$message.success('删除成功');
          if (this.$refs.table) {
            this.$refs.table.reloadCurrentPage();
          }
        })
        .catch((err) => {
          console.error(err);
          this.$message.error(err.msg || '删除失败');
        })
        .finally(() => {
          if (this.$refs.table) {
            this.$refs.table.loading = false;
          }
        });
    }
  }
};
</script>
