<template>
  <div class="sync-channel-syslog">
    <api-table
      ref="table"
      table-id="syncSyslogList"
      :data-source="getDataList"
      toolsLayout="headerPrepend"
    >
      <api-table-tool-register id="headerPrepend">
        <el-button type="primary" @click="showDetail()" size="mini">
          新增Syslog同步
        </el-button>
      </api-table-tool-register>
      <api-table-column label="名称" prop="name"></api-table-column>
      <api-table-column label="IP" prop="ip"></api-table-column>
      <api-table-column label="端口" prop="port"></api-table-column>
      <api-table-column label="协议" prop="protocol"></api-table-column>
      <api-table-column
        label="创建时间"
        prop="createdTime"
        formatter="formatTime"
      ></api-table-column>
      <api-table-column key="operation" label="操作" width="100" fixed="right">
        <template slot-scope="{ row }">
          <span class="action-link" @click="showDetail(row)">查看</span>
          <qz-popconfirm
            title="确定要删除该配置吗？"
            content
            class="action-link"
            @confirm="del(row)"
          >
            <div slot="content">配置删除后不可恢复！</div>
            <span slot="reference">删除</span>
          </qz-popconfirm>
        </template>
      </api-table-column>
    </api-table>
  </div>
</template>
<script>
import {
  getSubscribeConfigList,
  deleteSubscribeConfig
} from '@/service/subscribe-configs-service';
export default {
  methods: {
    getDataList(params) {
      return getSubscribeConfigList({ ...params, type: 'SYSLOG' });
    },
    showDetail(row = {}) {
      this.$dialogAlert({
        params: {
          syslogConfig: row,
          callBack: () => {
            this.$refs.table.reloadCurrentPage();
          }
        },
        component: () => import('../dialogs/channel-syslog-dialog.vue'),
        alertWidth: '600px',
        alertHeight: 'auto',
        alertTitle: row.id ? '编辑Syslog同步' : '新增Syslog同步',
        alertStyle: {
          zIndex: 3000
        }
      });
    },
    async del(row) {
      if (this.$refs.table) {
        this.$refs.table.loading = true;
      }
      deleteSubscribeConfig({ id: row.id })
        .then(() => {
          this.$message.success('删除成功');
          if (this.$refs.table) {
            this.$refs.table.reloadCurrentPage();
          }
        })
        .catch((err) => {
          console.error(err);
          this.$message.error(err.msg || '删除失败');
        })
        .finally(() => {
          if (this.$refs.table) {
            this.$refs.table.loading = false;
          }
        });
    }
  }
};
</script>
<style lang="less" scoped>
.sync-channel-syslog {
  margin-top: -10px;
}
</style>
