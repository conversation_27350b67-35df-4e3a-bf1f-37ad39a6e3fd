<template>
  <div class="sync-channel">
    <el-tabs v-model="curTab">
      <el-tab-pane label="Syslog方式同步" name="syslog">
        <syslog v-if="curTab == 'syslog'" />
      </el-tab-pane>
      <el-tab-pane label="Kafka方式同步" name="kafka">
        <kafka v-if="curTab == 'kafka'" />
      </el-tab-pane>
      <!--      <el-tab-pane label="邮件方式同步" name="email">-->
      <!--        <email v-if="curTab == 'email'" />-->
      <!--      </el-tab-pane>-->
    </el-tabs>
  </div>
</template>

<script>
import Syslog from './sync-channel-syslog.vue';
import Kafka from './sync-channel-kafka.vue';
import Email from './sync-channel-email.vue';

export default {
  components: {
    syslog: Syslog,
    kafka: Kafka,
    email: Email
  },
  data() {
    return {
      curTab: 'syslog'
    };
  },
  mounted() {
    if (this.$route.query.curb) {
      this.curTab = this.$route.query.curb;
    }
  }
};
</script>
<style lang="less" scoped>
.sync-channel {
  .api-table {
    margin-top: -50px !important;
  }
  .el-tabs__content {
    overflow: initial;
  }
}
</style>
