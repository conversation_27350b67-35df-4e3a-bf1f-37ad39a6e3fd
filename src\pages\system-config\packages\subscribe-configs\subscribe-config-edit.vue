<!--
 * @Fileoverview: 新增推送任务
 * @Description: 配置-数据推送-新增推送任务
-->
<template>
  <div class="subscribe-config-edit">
    <el-form ref="form" :rules="rules" :model="config" label-width="100px">
      <el-form-item label="任务名称" prop="name">
        <el-input
          v-model.trim="config.name"
          type="text"
          size="small"
          placeholder="请输入"
        ></el-input>
      </el-form-item>
      <el-form-item label="推送范围" prop="scope" class="is-required">
        <el-select
          ref="selectRef"
          class="width-full"
          @focus="showSelectDialog"
          size="small"
          placeholder="请选择"
          v-model="sendSoruceRangeList"
          multiple
        >
          <el-option
            v-for="d in selectOption"
            :key="d.name"
            :value="d.id"
            :label="d.name"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="推送方式" prop="type">
        <el-select
          v-model="config.type"
          clearable
          size="small"
          placeholder="请选择推送方式"
          style="width: 100%"
          @change="handleTypeChange"
        >
          <el-option label="Syslog推送" value="SYSLOG"></el-option>
          <el-option label="Kafka推送" value="KAFKA"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        v-if="config.type"
        :label="config.type == 'KAFKA' ? 'Kafka配置' : 'Syslog配置'"
        prop="pushConfigs"
        class="width-full"
      >
        <el-select
          v-model="config.pushConfigs"
          size="small"
          multiple
          style="width: 91%"
          :placeholder="
            config.type == 'KAFKA' ? '请选择Kafka配置' : '请选择Syslog配置'
          "
        >
          <el-option
            v-for="item in kafkaOptions"
            :key="item.value"
            :label="item.label"
            :value="`${item.value}`"
          ></el-option>
        </el-select>
        <!-- <el-button
          type="primary"
          class="newMore ml10"
          @click="addSetting(config.type)"
        >
          新增
        </el-button> -->
      </el-form-item>
      <el-form-item label="执行方式：" prop="executionType" class="w100">
        <el-radio-group v-model="config.executionType" @change="oneChange">
          <el-radio label="IMMEDIATE">立即执行</el-radio>
          <el-radio label="PERIOD">周期执行</el-radio>
        </el-radio-group>
        <div class="bg-gray" v-if="config.executionType != 'IMMEDIATE'">
          <el-form-item
            class="is-required"
            label="执行时间:"
            prop="startDate"
            label-width="90px"
            v-if="config.executionType == 'TIME'"
          >
            <el-date-picker
              v-model="config.startDate"
              size="small"
              type="datetime"
              value-format="timestamp"
              placeholder="选择日期时间"
              :picker-options="expireTimeOption"
            ></el-date-picker>
          </el-form-item>
          <el-form-item
            label="周期选择:"
            prop="period"
            label-width="90px"
            class="is-required"
            v-if="config.executionType == 'PERIOD'"
          >
            <el-select
              placeholder="频次"
              class="period-type-picker"
              size="small"
              v-model="config.period"
              @change="periodChange"
            >
              <el-option value="DAY" label="每日"></el-option>
              <el-option value="WEEK" label="每周"></el-option>
              <el-option value="MONTH" label="每月"></el-option>
            </el-select>
            <el-select
              placeholder="日期"
              class="period-type-picker"
              size="small"
              v-model="config.periodType"
              v-if="config.period != 'DAY'"
            >
              <template v-if="config.period == 'WEEK'">
                <el-option value="1" label="周一"></el-option>
                <el-option value="2" label="周二"></el-option>
                <el-option value="3" label="周三"></el-option>
                <el-option value="4" label="周四"></el-option>
                <el-option value="5" label="周五"></el-option>
                <el-option value="6" label="周六"></el-option>
                <el-option value="7" label="周日"></el-option>
              </template>
              <template v-if="config.period == 'MONTH'">
                <el-option
                  :value="item.value"
                  :label="item.label"
                  v-for="item in MONTH_LIST"
                  :key="item.value"
                ></el-option>
              </template>
            </el-select>
            <el-time-picker
              v-else
              class="time-range-picker"
              v-model="config.periodStart"
              placeholder="任意时间点"
              format="HH:mm"
              size="small"
              value-format="timestamp"
            ></el-time-picker>
          </el-form-item>
        </div>
      </el-form-item>
      <el-form-item label="输出字段">
        <el-input
          type="textarea"
          v-model.trim="customFieldsStr"
          disabled
          :rows="5"
        ></el-input>
        <div class="action-link" @click="customOutputFields">
          自定义推送字段
        </div>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import * as subscribeConfigsService from '@/service/subscribe-configs-service';
import CronTimePicker from '@/components/cron-time-picker.vue';
import {
  getSubscribeConfigList,
  addSubscribeConfig,
  updateSubscribeConfig
} from '@/service/subscribe-configs-service';
import MONTH_LIST from '@/constant/common-constants';
import { handleCron } from '@/utils/string-utils';
import { deepCopy } from '@/utils/deep-copy';

export default {
  components: { CronTimePicker },
  props: ['id', 'isLoading'],
  data() {
    return {
      MONTH_LIST,
      props: { multiple: true },
      customFieldsStr: '{\n}',
      config: {
        name: '',
        type: '',
        pushConfigs: '',
        scope: {
          dc_data_source: [],
          dc_database: []
        },
        executionType: 'IMMEDIATE',
        cron: '',
        customFields: '{}',
        startDate: '',
        periodStart: '',
        periodType: '',
        period: ''
      },
      kafkaOptions: [],
      rules: {
        name: [{ required: true, message: '请输入任务名称' }],
        type: [{ required: true, message: '请选择推送方式' }],
        pushConfigs: [{ required: true, message: '请选择推送配置' }],
        executionType: [
          { required: true, message: '请选择推送周期', trigger: 'change' }
        ]
      },
      expireTimeOption: {
        disabledDate(date) {
          return date.getTime() < Date.now() - 24 * 60 * 60 * 1000;
        }
      },
      saveLoading: this.isLoading,
      selectOption: [],
      sendSoruceRangeList: []
    };
  },
  watch: {
    saveLoading() {
      this.$emit('update:isLoading', this.saveLoading);
    },
    'config.type': {
      handler() {
        this.handleTypeChange();
      }
    }
  },
  mounted() {
    this.id && this.getDetail(this.id);
  },
  methods: {
    // 类型改变
    async handleTypeChange() {
      const params = {};
      params.type = this.config.type;
      const res = await getSubscribeConfigList(params);

      this.kafkaOptions = res.data.rows.map((item) => ({
        value: item.id, // 确保字符串类型
        label: item.name || '未命名配置' // 空值处理
      }));
    },

    showSelectDialog() {
      //立即执行失去焦点的逻辑，防止一直触发
      this.$refs.selectRef.blur();
      this.$DrawAlert({
        title: '推送范围',
        width: 70,
        params: {
          from: 'send',
          list: this.selectOption,
          callBack: (selectedData) => {
            selectedData.forEach((item) => {
              if (item.children.length > 0) {
                this.selectOption = this.selectOption.concat(
                  item.children.map((item) => {
                    const databaseInfos = item.split('@_@');
                    return {
                      id: parseInt(databaseInfos[0]),
                      name: databaseInfos[1],
                      url: item.url || '',
                      type: 'field'
                    };
                  })
                );
              } else {
                this.selectOption.push({
                  id: item.id,
                  name: item.name,
                  url: item.url,
                  type: 'source'
                });
              }
            });
            this.selectOption = [
              ...new Map(
                this.selectOption.map((item) => [item.id, item])
              ).values()
            ];
            // console.log(this.selectOption,'this.selectOption')
            this.sendSoruceRangeList = this.selectOption.map((item) => item.id);
          }
        },
        componentObj: {
          component: () =>
            import(
              '@/components-react/qz-components/asset-sance/instance-select.vue'
            )
        }
      });
    },
    // 自定义输出字段
    customOutputFields() {
      this.$dialogAlert({
        params: {
          customFields: this.config.customFields,
          callBack: (res) => {
            this.config.customFields = JSON.stringify(res.data);
            this.customFieldsStr = JSON.stringify(res.data, null, 2);
          }
        },
        component: () => import('./dialogs/custom-fields-dialog.vue'),
        alertWidth: '720px',
        alertHeight: 'auto',
        alertTitle: '自定义输出字段',
        alertStyle: {
          zIndex: 3000
        }
      });
    },
    save() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          const copyConfig = {
            ...deepCopy(this.config),
            scope: {
              dc_data_source: [],
              dc_database: []
            }
          };
          const fn = this.config.id
            ? updateSubscribeConfig
            : addSubscribeConfig;
          if (this.sendSoruceRangeList.length == 0) {
            this.$message.warning('请选择推送范围！');
            return;
          }
          this.sendSoruceRangeList.forEach((opItem) => {
            const info = this.selectOption.find((item) => item.id == opItem);
            if (info?.type == 'source') {
              copyConfig.scope.dc_data_source.push({
                id: info.id,
                name: info.name
              });
            } else {
              copyConfig.scope.dc_database.push({
                id: info.id,
                name: info.name
              });
            }
          });

          if (copyConfig.executionType == 'PERIOD') {
            const timeCycl = {
              cycl: copyConfig.period,
              date: copyConfig.periodStart,
              week: copyConfig.periodType,
              month: copyConfig.periodType
            };
            copyConfig.cron = handleCron(timeCycl);
          }
          await fn(copyConfig)
            .then((res) => {
              this.$emit('reload', null);
              this.$emit('update:show', false);
              this.$emit('update:isLoading', false);
              this.$message.success('新增成功');
            })
            .catch((err) => {
              this.$message.error(err.msg || '新增失败');
            });
        }
      });
    },
    async getDetail(id) {
      try {
        const res = await subscribeConfigsService.getSubscribeConfigDetail(id);

        const { data } = res;

        this.customFieldsStr = JSON.stringify(
          JSON.parse(data.customFields),
          null,
          2
        );

        this.config = {
          ...this.config,
          ...(data || {})
        };
        const sourceList = data.scope.dc_data_source.map((item) => {
          return {
            id: item.id,
            name: item.name,
            type: 'source'
          };
        });
        const dateBaseList = data.scope.dc_database.map((item) => {
          return {
            id: item.id,
            name: item.name,
            type: 'field'
          };
        });
        this.selectOption = sourceList.concat(dateBaseList);
        this.sendSoruceRangeList = this.selectOption.map((item) => item.id);

        if (this.config.executionType == 'PERIOD') {
          this.handleCronToTime(this.config.cron);
        }
      } catch (err) {
        console.error(err);
        this.$message.error(err.msg || '未知异常');
      }
    },
    addSetting(type) {
      if (type === 'SYSLOG') {
        this.$dialogAlert({
          params: {
            syslogConfig: {},
            callBack: () => {
              this.handleTypeChange();
            }
          },
          component: () => import('./dialogs/channel-syslog-dialog.vue'),
          alertWidth: '500px',
          alertHeight: 'auto',
          alertTitle: '新增Syslog同步',
          alertStyle: {
            zIndex: 3000
          }
        });
      } else {
        this.$dialogAlert({
          params: {
            kafkaConfig: {},
            callBack: () => {
              this.handleTypeChange();
            }
          },
          component: () => import('./dialogs/channel-kafka-dialog.vue'),
          alertWidth: '500px',
          alertHeight: 'auto',
          alertTitle: '新增Kafka同步',
          alertStyle: {
            zIndex: 3000
          }
        });
      }
    },
    // 订阅范围
    handleScope(data) {
      this.config.scope = data;
    },
    oneChange() {
      this.config.startDate = '';
      this.config.period = '';
      this.config.periodStart = '';
      this.config.periodType = '';
    },
    periodChange() {
      this.config.periodType = '';
      this.config.periodStart = '';
    },
    handleCronToTime(cron) {
      if (!cron) return;
      const corns = cron.split(' ');
      //每周周几
      if (cron.startsWith('0 0 0 ? *')) {
        const weekDay = corns[5];
        this.config.period = 'WEEK';
        this.config.periodType = weekDay.toString();
      } else if (cron.endsWith('* ? *')) {
        //每月几号
        const monthDay = corns[3];
        const day = monthDay == 'L' ? 'L' : monthDay;
        this.config.period = 'MONTH';
        this.config.periodType = day;
      } else {
        const h = corns[2];
        const m = corns[1];
        const s = corns[0];
        const date = new Date();
        const year = date.getFullYear();
        const month = date.getMonth();
        const day = date.getDay();
        const fixDay = `${year}-${month}-${day} ${h}:${m}:${s}`;
        const timestamp = new Date(fixDay).valueOf();
        this.config.period = 'DAY';
        this.config.periodStart = timestamp;
      }
    }
  }
};
</script>
<style lang="less" scoped>
@import '@/assets/css/common.less';

.subscribe-config-edit {
  padding: 0;
  height: 100%;
  .flex {
    display: flex;
    align-items: center;
  }
}
</style>
