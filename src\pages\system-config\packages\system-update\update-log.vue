<!--
 * @Fileoverview: 查看升级日志
 * @Description: 配置-系统升级-查看升级日志
-->
<template>
  <div class="update-log">
    <api-table
      ref="table"
      table-id="updateData"
      :showHeader="false"
      :data-source="getLogList"
    >
      <api-table-column
        prop="created_at"
        label="升级时间"
        formatter="formatTime"
      />
      <api-table-column prop="type" label="升级类别">
        <template slot-scope="{ row }">
          {{ row.rollbackStatusName ? '系统回滚' : '系统升级' }}
        </template>
      </api-table-column>
      <api-table-column prop="source" label="原版本" />
      <api-table-column prop="target" label="目标版本" />
      <api-table-column label="操作结果">
        <template slot-scope="{ row }">
          <span :class="row.status"> ● </span>
          {{ STATUS_MAP[row.status] }}
        </template>
      </api-table-column>
      <api-table-column key="operation" label="操作" width="80" fixed="right">
        <template slot-scope="{ row }">
          <div @click="showUpdateLog(row.log)" class="action-link">查看</div>
        </template>
      </api-table-column>
    </api-table>
  </div>
</template>

<script>
import { getUpdateLogList } from '@/service/upload-update-service';

export default {
  data() {
    return {
      STATUS_MAP: {
        SUCCESS: '成功',
        FAIL: '失败',
        INIT: '初始化',
        RUNNING: '运行中'
      }
    };
  },
  methods: {
    getLogList(params) {
      const paramsLog = {
        isPageQuery: true,
        limit: params.limit,
        page: params.page,
        columnList: [
          'id',
          'type',
          'status',
          'updated_at',
          'created_at',
          'log',
          'source',
          'target'
        ],
        sortList: [
          {
            fieldName: 'created_at',
            sortExp: 'desc'
          }
        ]
      };
      return getUpdateLogList(paramsLog);
    },
    // 查看某一次的升级日志
    showUpdateLog(updateLog = '') {
      this.$dialogAlert({
        params: {
          logInfo: updateLog,
          logFlag: true,
          callBack: () => {}
        },
        component: () => import('./upload-update.vue'),
        alertWidth: '50%',
        alertHeight: 'auto',
        alertTitle: '升级详情',
        alertStyle: {
          zIndex: 3000
        }
      });
    }
  }
};
</script>
<style lang="less" scoped>
.update-log {
  .api-table .el-table th:first-of-type,
  .api-table .el-table td:first-of-type {
    padding-left: 0;
  }
  .SUCCESS {
    color: @green-color;
  }
  .INIT {
    color: @orange-color;
  }
  .RUNNING {
    color: @orange-color;
  }
  .FAIL {
    color: @red-color;
  }
}
</style>
