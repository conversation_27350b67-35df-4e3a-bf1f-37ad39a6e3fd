<!--
 * @Fileoverview: 上传并更新
 * @Description: 配置-系统升级-上传并更新
-->
<template>
  <div class="upload-update">
    <div class="upload-update__wrapper" v-if="isShowDialog">
      <div class="upload-update__wrapper__content">
        <el-form ref="form" label-width="100px">
          <el-form-item label="升级包">
            <div>
              <el-upload
                ref="upload"
                :action="DATA_URL_UPLOAD_UPDATE_ADDRESS"
                :multiple="false"
                :limit="1"
                :show-file-list="false"
                :auto-upload="true"
                :on-error="handleError"
                :on-success="handleSuccess"
                :before-upload="handleBeforeLoad"
                :disabled="decryptStatus === 1"
                name="file"
                accept=".out"
              >
                <div class="upload-update__wrapper__file">
                  <qz-icon class="icon-upload icon-upload-style" />
                  {{ fileName }}
                </div>
              </el-upload>
            </div>
            <div class="upload-update__wrapper__item">支持扩展名: .out</div>
            <div class="upload-update__wrapper__item">
              支持文件大小: 最大20G
            </div>
          </el-form-item>
          <el-form-item>
            <el-progress
              v-if="progressPercent > 0 && progressPercent <= 100"
              style="margin-left: 100px"
              :percentage="progressPercent"
              :color="customColor"
            ></el-progress>
          </el-form-item>
          <el-form-item
            style="margin-left: 100px"
            class="upload-update__wrapper__tip"
          >
            {{ updateTip }}
          </el-form-item>
        </el-form>
        <div class="align-right">
          <el-button @click="closeDialog" size="small">取消</el-button>
          <el-button
            type="primary"
            size="small"
            :disabled="decryptStatus !== 2"
            @click="confirmUpdate"
          >
            确定
          </el-button>
        </div>
      </div>
    </div>
    <div class="upload-update__progress" v-if="!isShowDialog">
      <p v-if="isShowWarn" class="upload-update__progress__tip">
        系统升级中，请勿操作！
      </p>
      <div class="upload-update__progress__content" ref="updateInfoRef">
        <p v-for="(item, index) in updateInfo" :key="index">{{ item }}</p>
      </div>
      <div class="upload-update__progress__button">
        <el-button
          :type="btnType"
          size="small"
          :disabled="isDisabled"
          @click="closeDialog"
        >
          确定
        </el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { DATA_URL_UPLOAD_UPDATE_ADDRESS } from '@/constant/data-url-constants';
import {
  updateProgress,
  getUpdateLog,
  getDecryptStatus
} from '@/service/upload-update-service';
import { CHART_COLOR_THEME_COLOR } from '@/constant/common-constants';
import * as loginService from '@/service/login-service';

export default {
  props: ['params'],
  data() {
    return {
      getUpdateLog,
      DATA_URL_UPLOAD_UPDATE_ADDRESS,
      isShowDialog: true,
      isShowWarn: true,
      fileName: '上传升级包',
      btnType: 'info',
      isDisabled: true,
      progressPercent: 0,
      progressCompleted: false,
      customColor: CHART_COLOR_THEME_COLOR,
      updateTip: '',
      updateInfo: [],
      filePath: '',
      bucket: '',
      decryptStatus: '',
      timer: null,
      decryptStatusTimer: null,
      progressTimer: null,
      heartBeatTimer: null
    };
  },
  methods: {
    keepHeartBeating() {
      // 保持心跳，防止session超时
      clearTimeout(this.heartBeatTimer);
      this.heartBeatTimer = setTimeout(() => {
        loginService
          .checkIsLogin()
          .then(() => {})
          .catch(() => {})
          .finally(() => {
            if (!this.progressCompleted) {
              this.keepHeartBeating();
            }
          });
      }, 55 * 1000);
    },
    stopHearBeating() {
      clearTimeout(this.heartBeatTimer);
    },
    progressStart() {
      const work = () => {
        setTimeout(() => {
          if (!this.progressCompleted) {
            this.progressInc();
            work();
          }
        }, 1500);
      };
      work();
      this.keepHeartBeating();
    },
    progressInc() {
      let n = this.progressPercent;
      if (n > 100) {
        return;
      } else {
        let step = 0;
        if (n >= 0 && n < 20) {
          step = 1;
        } else if (n >= 20 && n < 80) {
          step = 2;
        } else if (n >= 80 && n < 99) {
          step = 1;
        } else {
          step = 0;
        }

        n = n + step;
        if (n < 0) n = 0;
        if (n > 99) n = 99;
        return (this.progressPercent = n);
      }
    },
    progressDone(success) {
      this.stopHearBeating();
      this.progressCompleted = true;
      if (success) {
        this.progressPercent = 100;
      }
    },
    // 点击取消关闭对话框
    closeDialog() {
      this.$refs.upload && this.$refs.upload.abort();
      this.progressDone();
      this.params.close();
    },

    // 文件上传前校验文件大小和文件格式
    handleBeforeLoad(file) {
      if (file.size > 20 * 1024 * 1024 * 1024) {
        this.$message.error('文件大小不可超过20G！');
        return false;
      }
      this.progressPercent = 0;
      this.updateTip = '';
      this.decryptStatus = '';
      this.fileName = file.name;
      this.progressStart();
      return true;
    },

    handleError(err) {
      if (err.status === 413) {
        this.updateTip = '升级包过大，请检查后端对文件大小的限制';
      } else if (err.status === 504) {
        this.updateTip = '接口处理超时，请合理配置超时时间';
      } else {
        this.updateTip = err.msg || '升级包上传失败，请重新上传升级程序';
      }
      this.progressDone();
    },
    // on-success 改变时触发
    handleSuccess(res) {
      if (res.success) {
        this.filePath = res.data.path;
        this.bucket = res.data.bucket;
        // 查询升级包的解压过程
        this.progressPercent = 100;
        this.progressDone(true);
        // this.getDecryptStatus();
        this.decryptStatus = 2;
        // progressStop();
      } else {
        this.progressDone();
        this.updateTip = res.msg || '升级包上传失败，请重新上传升级程序';
      }
    },

    // 点击确定进入升级
    confirmUpdate() {
      this.progressPercent = 0;
      this.isShowDialog = false;
      this.btnType = 'info';
      this.isDisabled = true;
      const dialogTitle = document.querySelector('.qz-alert__body__title');
      if (dialogTitle) {
        dialogTitle.innerText = '正在升级';
      }
      const dialogBody = document.querySelector('.qz-alert__body');
      if (dialogBody) {
        dialogBody.style.width = '45%';
      }

      const params = {
        path: this.filePath,
        bucket: this.bucket
      };
      updateProgress(params)
        .then((res) => {
          const self = this;
          if (res.success) {
            // 轮询获取更新过程中的信息
            if (self.timer) {
              clearInterval(self.timer);
              self.timer = null;
              self.updateInfo = [];
            } else {
              const logParams = {
                columnList: [
                  'id',
                  'type',
                  'status',
                  'source',
                  'target',
                  'created_at',
                  'updated_at',
                  'log'
                ],
                isPageQuery: true,
                page: 1,
                limit: 1,
                sortList: [
                  {
                    fieldName: 'created_at',
                    sortExp: 'desc'
                  }
                ]
              };
              self.timer = setInterval(() => {
                getUpdateLog(logParams).then((res) => {
                  const data = (res && res.data && res.data.rows[0]) || {};

                  const tempUpdateLog = data.log;
                  const updateLog =
                    (tempUpdateLog && tempUpdateLog.split('\n')) || '';
                  self.updateInfo = updateLog;

                  // if (data.status && data.status == 'RUNNING') {
                  //   const tempRollBackLog = data.log;
                  //   const rollbackLog = (tempRollBackLog && tempRollBackLog.split('\n')) || '';
                  //   self.updateInfo = [...updateLog, ...rollbackLog];
                  // }

                  if (data.status == 'SUCCESS' || data.status == 'FAIL') {
                    if (data.status == 'SUCCESS') {
                      const endInfo = ['升级完成！'];
                      self.updateInfo = [...self.updateInfo, ...endInfo];
                    }
                    // 当有滚动条时，展示底部最新信息
                    self.$nextTick(() => {
                      const info = self.$refs.updateInfoRef;
                      info.scrollTop = info.scrollHeight;
                    });
                    clearInterval(self.timer);
                    self.timer = null;
                  }

                  if (res.success) {
                    self.isShowWarn = false;
                    self.isDisabled = false;
                    self.btnType = 'primary';
                  }
                });
              }, 1500);
            }
          }
        })
        .catch((err) => {
          this.$message.error(err.msg || '升级失败，请稍后重试');
        });
    },
    getDecryptStatus() {
      getDecryptStatus().then((res) => {
        this.decryptStatus = res.data;
        if (this.decryptStatus === 1) {
          this.updateTip = '升级包已上传完成，正在解密...';
          this.decryptStatusTimer = setTimeout(() => {
            this.getDecryptStatus();
          }, 2 * 1000);
        } else {
          this.decryptStatusTimer && clearTimeout(this.decryptStatusTimer);
          if (this.decryptStatus === 2) {
            this.updateTip =
              '升级包解密完成，请点击确定进入升级。您也可以重新上传升级包';
          }
          if (this.decryptStatus === 3) {
            this.updateTip = '升级包解密失败，请重新上传升级程序';
          }
        }
      });
    }
  },
  mounted() {
    // 如果是查看升级日志，则显示对应行的升级过程信息
    if (this.params.logFlag) {
      this.isShowDialog = false;
      this.isShowWarn = false;
      this.btnType = 'primary';
      this.isDisabled = false;
      const tempInfo = this.params.logInfo;
      this.updateInfo = (tempInfo && tempInfo.split('\n')) || '';
      this.$nextTick(() => {
        const info = this.$refs.updateInfoRef;
        info.scrollTop = info.scrollHeight;
      });
    }
    // 上传升级包对话框，右上角的❌取消掉
    if (this.params.uploadFlag) {
      const dialogTitle = document.querySelector('.qz-alert__body__title');
      if (dialogTitle) {
        dialogTitle.innerText = '上传并更新';
      }
      const dialogBody = document.querySelector('.qz-alert__body');
      if (dialogBody) {
        dialogBody.style.width = '700px';
      }
      // 检查升级包是否正在解压，如果正在解压就不能上传
      this.getDecryptStatus();
    }

    window.onbeforeunload = function () {
      return '';
    };
  },
  beforeDestroy() {
    this.heartBeatTimer && clearTimeout(this.heartBeatTimer);
    this.decryptStatusTimer && clearTimeout(this.decryptStatusTimer);
    this.timer && clearInterval(this.timer);
  },
  destroyed() {
    if (this.timer) {
      clearInterval(this.timer);
      this.timer = null;
    }
    window.onbeforeunload = null;
  }
};
</script>
<style lang="less" scoped>
.upload-update {
  &__progress {
    &__tip {
      font-size: 12px;
      color: red;
    }
    &__content {
      height: 300px;
      border: 1px solid @border-base-color;
      border-radius: 5px;
      padding-left: 10px;
      padding-top: 10px;
      overflow: scroll;
      color: @text-primary-color;
    }
    &__button {
      float: right;
      margin-top: 20px;
    }
  }
  &__wrapper {
    position: relative;
    &__content {
      .el-progress-bar {
        margin-top: 10px;
        margin-bottom: 10px;
      }
    }
    &__item {
      color: @text-tip-color;
      font-size: 12px;
      line-height: 25px;
    }
    &__tip {
      color: @red-color;
      margin-bottom: 30px;
    }
    &__file {
      border: 1px solid @border-base-color;
      padding: 0 10px;
      border-radius: 10px;
      line-height: 34px;
      color: @text-primary-color;
      margin-bottom: 10px;
      & .icon-upload-style {
        margin-right: 5px;
        color: @red-color;
      }
    }
  }
}
</style>
