<template>
  <div class="strategy">
    <el-form :model="form" label-width="100px">
      <el-form-item label="策略名称">
        <el-input v-model="form.name" />
      </el-form-item>
      <el-form-item label="策略描述">
        <el-input v-model="form.description" />
      </el-form-item>
      <el-form-item label="策略代码">
        <el-input v-model="form.code" />
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  props: ['params'],
  data() {
    return {
      form: {
        name: '',
        description: '',
        code: ''
      }
    };
  }
};
</script>

<style scoped lang="less">
.strategy {
}
</style>
