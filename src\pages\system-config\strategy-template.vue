<!--
 * @Fileoverview: 策略模版管理
 * @Description: 配置-策略模版管理
-->
<template>
  <div class="strategy-template">
    <api-table
      ref="table"
      table-id="openApiList"
      :data-source="getDataList"
      :border="true"
      :search-input-options="{
        key: 'name',
        placeholder: '请输入模版名称查询'
      }"
      toolsLayout="searchInput,openApi"
    >
      <api-table-tool-register id="openApi">
        <el-button
          type="primary"
          size="mini"
          class="mr10"
          @click="importStrategy()"
        >
          导入
        </el-button>
        <el-button
          type="primary"
          size="mini"
          class="mr10"
          @click="exportStrategy()"
        >
          导出
        </el-button>
        <el-button
          type="primary"
          size="mini"
          class="mr10"
          @click="saveStrategy()"
        >
          新增模版
        </el-button>
      </api-table-tool-register>
      <api-table-column label="模版名称" prop="name"></api-table-column>
      <api-table-column label="模版描述" prop="description"></api-table-column>

      <api-table-column label="操作" width="160">
        <template slot-scope="{ row }">
          <span class="action-link" @click="getKeyData(row)">编辑</span>
          <qz-popconfirm
            title="删除后立即生效"
            content="删除动作不可撤回，你还要继续吗？"
            class="action-link"
            @confirm="deleteStragy(row)"
          >
            <span slot="reference">删除</span>
          </qz-popconfirm>
        </template>
      </api-table-column>
    </api-table>
  </div>
</template>
<script>
import { getStrategyList } from '@/service/strategy-template';

export default {
  data() {
    return {
      tableData: [],

      form: {
        name: '',
        description: '',
        builtIn: '',
        publishStatus: '',
        code: ''
      }
    };
  },
  methods: {
    saveStrategy() {
      this.$DrawAlert({
        title: '新增模版'
      });
    },
    exportStrategy() {},
    importStrategy() {},
    getKeyData(row) {},
    deleteStragy(row) {},
    getDataList(params) {
      return getStrategyList(params);
    }
  }
};
</script>
<style scoped lang="less">
.strategy-template {
}
</style>
