<template>
  <div class="system_auth">
    <div class="system_auth_info">
      <el-row :gutter="6" class="mb20">
        <el-col :span="8" class="info-box__item">
          <div class="info-box__label">产品名称：</div>
          <div class="info-box__content">{{ productName || '--' }}</div>
        </el-col>
        <el-col :span="8" class="info-box__item">
          <div class="info-box__label">产品版本：</div>
          <div class="info-box__content">{{ version || '--' }}</div>
        </el-col>
        <el-col :span="8" class="info-box__item">
          <div class="info-box__label">机器时间：</div>
          <div class="info-box__content">{{ systemTime }}</div>
        </el-col>
      </el-row>
      <el-row :gutter="6" class="mb20">
        <el-col :span="8" class="info-box__item">
          <div class="info-box__label">授权开始时间：</div>
          <div class="info-box__content">{{ startTime }}</div>
        </el-col>
        <el-col :span="8" class="info-box__item">
          <div class="info-box__label">授权状态：</div>
          <div class="info-box__content">
            <div class="license-status" :class="state == 1 ? 'has' : 'no'">
              {{ LICENSE_STATUS_MAP[state] }}
            </div>
          </div>
          <label class="action-link ml5">
            <span>
              {{ state != 1 ? '授权' : '重新授权' }}
            </span>
            <input type="file" class="hidden" @change="uploadPlatformLicense" />
          </label>
        </el-col>
        <el-col :span="8" class="info-box__item">
          <div class="info-box__label">机器码：</div>
          <div class="info-box__content">
            <el-popover
              placement="right"
              width="225"
              trigger="hover"
              @show="showQrCode('platform')"
            >
              <canvas ref="platform"></canvas>
              <i slot="reference" class="fa fa-qrcode"></i>
            </el-popover>
          </div>
        </el-col>
      </el-row>
      <el-row :gutter="6">
        <el-col :span="8" class="info-box__item">
          <div class="info-box__label">授权到期时间：</div>
          <div class="info-box__content">{{ endTime }}</div>
        </el-col>
      </el-row>
      <qz-icon class="icon-trash del_icon" @click.native="delLicense"></qz-icon>
    </div>
  </div>
</template>

<script>
import {
  getLicense,
  getLicenseDelete,
  getMachineCode,
  updateLicense
} from '@/service/system-auth-service';
import QRCode from 'qrcode';
import { formatTime } from '@/utils/string-utils';
import { setPageMenuAndBlockConfigs } from '@/utils/storage-utils';
import { mapState } from 'vuex';

const LICENSE_STATUS_MAP = {
  0: '未授权',
  1: '已授权',
  2: '授权过期'
};
export default {
  data() {
    return {
      LICENSE_STATUS_MAP,
      machineCode: '',
      platformLicenseInfo: {},
      version: '',
      startTime: 0,
      endTime: 0,
      systemTime: 0,
      state: 0
    };
  },

  components: {},

  mounted() {
    //获取授权机器码
    getMachineCode()
      .then((res) => {
        this.machineCode = res.data;
      })
      .catch((err) => {
        this.$message.error(err.msg || '获取机器码失败');
      });
    this.getDetailLicense();
  },
  computed: {
    ...mapState(['productName'])
  },

  methods: {
    getDetailLicense() {
      getLicense()
        .then((res) => {
          this.version = res.data.version;
          this.startTime = res.data.start_time
            ? formatTime(parseInt(res.data.start_time))
            : '--';
          this.endTime = res.data.end_time
            ? formatTime(parseInt(res.data.end_time))
            : '--';
          this.systemTime = formatTime(res.data.systemTime);
          this.state = res.data.state;

          setPageMenuAndBlockConfigs(res.data);
        })
        .catch((err) => {
          this.$message.error(err.msg || '获取授权信息失败');
        });
    },
    formatTime,
    showQrCode(ref) {
      const ele =
        this.$refs[ref] instanceof Array ? this.$refs[ref][0] : this.$refs[ref];
      QRCode.toCanvas(ele, this.machineCode, { margin: 0, width: 200 });
    },
    hideQrCode(event) {
      $(event.target).find('.qr-code').removeClass('shown').addClass('hidden');
    },
    uploadPlatformLicense(event) {
      const file = event.target.files[0];
      const formData = new FormData();
      formData.append('multipartFile', file);
      this.loading = true;
      updateLicense(formData)
        .then(
          () => {
            event.target.value = '';
            this.$message.success('授权成功');
            // // 强制刷新页面，让菜单修改生效
            // location.reload();
            this.getDetailLicense();
          },
          (error) => {
            event.target.value = '';
            this.$message.error(error.msg || '文件上传失败');
          }
        )
        .finally(() => {
          this.loading = false;
        });
    },
    delLicense() {
      this.$confirm('确定删除吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        getLicenseDelete()
          .then((res) => {
            this.$message.success('删除成功');
          })
          .catch((err) => {
            this.$message.error(err.msg || '删除失败');
          });
      });
    }
  }
};
</script>
<style lang="less" scoped>
.system_auth {
  .system_auth_info {
    position: relative;
    padding: 10px;
    background: #f3f4f5;
    // border: 1px solid #42d7dd66;
    .reset_auth {
      color: #15c5e0;
      cursor: pointer;
    }
    .code {
      display: flex;
      align-items: center;
      img {
        width: 50px;
        height: 50px;
      }
    }
    .info-box__item {
      display: flex;
      align-items: center;
      font-family: PingFangSC-Regular;
      font-size: 14px;
    }
    .info-box__label {
      flex: none;
      color: #2e3444;
    }
    .info-box__content {
      display: flex;
      align-items: center;
      flex-wrap: nowrap;
      color: #666666;
    }
    .edit_icon {
      color: @theme-blue;
      cursor: pointer;
      font-size: 20px;
    }
    .del_icon {
      position: absolute;
      right: 10px;
      top: 10px;
      color: #409eff;
      cursor: pointer;
      font-size: 20px;
    }
  }
}
.license-status {
  padding: 1px 11px;
  border-radius: 9px;
  border-radius: 9px;
  font-family: PingFangSC-Regular;
  font-size: 12px;
  &.has {
    background: #529b2e;
    color: #fff;
  }
  &.no {
    background: #fff4c9;
    color: darken(#fff4c9, 60%);
  }
}
.qr-code-wrapper {
  position: relative;
  cursor: pointer;
  i {
    font-size: 22px;
  }
  .qr-code.hidden {
    display: none;
  }
  .qr-code.shown {
    display: block;
    position: absolute;
    left: 100%;
    top: -20px;
    padding: 20px;
    background-color: #fff;
    z-index: 100;
  }
}
</style>
