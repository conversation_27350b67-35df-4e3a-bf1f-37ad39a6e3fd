<template>
  <div class="user-profile">
    <el-form ref="form" :model="user" :rules="rules" label-width="120px">
      <el-form-item label="用户名称">
        <el-input
          v-model.trim="user.username"
          disabled
          size="medium"
        ></el-input>
      </el-form-item>
      <el-form-item label="用户角色">
        <el-input
          v-model.trim="user.roleName"
          disabled
          size="medium"
        ></el-input>
      </el-form-item>
      <el-form-item label="旧密码" prop="oldPassword">
        <el-input
          v-model.trim="user.oldPassword"
          type="password"
          size="medium"
          placeholder="请输入旧密码"
        ></el-input>
      </el-form-item>
      <el-form-item label="新密码" prop="newPassword">
        <el-input
          v-model.trim="user.newPassword"
          type="password"
          size="medium"
          placeholder="请输入长度至少为8的新密码"
        ></el-input>
        <div v-if="user.newPassword" class="pwd-intensity">
          <div
            :class="
              pwdIntensity >= 1
                ? 'pwd-intensity--low'
                : 'pwd-intensity--default'
            "
          ></div>
          <div
            :class="
              pwdIntensity >= 2
                ? 'pwd-intensity--mid'
                : 'pwd-intensity--default'
            "
          ></div>
          <div
            :class="
              pwdIntensity >= 3
                ? 'pwd-intensity--high'
                : 'pwd-intensity--default'
            "
          ></div>
          <div class="pwd-intensity__text">
            {{ intensityText[pwdIntensity] }}
          </div>
        </div>
      </el-form-item>
      <el-form-item label="确认密码" prop="confirmPassword">
        <el-input
          v-model.trim="user.confirmPassword"
          type="password"
          size="medium"
          placeholder="请再次输入新密码"
        ></el-input>
      </el-form-item>
      <el-form-item label="邮箱" prop="email">
        <el-input
          v-model.trim="user.email"
          size="medium"
          placeholder="请输入新邮箱"
        ></el-input>
      </el-form-item>
      <div class="user-profile__footer">
        <el-button :disabled="saveLoading" type="primary" @click="save">
          保存
        </el-button>
      </div>
    </el-form>
  </div>
</template>

<script>
/* eslint-disable */
import {
  getSafeConfig,
  getUserDetailByName,
  editUser,
  getUserDetailByToken
} from '@/service/management-service';
import {
  getLoginSecurityInfoById,
  getLoginSecurityInfos,
  modifyPassword
} from '@/service/login-service';
import { getLocalUserInfo } from '@/utils/storage-utils';
import md5 from 'md5';
import { validateEmail } from '@/utils/string-utils';
import { getPwdIntensity, pwdValidators } from '@/utils/password-utils';
export default {
  data() {
    const validateOldPass = (rule, value, callback) => {
      if (this.user.newPassword && !this.user.oldPassword) {
        callback(new Error('请输入旧密码!'));
        return;
      }
      callback();
    };
    const validateNewPass = (rule, value, callback) => {
      if (
        this.user.newPassword &&
        this.user.confirmPassword &&
        this.user.newPassword !== this.user.confirmPassword
      ) {
        callback(new Error('两次输入密码不一致!'));
        return;
      } else if (this.user.newPassword && this.user.confirmPassword) {
        const { valid, err } = this.pwdValidator(this.user.newPassword);
        if (!valid) {
          return callback(new Error(err));
        }
      }
      callback();
    };
    const emailValidator = (rule, value, callback) => {
      if (this.user.email && !validateEmail(this.user.email)) {
        callback(new Error('邮箱格式不正确'));
        return;
      }
      callback();
    };
    return {
      intensityText: ['强度', '低强度', '中强度', '高强度'],
      user: {
        id: '',
        username: '',
        roleName: '',
        oldPassword: '',
        newPassword: '',
        confirmPassword: '',
        email: ''
      },
      rules: {
        oldPassword: [{ validator: validateOldPass, trigger: 'blur' }],
        confirmPassword: [{ validator: validateNewPass, trigger: 'blur' }],
        email: [{ validator: emailValidator, trigger: 'blur' }]
      },
      saveLoading: false,

      pwdLevel: 3
    };
  },
  computed: {
    pwdIntensity() {
      return getPwdIntensity(this.user.newPassword);
    },
    pwdValidator() {
      return pwdValidators[this.pwdLevel];
    }
  },
  async mounted() {
    // 获取密码强度配置
    getLoginSecurityInfoById('/passwordSecurity').then((res) => {
      this.pwdLevel = JSON.parse(res.data).passwordLevel;
    });
    //获取当前登录用户
    await getUserDetailByToken().then((res) => {
      this.user.id = res.data.id;
    });
    await getUserDetailByName({ id: this.user.id }).then((res) => {
      this.user.username = res.data.username;
      let roleName = [];
      res.data.roleList.forEach((item) => {
        roleName.push(item.roleName);
      });
      this.user.roleName = roleName.join(',');
      this.user.email = res.data.email;
    });
  },
  methods: {
    save() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          const promiseFuncs = [];
          // 如果有输入密码，要求确认一遍密码
          if (this.user.newPassword) {
            if (!this.user.confirmPassword) {
              this.$message.warning('请再输入一遍确认密码');
              return;
            }
            if (this.user.newPassword !== this.user.confirmPassword) {
              this.$message.warning('两次输入密码不一致');
              return;
            }
            promiseFuncs.push(
              modifyPassword({
                id: this.user.id,
                newPassword: md5(this.user.newPassword),
                oldPassword: md5(this.user.oldPassword),
                email: this.user.email
              })
            );
          }
          // if (this.user.email) {
          //   const params = { ...this.user };
          //   params.password = '';
          //   promiseFuncs.push(editUser(params));
          // }
          if (promiseFuncs.length > 0) {
            this.saveLoading = true;
            Promise.all(promiseFuncs)
              .then(
                () => {
                  this.$message.success('保存成功');
                  //刷新或者重新登录-逻辑待后端同步
                },
                (err) => {
                  this.$message.error(err.msg || '保存失败');
                }
              )
              .finally(() => {
                this.saveLoading = false;
              });
          } else {
            this.$message.warning('没有需要保存的信息');
          }
        }
      });
    }
  }
};
</script>
<style lang="less">
.intensity-style(@color) {
  background: @color;
  width: 40px;
  height: 3px;
}
.user-profile {
  padding: 20px;
  .el-form {
    width: 700px;
    margin: auto;
  }
  &__footer {
    display: flex;
    justify-content: center;
  }
  .pwd-intensity {
    display: flex;
    align-items: center;
    position: absolute;
    margin-top: -12px;
    justify-content: flex-end;
    width: 100%;
    &--default {
      .intensity-style(@border-base-color);
    }
    &--low {
      .intensity-style(@red-color);
    }
    &--mid {
      .intensity-style(@orange-color);
    }
    &--high {
      .intensity-style( @green-color);
    }
    &__text {
      font-size: 12px;
      color: @text-regular-color;
      margin-left: 5px;
    }
  }
}
</style>
