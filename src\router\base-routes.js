import * as pageUrlConstants from '@/constant/page-url-constants';
import * as menuPageConstants from '@/constant/menu-page-constants';
export default [
  {
    name: menuPageConstants.MENU_ID_ACCOUNT_MANAGEMENT,
    title: '账号管理',
    path: pageUrlConstants.PAGE_URL_ACCOUNT_MANAGEMENT,
    code: 'account',
    component: () =>
      import(/* webpackChunkName:"account" */ '@/pages/management/account.vue'),
    children: []
  },
  {
    name: menuPageConstants.MENU_ID_ROLE_MANAGEMENT,
    title: '角色管理',
    path: pageUrlConstants.PAGE_URL_ROLE_MANAGEMENT,
    code: 'role',
    component: () =>
      import(/* webpackChunkName:"role" */ '@/pages/management/role.vue'),
    meta: {
      title: '角色管理'
    }
  },
  {
    name: menuPageConstants.MENU_ID_DATA_MANAGEMENT,
    title: '数据管理',
    path: pageUrlConstants.PAGE_URL_DATA_MANAGEMENT,
    code: 'dataManage',
    component: () =>
      import(
        /* webpackChunkName:"dataManage" */ '@/pages/management/data-manage.vue'
      ),
    meta: {
      title: '数据管理'
    }
  },
  {
    name: menuPageConstants.MENU_ID_MENU_MANAGE,
    title: '菜单管理',
    path: pageUrlConstants.PAGE_URL_MENU_MANAGEMENT,
    code: 'menuMange',
    component: () =>
      import(/* webpackChunkName:"menu" */ '@/pages/management/menu.vue'),
    meta: {
      title: '菜单管理'
    }
  },
  {
    name: menuPageConstants.MENU_ID_SAFE_CONFIG,
    title: '安全配置',
    path: pageUrlConstants.PAGE_URL_SAFE_CONFIG,
    code: 'security',
    component: () =>
      import(
        /* webpackChunkName:"safeConfig" */ '@/pages/management/safe-config.vue'
      ),
    meta: {
      title: '安全配置'
    }
  },
  {
    name: menuPageConstants.MENU_ID_AUDIT_RECORD,
    title: '审计日志',
    path: pageUrlConstants.PAGE_URL_AUDIT_RECORD,
    code: 'audit',
    component: () =>
      import(/* webpackChunkName:"aduit" */ '@/pages/audit-record/index.vue'),
    children: [],
    meta: {
      title: '审计日志'
    }
  },
  {
    name: menuPageConstants.MENU_ID_SYSTEM_CONFIG,
    title: '配置',
    path: pageUrlConstants.PAGE_URL_SYSTEM_CONFIG,
    code: 'config',
    component: () =>
      import(/* webpackChunkName:"config" */ '@/pages/system-config/index.vue'),
    children: [],
    meta: {
      title: '配置'
    }
  },
  {
    name: menuPageConstants.PAGE_URL_AMIS,
    path: pageUrlConstants.PAGE_URL_AMIS,
    code: 'amis',
    component: () =>
      import(/* webpackChunkName:"amis" */ '@/pages/amis/amis-index.vue'),
    meta: {
      title: 'AMIS'
    }
  },
  {
    name: menuPageConstants.PAGE_URL_AMIS_EDIT,
    path: pageUrlConstants.PAGE_URL_AMIS_EDIT,
    code: 'amisEdit',
    component: () =>
      import(/* webpackChunkName:"amis" */ '@/pages/amis/edit.vue'),
    meta: {
      title: 'AMIS编辑'
    }
  },
  {
    name: menuPageConstants.PAGE_URL_AMIS_PRE,
    path: pageUrlConstants.PAGE_URL_AMIS_PRE,
    code: 'amisPre',
    component: () =>
      import(/* webpackChunkName:"amis" */ '@/pages/amis/preview.vue'),
    meta: {
      title: ''
    }
  },
  {
    name: menuPageConstants.PAGE_URL_USER_PROFILE,
    path: pageUrlConstants.PAGE_URL_USER_PROFILE,
    code: 'user',
    component: () =>
      import(/* webpackChunkName:"user" */ '@/pages/user/user-profile.vue'),
    meta: {
      title: '个人信息'
    }
  }
];
