import Vue from 'vue';
import Router from 'vue-router';
import { Message } from 'element-ui';
import {
  PAGE_URL_LOGIN,
  PAGE_URL_OVERVIEW,
  PAGE_URL_SYSTEM_AUTH
} from '@/constant/page-url-constants';
import {
  getLocalUserInfo,
  getPageMenuAndBlockConfigs
} from '@/utils/storage-utils';
import baseRoutes from './base-routes';
import systemRoutes from './system-config';
export const staticRoutesList = [
  {
    path: PAGE_URL_OVERVIEW,
    name: PAGE_URL_OVERVIEW,
    component: () => import('@/pages/layout.vue')
  },
  ...baseRoutes,
  ...systemRoutes
];
const router = new Router({
  mode: 'history',
  routes: [
    {
      path: '/login',
      name: 'login',
      component: () =>
        import(/* webpackChunkName:"login" */ '@/pages/login/index.vue')
    },
    {
      path: PAGE_URL_OVERVIEW,
      name: PAGE_URL_OVERVIEW,
      component: () => import('@/pages/layout.vue'),
      // redirect:PAGE_URL_OVERVIEW,
      children: [
        // {
        //   name:PAGE_URL_APP_MANAGE,
        //   path:PAGE_URL_APP_MANAGE,
        //   component:()=>import(/* webpackChunkName:"homeLayout" */ "@/pages/applicationManage/index.vue"),
        //   meta:{
        //     title:''
        //   }
        // },
        // {
        //   name:PAGE_URL_CONFIG_MANAGE,
        //   path:PAGE_URL_CONFIG_MANAGE,
        //   component:()=>import(/* webpackChunkName:"homeLayout" */ "@/pages/configMange/index.vue"),
        //   meta:{
        //     title:''
        //   }
        // },
        ...baseRoutes,
        ...systemRoutes
      ]
    }
  ]
});

Vue.use(Router);
// 解决路由守卫中使用next('xx')、next({path: 'xx'})时的报错
const originalPush = Router.prototype.push;
Router.prototype.push = function (location, onComplete, onAbort) {
  if (onComplete || onAbort) {
    return originalPush.call(this, location, onComplete, onAbort);
  } else {
    return originalPush.call(this, location).catch((err) => err);
  }
};

// 添加全局前置守卫
router.beforeEach((to, from, next) => {
  const isLogin = Object.keys(getLocalUserInfo()).length ? true : false;

  const state = getPageMenuAndBlockConfigs().state;
  if ((!isLogin && to.name !== 'login') || (to.path == '/' && !isLogin)) {
    next({
      path: PAGE_URL_LOGIN
    });
  } else if (to.path === PAGE_URL_SYSTEM_AUTH || to.path === PAGE_URL_LOGIN) {
    next();
  } else if (isLogin && to.path === '/') {
    if (state === 1) {
      next({ path: PAGE_URL_OVERVIEW });
    } else {
      Message.warning('系统未授权，请先授权使用！');
      next({
        path: PAGE_URL_SYSTEM_AUTH
      });
    }
  } else {
    console.log(state);
    if (state === 1) {
      next();
    } else {
      Message.warning('系统未授权，请先授权使用！');
      next({
        path: PAGE_URL_SYSTEM_AUTH
      });
    }
  }
});
router.afterEach((to) => {
  // 修改当前标签页的名称
  const title = to.meta.title;
  const titleNode = document.querySelector('head > title');
  if (title && titleNode) {
    titleNode.textContent = title;
  }
  // 跳转到另一个页面时，回到顶部
  const ele = document.querySelector('#app > div');
  ele && ele.scrollTo(0, 0);
});

export default router;
