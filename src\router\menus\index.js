/**
 * 菜单配置
 * @type {Array}
 *   id: 菜单ID
 *   name: 菜单名称
 *   icon: 菜单图标
 *   meta: 菜单元数据
 *     openType: 菜单打开方式: 默认为'_self'，'_blank'为新窗口打开
 *   entry: 菜单对应的页面ID
 *   pages: 菜单对应的页面列表
 *   configable: 是否可以出现在角色配置中，只有配置为false的菜单不会出现在角色配置中，配置为true或者没这个字段的菜单会出现在角色配置中
 *   children: 子菜单
 */
import * as menuPageConstants from '@/constant/menu-page-constants';
const menuList = [
  {
    id: menuPageConstants.MENU_ID_ACCOUNT_MANAGEMENT,
    name: '账号管理',
    entry: menuPageConstants.PAGE_ID_ACCOUNT_MANAGEMENT,
    configable: false
  },
  {
    id: menuPageConstants.MENU_ID_ROLE_MANAGEMENT,
    name: '角色管理',
    entry: menuPageConstants.PAGE_ID_ROLE_MANAGEMENT,
    configable: false
  },
  {
    id: menuPageConstants.MENU_ID_DATA_MANAGEMENT,
    name: '数据管理',
    entry: menuPageConstants.PAGE_ID_DATA_MANAGEMENT,
    configable: false
  },
  {
    id: menuPageConstants.MENU_ID_SAFE_CONFIG,
    name: '安全配置',
    entry: menuPageConstants.PAGE_ID_SAFE_CONFIG,
    configable: false
  },
  {
    id: menuPageConstants.MENU_ID_AUDIT_RECORD,
    name: '操作日志',
    entry: menuPageConstants.PAGE_ID_AUDIT_RECORD,
    //   pages: [menuPageConstants.PAGE_ID_AUDIT_RECORD_ANALYSIS],
    configable: false
  },
  {
    id: menuPageConstants.MENU_ID_SETTING,
    name: '配置',
    children: [
      {
        id: menuPageConstants.MENU_ID_SETTING_SYSTEM_MANAGEMENT,
        name: '系统管理',
        children: [
          {
            id: menuPageConstants.MENU_ID_NOTICE_RECORD,
            name: '通知管理',
            entry: menuPageConstants.PAGE_ID_NOTICE_RECORD
          },
          {
            id: menuPageConstants.MENU_ID_SYSTEM_AUTHORIZATION,
            name: '系统授权',
            entry: menuPageConstants.PAGE_ID_SYSTEM_AUTHORIZATION
          },
          {
            id: menuPageConstants.MENU_ID_SETTINGS_UPDATE,
            name: '系统升级',
            entry: menuPageConstants.PAGE_ID_SETTINGS_UPDATE
          },
          {
            id: menuPageConstants.MENU_ID_SYSTEM_MONITOR,
            name: '系统监控',
            entry: menuPageConstants.PAGE_ID_SYSTEM_MONITOR
          },
          {
            id: menuPageConstants.MENU_ID_SYSTEM_CONFIG,
            name: '系统配置',
            entry: menuPageConstants.PAGE_ID_SYSTEM_CONFIG
          }
        ]
      }
    ]
  }
];

export function getConfigableMenuList(menuData) {
  if (!menuData) menuData = menuList;

  return menuData
    .map((item) => {
      const configable = item.configable;
      if (configable === false) return null;
      if (typeof configable === 'function' && !configable()) return null;

      const filteredItem = { ...item };
      if (item.children) {
        filteredItem.children = getConfigableMenuList(item.children);
      }
      return filteredItem;
    })
    .filter((item) => !!item);
}

export default menuList;
