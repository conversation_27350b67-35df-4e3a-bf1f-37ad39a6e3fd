import * as pageUrlConstants from '@/constant/page-url-constants';
import * as menuPageConstants from '@/constant/menu-page-constants';
export default [
  {
    name: menuPageConstants.MENU_ID_SYSTEM_UPDATE,
    title: '系统升级',
    path: pageUrlConstants.PAGE_URL_SYSTEM_UPDATE,
    code: 'config_upgrade',
    component: () => import('@/pages/system-config/system-update.vue'),
    children: []
  },
  {
    name: menuPageConstants.PAGE_URL_SYSTEM_AUTH,
    title: '系统授权',
    path: pageUrlConstants.PAGE_URL_SYSTEM_AUTH,
    code: 'config_auth',
    component: () => import('@/pages/system-config/system-auth.vue'),
    children: []
  },
  {
    name: menuPageConstants.PAGE_URL_SYSTEM_DATA_SEND,
    title: '数据推送',
    path: pageUrlConstants.PAGE_URL_SYSTEM_DATA_SEND,
    code: 'config_push',
    component: () => import('@/pages/system-config/data-send.vue'),
    children: []
  },
  // {
  //     name:menuPageConstants.PAGE_URL_SYSTEM_NOTICE,
  //     title:'通知管理',
  //     path: pageUrlConstants.PAGE_URL_SYSTEM_NOTICE,
  //     component: () => import('@/pages/system-config/notice-manage.vue'),
  //     children:[

  //     ]
  // },
  {
    name: menuPageConstants.PAGE_URL_SYSTEM_BASE_CONFIG,
    title: '基本配置',
    path: pageUrlConstants.PAGE_URL_SYSTEM_BASE_CONFIG,
    code: 'config_basic',
    component: () => import('@/pages/system-config/base-config.vue'),
    children: []
  },
  {
    name: menuPageConstants.PAGE_URL_SYSTEM_OPEN_API,
    title: '开放接口管理',
    path: pageUrlConstants.PAGE_URL_SYSTEM_OPEN_API,
    code: 'config_api',
    component: () => import('@/pages/system-config/open-api.vue'),
    children: []
  },
  {
    name: menuPageConstants.PAGE_URL_PAGE_DEV,
    title: '页面开发',
    path: pageUrlConstants.PAGE_URL_SYSTEM_PAGE_DEV,
    code: 'page_dev',
    component: () => import('@/pages/amis/amis-index.vue'),
    children: []
  }
];
