import { doGet, doPost } from '@quanzhiFE/qz-frontend';
import { DATA_URL_ACCOUNT } from '@/constant/data-url-constants';
/**
 * 账号列表
 * @param {*} params
 * @returns
 */
export const postAccountList = (params) => {
  return doPost(
    {
      url: `${DATA_URL_ACCOUNT}/list`,
      params
    },
    true
  );
};
/**
 * 保存账号
 * @param {*} params
 * @returns
 */
export const postSaveAccount = (params) => {
  return doPost(
    {
      url: `${DATA_URL_ACCOUNT}/save`,
      params
    },
    true
  );
};
/**
 * 账号删除
 * @param {*} params
 * @returns
 */
export const postDeleteAccountList = (params) => {
  return doPost(
    {
      url: `${DATA_URL_ACCOUNT}/delete`,
      params
    },
    true
  );
};
/**
 * 账号详情
 * @param {*} params
 * @returns
 */
export const getAccountDetail = (params) => {
  return doGet({
    url: `${DATA_URL_ACCOUNT}/detail`,
    params
  });
};
/**
 * 更新Google认证秘钥
 * @param {*} params
 * @returns
 */
export const postDeleteAccountGoogleAuth = (params) => {
  return doPost(
    {
      url: `${DATA_URL_ACCOUNT}/updateGoogleSecret`,
      params
    },
    true
  );
};
/**
 * 修改账号密码
 * @param {*} params
 * @returns
 */
export const postAccountPassWord = (params) => {
  return doPost(
    {
      url: `${DATA_URL_ACCOUNT}/modifyPassword`,
      params
    },
    true
  );
};
