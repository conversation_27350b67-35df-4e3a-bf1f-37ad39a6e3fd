//审计日志
import { doGet, doPost } from '@quanzhiFE/qz-frontend';

import {
  DATA_URL_AUDIT_LOG,
  DATA_URL_SYSLOG_MODULE
} from '@/constant/data-url-constants';

/**
 * 审计日志列表
 * @param {*} params
 * @returns
 */
export const postAuditLogList = (params) => {
  return doPost(
    {
      url: `${DATA_URL_AUDIT_LOG}/page`,
      params
    },
    true
  );
};
export const getAuditRecordCriteria = () => {
  return doGet(
    {
      url: `${DATA_URL_SYSLOG_MODULE}`
    },
    true
  );
};
/**
 * 审计日志列表
 * @param {*} params
 * @returns
 */
export const postAuditLogExport = (params) => {
  return doPost(
    {
      url: `${DATA_URL_AUDIT_LOG}/export`,
      params
    },
    true
  );
};
