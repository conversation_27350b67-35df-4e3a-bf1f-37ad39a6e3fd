import {
  DATA_URL_CLASS_ADD,
  DATA_URL_CLASS_DETAIL,
  DATA_URL_TASK_LIST,
  DATA_URL_AI_LIST,
  DATA_URL_LABEL_LEVEL,
  DATA_URL_LAEL_CLASS,
  DATA_UTL_CLASS_ADD,
  DATA_URL_RULE,
  DATA_URL_LABEL_DETAIL,
  DATA_URL_FILED_LIST,
  DATA_URL_FILED_TEST,
  DATA_URL_MODEL_LIST
} from '@/constant/data-url-constants';
import { doGet, doPost } from '@quanzhiFE/qz-frontend';
export const postAddClassData = (params) => {
  return doPost(
    {
      url: DATA_URL_CLASS_ADD,
      params
    },
    true
  );
};
export const postClassDetail = (params) => {
  return doPost(
    {
      url: DATA_URL_CLASS_DETAIL,
      params
    },
    true
  );
};

export const postTaskList = (params) => {
  return doPost(
    {
      url: DATA_URL_TASK_LIST,
      params
    },
    true
  );
};

export const postAiList = (params) => {
  return doPost(
    {
      url: DATA_URL_AI_LIST,
      params
    },
    true
  );
};
export const postFiledClass = (params) => {
  return doPost(
    {
      url: DATA_URL_LAEL_CLASS,
      params
    },
    true
  );
};

export const postFiledLevel = (params) => {
  return doPost(
    {
      url: DATA_URL_LABEL_LEVEL,
      params
    },
    true
  );
};

export const postFiledLabelAdd = (params) => {
  return doPost(
    {
      url: DATA_UTL_CLASS_ADD,
      params
    },
    true
  );
};

export const postRules = () => {
  return doGet({
    url: DATA_URL_RULE
  });
};

export const postLabelDetail = (params) => {
  return doPost(
    {
      url: DATA_URL_LABEL_DETAIL,
      params
    },
    true
  );
};

export const postFiledList = (params) => {
  return doPost(
    {
      url: DATA_URL_FILED_LIST,
      params
    },
    true
  );
};

export const postFiledTest = (params) => {
  return doPost(
    {
      url: DATA_URL_FILED_TEST,
      params
    },
    true
  );
};

export const postModelList = (id) => {
  const hasuraQuery = {
    isPageQuery: false,
    searchConditionList: [],
    sortList: [],
    columnList: []
  };

  // 精确搜索字段
  if (id) {
    hasuraQuery.searchConditionList.push({
      fieldName: 'template_id',
      columnExp: '=',
      value: `${id}`
    });
  }

  const columns = ['id', 'name'];
  hasuraQuery.columnList = columns;

  return doPost(
    {
      url: DATA_URL_MODEL_LIST,
      params: hasuraQuery
    },
    true
  );
};
