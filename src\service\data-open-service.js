import { doPost } from '@quanzhiFE/qz-frontend';
import { DATA_URL_OPEN_API } from '@/constant/data-url-constants';
export const getListKey = (params) => {
  return doPost(
    {
      url: `${DATA_URL_OPEN_API}/list`,
      params
    },
    true
  );
};
export const postOpenApiList = (params) => {
  return doPost(
    {
      url: `${DATA_URL_OPEN_API}/list`,
      params
    },
    true
  );
};
export const getNewKey = () => {
  return doPost(
    {
      url: `${DATA_URL_OPEN_API}/newKey`
    },
    true
  );
};
export const getSensitiveKey = (params) => {
  return doPost(
    {
      url: `${DATA_URL_OPEN_API}/detail`,
      params
    },
    true
  );
};
export const saveKey = (params) => {
  return doPost(
    {
      url: `${DATA_URL_OPEN_API}/add`,
      params
    },
    true
  );
};
export const deleteKey = (params) => {
  return doPost(
    {
      url: `${DATA_URL_OPEN_API}/delete`,
      params
    },
    true
  );
};
export const cancelKey = (params) => {
  return doPost(
    {
      url: `${DATA_URL_OPEN_API}/desensitizeKey`,
      params
    },
    true
  );
};
export const editKey = () => {};
export const getDocList = () => {};
