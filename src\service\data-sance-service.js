import { doGet, doPost } from '@quanzhiFE/qz-frontend';
import {
  DATA_URL_SOURCE_LIST,
  DATA_URL_SOURCE_TREE,
  DATA_URL_TEST,
  DATA_URL_SANCE_ADD,
  DATA_URL_SORCE_DETAIL,
  DATA_URL_DATABASE_LIST,
  DATA_URL_TREE,
  DATA_URL_SANCE_DETAIL,
  DATA_URL_GET_DATABASE
} from '@/constant/data-url-constants';
export const postSourceList = (params) => {
  return doPost(
    {
      url: DATA_URL_SOURCE_LIST,
      params
    },
    true
  );
};
export const postSourceTree = (params) => {
  return doPost(
    {
      url: DATA_URL_SOURCE_TREE,
      params
    },
    true
  );
};
export const postSourceTest = (params) => {
  return doPost(
    {
      url: DATA_URL_TEST,
      params
    },
    true
  );
};

export const postSourceSan = (params) => {
  return doPost(
    {
      url: DATA_URL_SANCE_ADD,
      params
    },
    true
  );
};

export const postServerDetail = (params) => {
  return doPost(
    {
      url: DATA_URL_SORCE_DETAIL,
      params
    },
    true
  );
};

export const postDataBaseList = (params) => {
  return doPost(
    {
      url: DATA_URL_DATABASE_LIST,
      params
    },
    true
  );
};

export const postTreeList = (params) => {
  return doPost(
    {
      url: DATA_URL_TREE,
      params
    },
    true
  );
};

export const postTaskDetail = (params) => {
  return doPost(
    {
      url: DATA_URL_SANCE_DETAIL,
      params
    },
    true
  );
};

export const postGetDatabase = (params) => {
  return doGet({
    url: DATA_URL_GET_DATABASE,
    params
  });
};
