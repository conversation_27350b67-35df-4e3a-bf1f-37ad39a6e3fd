import { doPost } from '@quanzhiFE/qz-frontend';

import {
  DATA_URL_DC_DAT_TEST,
  DATA_URL_DC_DAT_SAVE,
  DATA_URL_DC_DAT_TYPE,
  DATA_URL_DC_DRIVER_LIST,
  DATA_URL_DC_UI_CONFIG,
  DATA_URL_JDBCURL_RENDER,
  DATA_URL_JDBCURL_PARSE,
  DATA_URL_DIALECT_LIST,
  DATA_URL_SERVICE_TYPE_SAVE,
  DATA_URL_SERVICE_VERSION_SAVE,
  DATA_URL_DATASOURCE_DETAIL
} from '@/constant/data-url-constants';

/**
 * 获取数据库服务类型
 * @returns
 */
export const getDcDataType = () => {
  const params = {
    isPageQuery: false,
    columnList: ['name', 'id', 'templates']
  };
  return doPost(
    {
      url: `${DATA_URL_DC_DAT_TYPE}`,
      params
    },
    true
  );
};

export const getDcDriverList = (sourceTypeId) => {
  const params = {
    isPageQuery: false,
    columnList: ['name', 'id'],
    searchConditionList: [
      {
        fieldName: 'source_type_id',
        columnExp: '=',
        value: sourceTypeId
      }
    ]
  };
  return doPost(
    {
      url: `${DATA_URL_DC_DRIVER_LIST}`,
      params
    },
    true
  );
};

export const getUiConfig = (sourceTypeId) => {
  const params = {
    isPageQuery: false,
    columnList: ['id', 'ui_config'],
    searchConditionList: [
      {
        fieldName: 'id',
        columnExp: '=',
        value: sourceTypeId
      }
    ]
  };
  return doPost(
    {
      url: `${DATA_URL_DC_UI_CONFIG}`,
      params
    },
    true
  );
};

export function doDatasourceTest(data) {
  return doPost(
    {
      url: DATA_URL_DC_DAT_TEST,
      params: data
    },
    true
  );
}

export function saveDatasource(data) {
  return doPost(
    {
      url: DATA_URL_DC_DAT_SAVE,
      params: data
    },
    true
  );
}

export function getJdbcUrlRender(data) {
  return doPost(
    {
      url: DATA_URL_JDBCURL_RENDER,
      params: data
    },
    true
  );
}

export function getJdbcUrlParse(data) {
  return doPost(
    {
      url: DATA_URL_JDBCURL_PARSE,
      params: data
    },
    true
  );
}

export function getDialectList() {
  return doPost(
    {
      url: DATA_URL_DIALECT_LIST,
      params: {
        isPageQuery: false,
        columnList: ['dialect']
      }
    },
    true
  );
}

export function saveServiceType(data) {
  return doPost(
    {
      url: DATA_URL_SERVICE_TYPE_SAVE,
      params: data
    },
    true
  );
}

export function saveServiceVersion(data) {
  return doPost(
    {
      url: DATA_URL_SERVICE_VERSION_SAVE,
      params: data
    },
    true
  );
}

export function getDatasourceDetail(id) {
  return doPost(
    {
      url: DATA_URL_DATASOURCE_DETAIL,
      params: {
        isPageQuery: false,
        columnList: [
          'id',
          'name',
          'business_system',
          'source_type_id',
          'driver_id',
          'url',
          'config'
        ],
        searchConditionList: [
          {
            fieldName: 'id',
            columnExp: '=',
            value: id
          }
        ]
      }
    },
    true
  );
}
