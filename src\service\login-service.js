import { doGet, doPost } from '@quanzhiFE/qz-frontend';
import {
  DATA_URL_ACCOUNT,
  DATA_URL_DETAIL,
  DATA_URL_DETAIL_SYSCONFIG,
  DATA_URL_DETAIL_SYSCONFIGS,
  DATA_URL_LOGIN,
  DATA_URL_LOGIN_OUT,
  DATA_URL_SAVE_SYSCONFIG
} from '@/constant/data-url-constants';

export const doLogin = (params) => {
  return doPost(
    {
      url: DATA_URL_LOGIN,
      params
    },
    true
  );
};

export const doLoginOut = (params) => {
  return doPost(
    {
      url: DATA_URL_LOGIN_OUT,
      params
    },
    true
  );
};

export const checkIsLogin = (params) => {
  return doPost(
    {
      url: '',
      params
    },
    true
  );
};

/**
 * 登录安全信息查看
 */
export const getLoginSecurityInfo = () => {
  return doGet(
    {
      url: DATA_URL_DETAIL_SYSCONFIG
    },
    true
  );
};

/**
 * 批量获取页面配置
 */
export const getLoginSecurityInfos = (params) => {
  return doPost(
    {
      url: DATA_URL_DETAIL_SYSCONFIGS,
      params
    },
    true
  );
};

export const getLoginSecurityInfoById = (url) => {
  return doGet(
    {
      url: DATA_URL_DETAIL + url
    },
    true
  );
};
/**
 * 保存安全信息
 */
export const saveLoginSecurityInfo = (params) => {
  return doPost(
    {
      url: DATA_URL_SAVE_SYSCONFIG,
      params
    },
    true
  );
};

export const modifyPassword = (params) => {
  return doPost(
    {
      url: `${DATA_URL_ACCOUNT}/modifyPassword`,
      params
    },
    true
  );
};
