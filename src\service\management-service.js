import { doGet, doPost } from '@quanzhiFE/qz-frontend';
import { DATA_URL_ACCOUNT } from '@/constant/data-url-constants';

export const getUserDetailByName = (params) => {
  return doGet(
    {
      url: `${DATA_URL_ACCOUNT}/detail`,
      params
    },
    true
  );
};

export const getUserDetailByToken = () => {
  return doGet(
    {
      url: `${DATA_URL_ACCOUNT}/current`
    },
    true
  );
};

export const editUser = (params) => {
  return doPost({
    url: ``,
    params
  });
};
