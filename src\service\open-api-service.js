import { doGet, doPost } from '@quanzhiFE/qz-frontend';
import { DATA_URL_OPEN_API } from '@/constant/data-url-constants';
import { DATA_URL_OPEN_API_CONFIG } from '@/constant/data-url-constants';
/**
 * 新增接口
 * @param {*} params
 * @returns
 */
export const postOpenApiAdd = (params) => {
  return doPost(
    {
      url: `${DATA_URL_OPEN_API}/add`,
      params
    },
    true
  );
};
export const postOpenApiList = (params) => {
  return doPost(
    {
      url: `${DATA_URL_OPEN_API}/list`,
      params
    },
    true
  );
};
export const postOpenApiNewKey = (params) => {
  return doPost(
    {
      url: `${DATA_URL_OPEN_API}/newKey`,
      params
    },
    true
  );
};
