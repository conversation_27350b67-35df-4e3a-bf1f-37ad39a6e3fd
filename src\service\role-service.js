import { doGet, doPost } from '@quanzhiFE/qz-frontend';
import {
  DATA_URL_ROLE_LIST,
  DATA_URL_MENU,
  DATA_URL_AMIS_CONFIG,
  DATA_URL_ROLE_SAVE
} from '@/constant/data-url-constants';
export const postRoleList = (params) => {
  return doPost(
    {
      url: DATA_URL_ROLE_LIST,
      params
    },
    true
  );
};

// 获取菜单
export const getMenu = () => {
  return doGet({
    url: DATA_URL_MENU
  });
};

// 获取amis配置
export const getAmisConfig = (params) => {
  return doGet({
    url: DATA_URL_AMIS_CONFIG,
    params
  });
};

export const saveRole = (params) => {
  return doPost(
    {
      url: DATA_URL_ROLE_SAVE,
      params
    },
    true
  );
};
