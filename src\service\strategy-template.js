import { DATA_URL_STRATEGY_TEMPLATE } from '@/constant/data-url-constants';
import { doPost, doGet } from '@quanzhiFE/qz-frontend';
export const getStrategyList = (params) => {
  return doPost(
    {
      url: `${DATA_URL_STRATEGY_TEMPLATE}/list`,
      params
    },
    true
  );
};
export const getStrategyDetail = (params) => {
  return doGet(
    {
      url: `${DATA_URL_STRATEGY_TEMPLATE}/detail`,
      params
    },
    true
  );
};

export const saveStrategy = (params) => {
  return doPost(
    {
      url: `${DATA_URL_STRATEGY_TEMPLATE}/save`,
      params
    },
    true
  );
};

export const deleteStrategy = (params) => {
  return doGet(
    {
      url: `${DATA_URL_STRATEGY_TEMPLATE}/delete`,
      params
    },
    true
  );
};
