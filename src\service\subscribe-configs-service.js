import { doGet, doPost } from '@quanzhiFE/qz-frontend';
import {
  DATA_URL_DATA_SEND_CONFIG,
  DATA_URL_DATA_PUSH_TASK_ADD,
  DATA_URL_DATA_PUSH_TASK_DELETE,
  DATA_URL_DATA_PUSH_TASK_UPDATE,
  DATA_URL_DATA_PUSH_TASK_DETAIL,
  DATA_URL_SUBSCRIBE_CUSTOM_FIELDS,
  DATA_URL_SUBSCRIBE_LOG_LIST
} from '@/constant/data-url-constants';

export const getSubscribeConfigList = (params) => {
  return doPost(
    {
      url: `${DATA_URL_DATA_SEND_CONFIG}/list`,

      params
    },
    true
  );
};

export const getSubscribeConfigSave = (params) => {
  return doPost(
    {
      url: `${DATA_URL_DATA_SEND_CONFIG}/create`,
      params
    },
    true
  );
};
export const getSubscribeConfigUpdate = (params) => {
  return doPost(
    {
      url: `${DATA_URL_DATA_SEND_CONFIG}/update`,
      params
    },
    true
  );
};
export const deleteSubscribeTask = (id) => {
  return doPost(
    {
      url: DATA_URL_DATA_PUSH_TASK_DELETE,
      params: {
        id
      }
    },
    true
  );
};

export const deleteSubscribeConfig = (params) => {
  return doGet(
    {
      url: `${DATA_URL_DATA_SEND_CONFIG}/delete`,
      params
    },
    true
  );
};
export const getSubscribeConfigDetail = (id) => {
  return doPost(
    {
      url: DATA_URL_DATA_PUSH_TASK_DETAIL,
      params: {
        id
      }
    },
    true
  );
};

export const changeSubscribeConfigStatus = () => {};
export const getSubscribeNetworkSegmentsForTree = () => {};

export const editSubscribeConfig = () => {};

export const editSubscribeConfigFully = () => {};

export const testSubscribeSyncSetting = (params) => {
  return doPost(
    {
      url: `${DATA_URL_DATA_SEND_CONFIG}/testConnection`,
      params
    },
    true
  );
};

//获取树形结构数据

export const addSubscribeConfig = (params) => {
  return doPost(
    {
      url: DATA_URL_DATA_PUSH_TASK_ADD,
      params
    },
    true
  );
};
export const updateSubscribeConfig = (params) => {
  return doPost(
    {
      url: DATA_URL_DATA_PUSH_TASK_UPDATE,
      params
    },
    true
  );
};

export function getSubscribeCustomFields() {
  return doGet({
    url: DATA_URL_SUBSCRIBE_CUSTOM_FIELDS
  });
}

export const getSubscribeLogList = (params) => {
  return doPost(
    {
      url: DATA_URL_SUBSCRIBE_LOG_LIST,
      params
    },
    true
  );
};
