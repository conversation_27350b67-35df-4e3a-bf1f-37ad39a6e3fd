import { doGet, doPost } from '@quanzhiFE/qz-frontend';
import { DATA_URL_SYS_AUTH } from '@/constant/data-url-constants';

export const getMachineCode = () => {
  return doGet(
    {
      url: `${DATA_URL_SYS_AUTH}/machineCode`
    },
    true
  );
};

export const getLicense = () => {
  return doPost(
    {
      url: `${DATA_URL_SYS_AUTH}/licenseDetail`
    },
    true
  );
};
export const updateLicense = (params) => {
  return doPost(
    {
      url: `${DATA_URL_SYS_AUTH}/uploadLicense`,
      params
    },
    true
  );
};

export const getLicenseDelete = () => {
  return doPost(
    {
      url: `${DATA_URL_SYS_AUTH}/deleteLicense`
    },
    true
  );
};
