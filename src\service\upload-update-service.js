import { doGet, doPost } from '@quanzhiFE/qz-frontend';
import {
  DATA_URL_FILE_TASK_ADD,
  DATA_URL_FILE_STATUS,
  DATA_URL_FILE_SERVICE_TEMPLATE,
  DATA_URL_ASSET_TEMPLATE,
  DATA_URL_LABEL_IMPORT,
  DATA_URL_ASSET_REMARK_TEMPLATE,
  DATA_URL_UPLOAD_UPDATE,
  DATA_URL_UPDATE_LOG
} from '@/constant/data-url-constants';

export const uploadFileTask = (params) => {
  return doPost(
    {
      url: DATA_URL_FILE_TASK_ADD,
      params
    },
    true
  );
};

export const postTaskStatus = (params) => {
  return doPost(
    {
      url: DATA_URL_FILE_STATUS,
      params
    },
    true
  );
};

export const updateProgress = (params) => {
  return doPost(
    {
      url: DATA_URL_UPLOAD_UPDATE,
      params
    },
    true
  );
};

export const getUpdateLog = (params) => {
  return doPost(
    {
      url: DATA_URL_UPDATE_LOG,
      params
    },
    true
  );
};

export const getUpdateLogList = (params) => {
  return doPost(
    {
      url: DATA_URL_UPDATE_LOG,
      params
    },
    true
  );
};

export const getDecryptStatus = (params) => {
  return doPost(
    {
      url: '',
      params
    },
    true
  );
};
//下载服务
export const getServiceTemplate = () => {
  return doGet({
    url: DATA_URL_FILE_SERVICE_TEMPLATE,
    responseType: 'blob'
  });
};
//下载资产模版
export const getAssetTemplate = () => {
  return doGet({
    url: DATA_URL_ASSET_TEMPLATE,
    responseType: 'blob'
  });
};
//导入标签模版
export const getLabelTemplate = () => {
  return doGet({
    url: DATA_URL_LABEL_IMPORT,
    responseType: 'blob'
  });
};
//下载资产备注&分类模版
export const postAssetRemarkTemplate = (params) => {
  return doPost(
    {
      url: DATA_URL_ASSET_REMARK_TEMPLATE,
      responseType: 'blob',
      params
    },
    true
  );
};
