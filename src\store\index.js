import Vue from 'vue';
import Vuex from 'vuex';
import { getLocalUserInfo } from '@/utils/storage-utils';

Vue.use(Vuex);

const defaultIcon = require('@/assets/imgs/qz_icon_blue.png');
const defaultLogo = require('@/assets/imgs/qz_logo_w.png');
const defaultBack = require('@/assets/imgs/qz_bg.png');
const defaultUserInfo = getLocalUserInfo();

function updateIcon(iconImg) {
  const headNode = document.querySelector('head');
  let iconNode = headNode.querySelector('link[rel="icon"]');
  if (iconNode) {
    iconNode.parentElement.removeChild(iconNode);
  }
  iconNode = document.createElement('link');
  iconNode.setAttribute('rel', 'icon');
  iconNode.setAttribute('href', iconImg);

  headNode.appendChild(iconNode);
}

export default new Vuex.Store({
  state: {
    logoImg: defaultLogo,
    iconImg: defaultIcon,
    backImg: defaultBack,
    productName: '数据分类分级系统',
    userInfo: defaultUserInfo,
    eoLinkPageMenuConfigs: {
      state: 0
    },
    company: '',
    originalStyle: '',
    isOpenWatermark: false,
    fileInfo: null //用于amis中文件导出操作
  },
  mutations: {
    setWatermark(state, isOpenWatermark) {
      state.isOpenWatermark = isOpenWatermark;
    },
    setFileInfo(state, info) {
      state.fileInfo = info;
    },
    setUserInfo(state, userInfo) {
      state.userInfo = userInfo;
    },

    setLogoImg(state, base64) {
      if (base64) {
        const headerStr = 'data:image/png;base64,';
        if (base64.indexOf(headerStr) === 0 || base64.indexOf('/img') === 0) {
          state.logoImg = base64;
        } else {
          state.logoImg = headerStr + base64;
        }
      }
    },
    setIconImg(state, base64) {
      const formatIconImg = (img) => {
        return new Promise((resolve) => {
          const reg = /^.+(\.png)$/;
          if (reg.test(img)) {
            const image = new Image();
            image.setAttribute('crossOrigin', 'anonymous');
            const imageUrl = img;
            let dataURL = '';
            image.src = imageUrl;
            image.onload = () => {
              const canvas = document.createElement('canvas');
              canvas.width = image.width;
              canvas.height = image.height;
              const context = canvas.getContext('2d');
              context.drawImage(image, 0, 0, image.width, image.height);
              const quality = 0.8;
              dataURL = canvas.toDataURL('image/png', quality);
              resolve(dataURL.split('base64,')[1]);
            };
          } else {
            resolve(img);
          }
        });
      };
      if (base64) {
        formatIconImg(base64).then((res) => {
          const headerStr = 'data:image/png;base64,';
          if (base64.indexOf(headerStr) === 0) {
            state.iconImg = res;
          } else {
            state.iconImg = headerStr + res;
          }
          updateIcon(state.iconImg);
        });
      }
    },
    setBackImg(state, base64) {
      if (base64) {
        const headerStr = 'data:image/png;base64,';
        if (base64.indexOf(headerStr) === 0 || base64.indexOf('/img') === 0) {
          state.backImg = base64;
        } else {
          state.backImg = headerStr + base64;
        }
      }
    },
    setProductName(state, productName) {
      if (productName) {
        state.productName = productName;
      }
    },
    setCopyright(state, copyright) {
      console.log('setCopyright', copyright);
      if (copyright) {
        state.copyright = copyright;
      }
    },
    setCompany(state, company) {
      if (company) {
        state.company = company;
      }
    },
    setIsStandLong(state, isStandLong) {
      if (isStandLong !== undefined && isStandLong !== null) {
        state.isStandLong = isStandLong;
      }
    }
  },
  actions: {}
});
