/*
 * @Fileoverview: 密码加解密
 * @Description: 使用于密码的加解密
 */
'use strict';

import cryptoJs from 'crypto-js';
const KEY = 'QUANZHIKEJI12345';

class AESUtil {
  /**
   * 加密
   * @param word
   */
  static encrypt(word) {
    var key = cryptoJs.enc.Utf8.parse(KEY);
    var srcs = cryptoJs.enc.Utf8.parse(word);
    var encrypted = cryptoJs.AES.encrypt(srcs, key, {
      mode: cryptoJs.mode.ECB,
      padding: cryptoJs.pad.Pkcs7
    });
    return encrypted.toString();
  }

  /**
   * 解密
   * @param word
   */
  static decrypt(word) {
    var key = cryptoJs.enc.Utf8.parse(KEY);
    var decrypt = cryptoJs.AES.decrypt(word, key, {
      mode: cryptoJs.mode.ECB,
      padding: cryptoJs.pad.Pkcs7
    });
    return cryptoJs.enc.Utf8.stringify(decrypt).toString();
  }
}

export { AESUtil };
