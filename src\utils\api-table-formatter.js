/*
 * @Fileoverview: 表格单元格内容格式化方法
 * @Description: 使用于表格后端返回数据处理
 */
import { registerFormatter } from '@/components/api-table/src/config';
import moment from 'moment';

// 格式化数字
registerFormatter('formatNumber', function (_row, _column, value) {
  if (value !== null && value !== undefined && value !== '') {
    return value.toLocaleString();
  } else {
    return 0;
  }
});

export function formatNumber(num) {
  if (num !== null && num !== undefined && num !== '') {
    return num.toLocaleString();
  } else {
    return 0;
  }
}

// 格式化访问次数
registerFormatter('formatVisitCnt', function (_row, _column, value) {
  if (value === null || value === undefined || value === '') {
    return 0;
  }

  const absNum = Math.abs(value);
  const numStr = absNum.toString();
  const length = numStr.length;

  if (length < 4) {
    return value;
  }

  let formattedValue;
  let unit;

  if (length < 5) {
    formattedValue = (value / 1000).toFixed(1);
    unit = '千';
  } else if (length < 9) {
    formattedValue = (value / 10000).toFixed(1);
    unit = '万';
  } else if (length < 13) {
    formattedValue = (value / 100000000).toFixed(1);
    unit = '亿';
  } else {
    formattedValue = (value / 1000000000000).toFixed(1);
    unit = '兆';
  }

  // 特殊值处理
  if (parseFloat(formattedValue) === 10.0) {
    switch (unit) {
      case '千':
        formattedValue = '1.0';
        unit = '万';
        break;
      case '万':
        formattedValue = '1.0';
        unit = '亿';
        break;
      case '亿':
        formattedValue = '1.0';
        unit = '兆';
        break;
      default:
        break;
    }
  }

  return `${formattedValue}${unit}`;
});

export function formatVisitCnt(num) {
  if (num === null || num === undefined || num === '') {
    return 0;
  }

  const absNum = Math.abs(num);
  const numStr = absNum.toString();
  const length = numStr.length;

  if (length < 4) {
    return num;
  }

  let formattedValue;
  let unit;

  if (length < 5) {
    formattedValue = (num / 1000).toFixed(1);
    unit = '千';
  } else if (length < 9) {
    formattedValue = (num / 10000).toFixed(1);
    unit = '万';
  } else if (length < 13) {
    formattedValue = (num / 100000000).toFixed(1);
    unit = '亿';
  } else {
    formattedValue = (num / 1000000000000).toFixed(1);
    unit = '兆';
  }

  // 特殊值处理
  if (parseFloat(formattedValue) === 10.0) {
    switch (unit) {
      case '千':
        formattedValue = '1.0';
        unit = '万';
        break;
      case '万':
        formattedValue = '1.0';
        unit = '亿';
        break;
      case '亿':
        formattedValue = '1.0';
        unit = '兆';
        break;
      default:
        break;
    }
  }

  return `${formattedValue}${unit}`;
}

registerFormatter('formatUri', function (_row, _column, value) {
  if (Array.isArray(value)) {
    const tmp = [];
    value.forEach((item) => {
      tmp.push(item.replace(/^httpap(i|p):/, ''));
    });
    return tmp.join('，');
  } else {
    return value.replace(/^httpap(i|p):/, '');
  }
});

// 格式化数组
registerFormatter('formatArray', function (_row, _column, value) {
  if (Array.isArray(value)) {
    if (value && value.length) {
      return value.join('，');
    } else {
      return '--';
    }
  } else {
    return value || '--';
  }
});

export function formatArray(value) {
  if (Array.isArray(value)) {
    if (value && value.length) {
      return value.join('，');
    } else {
      return '--';
    }
  } else {
    return value || '--';
  }
}

// 时间
registerFormatter('formatTime', function (_row, _column, value) {
  if (value === null || value === undefined) {
    return '--';
  }
  if (value.length || !Number.isNaN(value)) {
    return moment(value).format('YYYY-MM-DD HH:mm:ss');
  } else {
    return '--';
  }
});

registerFormatter('formatDate', function (_row, _column, value) {
  if (value === null || value === undefined) {
    return '--';
  }
  if (value.length || !Number.isNaN(value)) {
    return moment(value).format('YYYY/MM/DD');
  } else {
    return '--';
  }
});

// 格式化文件大小
registerFormatter('formatFileLen', function (_row, _column, value) {
  if (value !== null && value !== undefined && value !== '') {
    if (value) {
      const list = ['B', 'KB', 'MB', 'GB', 'TB'];
      let cnt = 0;
      while (Math.floor(value / 1024)) {
        value = value / 1024;
        cnt++;
      }
      const result = value.toFixed(2) + list[cnt];
      return result;
    } else {
      return '';
    }
  } else {
    return 0;
  }
});

// 处理网段筛选格式化
export function handleDomains(networkList, domain) {
  const res = [];
  if (domain === '互联网' || domain === '局域网') {
    networkList.map((item) => {
      if (item.value == domain) {
        item.children.forEach((child) => res.push([item.value, child.value]));
      }
    });
  } else {
    const arr = domain.split('-');
    res.push([arr[0], domain]);
  }
  return res;
}

// 处理有级联多选列表下的筛选
export function handleCascader(data) {
  if (Array.isArray(data)) {
    const tmp = [];
    if (data && data.length > 0) {
      data.forEach((item) => {
        ['ALL', '全部'].includes(item[0]) ? tmp.push('ALL') : tmp.push(item[1]);
      });
    }
    return tmp;
  } else {
    return data;
  }
}

// 还原级联多选项
export function handleRestoreCascader(data, comparingValues) {
  if (Array.isArray(comparingValues)) {
    const tmp = [];
    data.forEach((item) => {
      item.children.forEach((child) => {
        comparingValues.forEach((val) => {
          if (val == child.value) {
            tmp.push([item.value, val]);
          }
        });
      });
    });
    return tmp;
  } else {
    return [];
  }
}

// 分组筛选转为数组
export function handleGroupArraySearch(params, prop) {
  if (params[prop] && !Array.isArray(params[prop])) {
    return [params[prop]];
  } else {
    return params[prop] || [];
  }
}

const numberMap = {
  50: 50,
  100: 100,
  500: 500,
  1000: '1千',
  5000: '5千',
  10000: '1万',
  50000: '5万',
  100000: '10万',
  500000: '50万',
  1000000: '100万',
  5000000: '500万'
};

// 格式化去重数据量
registerFormatter('formatDistinctCnt', function (_row, _column, value) {
  const ranges = [
    50, 100, 500, 1000, 5000, 10000, 50000, 100000, 500000, 1000000, 5000000
  ]; // 可根据需要扩展或修改区间范围
  if (value !== undefined || value !== null) {
    for (const range of ranges) {
      if (value < range) {
        return `小于${numberMap[range]}`;
      }
    }

    return '小于50';
  } else {
    return '小于50';
  }
});

export function formatDistinctCnt(value) {
  const ranges = [
    50, 100, 500, 1000, 5000, 10000, 50000, 100000, 500000, 1000000, 5000000
  ]; // 可根据需要扩展或修改区间范围
  if (value !== undefined || value !== null) {
    for (const range of ranges) {
      if (value < range) {
        return `小于${numberMap[range]}`;
      }
    }

    return `小于50`;
  } else {
    return '小于50';
  }
}
