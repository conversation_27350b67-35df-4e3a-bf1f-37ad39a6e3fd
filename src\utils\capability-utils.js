import {
  getEnabledModules,
  getPageMenuAndBlockConfigs,
  getProductLevel,
  getEoLinkPageMenuConfigs
} from './storage-utils';

function hasAuthorizedWithPrefix(code) {
  const configs = getEnabledModules() || {};
  const keys = Object.keys(configs);
  if (keys === 0) return false;

  for (const key of keys) {
    if (key.startsWith(code) && !!configs[key]) {
      return true;
    }
  }

  return false;
}

function hasAuthorized(code) {
  const configs = getEnabledModules() || {};
  return !!configs[code];
}

// 3.2版本及之后的页面模块配置
const ALL_MODULE_CONFIGS = {
  // 态势感知: 基础版没有
  situation: { basic: false, advanced: true, flagship: true },

  // 概览-风险统计: 基础版没有
  overview_risk: { basic: false, advanced: true, flagship: true },
  // 概览-风险分布: 基础版没有
  overview_risk_distribution: { basic: false, advanced: true, flagship: true },
  // 概览-数据流动情况：旗舰版才有
  overview_data_flow: { basic: false, advanced: false, flagship: true },

  // API清单-审计状态: 基础版没有，高级版可选，旗舰版有
  api_list_audit_status: {
    basic: false,
    advanced: () => hasAuthorizedWithPrefix('audit_log_'),
    flagship: true
  },
  // API清单-风险数量: 基础版没有
  api_list_risk_count: { basic: false, advanced: true, flagship: true },
  // API清单-风险事件: 基础版没有
  api_list_risk_event: { basic: false, advanced: true, flagship: true },

  // API画像-风险: 基础版没有，高级版可选，旗舰版有
  api_portrait_risk: { basic: false, advanced: true, flagship: true },

  // 应用清单-用户量级: 基础版没有
  app_list_user_level: { basic: false, advanced: true, flagship: true },
  // 应用清单-审计API数量: 基础版没有，高级版可选，旗舰版有
  app_list_audit_api_count: {
    basic: false,
    advanced: () => hasAuthorizedWithPrefix('audit_log_'),
    flagship: true
  },
  // 应用清单-风险数量: 基础版没有
  app_list_risk_count: { basic: false, advanced: true, flagship: true },
  // 应用清单-风险事件: 基础版没有
  app_list_risk_event: { basic: false, advanced: true, flagship: true },

  // 应用画像-风险分布: 基础版没有
  app_portrait_risk_distribution: {
    basic: false,
    advanced: true,
    flagship: true
  },
  // 应用画像-风险: 基础版没有
  app_portrait_risk: { basic: false, advanced: true, flagship: true },

  // 数据：旗舰版才有
  data: { basic: false, advanced: false, flagship: true },

  // 风险页面: 基础版没有
  risk: { basic: false, advanced: true, flagship: true },

  // IP页面: 只在旗舰版
  ip: { basic: false, advanced: false, flagship: true },

  // 账号页面: 只在旗舰版
  account: { basic: false, advanced: false, flagship: true },

  // 文件页面: 只在旗舰版
  file: { basic: false, advanced: false, flagship: true },

  // 数据溯源: 只在旗舰版根据授权开放
  data_trace: {
    basic: false,
    advanced: false,
    flagship: () => hasAuthorized('audit_trace_data')
  },
  // 主体溯源: 只在旗舰版有
  subject_trace: { basic: false, advanced: false, flagship: true },
  // 待离职人员审计: 只在旗舰版根据授权开放
  pending_audit: {
    basic: false,
    advanced: false,
    flagship: () => hasAuthorized('audit_trace_suspiciousAccount')
  },
  // 特权账号审计: 只在旗舰版根据授权开放
  privilege_account_audit: {
    basic: false,
    advanced: false,
    flagship: () => hasAuthorized('audit_trace_suspiciousAccount')
  },
  // 资产与弱点离线审计: 只在旗舰版有
  asset_and_weakness_offline_audit: {
    basic: false,
    advanced: false,
    flagship: true
  },
  // 审计日志: 基础版没有
  audit_log: { basic: false, advanced: true, flagship: true },
  // 简单日志: 只在高级版根据授权开放
  simple_log: {
    basic: false,
    advanced: () => hasAuthorized('audit_log_basis'),
    flagship: false
  },
  // 复杂日志: 只在旗舰版有
  complex_log: { basic: false, advanced: false, flagship: true },
  // 运营分析报告: 基础版没有
  operation_analysis_report: { basic: false, advanced: true, flagship: true },
  // 账号行为风险治理报告: 只在旗舰版有
  account_behavior_risk_governance_report: {
    basic: false,
    advanced: false,
    flagship: true
  },
  // 数据出境报告: 只在旗舰版有
  data_outbound_report: { basic: false, advanced: false, flagship: true },
  // 设置-API审计策略: 基础版没有，高级版可选，旗舰版有
  settings_api_audit_policy: {
    basic: false,
    advanced: () => hasAuthorized('audit_log_basis'),
    flagship: true
  },
  // 设置-IP风险等级: 基础版没有
  settings_ip_risk_level: { basic: false, advanced: true, flagship: true },
  // 设置-IP威胁情报库: 基础版没有
  settings_ip_threat_intelligence_library: {
    basic: false,
    advanced: true,
    flagship: true
  },
  // 设置-账号风险等级: 基础版没有
  settings_account_risk_level: { basic: false, advanced: true, flagship: true },
  // 设置-账号状态: 基础版没有
  settings_account_status: { basic: false, advanced: true, flagship: true },
  // 设置-组织架构配置: 基础版没有
  settings_organization_structure: {
    basic: false,
    advanced: true,
    flagship: true
  },
  // 设置-账号解析: 基础版没有
  settings_account_parse: { basic: false, advanced: true, flagship: true },
  // 设置-账号解析-登录认证提取: 只在旗舰版
  settings_account_login_authentication_extraction: {
    basic: false,
    advanced: false,
    flagship: true
  },
  // 设置-账号解析-简单账号提取: 基础版没有
  settings_account_simple_account_extraction: {
    basic: false,
    advanced: true,
    flagship: true
  },
  // 设置-账号解析-SSO认证提取: 只在旗舰版根据授权开放
  settings_account_sso_authentication_extraction: {
    basic: false,
    advanced: false,
    flagship: () => hasAuthorized('account_parse_sso')
  },
  // 设置-数据标签模板-其他: 基础版根据授权开放，其他版本都有
  settings_data_tag_template_other: {
    basic: () =>
      hasAuthorized('eventProcess_dataLabel_finance') ||
      hasAuthorized('eventProcess_dataLabel_operator') ||
      hasAuthorized('eventProcess_dataLabel_specialIndustry'),
    advanced: true,
    flagship: true
  },
  // 设置-数据订阅风险: 基础版没有
  settings_data_subscription_risk: {
    basic: false,
    advanced: true,
    flagship: true
  },
  // 设置-数据订阅日志: 只在旗舰版
  settings_data_subscription_log: {
    basic: false,
    advanced: false,
    flagship: true
  },
  // 设置-文件敏感等级: 只在旗舰版
  settings_file_sensitivity_level: {
    basic: false,
    advanced: false,
    flagship: true
  },
  // 设置-风险-异常规则: 基础版没有
  settings_risk_rule: { basic: false, advanced: true, flagship: true },
  // 设置-风险-异常指标: 基础版没有
  settings_risk_quota: { basic: false, advanced: true, flagship: true },
  // 设置-阻断策略: 基础版没有
  settings_blocking_strategy: { basic: false, advanced: true, flagship: true },
  // 设置-留存策略-日志: 基础版没有，高级版可选，旗舰版有
  settings_retention_policy_log: {
    basic: false,
    advanced: () => hasAuthorized('audit_log_basis'),
    flagship: true
  },

  // 样例-gzip展示: 根据授权展示
  example_gzip: {
    basic: () => hasAuthorized('eventProcess_decompression_gzip'),
    advanced: () => hasAuthorized('eventProcess_decompression_gzip'),
    flagship: () => hasAuthorized('eventProcess_decompression_gzip')
  },
  // eolink管控：基础版没有，其余版本根据授权展示
  eolink: {
    basic: false,
    advanced: () => hasAuthorized('eolink'),
    flagship: () => hasAuthorized('eolink')
  }
};

/**
 * 检测当前用户是否拥有对应的功能
 */
const versionMap = {
  基础版: 'basic',
  高级版: 'advanced',
  旗舰版: 'flagship'
};
export function hasCapability({ oldCode, newCode }) {
  const newConfigs = getEnabledModules() || {};

  // 3.2版本授权之后才有 enabledModules 数据，否则就按原来的逻辑判断
  if (Object.keys(newConfigs).length === 0) {
    // 如果没有指定oldCode，默认开放对应模块(eolink除外，需要控制菜单和授权显示)
    if (!oldCode && newCode !== 'eolink') return true;
    const oldConfigs = getPageMenuAndBlockConfigs() || {};
    return !!oldConfigs[oldCode];
  }

  // 如果没有指定code，默认开放对应模块
  if (!newCode) return true;

  const productLevel = getProductLevel();
  if (!productLevel) return false;

  const version = versionMap[productLevel];
  if (!version) return false;

  if (typeof newCode === 'string') {
    const config = ALL_MODULE_CONFIGS[newCode];
    if (!config) return false;

    if (typeof config[version] === 'function') {
      // eolink未授权菜单不可见
      if (oldCode && newCode === 'eolink') {
        return config[version]() && getEoLinkPageMenuConfigs().state;
      }
      return config[version]();
    } else {
      return config[version];
    }
  } else if (Array.isArray(newCode)) {
    return newCode.some((code) => {
      const config = ALL_MODULE_CONFIGS[code];
      if (!config) return false;

      if (typeof config[version] === 'function') {
        return config[version]();
      } else {
        return config[version];
      }
    });
  }
}
