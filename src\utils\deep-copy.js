export function deepCopy(obj, cache = new WeakMap()) {
  if (!(obj instanceof Object) || obj instanceof Function) {
    return obj;
  }
  if (cache.get(obj)) {
    return cache.get(obj);
  }
  if (obj instanceof Date) {
    return new Date(obj);
  }
  if (obj instanceof RegExp) {
    return new RegExp(obj.source, obj.flags);
  }
  const res = Array.isArray(obj) ? [] : {};
  cache.set(obj, res);
  Object.keys(obj).forEach((key) => {
    if (obj[key] instanceof Object) {
      res[key] = deepCopy(obj[key], cache);
    } else {
      res[key] = obj[key];
    }
  });
  return res;
}
