/*
 * @Fileoverview: 处理级联问题
 * @Description: 处理级联问题：（1）输入联想关键词并选择后关闭联想下拉框并清空联想内容；（2）处理全部的禁用
 */
export function handleCascaderChange(ctx, node, typeMap, type) {
  let cascaderEle = ctx.$refs[`${type}Cascader`];
  cascaderEle = Array.isArray(cascaderEle) ? cascaderEle[0] : cascaderEle;
  if (cascaderEle?.suggestions?.length) {
    cascaderEle.toggleDropDownVisible(false);
    cascaderEle.suggestions = [];
  }
  if (typeMap[type]) {
    const changeNodeDisabled = (allNodeDisabled, otherNodeDisabled) => {
      ctx[typeMap[type]].forEach((firstLevelNode) => {
        if (firstLevelNode.value === 'ALL') {
          if (firstLevelNode.disabled !== allNodeDisabled) {
            ctx.$set(firstLevelNode, 'disabled', allNodeDisabled);
          }
        } else {
          if (firstLevelNode.disabled !== otherNodeDisabled) {
            ctx.$set(firstLevelNode, 'disabled', otherNodeDisabled);
          }
        }
      });
    };
    if (node.length) {
      if (node[0].includes('ALL')) {
        changeNodeDisabled(false, true);
      } else {
        changeNodeDisabled(true, false);
      }
    } else {
      changeNodeDisabled(false, false);
    }
  }
}
