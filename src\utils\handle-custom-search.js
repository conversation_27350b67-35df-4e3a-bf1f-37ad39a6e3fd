/*
 * @Fileoverview: 处理自定义筛选
 * @Description: 用于处理带有且或及其他筛选条件的自定义筛选项
 */
import {
  QS_TIME_SEARCH_MAP,
  CONDITION_LOGIC_MAP,
  CONDITION_LOGIC_OR
} from '@/constant/common-constants';
import { deepCopy } from '@/utils/deep-copy';
const getLabel = (itemVal, dataList) => {
  let showLabel = '';
  dataList.forEach((item) => {
    if (item.value == itemVal) {
      showLabel = item.label;
      return false;
    } else {
      if (item.children?.length > 0) {
        const activeLabel = getLabel(itemVal, item.children);
        if (activeLabel) {
          showLabel = activeLabel;
          return false;
        }
      } else {
        return false;
      }
    }
  });
  return showLabel;
};

/**
 * 格式化级联tag value转文字，选择最后一级
 * @param {Array} vals tag原始内容
 * @param {Array} dataList 枚举值
 */
export function formatLastLabels(vals = [], dataList = []) {
  let res = '';
  if (dataList.length) {
    const labels = [];
    (vals || []).forEach((item) => {
      const label = getLabel(item, dataList);
      if (label) {
        labels.push(label);
      }
    });
    res = labels.join('，');
  } else {
    res = vals?.length ? vals.join('，') : '';
  }
  res = res.replaceAll('undefined', '已删除');
  return res;
}

/**
 * 格式化级联tag value转文字
 * @param {Array} vals tag原始内容
 * @param {Array} dataList 枚举值
 */
const formatLabels = (
  vals = [],
  dataList = [],
  optionsType = { label: 'label', value: 'value' }
) => {
  let res = '';
  if (dataList.length) {
    const names = [];
    const labels = [];
    dataList.forEach((item) => {
      vals.map((val) => {
        if (val[0] == item[optionsType.value]) {
          names.push({
            label: item[optionsType.label],
            value: item[optionsType.value]
          });
        }
        if (item.children && item.children.length) {
          item.children.map((child) => {
            if (val[1] == child[optionsType.value]) {
              labels.push({
                label: child[optionsType.label],
                value: child[optionsType.value]
              });
            }
          });
        }
      });
    });
    const front = names.map((item) => item.label);
    const end = labels.map((item) => item.label);
    res = `${front.map((_item, index) => front[index] + '/' + end[index])}`;
  } else {
    res = vals
      .map((item) => {
        return `${item[0]}/${item[1]}`;
      })
      .join('，');
  }
  res = res.replaceAll('undefined', '已删除');
  return res;
};

/**
 * 单个标签格式化级联tag value转文字
 * @param {Array} vals tag绑定值（字符串）
 * @param {Array} dataList 枚举值，树形数据
 */
const findNameByIdRecursive = (tree, id) => {
  for (const node of tree) {
    if (node.id === id) {
      return node.name;
    }
    if (node.children && node.children.length > 0) {
      const result = findNameByIdRecursive(node.children, id);
      if (result) {
        return result;
      }
    }
  }
  return null;
};

/**
 * 多个标签格式化级联tag value转文字
 * @param {Array} vals tag绑定值（数组）
 * @param {Array} dataList 枚举值，树形数据
 */
const findNamesByIds = (ids = [], tree = []) => {
  return ids.map((id) => findNameByIdRecursive(tree, id));
};

/**
 * 格式化普通tag value转文字
 * @param {Array} vals tag原始内容
 * @param {Array} dataList 枚举值
 */
const formateNormalLabels = (vals = [], dataList = []) => {
  const labels = [];
  vals?.forEach((value) => {
    const label = dataList.find((item) => item.value == value);
    if (label) {
      labels.push(label.label);
    }
  });
  return labels.join('，');
};

/**
 * 格式化api标签tag value转文字
 * @param {Array} keys tag原始内容
 * @param {Array} dataList 枚举值
 */
const formatApiLabels = (keys = [], dataList = []) => {
  const names = [];
  keys.forEach((key) => {
    const label = dataList.find((item) => item.key == key);
    if (label) {
      names.push(label.value.name);
    }
  });
  return names.join('，');
};

/**
 * 格式化搜索项value，用于tag展示
 * @param {String} listType 表格所属模块
 * @param {String} type 自定义筛选字段名
 * @param {Object} tableSearchParams 自定义字段筛选参数
 * @param {Object} optionsMap 枚举值映射表
 */
export function formatTags(listType, type, tableSearchParams, optionsMap) {
  let resTagText = '';
  if (type === 'timeRange') {
    if (
      tableSearchParams.timeSearchType === 'custom' &&
      tableSearchParams[type]?.length > 0
    ) {
      resTagText = `${moment(tableSearchParams[type][0]).format(
        'YYYY-MM-DD HH:mm:ss'
      )}~${moment(tableSearchParams[type][1]).format('YYYY-MM-DD HH:mm:ss')}`;
    } else if (tableSearchParams.timeSearchType !== 'custom') {
      resTagText = QS_TIME_SEARCH_MAP[tableSearchParams.timeSearchType];
    }
  } else if (
    [
      'discoverTime',
      'earlyTimestamp',
      'lastTimestamp',
      'firstDate',
      'lastDate',
      'activeTime',
      'updateTime',
      'firstTime',
      'lastTime',
      'notifyTime'
    ].includes(type)
  ) {
    if (
      tableSearchParams[`${type}Type`] === 'custom' &&
      tableSearchParams[type]?.length > 0
    ) {
      resTagText = `${moment(tableSearchParams[type][0]).format(
        'YYYY-MM-DD HH:mm:ss'
      )}~${moment(tableSearchParams[type][1]).format('YYYY-MM-DD HH:mm:ss')}`;
    } else if (tableSearchParams[`${type}Type`] !== 'custom') {
      resTagText = QS_TIME_SEARCH_MAP[tableSearchParams[`${type}Type`]];
    }
  } else if (type === 'host') {
    if (tableSearchParams.host?.length > 0) {
      resTagText = tableSearchParams.host;
      if (tableSearchParams.hostIgnoreCase) {
        resTagText += '（忽略大小写）';
      }
    }
  } else if (type === 'showInvalid') {
    if (tableSearchParams.showInvalid) {
      resTagText = '隐藏无效应用';
    }
  } else if (
    type === 'sourceIp' ||
    type === 'systemIp' ||
    type === 'deviceIp' ||
    type === 'sysIp'
  ) {
    if (tableSearchParams[type]?.length > 0) {
      resTagText = `${formateNormalLabels([tableSearchParams[type]], optionsMap.equipmentList)}`;
    }
  } else if (tableSearchParams[type]?.length > 0) {
    // eslint-disable-next-line
    switch (type) {
      case 'reqDataLabels':
        tableSearchParams[type].forEach((item) => {
          if (item[0] === 'ALL') {
            resTagText = `(${CONDITION_LOGIC_MAP[tableSearchParams[type + 'Operator']]}) 全部`;
          } else {
            resTagText = `(${
              CONDITION_LOGIC_MAP[tableSearchParams[type + 'Operator']]
            }) ${formatLabels(tableSearchParams[type], optionsMap.labelOptions)}`;
          }
        });
        if (tableSearchParams.reqIsSingle) {
          resTagText += '（按单样例筛）';
        }
        break;
      case 'rspDataLabels':
        tableSearchParams[type].forEach((item) => {
          if (item[0] === 'ALL') {
            resTagText = `(${CONDITION_LOGIC_MAP[tableSearchParams[type + 'Operator']]}) 全部`;
          } else {
            resTagText = `(${
              CONDITION_LOGIC_MAP[tableSearchParams[type + 'Operator']]
            }) ${formatLabels(tableSearchParams[type], optionsMap.labelOptions)}`;
          }
        });
        if (tableSearchParams.rspIsSingle) {
          resTagText += '（按单样例筛）';
        }
        break;
      case 'rspDataLabelList':
      case 'reqDataLabelList':
      case 'rspLabelList':
      case 'reqDataLabelIds':
      case 'rspDataLabelIds':
      case 'dataLabelList':
        tableSearchParams[type].forEach((item) => {
          if (item[0] === 'ALL') {
            resTagText = `(${CONDITION_LOGIC_MAP[tableSearchParams[type + 'Operator']]}) 全部`;
          } else {
            resTagText = `(${
              CONDITION_LOGIC_MAP[tableSearchParams[type + 'Operator']]
            }) ${formatLabels(tableSearchParams[type], optionsMap.labelOptions)}`;
          }
        });
        break;
      case 'dataLabelIds':
        resTagText = ` ${findNamesByIds(tableSearchParams[type], optionsMap.labelOptions)}`;
        break;
      case 'featureLabels':
        if (['api', 'weakness'].includes(listType)) {
          resTagText = `(${
            CONDITION_LOGIC_MAP[tableSearchParams[type + 'Operator']]
          }) ${formatApiLabels(tableSearchParams[type], optionsMap.newFeatureList)} `;
        } else if (listType === 'app') {
          resTagText = `(${
            CONDITION_LOGIC_MAP[tableSearchParams[type + 'Operator']]
          }) ${formateNormalLabels(tableSearchParams[type], optionsMap.featureList)} `;
        }
        break;
      case 'visitDomains':
      case 'networkSegment':
      case 'deployDomains':
      case 'accessDomainIds':
        resTagText = `(${
          CONDITION_LOGIC_MAP[tableSearchParams[type + 'Operator']]
        }) ${formatLabels(tableSearchParams[type], optionsMap.networks)} `;
        break;
      case 'weaknessNames':
        tableSearchParams[type].forEach((item) => {
          if (item[0] === '全部') {
            resTagText = `(${CONDITION_LOGIC_MAP[tableSearchParams[type + 'Operator']]}) 全部`;
          } else {
            resTagText = `(${
              CONDITION_LOGIC_MAP[tableSearchParams[type + 'Operator']]
            }) ${formatLabels(tableSearchParams[type])} `;
          }
        });
        break;
      case 'riskIds':
        tableSearchParams[type].forEach((item) => {
          if (item === 'ALL') {
            resTagText = `(${CONDITION_LOGIC_MAP[tableSearchParams[type + 'Operator']]}) 全部`;
          } else {
            resTagText = `(${
              CONDITION_LOGIC_MAP[tableSearchParams[type + 'Operator']]
            }) ${formateNormalLabels(tableSearchParams[type], optionsMap.riskOptions)} `;
          }
        });
        break;
      case 'weaknessIds':
        tableSearchParams[type].forEach((item) => {
          if (item[0] === 'ALL') {
            resTagText = `(${CONDITION_LOGIC_MAP[tableSearchParams[type + 'Operator']]}) 全部`;
          } else {
            resTagText = `(${
              CONDITION_LOGIC_MAP[tableSearchParams[type + 'Operator']]
            }) ${formatLabels(tableSearchParams[type], optionsMap.weaknessOptions)} `;
          }
        });
        break;
      case 'uaTypes':
      case 'riskNames':
      case 'threatLabels':
      case 'terminals':
        tableSearchParams[type].forEach((item) => {
          if (item === 'ALL') {
            resTagText = `(${CONDITION_LOGIC_MAP[tableSearchParams[type + 'Operator']]}) 全部`;
          } else {
            resTagText = `(${
              CONDITION_LOGIC_MAP[tableSearchParams[type + 'Operator']]
            }) ${tableSearchParams[type].join('，')} `;
          }
        });
        break;
    }
  }
  return resTagText;
}

/**
 * 清空指定字段的请求参数
 * @param {String} type 自定义筛选字段名
 * @param {Object} tableSearchParams 自定义字段筛选参数
 */
export function clearTags(type, tableSearchParams) {
  if (type === 'host') {
    tableSearchParams[type] = '';
    tableSearchParams.hostIgnoreCase = false;
  } else if (type === 'showInvalid') {
    tableSearchParams.showInvalid = false;
  } else {
    if (
      [
        'timeRange',
        'notifyTime',
        'discoverTime',
        'earlyTimestamp',
        'lastTimestamp',
        'firstDate',
        'lastDate',
        'activeTime',
        'updateTime',
        'firstTime',
        'lastTime'
      ].includes(type)
    ) {
      tableSearchParams[`${type} Type`] = 'custom';
    } else if (type === 'reqDataLabels') {
      tableSearchParams[type + 'Operator'] = CONDITION_LOGIC_OR;
      tableSearchParams.reqIsSingle = false;
    } else if (type === 'rspDataLabels') {
      tableSearchParams[type + 'Operator'] = CONDITION_LOGIC_OR;
      tableSearchParams.rspIsSingle = false;
    } else {
      tableSearchParams[type + 'Operator'] = CONDITION_LOGIC_OR;
    }
    tableSearchParams[type] = null;
  }
  return tableSearchParams;
}

/**
 * 表格调用、拼接自定义筛选项请求参数
 * @param {String} type 自定义筛选字段名
 * @param {Object} tableSearchParams 自定义字段筛选参数
 * @param {String} listType 表格所属模块
 */
export function formatParams(type, tableSearchParams, listType = '') {
  if (
    [
      'timeRange',
      'notifyTime',
      'discoverTime',
      'earlyTimestamp',
      'lastTimestamp',
      'firstDate',
      'lastDate',
      'activeTime',
      'updateTime',
      'firstTime',
      'lastTime'
    ].includes(type)
  ) {
    let timeRange = [];
    //	eslint-disable-next-line
    switch (tableSearchParams[`${type} Type`]) {
      case 'custom':
        if (tableSearchParams[type]?.length) {
          timeRange = [
            Number(moment(tableSearchParams[type][0]).format('x')),
            Number(moment(tableSearchParams[type][1]).format('x'))
          ];
        } else {
          timeRange = [];
        }
        break;
      case 'recentOneDay':
      case 'recentThreeDays':
      case 'recentOneWeek':
      case 'recentOneMonth':
      case 'recentThreeMonths':
      case 'recentSixMonths':
        timeRange = [];
        break;
    }
    return {
      [`${type} Type`]: tableSearchParams[`${type} Type`],
      [type]: timeRange
    };
  } else if (type === 'host') {
    return {
      [type]: tableSearchParams[type],
      hostIgnoreCase: tableSearchParams.hostIgnoreCase
    };
  } else if (type === 'showInvalid') {
    return {
      [type]: tableSearchParams[type]
    };
  } else if (tableSearchParams[type]?.length > 0) {
    if (type === 'featureLabels') {
      if (listType === 'app') {
        return {
          [type + 'Operator']: tableSearchParams[type + 'Operator'],
          featureLabels: tableSearchParams[type]
        };
      } else {
        return {
          [type + 'Operator']: tableSearchParams[type + 'Operator'],
          newFeatureLabels: tableSearchParams[type]
        };
      }
    } else if (type === 'rspDataLabels' || type === 'reqDataLabels') {
      return {
        [`${type} Operator`]: tableSearchParams[`${type} Operator`],
        [type]: tableSearchParams[type],
        reqIsSingle: tableSearchParams.reqIsSingle,
        rspIsSingle: tableSearchParams.rspIsSingle
      };
    } else {
      return {
        [`${type} Operator`]: tableSearchParams[`${type} Operator`],
        [type]: tableSearchParams[type]
      };
    }
  }
}

/**
 * 将自定义字段筛选参数覆盖于表格请求参数
 * @param {String} listType 表格所属模块
 * @param {String} type 自定义筛选字段名
 * @param {Object} params 表格请求参数
 * @param {Object} tableSearchParams 自定义字段筛选参数
 */
export function setParams(listType, type, params, tableSearchParams) {
  if (
    [
      'timeRange',
      'discoverTime',
      'notifyTime',
      'earlyTimestamp',
      'lastTimestamp',
      'firstDate',
      'lastDate',
      'activeTime',
      'updateTime',
      'firstTime',
      'lastTime'
    ].includes(type)
  ) {
    tableSearchParams[`${type} Type`] = params[`${type} Type`] || 'custom';
    if (tableSearchParams[`${type} Type`] === 'custom') {
      tableSearchParams[type] = params[type];
    } else {
      tableSearchParams[type] = [];
    }
  } else if (type === 'host') {
    tableSearchParams.host = params.host;
    tableSearchParams.hostIgnoreCase = params.hostIgnoreCase;
  } else if (type === 'showInvalid') {
    tableSearchParams.showInvalid = params.showInvalid;
  } else {
    if (type === 'featureLabels') {
      if (listType === 'api' || listType === 'weakness') {
        tableSearchParams[`${type} Operator`] = params[`${type} Operator`];
        tableSearchParams[type] = params.newFeatureLabels;
      } else if (listType === 'app') {
        tableSearchParams[`${type} Operator`] = params[`${type} Operator`];
        tableSearchParams[type] = params.featureLabels;
      }
    } else if (type === 'rspDataLabels') {
      tableSearchParams[`${type} Operator`] = params[`${type} Operator`];
      tableSearchParams[type] = params[type];
      tableSearchParams.rspIsSingle = params.rspIsSingle;
    } else if (type === 'reqDataLabels') {
      tableSearchParams[`${type} Operator`] = params[`${type} Operator`];
      tableSearchParams[type] = params[type];
      tableSearchParams.reqIsSingle = params.reqIsSingle;
    } else {
      tableSearchParams[`${type} Operator`] = params[`${type} Operator`];
      tableSearchParams[type] = params[type];
    }
  }
  return tableSearchParams;
}

/**
 * 视图筛选处理请求参数
 * @param {Object} viewFeSearchParams 视图筛选处理请求参数
 */
export function handleViewSearch(viewFeSearchParams) {
  let searchParams = {};
  if (viewFeSearchParams.searchParams) {
    if (viewFeSearchParams.searchParams.attackSuccess) {
      viewFeSearchParams.searchParams.attackSuccess = JSON.parse(
        viewFeSearchParams.searchParams.attackSuccess
      );
    }
    if (viewFeSearchParams.searchParams.riskMark) {
      viewFeSearchParams.searchParams.riskMark = JSON.parse(
        viewFeSearchParams.searchParams.riskMark
      );
    }
    searchParams = deepCopy(viewFeSearchParams.searchParams);
  }
  if (viewFeSearchParams.tableSearchParams) {
    const tableSearchParams = deepCopy(viewFeSearchParams.tableSearchParams);
    if (tableSearchParams.rspLabelList?.length) {
      searchParams.rspLabelList = tableSearchParams.rspLabelList;
      searchParams.rspLabelListOperator =
        tableSearchParams.rspLabelListOperator;
    }
    const arrayList = [
      'riskNames',
      'riskIds',
      'weaknessIds',
      'threatLabels',
      'weaknessNames',
      'visitDomains',
      'networkSegment',
      'accessDomainIds',
      'deployDomains',
      'reqDataLabels',
      'rspDataLabels',
      'rspDataLabelList',
      'reqDataLabelList',
      'featureLabels',
      'rspLabelList',
      'terminals',
      'uaTypes',
      'accessDomainIds'
    ];
    arrayList.forEach((item) => {
      if (tableSearchParams[`${[item]} Operator`]) {
        if (searchParams[item]?.length) {
          searchParams[item] = tableSearchParams[item];
          searchParams[`${[item]} Operator`] =
            tableSearchParams[`${[item]} Operator`];
          if (item === 'reqDataLabels') {
            searchParams.reqIsSingle = tableSearchParams.reqIsSingle || false;
          } else if (item === 'rspDataLabels') {
            searchParams.rspIsSingle = tableSearchParams.rspIsSingle || false;
          }
        }
      }
    });
    const timeNameList = [
      'firstTime',
      'updateTime',
      'activeTime',
      'discoverTime',
      'notifyTime',
      'earlyTimestamp',
      'lastTimestamp',
      'firstDate',
      'lastDate'
    ];
    timeNameList.forEach((item) => {
      if (tableSearchParams[item]?.length) {
        searchParams.item = tableSearchParams.item;
        searchParams[`${item} Type`] = tableSearchParams[`${item} Type`];
      }
    });
  }
  return searchParams;
}
