/*
 * @Fileoverview: 密码强度校验
 * @Description: 密码强度校验展示用
 */

// 高密码强度
const HIGH_STRENGTH = 3;
// 中密码强度
const MIDDLE_STRENGTH = 2;
// 弱密码强度
const LOW_STRENGTH = 1;

/**
 * 弱：密码长度6位以上
 * @param {String} pwd 密码
 */
function isLowIntensity(pwd = '') {
  return pwd.length > 6;
}

/**
 * 中：密码长度8位及以上，必须包含大写字母、小写字母、数字中的两类且不能全部都是字母
 * @param {String} pwd 密码
 */
function isMiddleIntensity(pwd = '') {
  return pwd.length >= 8 && /^(?=.*\d)(?=.*[A-Za-z]).{8,}$/.test(pwd);
}

/**
 * 高：密码长度8位以上，必须包含大写字母、小写字母、数字、下划线中的三类
 * @param {String} pwd 密码
 */
function isHighIntensity(pwd = '') {
  if (pwd.length <= 8) {
    return false;
  }
  // 有数字
  const hasNumber = /\d/.test(pwd);
  // 有大写字母
  const hasUpperLetter = /[A-Z]/.test(pwd);
  // 有小写字母
  const hasLowerLetter = /[a-z]/.test(pwd);
  // 有下划线
  const hasUnderline = /_/.test(pwd);
  return (
    Number(hasNumber) +
      Number(hasUpperLetter) +
      Number(hasLowerLetter) +
      Number(hasUnderline) >=
    3
  );
}

/**
 * 校验强度
 * @param {Function} checkFunc 校验方法
 * @param {String} errMsg 错误文案
 */
function genValidator(checkFunc, errMsg) {
  return function (pwd) {
    let valid = true;
    let err = '';
    if (!checkFunc(pwd)) {
      valid = false;
      err = errMsg;
    }
    return { valid, err };
  };
}

/**
 * 获取密码强度
 * @param {String} pwd 密码
 */
export function getPwdIntensity(pwd = '') {
  let intensity = 0;
  if (isLowIntensity(pwd)) {
    intensity = 1;
  }
  if (isMiddleIntensity(pwd)) {
    intensity = 2;
  }
  if (isHighIntensity(pwd)) {
    intensity = 3;
  }
  return intensity;
}

export const pwdValidators = {
  [HIGH_STRENGTH]: genValidator(
    isHighIntensity,
    '密码长度必须8位以上，必须包含大写字母、小写字母、数字、下划线中的三类'
  ),
  [MIDDLE_STRENGTH]: genValidator(
    isMiddleIntensity,
    '密码长度必须8位以上，必须包含大写字母、小写字母、数字中的两类且必须包含数字'
  ),
  [LOW_STRENGTH]: genValidator(isLowIntensity, '密码长度必须6位以上')
};
