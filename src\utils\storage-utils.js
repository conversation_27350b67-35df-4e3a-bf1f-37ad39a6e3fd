function getLocalStorageKey(key) {
  // eslint-disable-next-line no-undef
  return `${__webpack_public_path__}_${key}`;
}

export function setLocalStorage(key, value) {
  const finalKey = getLocalStorageKey(key);
  localStorage.setItem(finalKey, value);
}

export function getLocalStorage(key) {
  const finalKey = getLocalStorageKey(key);
  return localStorage.getItem(finalKey);
}

export function removeLocalStorage(key) {
  const finalKey = getLocalStorageKey(key);
  localStorage.removeItem(finalKey);
}

/*
 * @Fileoverview: 用户、授权信息读取
 * @Description: 用于读取存储在本地库中的用户、授权信息
 */

/**
 * 读取本地用户信息
 */
export function getLocalUserInfo() {
  const str = getLocalStorage('userInfo') || '{}';
  return JSON.parse(str);
}

/**
 * 更新本地用户信息
 * @param {Object} userInfo 用户信息
 * @param {Object} store vuex库
 */
export function setLocalUserInfo(userInfo, store) {
  setLocalStorage('userInfo', JSON.stringify(userInfo));
  store.commit('setUserInfo', userInfo);
}

/**
 * 清理本地用户信息
 * @param {Object} store 本地库
 */
export function clearLocalUserInfo(store) {
  removeLocalStorage('userInfo');
  store.commit('setUserInfo', {});
}

/**
 * 获取本地模块授权信息
 */
export function getPageMenuAndBlockConfigs() {
  const str = getLocalStorage('pageMenuAndBlockConfigs') || '{}';
  return JSON.parse(str);
}

/**
 * 更新本地模块授权信息
 * @param {Object} configs 授权信息
 * @param {Object} store 本地库
 */
export function setPageMenuAndBlockConfigs(configs) {
  setLocalStorage('pageMenuAndBlockConfigs', JSON.stringify(configs));
}

/**
 * 获取eolink模块授权信息
 */
export function getEoLinkPageMenuConfigs() {
  const str = getLocalStorage('eoLinkPageMenuConfigs') || '{}';
  return JSON.parse(str);
}

export function setEoLinkPageMenuConfigs(configs, store) {
  setLocalStorage('eoLinkPageMenuConfigs', JSON.stringify(configs));
  store.commit('setEoLinkPageMenuConfigs', configs);
}

export function setDomainQuery(query) {
  setLocalStorage('domainQuery', JSON.stringify(query));
}

export function getDomainQuery() {
  // eslint-disable-next-line no-undef
  const str = getLocalStorage('domainQuery') || '';

  if (str) {
    return JSON.parse(str);
  }
}

export function clearDomainQuery() {
  removeLocalStorage('domainQuery');
}

/**
 * 获取本地模块授权信息
 */
export function getCompany() {
  const str = getLocalStorage('company') || '';
  return str;
}

/**
 * 更新客户信息
 * @param {Object} store 本地库
 */
export function setCompany(company, store) {
  setLocalStorage('company', company);
  store.commit('setCompany', company);
}

/**
 * 获取产品级别
 */
export function getProductLevel() {
  const str = getLocalStorage('productLevel') || '';
  return str;
}

/**
 * 更新产品级别
 * @param {Object} store 本地库
 */
export function setProductLevel(company, store) {
  setLocalStorage('productLevel', company);
  store.commit('setProductLevel', company);
}

/**
 * 获取能力分层数据
 */
export function getEnabledModules() {
  const str = getLocalStorage('enabled_modules') || '{}';
  return JSON.parse(str);
}

/**
 * 更新客户信息
 * @param {Object} store 本地库
 */
export function setEnabledModules(data, store) {
  setLocalStorage('enabled_modules', JSON.stringify(data));
  store.commit('setEnabledModules', data);
}

const historyClearKey = 'history_clear_queue';
/**
 * 获取预清理ID队列
 */
export function getHistoryClearTasks() {
  const str = getLocalStorage(historyClearKey) || '{}';
  return JSON.parse(str);
}

/**
 * 存储预清理ID队列
 */
export function setHistoryClearTasks(id, statusMap, store) {
  if (!id) return;

  const taskMap = getHistoryClearTasks();
  if (!taskMap[id]) {
    taskMap[id] = { preCompleted: false, finalCompleted: false };
  } else {
    taskMap[id].preCompleted = statusMap.preCompleted || false;
    taskMap[id].finalCompleted = statusMap.finalCompleted || false;
    taskMap[id].messaged = statusMap.messaged || false;
  }

  setLocalStorage(historyClearKey, JSON.stringify(taskMap));
  store.commit('setHistoryClearTasks', taskMap);
}

/**
 * 删除指定清理ID
 */
export function deleteHistoryClearTask(id, store) {
  if (!id) return;

  const taskMap = getHistoryClearTasks();
  if (taskMap[id]) {
    delete taskMap[id];
    setLocalStorage(historyClearKey, JSON.stringify(taskMap));
    store.commit('setHistoryClearTasks', taskMap);
  }
}

/**
 * 更新是否主节点
 * @param {Object} store 本地库
 */
export function setIsMaster(isMaster, store) {
  setLocalStorage('is_master', isMaster);
  store.commit('setIsMaster', isMaster);
}

/**
 * 获取是否独立节点
 */
export function getIsStandLong() {
  const str =
    localStorage.getItem(
      // eslint-disable-next-line no-undef
      `${__webpack_public_path__}_is_stand_long`
    ) || '';
  return str === 'true';
}

/**
 * 更新是否独立节点
 * @param {Object} store 本地库
 */
export function setIsStandLong(isStandLong, store) {
  localStorage.setItem(
    // eslint-disable-next-line no-undef
    `${__webpack_public_path__}_is_stand_long`,
    isStandLong
  );
  store.commit('setIsStandLong', isStandLong);
}

/**
 * 设置OriginalStyle
 * @param {Object} store 本地库
 */
export function setOriginalStyle(style, store) {
  setLocalStorage('style', style);
  store.commit('setOriginalStyle', style);
}

/**
 * 获取OriginalStyle
 */
export function getOriginalStyle() {
  const str = getLocalStorage('style') || '';
  return str;
}

/**
 * 获取是否启用了ldap
 */
export function getLdapLoggedIn() {
  const str = getLocalStorage('ldapLoggedIn') || 'false';
  return str === 'true';
}

/**
 * 设置是否启用了ldap
 */
export function setLdapLoggedIn(ldapLoggedIn) {
  setLocalStorage('ldapLoggedIn', ldapLoggedIn);
}

/**
 * 存储菜单
 */

export function setMenuData(menuData) {
  setLocalStorage('menuData', JSON.stringify(menuData));
}

/**
 *获取菜单
 */
export function getMenuData() {
  return JSON.parse(getLocalStorage('menuData')) || [];
}

/**
 * 记录菜单code
 */
export function setRecordMenuCode(menuCodes) {
  setLocalStorage('recordMenuCode', JSON.stringify(menuCodes));
}

/**
 * 获取菜单code
 */
export function getRecordMenuCode() {
  return JSON.parse(getLocalStorage('recordMenuCode'));
}
/**
 * 清除所有本地缓存
 */
export function clearAllLocalStorage() {
  localStorage.clear();
}
/**
 * 存储模版是否启用
 * @param {*}
 * @returns
 */

export function setSystemTemplate(val) {
  //val:none,系统初始化没设置，has已设置，notip不提醒
  setLocalStorage('isHasTemplate', JSON.stringify(val));
}

export function getSystemTemplate() {
  return JSON.parse(getLocalStorage('isHasTemplate'));
}
