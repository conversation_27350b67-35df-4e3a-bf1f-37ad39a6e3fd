/*
 * @Fileoverview: 字符串格式化处理
 * @Description: 全局通用字符串格式化处理
 */

import moment from 'moment';
// 转换日期字符串形式为日期格式
export function stringToDate(date) {
  return `${date.substring(0, 4)}-${date.substring(4, 6)}-${date.substring(
    6,
    8
  )}`;
}

/**
 * 根据正则表达式生成字符串验证方法
 * @param {RegExp} reg 正则表达式
 */
function genRegValidateFunc(reg) {
  return function (text) {
    if (!text) {
      return false;
    }
    text = text.toString().trim();
    return reg.test(text);
  };
}

// ip地址校验，可用逗号或短横线连接
const ipRegStr =
  '(?:25[0-5]|2[0-4]\\d|1\\d{2}|[1-9]?\\d)(?:\\.(?:25[0-5]|2[0-4]\\d|1\\d{2}|[1-9]?\\d)){3}';
const maskRegStr = '(?:3[0-2]|[1-2]?\\d)';
const ipWithMaskRegStr = `${ipRegStr}(?:\\/${maskRegStr})?`;
const ipReg = new RegExp(`^${ipWithMaskRegStr}(?:(,|-)${ipWithMaskRegStr})*$`);
export const validateIp = genRegValidateFunc(ipReg);

// 邮箱校验
const emailReg =
  /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
export const validateEmail = genRegValidateFunc(emailReg);

// 校验端口号
const PortsReg =
  /^([1-9](\d{0,3}))$|^([1-5]\d{4})$|^(6[0-4]\d{3})$|^(65[0-4]\d{2})$|^(655[0-2]\d)$|^(6553[0-5])$/;
export const validatePorts = genRegValidateFunc(PortsReg);

// 校验数字
const numberReg = /^([1-9]|[1-9]\d|100)$/;
export const validateNumber = genRegValidateFunc(numberReg);

/**
 * 格式化uri为url
 */
export const formatUri = function (uri) {
  if (uri) {
    return uri.replace(/^httpap(i|p):/, '');
  }
  return uri;
};
// eslint-disable-next-line
export const formatTime = function (
  timestamp,
  pattern = 'YYYY-MM-DD HH:mm:ss'
) {
  if (!timestamp) return timestamp;
  return moment(timestamp).format(pattern);
};
// eslint-disable-next-line
export const formatTimeUtc = function (timeStr) {
  const date = new Date(timeStr);

  // 转为本地时间格式：YYYY-MM-DD HH:mm:ss
  const formatted =
    `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')} ` +
    `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}:${date.getSeconds().toString().padStart(2, '0')}`;

  console.log(formatted);
  // 输出示例（取决于你的时区）: 2025-04-10 19:59:11
};

export const convertTimestampToDateTime = function (timestamp) {
  if (!timestamp) return '-';
  const date = new Date(timestamp);

  // 年月日部分
  const year = date.getUTCFullYear();
  const month = String(date.getUTCMonth() + 1).padStart(2, '0'); // 月份从0开始，需+1
  const day = String(date.getUTCDate()).padStart(2, '0');

  // 时分秒部分
  const hours = String(date.getUTCHours()).padStart(2, '0');
  const minutes = String(date.getUTCMinutes()).padStart(2, '0');
  const seconds = String(date.getUTCSeconds()).padStart(2, '0');

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};

// bit转Mbps，只保留两位小数，没有四舍五入
export function bitToMbps(bit, isShowUnit = true) {
  const num = Number(bit);

  if (num < 1000) {
    return [num, isShowUnit ? 'bps' : ''].join(' ');
  }

  const kNum = num / 1000;
  if (kNum < 1000) {
    return [kNum.toFixed(2), isShowUnit ? 'Kbps' : ''].join(' ');
  }

  const mNum = kNum / 1000;
  return [mNum.toFixed(2), isShowUnit ? 'Mbps' : ''].join(' ');
}

// 指标规则与作用域规则判断
export function checkRuleIsValid(rule, value, callback, ruleList) {
  if (value == '') {
    return callback(new Error('规则不能为空！'));
  }

  const formatRule = value.replace(/[!,&,|,(,),[,0-9]/g, '');
  if (formatRule.length > 0) {
    if (formatRule.includes('（') || formatRule.includes('）')) {
      return callback(new Error('请使用英文括号()！'));
    }
    return callback(new Error('规则中包含非法字符！'));
  }

  const numMatchRegExp = /[0-9]+/g;
  const numList = (value.match(numMatchRegExp) || []).map((item) => +item);
  const idList = new Array(ruleList.length).fill(0).map((item, idx) => idx + 1);

  if (numList.length == 0) {
    return callback(new Error('规则填写不规范!'));
  }

  for (const id of idList) {
    if (!numList.includes(id)) {
      return callback(new Error(`指标 ${id} 没有配置在规则中`));
    }
  }

  for (const num of numList) {
    if (!idList.includes(num)) {
      return callback(
        new Error(`规则中使用的指标 ${num} 不存在，请先配置指标`)
      );
    }
  }

  callback();
}

/**
 * 格式化存储大小，将字节转换为合适的单位
 */
export function formatStorageSize(size) {
  const units = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
  if (size === 0) return '0B';
  const i = Math.floor(Math.log(size) / Math.log(1024));
  const value = size / Math.pow(1024, i);
  return `${value.toFixed(2)}${units[i]}`;
}

// 将字符串中的特殊字符转义，使其可以在正则表达式中正常使用
export function strToRegex(str, needTransformChar) {
  needTransformChar = needTransformChar || [
    '(',
    ')',
    '{',
    '}',
    '[',
    ']',
    '|',
    '+',
    '*',
    '\\',
    '$',
    '.',
    '^',
    '?'
  ];
  let strArr = str.split('');
  strArr = strArr.map((item) => {
    if (needTransformChar.includes(item)) {
      item = '\\' + item;
      return item;
    } else {
      return item;
    }
  });
  return strArr.join('');
}
/**
 * 将时间戳转为年月日时分秒
 * @param {number} timestamp 时间戳
 * @returns {string} 格式化后的时间字符串
 */
export function formatTimestamp(timestamp) {
  const date = new Date(timestamp);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

//获取根据url获取url中的key的值
export function getUrlParms(url, name) {
  var reg = new RegExp('(^|\\?|&)' + name + '=([^&]*)(\\s|&|$)', 'i');
  if (reg.test(url)) return unescape(RegExp.$2.replace(/\+/g, ' '));
  return '';
}

export const handleCron = (context = {}) => {
  let cron = '';
  // 处理cron表达式
  const cycl = context?.cycl || '';
  const date = context?.date || '';
  const week = context?.week || {};
  const month = context?.month || {};
  if (cycl === 'DAY') {
    const hours = new Date(parseInt(date)).getHours();
    const minutes = new Date(parseInt(date)).getMinutes();
    const seconds = new Date(parseInt(date)).getSeconds();
    cron = `${seconds} ${minutes} ${hours} ? * *`;
  } else if (cycl === 'WEEK') {
    const dayIndex = ['1', '2', '3', '4', '5', '6', '7'].indexOf(week) + 1;
    cron = `0 0 0 ? * ${dayIndex} *`;
  } else if (cycl === 'MONTH') {
    const day = month === 'L' ? 'L' : month;
    cron = `0 0 0 ${day} * ? *`;
  }
  return cron;
};

/**
 * 时间戳转为UTC时间(东八区)
 * @param {*} timestamp
 * @returns
 */
export const timestampToUTC8 = (timestamp) => {
  const date = new Date(timestamp); // 加上东八区偏移（毫秒）
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');
  return `${year}-${month}-${day}T${hours}:${minutes}:${seconds}Z`;
};
/**
 * 将utc时间转换
 * @param {*} utcTime
 * @returns
 */
export const handleUtcTime = (utcTime) => {
  const date = new Date(utcTime);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};
