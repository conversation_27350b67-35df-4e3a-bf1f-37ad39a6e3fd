/**
 * 多对象监听 + 排除字段 + 深度监听 + immediate
 * @param {Vue} vm 当前组件实例
 * @param {Array<Object>} configs 监听配置项数组
 * @example config: {
 *   sourceKey: 'obj',                     // 监听字段
 *   excludePaths: ['b', 'c.deep.val'],    // 要排除的字段或路径
 *   callback: (newVal, oldVal) => {},     // 变化回调
 *   deep: true,                           // 是否深度监听
 *   immediate: true                       // 是否立即执行
 * }
 */
export function createMultiWatchExclude(vm, configs) {
  vm.$options.computed = vm.$options.computed || {};

  configs.forEach((cfg, index) => {
    const {
      sourceKey,
      excludePaths = [],
      callback,
      deep = false,
      immediate = false
    } = cfg;

    const computedName = `__filtered_${sourceKey}_${index}__`;

    if (!vm.$options.computed[computedName]) {
      vm.$options.computed[computedName] = function () {
        return filterByExcludePath(this[sourceKey], excludePaths, deep);
      };
    }

    vm.$watch(
      computedName,
      function (newVal, oldVal) {
        callback.call(this, newVal, oldVal);
      },
      { deep: false, immediate }
    );
  });
}

/**
 * 过滤对象中指定的路径
 * @param {Object} obj 被过滤的对象
 * @param {string[]} excludePaths 要排除的路径（支持 a、a.b、a.b.c）
 * @param {boolean} deep 是否深度复制
 */
function filterByExcludePath(obj, excludePaths = [], deep = false) {
  if (!obj || typeof obj !== 'object') return obj;

  const result = Array.isArray(obj) ? [] : {};

  for (const key in obj) {
    const fullKeyPaths = excludePaths.filter(
      (path) => path === key || path.startsWith(`${key}.`)
    );
    const subPaths = fullKeyPaths
      .map((path) => path.split('.'))
      .filter((parts) => parts.length > 1)
      .map((parts) => parts.slice(1).join('.'));

    if (fullKeyPaths.includes(key)) continue;

    const value = obj[key];

    if (deep && typeof value === 'object' && value !== null) {
      result[key] = filterByExcludePath(value, subPaths, true);
    } else {
      result[key] = value;
    }
  }

  return result;
}
